package com.insta360.store.business.integration.wto.oms.service.handler.order;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.configuration.utils.ProfileUtil;
import com.insta360.store.business.discount.model.Coupon;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.service.CouponService;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.integration.avalara.enums.AvalaraJurisdictionType;
import com.insta360.store.business.integration.avalara.enums.StoreTransactionType;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteResultBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsItemTaxBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsOrderDeliveryBO;
import com.insta360.store.business.integration.wto.oms.constant.StoreOrderConstant;
import com.insta360.store.business.integration.wto.oms.lib.module.ExtendProps;
import com.insta360.store.business.integration.wto.oms.lib.module.ReceiverInfo;
import com.insta360.store.business.integration.wto.oms.lib.module.SevenLevelTaxPrice;
import com.insta360.store.business.integration.wto.oms.lib.module.order.DeliveryOrder;
import com.insta360.store.business.integration.wto.oms.lib.module.order.MallSalesOrderDiscount;
import com.insta360.store.business.integration.wto.oms.lib.module.order.OrderDetail;
import com.insta360.store.business.integration.wto.oms.lib.module.order.OrderLine;
import com.insta360.store.business.integration.wto.oms.lib.requrest.BaseOmsRequest;
import com.insta360.store.business.integration.wto.oms.lib.requrest.order.PushStoreOrderRequest;
import com.insta360.store.business.integration.wto.oms.lib.response.PushStoreOrderResponse;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.model.AvalaraTaxInfo;
import com.insta360.store.business.meta.model.AvalaraTaxRateSummary;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.enums.OrderStockSourceType;
import com.insta360.store.business.order.enums.PushPlatformType;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.order.service.OrderAdminRemarkService;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderItemCustomsTaxRateService;
import com.insta360.store.business.order.service.OrderOperatorRecordService;
import com.insta360.store.business.tradeup.model.TradeupOrder;
import com.insta360.store.business.tradeup.service.TradeupOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2024/11/29
 * @Description:
 */
@Scope("prototype")
@Component
public class StorePushOrderHandler extends BaseStoreOrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StorePushOrderHandler.class);

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    OrderAdminRemarkService orderAdminRemarkService;

    @Autowired
    TradeupOrderService tradeupOrderService;

    @Autowired
    OrderOperatorRecordService orderOperatorRecordService;

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    CouponService couponService;

    @Autowired
    OrderItemCustomsTaxRateService orderItemCustomsTaxRateService;

    /**
     * 收货人信息
     */
    private OrderDelivery orderDelivery;

    /**
     * 订单子项
     */
    private List<OrderItem> orderItems;

    @Override
    protected void init(OmsExecuteBO omsExecuteBo) {
        super.init(omsExecuteBo);
        this.orderDelivery = orderDeliveryService.getOrderDelivery(omsExecuteBo.getOrderId());
        this.orderItems = orderItemService.getByOrder(omsExecuteBo.getOrderId());
    }

    @Override
    protected OmsExecuteResultBO doExecuteOmsTransaction(OmsExecuteBO omsExecuteBo) {
        LOGGER.info(String.format("商城推单开始。。。入参{%s}", omsExecuteBo));
        // 初始化推单相关信息
        this.init(omsExecuteBo);
        // 封装推单参数
        BaseOmsRequest omsRequest = this.getOmsRequest(omsExecuteBo);
        LOGGER.info(String.format("商城推单请求，订单号{%s} 参数{%s}", order.getOrderNumber(), omsRequest));
        // 处理执行结果
        String response = omsRequest.executePost();
        LOGGER.info(String.format("商城推单响应，订单号{%s} 参数{%s}", order.getOrderNumber(), response));
        // 保存回调结果
        this.saveCallRecord(order.getOrderNumber(), StringUtils.EMPTY, JSON.toJSONString(omsRequest), JSON.toJSONString(response), StoreSdkCallBusinessType.JY_OMS, StoreSdkCallApiType.STORE_PUSH_ORDER_TO_OMS);
        PushStoreOrderResponse storeOrderResponse = PushStoreOrderResponse.parse(response);
        // 失败告警
        if (storeOrderResponse.isFail()) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单{%s}推送oms失败！异常信息{%s}", order.getOrderNumber(), storeOrderResponse.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);

            String message = String.format("商城订单:[%s]，推送oms失败,错误原因:%s", order.getOrderNumber(), storeOrderResponse.getMessage());
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.OPERATION_NOTICE);
        } else {
            this.updateOrderPushPlatform(order);
        }
        // 系统自动推单记录
        if (storeOrderResponse.getSuccess() && OrderStockSourceType.SYSTEM.equals(omsExecuteBo.getOrderStockSourceType())) {
            this.updateOrderOperatorRecord(order);
        }
        LOGGER.info(String.format("商城订单{%s} 推单结束。。。响应{%s}", order.getOrderNumber(), response));
        return new OmsExecuteResultBO(storeOrderResponse);
    }

    @Override
    protected BaseOmsRequest getOmsRequest(OmsExecuteBO omsExecuteBo) {
        PushStoreOrderRequest pushStoreOrderRequest = new PushStoreOrderRequest(omsConfiguration);

        // 判断是否重新分摊商品子项优惠金额
        if (this.isNormalDiscountOrder()) {
            this.itemDiscountRecalculate();
        }

        pushStoreOrderRequest.setDeliveryOrder(packDeliveryOrder());
        pushStoreOrderRequest.setOrderLines(packOrderLines());
        pushStoreOrderRequest.setExtendProps(packExtendProps());
        if (InstaCountry.BR.equals(order.country())) {
            DeliveryOrder deliveryOrder = pushStoreOrderRequest.getDeliveryOrder();
            ExtendProps extendProps = pushStoreOrderRequest.getExtendProps();
            deliveryOrder.setFreight(new BigDecimal(String.valueOf(orderPayment.getShippingCost())).add(new BigDecimal(extendProps.getExpressFeeTaxAmount())).toString());
            pushStoreOrderRequest.setDeliveryOrder(deliveryOrder);
        }

        return pushStoreOrderRequest;
    }

    /**
     * 封装订单相关信息
     *
     * @return
     */
    private DeliveryOrder packDeliveryOrder() {
        LOGGER.info("订单{} packDeliveryOrder开始。。。", order.getOrderNumber());
        DeliveryOrder deliveryOrder = new DeliveryOrder();
        // 订单信息
        deliveryOrder.setBuyerMessage(StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : StringUtils.EMPTY);
        OrderAdminRemark adminRemark = orderAdminRemarkService.getByOrder(this.order.getId());
        deliveryOrder.setSellerMessage(Objects.nonNull(adminRemark) ? adminRemark.getContent() : StringUtils.EMPTY);
        deliveryOrder.setBuyerNick(getVipName());
        deliveryOrder.setDeliveryOrderCode(order.getOrderNumber());
        deliveryOrder.setOaidOrderSourceCode(order.getOrderNumber());
        deliveryOrder.setIsUrgency(order.getShipPriority());
        deliveryOrder.setOrderType(StoreOrderConstant.ORDER_TYPE);
        // 兼容历史订单数据
        List<OrderState> orderStates = Arrays.asList(OrderState.success, OrderState.on_delivery, OrderState.charge_back, OrderState.closed);
        deliveryOrder.setStatus(orderStates.contains(order.orderState()) ? "TRADE_FINISHED" : StoreOrderConstant.ORDER_STATUS);

        // 支付信息
        String totalPayAmount = String.valueOf(orderPayment.getTotalPayPrice().getAmount());
        deliveryOrder.setGotAmount(totalPayAmount);
        deliveryOrder.setTotalAmount(totalPayAmount);
        deliveryOrder.setShopCode(this.getShopCode());
        BigDecimal shipCost = new BigDecimal(String.valueOf(orderPayment.getShippingCost())).add(orderPayment.getShippingCostTax());
        deliveryOrder.setFreight(shipCost.toString());
        packUsCustomsTax(deliveryOrder);

        // 时间
        String orderCreateTime = timestampConversion(order.getCreateTime());
        String orderPayTime = timestampConversion(orderPayment.getPayTime());
        deliveryOrder.setCreateTime(orderCreateTime);
        deliveryOrder.setOperateTime(orderCreateTime);
        deliveryOrder.setPlaceOrderTime(orderPayTime);
        deliveryOrder.setModifiedTime(timestampConversion(LocalDateTime.now()));

        // 收件人信息
        deliveryOrder.setReceiverInfo(packReceiverInfo());
        LOGGER.info(String.format("订单{%s} packDeliveryOrder结束。。。{%s}", order.getOrderNumber(), deliveryOrder));
        return deliveryOrder;
    }

    /**
     * 封装扩展参数
     *
     * @return
     */
    private ExtendProps packExtendProps() {
        LOGGER.info("订单{} packExtendProps开始。。。", order.getOrderNumber());
        ExtendProps extendProps = new ExtendProps();
        extendProps.setCurrencyCode(orderPayment.getCurrency());
        extendProps.setTotalTaxPrice(packTotalTaxPrice());
        extendProps.setTagCodes(packTagCodes());
        extendProps.setSevenLevelTaxPrice(packSevenLevelTaxPrice());
        extendProps.setMallSalesOrderDiscounts(packOrderDiscounts());

        if (PaymentChannel.isNormalLyChannel(orderPayment.paymentChannel())) {
            extendProps.setCustomCodeOne(StoreOrderConstant.FREE_COUPON_CUSTOM_CODE);
        }

        // 传税地区处理（美国、加拿大、巴西）
        if (StoreOrderConstant.TAX_PRICE_AREA.contains(order.country())) {
            if (InstaCountry.BR.equals(order.country())) {
                Map<Integer, BigDecimal> taxApportionDetailMap = this.getBrazilOrderItemTaxApportionDetail();
                BigDecimal shippingCostTax = orderPayment.getShippingCostTax();
                extendProps.setExpressFeeTaxAmount(Objects.nonNull(shippingCostTax) ? shippingCostTax.toString() : BigDecimal.ZERO.toString());
                extendProps.setOrderDetails(packBrazilOrderDetails(taxApportionDetailMap));
            } else {
                extendProps.setOrderDetails(packOrderDetails());
                extendProps.setExpressFeeTaxAmount(orderPayment.getShippingCostTax().toString());
            }
        } else {
            // 其他地区不传运费税额，由OMS根据税率配置计算
            extendProps.setOrderDetails(packOrderDetails());
            extendProps.setExpressFeeTaxAmount(null);
        }

        LOGGER.info(String.format("订单 {%s} packExtendProps结束。。。{%s}", order.getOrderNumber(), extendProps));
        return extendProps;
    }

    /**
     * 封装订单优惠信息
     *
     * @return
     */
    private List<MallSalesOrderDiscount> packOrderDiscounts() {
        Float totalDiscountFee = orderPayment.getTotalDiscountFee();
        if (Objects.isNull(totalDiscountFee) || totalDiscountFee == 0) {
            return new ArrayList<>(0);
        }
        // 默认其他优惠类型
        MallSalesOrderDiscount mallSalesOrderDiscount = new MallSalesOrderDiscount();
        mallSalesOrderDiscount.setDiscountAmount(String.valueOf(totalDiscountFee));
        mallSalesOrderDiscount.setDiscountName(StoreOrderConstant.OTHER);
        mallSalesOrderDiscount.setDiscountType(StoreOrderConstant.OTHER);

        // 代金券优惠
        String giftCardCode = order.getGiftCardCode();
        if (StringUtils.isNotBlank(giftCardCode)) {
            GiftCard giftCard = giftCardService.getByCode(giftCardCode);
            if (Objects.nonNull(giftCard)) {
                mallSalesOrderDiscount.setDiscountType(StoreOrderConstant.GIFT_CARD_TYPE);
                mallSalesOrderDiscount.setDiscountName(giftCard.discountType().name);
            }
            return Collections.singletonList(mallSalesOrderDiscount);
        }

        // 优惠券
        String couponCode = order.getCouponCode();
        if (StringUtils.isNotBlank(couponCode)) {
            Coupon coupon = couponService.getByCode(couponCode);
            if (Objects.nonNull(coupon)) {
                mallSalesOrderDiscount.setDiscountType(StoreOrderConstant.COUPON_TYPE);
                mallSalesOrderDiscount.setDiscountName(coupon.discountType().name);
            }
        }
        return Collections.singletonList(mallSalesOrderDiscount);
    }

    /**
     * 封装订单子项明细
     *
     * @return
     */
    private List<OrderDetail> packOrderDetails() {
        return orderItems.stream().map(orderItem -> {
            OrderDetail orderDetail = new OrderDetail();
            String orderItemId = String.valueOf(orderItem.getId());
            orderDetail.setOrderLineNo(orderItemId);
            orderDetail.setActualAmount(orderItem.getItemTotalAmountPaid().add(orderItem.getTotalTax()).toString());
            orderDetail.setDiscountAmount(orderItem.getTotalDiscount().toString());
            // 传税地区
            if (StoreOrderConstant.TAX_PRICE_AREA.contains(order.country())) {
                orderDetail.setTaxPrice(orderItem.getTotalTax().toString());
            }

            return orderDetail;
        }).collect(Collectors.toList());
    }

    /**
     * 封装巴西订单子项明细
     *
     * @return
     */
    private List<OrderDetail> packBrazilOrderDetails(Map<Integer, BigDecimal> taxApportionDetailMap) {
        return orderItems.stream().map(orderItem -> {
            OrderDetail orderDetail = new OrderDetail();
            String orderItemId = String.valueOf(orderItem.getId());
            orderDetail.setOrderLineNo(orderItemId);

            // 税费
            BigDecimal tax = Objects.nonNull(taxApportionDetailMap.get(orderItem.getId())) ? taxApportionDetailMap.get(orderItem.getId()) : BigDecimal.ZERO;
            orderDetail.setTaxPrice(tax.toString());

            orderDetail.setActualAmount(orderItem.getItemTotalAmountPaid().add(tax).toString());
            orderDetail.setDiscountAmount(orderItem.getTotalDiscount().toString());

            return orderDetail;
        }).collect(Collectors.toList());
    }

    /**
     * 封装七级税费
     *
     * @return
     */
    private SevenLevelTaxPrice packSevenLevelTaxPrice() {
        LOGGER.info("订单{} packSevenLevelTaxPrice开始。。。", order.getOrderNumber());
        if (!InstaCountry.US.equals(order.country())) {
            return null;
        }
        AvalaraTaxInfo orderAvalaraTaxInfo = avalaraTaxInfoService.getOrderAvalaraTaxInfo(StoreTransactionType.ORDER_CREATE_TRANSACTION.getNameEn(), order.getOrderNumber());
        if (Objects.isNull(orderAvalaraTaxInfo)) {
            return null;
        }
        List<AvalaraTaxRateSummary> avalaraTaxRateSummaries = avalaraTaxRateSummaryService.listByTaxInfoId(orderAvalaraTaxInfo.getId());
        if (CollectionUtils.isEmpty(avalaraTaxRateSummaries)) {
            return null;
        }

        // 将行税详情按照税收管辖区类型分组，并计算每种类型的总税额
        Map<String, BigDecimal> avalaraTaxRateSummaryMap = avalaraTaxRateSummaries.stream().collect(
                Collectors.groupingBy(AvalaraTaxRateSummary::getJurisType,
                        Collectors.mapping(AvalaraTaxRateSummary::getTax,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
                )
        );
        SevenLevelTaxPrice sevenLevelTaxPrice = new SevenLevelTaxPrice();
        for (String jurisType : avalaraTaxRateSummaryMap.keySet()) {
            AvalaraJurisdictionType jurisdictionType = AvalaraJurisdictionType.parse(jurisType);
            if (Objects.isNull(jurisdictionType)) {
                continue;
            }
            switch (jurisdictionType) {
                case STATE:
                    sevenLevelTaxPrice.setZJ01(avalaraTaxRateSummaryMap.get(jurisType).toString());
                    break;
                case COUNTY:
                    sevenLevelTaxPrice.setZJ02(avalaraTaxRateSummaryMap.get(jurisType).toString());
                    break;
                case CITY:
                    sevenLevelTaxPrice.setZJ03(avalaraTaxRateSummaryMap.get(jurisType).toString());
                    break;
                case SPECIAL:
                    sevenLevelTaxPrice.setZJ04(avalaraTaxRateSummaryMap.get(jurisType).toString());
                    break;
                case COUNTRY:
                    sevenLevelTaxPrice.setZJ05(avalaraTaxRateSummaryMap.get(jurisType).toString());
                    break;
            }
        }
        LOGGER.info(String.format("订单{%s} packSevenLevelTaxPrice结束。。。{%s}", order.getOrderNumber(), sevenLevelTaxPrice));
        return sevenLevelTaxPrice;
    }


    /**
     * 封装美国关税到运费字段
     *
     * @param deliveryOrder
     */
    private void packUsCustomsTax(DeliveryOrder deliveryOrder) {
        // 非美国地区不处理
        if (!InstaCountry.US.equals(order.country())) {
            return;
        }

        List<OrderItemCustomsTaxRate> orderItemCustomsTaxRates = orderItemCustomsTaxRateService.listByOrderId(order.getId());
        if (CollectionUtils.isEmpty(orderItemCustomsTaxRates)) {
            return;
        }
        Double itemTotalTax = orderItemCustomsTaxRates.stream().map(OrderItemCustomsTaxRate::getTotalTax).reduce(Double::sum).get();
        BigDecimal itemTotalFreight = new BigDecimal(deliveryOrder.getFreight()).add(new BigDecimal(itemTotalTax.toString()));
        deliveryOrder.setFreight(itemTotalFreight.toString());
    }

    /**
     * 封装订单总税额
     *
     * @return
     */
    private String packTotalTaxPrice() {
        // 传税地区
        if (StoreOrderConstant.TAX_PRICE_AREA.contains(order.country())) {
            return String.valueOf(orderPayment.getTax());
        }
        return null;
    }

    /**
     * 封装订单标签
     *
     * @return
     */
    private List<String> packTagCodes() {
        List<String> tagCodes = new ArrayList<>(1);
        TradeupOrder tradeupOrder = tradeupOrderService.getByOrder(order.getId());
        if (Objects.nonNull(tradeupOrder)) {
            tagCodes.add(StoreOrderConstant.TRADE_UP_ORDER_TAG);
        }
        return tagCodes;
    }

    /**
     * 封装子项信息
     *
     * @return
     */
    private List<OrderLine> packOrderLines() {
        LOGGER.info("订单{} packOrderLines开始。。。", order.getOrderNumber());
        List<Integer> commodityIds = orderItems.stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        Map<Integer, CommodityCode> commodityCodeMap = commodityCodeService.listCommodityCode(commodityIds, order.getArea()).stream().collect(Collectors.toMap(CommodityCode::getCommodity, c -> c));
        List<OrderLine> orderLineList = orderItems.stream().map(orderItem -> {
            OrderLine orderLine = new OrderLine();
            String orderItemId = String.valueOf(orderItem.getId());
            orderLine.setOrderLineNo(orderItemId);
            orderLine.setActualPrice(String.valueOf(orderItem.getTotalItemPayPrice().getAmount()));
            orderLine.setSubSourceOrderCode(orderItemId);
            orderLine.setPlanQty(String.valueOf(orderItem.getNumber()));
            CommodityCode commodityCode = commodityCodeMap.get(orderItem.getCommodity());
            // todo 不存在料号告警
            orderLine.setItemCode(Objects.isNull(commodityCode) ? null : commodityCode.getCode());

            // 兼容部分发货订单数据同步
            if (OrderState.part_delivery.equals(order.orderState()) && Objects.nonNull(orderItem.getDeliveryState()) && orderItem.getDeliveryState() == 4) {
                OrderItemState orderItemState = orderItem.orderItemState();
                if (!OrderItemState.isRmaFinalState(orderItemState)) {
                    orderLine.setStatus("TRADE_FINISHED");
                }
            }
            return orderLine;
        }).collect(Collectors.toList());
        LOGGER.info(String.format("订单{%s} packOrderLines结束。。。{%s}", order.getOrderNumber(), orderLineList));
        return orderLineList;
    }

    /**
     * 是否为正常优惠订单
     *
     * @return
     */
    private Boolean isNormalDiscountOrder() {
        // 商品总优惠金额
        BigDecimal itemTotalDiscountAmount = orderItems.stream().map(OrderItem::getTotalDiscount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 订单总优惠金额
        BigDecimal orderTotalDiscountAmount = new BigDecimal(String.valueOf(orderPayment.getTotalDiscountFee()));

        return orderTotalDiscountAmount.compareTo(itemTotalDiscountAmount) != 0;
    }

    /**
     * 订单优惠信息重新计算
     *
     * @return
     */
    private void itemDiscountRecalculate() {
        // 赠品不参与优惠分摊
        List<OrderItem> orderItemList = orderItems.stream().filter(orderItem -> !orderItem.getIsGift()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemList)) {
            return;
        }

        // 商品总金额
        BigDecimal orderItemTotalAmount = orderItemList.stream().map(OrderItem::getItemTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 订单总优惠金额
        BigDecimal orderTotalDiscountAmount = new BigDecimal(String.valueOf(orderPayment.getTotalDiscountFee()));
        // 商品总金额重排序
        orderItemList.sort(Comparator.comparing(OrderItem::getItemTotalAmount, Comparator.reverseOrder()));

        // 已累计优惠总额
        BigDecimal grandTotalDiscountAmount = BigDecimal.ZERO;
        for (int i = 0; i < orderItemList.size(); i++) {
            OrderItem orderItem = orderItemList.get(i);
            // 当前商品占比优惠金额
            BigDecimal itemDiscountAmount;
            // 判断是否最后一个商品了
            if (i == orderItemList.size() - 1) {
                itemDiscountAmount = orderTotalDiscountAmount.subtract(grandTotalDiscountAmount);
            } else {
                // 进行优惠分摊（税费占比：保留两位小数，四舍五入）
                itemDiscountAmount = orderItem.getItemTotalAmount().divide(orderItemTotalAmount, 10, BigDecimal.ROUND_HALF_UP).multiply(orderTotalDiscountAmount).setScale(2, BigDecimal.ROUND_DOWN);
                // 累计优惠金额
                grandTotalDiscountAmount = grandTotalDiscountAmount.add(itemDiscountAmount);
            }

            // 商品优惠平均金额
            BigDecimal itemDiscountAvgAmount = itemDiscountAmount.divide(new BigDecimal(String.valueOf(orderItem.getNumber())), 2, BigDecimal.ROUND_DOWN);
            orderItem.setTotalDiscount(itemDiscountAmount);
            orderItem.setDiscountFee(itemDiscountAvgAmount.floatValue());
        }
    }

    /**
     * 封装收件人信息
     *
     * @return
     */
    private ReceiverInfo packReceiverInfo() {
        LOGGER.info("订单{} packReceiverInfo开始。。。", order.getOrderNumber());
        ReceiverInfo receiverInfo = new ReceiverInfo();
        String fullPhone = ProfileUtil.getFullPhone(orderDelivery.getPhoneCode(), orderDelivery.getPhone());
        receiverInfo.setProvince(packProvince());
        receiverInfo.setCity(orderDelivery.getCity());
        receiverInfo.setArea(orderDelivery.getDistrict());
        receiverInfo.setMobile(fullPhone);
        receiverInfo.setPhone(fullPhone);
        receiverInfo.setName(ProfileUtil.getFullName(orderDelivery.getFirstName(), orderDelivery.getLastName()));
        receiverInfo.setZipCode(orderDelivery.getZipCode());
        receiverInfo.setCountryCode(orderDelivery.getCountryCode());
        receiverInfo.setEmail(order.getContactEmail());
        OmsOrderDeliveryBO orderDeliveryBo = new OmsOrderDeliveryBO();
        BeanUtil.copyProperties(orderDelivery, orderDeliveryBo);
        receiverInfo.setDetailAddress(ProfileUtil.parseAddressDetails(orderDeliveryBo));
        LOGGER.info(String.format("订单{%s} packReceiverInfo结束。。。{%s}", order.getOrderNumber(), receiverInfo));
        return receiverInfo;
    }

    /**
     * 获取省份信息
     *
     * @return
     */
    private String packProvince() {
        return ProfileUtil.parseOmsProvince(order.country(), orderDelivery.getProvince());
    }

    /**
     * 获取发货人名称
     *
     * @return
     */
    private String getVipName() {
        return ProfileUtil.getFullName(orderDelivery.getFirstName(), orderDelivery.getLastName());
    }

    /**
     * 系统推单成功后，记录日志
     *
     * @param order
     */
    private void updateOrderOperatorRecord(Order order) {
        // 非支付/已配货状态不做记录
        if (!OrderState.payedOrPrepared().contains(order.orderState())) {
            return;
        }
        OrderOperatorRecord orderOperatorRecord = new OrderOperatorRecord();
        orderOperatorRecord.setOrderId(order.getId());
        orderOperatorRecord.setJobNumber(OrderStockSourceType.SYSTEM.getCode());
        orderOperatorRecord.setUsername(OrderStockSourceType.SYSTEM.getName());
        orderOperatorRecord.setOperatorType("订单状态");
        orderOperatorRecord.setFromData(OrderState.payed.getNameZh());
        orderOperatorRecord.setToData(OrderState.prepared.getNameZh());
        orderOperatorRecord.setCreateTime(LocalDateTime.now());
        orderOperatorRecord.setUpdateTime(LocalDateTime.now());
        orderOperatorRecordService.save(orderOperatorRecord);
    }

    /**
     * 更新订单推送平台
     */
    private void updateOrderPushPlatform(Order order) {
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setPushPlatform(PushPlatformType.JU_YI.getType());
        orderService.updateById(updateOrder);
    }

    /**
     * 获取巴西订单商品子项税费分摊明细（含运费）
     *
     * @return
     */
    private Map<Integer, BigDecimal> getBrazilOrderItemTaxApportionDetail() {
        // 商品转化
        List<OmsItemTaxBO> orderItemTaxBoList = orderItems.stream().filter(orderItem -> !orderItem.getIsGift() && orderItem.getPrice() > 0).map(orderItem -> new OmsItemTaxBO(orderItem)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemTaxBoList)) {
            return new HashMap<>();
        }
        return this.getBrazilTaxDetailMap(orderItemTaxBoList);
    }

    /**
     * 获取巴西订单税费分摊明细MAP
     *
     * @param orderItemTaxBoList
     * @return
     */
    private Map<Integer, BigDecimal> getBrazilTaxDetailMap(List<OmsItemTaxBO> orderItemTaxBoList) {
        // 按每个商品的商品总金额进行降序，便于后续计算每个商品的税费分摊占比
        orderItemTaxBoList.sort(Comparator.comparing(OmsItemTaxBO::getItemTotalAmount, Comparator.reverseOrder()));
        // 商品总金额（不含运费）
        BigDecimal orderItemTotalAmount = orderItemTaxBoList.stream().map(OmsItemTaxBO::getItemTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 订单总税费
        BigDecimal orderTotalTax = new BigDecimal(String.valueOf(orderPayment.getTax()));
        // 运费税费
        BigDecimal shippingCostTax = orderPayment.getShippingCostTax();
        // 订单应分摊总税费
        BigDecimal toSplitTotalTax = orderTotalTax.subtract(shippingCostTax);
        Map<Integer, BigDecimal> itemTaxMap = new HashMap<>();

        // 已累计税费总额
        BigDecimal grandTotalTax = BigDecimal.ZERO;
        for (int i = 0; i < orderItemTaxBoList.size(); i++) {
            OmsItemTaxBO omsItemTaxBo = orderItemTaxBoList.get(i);
            BigDecimal itemTax;

            // 判断是否最后一个商品了
            if (i == orderItemTaxBoList.size() - 1) {
                itemTax = toSplitTotalTax.subtract(grandTotalTax);
            } else {
                // 进行税费分摊（税费占比：保留两位小数，四舍五入）
                itemTax = omsItemTaxBo.getItemTotalAmount().divide(orderItemTotalAmount, 10, BigDecimal.ROUND_HALF_UP).multiply(toSplitTotalTax).setScale(2, BigDecimal.ROUND_DOWN);
                // 累计税费
                grandTotalTax = grandTotalTax.add(itemTax);
            }

            itemTaxMap.put(omsItemTaxBo.getOrderItemId(), itemTax);
        }

        return itemTaxMap;
    }
}
