package com.insta360.store.business.order.service.impl.check;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.meta.enums.OrderPushBlockReasonTypeEnum;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.model.MisBehaviorNames;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.MisBehaviorNamesService;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.bo.RepeatOrderQueryBO;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.constant.OrderAutoPushConstantPool;
import com.insta360.store.business.order.service.impl.helper.OrderPushAutoHelper;
import com.insta360.store.business.outgoing.common.bo.AutoPushConfigBO;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductCategorySubset;
import com.insta360.store.business.product.service.ProductCategorySubsetService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 对订单检查的具体实现类
 *
 * <AUTHOR>
 */
@Component
public class LowPossibilityOrderCheck extends BaseOrderPushAutoCheckChain {

    private static final Logger LOGGER = LoggerFactory.getLogger(LowPossibilityOrderCheck.class);

    @Autowired
    OrderService orderService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    ProductService productService;

    @Autowired
    MisBehaviorNamesService misBehaviorNamesService;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    ProductCategorySubsetService productCategorySubsetService;

    @Autowired
    OrderPushAutoHelper orderPushAutoHelper;

    @Override
    public boolean doCheck(Order order) {
        return ifProductNumOverLimit(order) && isPaySecurity(order) && ifPickMisBehaviorName(order) && doesSatisfyConfiguration(order) && isRepeatOrder(order);
    }

    /**
     * j.订单中商品数量是否超过限制
     *
     * @param order
     * @return
     */
    private boolean ifProductNumOverLimit(Order order) {
        // 提前排除补差价套餐，因为某些原因，这类套餐被归类为相机类目
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId())
                .stream()
                .filter(orderItem -> !OrderAutoPushConstantPool.DIFFERENCE_PACKAGE.contains(orderItem.getCommodity()))
                .collect(Collectors.toList());
        List<Integer> productIds = orderItems.stream().map(OrderItem::getProduct).collect(Collectors.toList());
        List<Product> products = productService.getProducts(productIds);
        // key -> productId     value -> 是否为相机
        Map<Integer, Boolean> productMap = products.stream().collect(Collectors.toMap(Product::getId, Product::whetherCamera));
        List<ProductCategorySubset> productCategorySubsets = getProductCategorySubset();
        // key -> productId     value -> 是否为配件
        Map<Integer, Boolean> accessoryMap = products.stream().collect(Collectors.toMap(Product::getId, product -> isAccessory(product, productCategorySubsets)));
        // key -> productId     value -> 该产品数量
        Map<Integer, Integer> productToNumberMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getProduct, OrderItem::getNumber, Integer::sum));
        AtomicInteger cameraCount = new AtomicInteger();
        AtomicInteger accessoryCount = new AtomicInteger();
        productToNumberMap.forEach((key, value) -> {
            if (productMap.get(key)) {
                cameraCount.addAndGet(value);
            } else if (accessoryMap.get(key) && value > accessoryCount.get()) {
                accessoryCount.set(value);
            }
        });
        if (cameraCount.get() > OrderAutoPushConstantPool.AUTO_PUSH_CAMERA_COUNT_LIMIT || (InstaCountry.CN.equals(order.country()) && accessoryCount.get() > OrderAutoPushConstantPool.AUTO_PUSH_ACCESSORY_COUNT_LIMIT)) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.PRODUCT_NUM_OVER_LIMIT, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * k.订单是否为支付风险订单
     *
     * @param order
     * @return
     */
    private boolean isPaySecurity(Order order) {
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        // 香港或顺势，不安全的支付渠道，拦截；   还未capture的非klarna支付渠道，拦截。
        if ((PaymentTradeSecurityType.NOT_SECURITY.equals(orderPayment.tradeSecurityType()) && PaymentChannel.isHkOrSsPayChannel(orderPayment.paymentChannel()))
                || (PaymentTradeSecurityType.HAVE_NOT_CAPTURE.equals(orderPayment.tradeSecurityType()) && !PaymentChannel.isKlarnaChannel(orderPayment.paymentChannel()))) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.PAY_NOT_SECURITY, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * l.订单邮箱，地址信息是否匹配到“行为不端用户”中信息（飞书告警）
     *
     * @param order
     * @return
     */
    private boolean ifPickMisBehaviorName(Order order) {
        String email = order.getContactEmail();
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        String lastName = orderDelivery.getLastName();
        String firstName = orderDelivery.getFirstName();
        String phoneCode = orderDelivery.getPhoneCode();
        String phone = orderDelivery.getPhone();
        // 根据邮箱查表
        List<MisBehaviorNames> misBehaviorNames = misBehaviorNamesService.listByEmail(email);
        if (CollectionUtils.isNotEmpty(misBehaviorNames)) {
            this.sendFeiShuMessage(order, "email", email);
            orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.MIS_BEHAVIOR, order.getId(), "类型：email，内容：" + email);
            return false;
        }
        // 根据姓名查表
        misBehaviorNames = misBehaviorNamesService.listByLastNameAndFirstName(lastName, firstName);
        if (CollectionUtils.isNotEmpty(misBehaviorNames)) {
            this.sendFeiShuMessage(order, "name", lastName + " " + firstName);
            orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.MIS_BEHAVIOR, order.getId(), "类型：name，内容：" + lastName + " " + firstName);
            return false;
        }
        // 根据电话查表
        misBehaviorNames = misBehaviorNamesService.listByPhoneAndAreaCode(phone, phoneCode);
        if (CollectionUtils.isNotEmpty(misBehaviorNames)) {
            this.sendFeiShuMessage(order, "phone", phoneCode + " " + phone);
            orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.MIS_BEHAVIOR, order.getId(), "类型：phone，内容：" + phoneCode + " " + phone);
            return false;
        }

        // 兼容测试环境，区号不带加号的情况
        phoneCode = removePlusSign(phoneCode);
        misBehaviorNames = misBehaviorNamesService.listByPhoneAndAreaCode(phone, phoneCode);
        if (CollectionUtils.isNotEmpty(misBehaviorNames)) {
            this.sendFeiShuMessage(order, "phone", phoneCode + " " + phone);
            orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.MIS_BEHAVIOR, order.getId(), "类型：phone，内容：" + phoneCode + " " + phone);
            return false;
        }
        return true;
    }

    /**
     * m.满足配置条件
     *
     * @param order
     * @return
     */
    private boolean doesSatisfyConfiguration(Order order) {
        if (!Boolean.TRUE.toString().equals(storeConfigService.getConfigValue(StoreConfigKey.auto_push_to_gy_switch))) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.ORDER_PUSH_TURNED_OFF, null, order.getOrderNumber(), order.getId());
            return false;
        }
        String configStr = storeConfigService.getConfigValue(StoreConfigKey.auto_push_to_gy_params);
        AutoPushConfigBO autoPushConfigBO = JSONObject.parseObject(configStr, AutoPushConfigBO.class);
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        List<Integer> commodities = orderItemService.getByOrder(order.getId()).stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        List<String> noCountryCodes = autoPushConfigBO.getNoCountryCodeList();
        List<String> noPaymentChannels = autoPushConfigBO.getNoPaymentChannelList();
        List<Integer> noCommodities = autoPushConfigBO.getNoCommodities();

        // 国家代码，支付渠道，套餐均未配置，可以直接推单
        if (CollectionUtils.isEmpty(noCountryCodes) && CollectionUtils.isEmpty(noPaymentChannels) && CollectionUtils.isEmpty(noCommodities)) {
            return true;
        }

        // 如果国家代码不满足配置，则进行推单
        if (CollectionUtils.isNotEmpty(noCountryCodes) && !noCountryCodes.contains(order.country().name())) {
            return true;
        }

        // 如果支付渠道不满足配置，则进行推单
        if (CollectionUtils.isNotEmpty(noPaymentChannels) && !noPaymentChannels.contains(orderPayment.paymentChannel().name())) {
            return true;
        }

        // 如果套餐不满足配置，则进行推单
        if (CollectionUtils.isNotEmpty(noCommodities) && noCommodities.stream().noneMatch(commodities::contains)) {
            return true;
        }

        // 剩余情况属于不推单，需要记录原因
        orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.NOT_SATISFIED_CONFIGURATION, null, order.getOrderNumber(), order.getId());
        return false;
    }

    /**
     * n.重复下单（飞书告警）
     *
     * @param order
     * @return
     */
    private boolean isRepeatOrder(Order order) {
        String email = order.getContactEmail();
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        String phone = orderDelivery.getPhone();
        String phoneCode = orderDelivery.getPhoneCode();
        String country = orderDelivery.getCountry();
        String province = orderDelivery.getProvince();
        String city = orderDelivery.getCity();
        String address = orderDelivery.getAddress();
        String subAddress = orderDelivery.getSubAddress();
        LocalDateTime now = LocalDateTime.now();
        List<Integer> orderStateCodes = OrderState.afterPayedState().stream().map(OrderState::getCode).collect(Collectors.toList());
        // 根据邮箱查询历史单
        List<Order> ordersWithTheSameEmail = orderService.listByEmail(email, OrderState.afterPayedState(), now);
        // 根据电话查询历史单
        List<Order> ordersWithTheSamePhone = orderService.listByPhoneOrAddress(new RepeatOrderQueryBO(phone, phoneCode, orderStateCodes, now.minusDays(7), now));
        // 根据地址查询历史单
        List<Order> ordersWithTheSameAddress = orderService.listByPhoneOrAddress(new RepeatOrderQueryBO(country, province, city, address, subAddress, orderStateCodes, now.minusDays(7), now));
        return judgeOrderRepeat(ordersWithTheSameEmail, order) && judgeOrderRepeat(ordersWithTheSamePhone, order) && judgeOrderRepeat(ordersWithTheSameAddress, order);
    }

    /**
     * 去掉开头的加号
     *
     * @param phoneCode 区号
     * @return
     */
    private String removePlusSign(String phoneCode) {
        if (StringUtil.isBlank(phoneCode)) {
            return "";
        }
        while (phoneCode.startsWith("+")) {
            phoneCode = phoneCode.substring(1);
        }
        return phoneCode;
    }

    /**
     * 飞书告警
     *
     * @param order
     * @param type
     * @param content
     */
    private void sendFeiShuMessage(Order order, String type, String content) {
        LOGGER.error(String.format("订单自动推送至OMS，匹配到行为不端用户. 订单号:{%s}, 类型：{%s}, 内容：{%s}", order.getOrderNumber(), type, content));
        FeiShuMessageUtil.storeGeneralMessage(String.format("[订单自动推送至OMS]匹配到行为不端用户. 订单号:{%s}, 类型：{%s}, 内容：{%s}",
                order.getOrderNumber(), type, content), FeiShuGroupRobot.MainNotice, FeiShuAtUser.CUSTOMER_NOTICE);
    }

    /**
     * 飞书告警
     *
     * @param order
     */
    private void sendFeiShuMessage(Order order) {
        LOGGER.error(String.format("订单自动推送至OMS，重复下单预警. 订单号:{%s}", order.getOrderNumber()));
        FeiShuMessageUtil.storeGeneralMessage(String.format("[订单自动推送至OMS]重复下单预警. 订单号:{%s}",
                order.getOrderNumber()), FeiShuGroupRobot.DoubleBookingOrder, FeiShuAtUser.ZM);
    }

    /**
     * 获取属于配件的一级类目下所有的二级类目
     *
     * @return
     */
    private List<ProductCategorySubset> getProductCategorySubset() {
        List<String> categoryMainKeys = new ArrayList<>();
        categoryMainKeys.add(ProductCategoryMainType.CM_ACCESSORY.name());
        return productCategorySubsetService.listByCategoryMainKeys(categoryMainKeys);
    }


    /**
     * 判断产品是否为配件
     *
     * @param product
     * @return
     */
    private boolean isAccessory(Product product, List<ProductCategorySubset> productCategorySubsets) {
        String categoryKey = product.getCategoryKey();
        List<String> categorySubKeys = productCategorySubsets
                .stream()
                .map(ProductCategorySubset::getCategorySubsetKey)
                .collect(Collectors.toList());
        return categorySubKeys.contains(categoryKey);
    }

    /**
     * 判断当前单是否重复
     *
     * @param orders 历史单集合（根据当前单的邮箱/电话/地址查出）
     * @param order  当前单
     * @return
     */
    private boolean judgeOrderRepeat(List<Order> orders, Order order) {
        // 去除掉内部邮箱下的单
        List<Integer> orderIds = getNotInternalEmailOrders(orders);
        // remove掉自己当前的单
        orderIds.remove(order.getId());
        // orderIds为空，直接放行
        if (CollectionUtils.isEmpty(orderIds)) {
            return true;
        }
        // 去除市场领用订单
        orderIds = orderPaymentService.listByOrderIds(orderIds)
                .stream()
                .filter(orderPayment -> !PaymentChannel.isInternalPayFilterChannel(orderPayment.paymentChannel()))
                .map(OrderPayment::getOrder)
                .collect(Collectors.toList());
        // orderIds为空，直接放行
        if (CollectionUtils.isEmpty(orderIds)) {
            return true;
        }

        // 去除包含产品id=21，225，267的订单
        List<OrderItem> orderItems = orderItemService.listByOrderIds(orderIds);
        List<Integer> specificProductOrderIds = orderItems.stream()
                .filter(orderItem -> Product.REPEAT_ORDER_EXCLUDE_PRODUCT.contains(orderItem.getProduct()))
                .map(OrderItem::getOrder)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(specificProductOrderIds)) {
            orderIds.removeAll(specificProductOrderIds);
        }
        // 去除仅含虚拟商品订单，仅含补差价订单
        orderItems = orderItemService.listByOrderIds(orderIds);
        // 虚拟产品ID集合
        List<Integer> virtualProducts = Product.INSURANCE_SERVICE_PRODUCT;
        String virtualGoodsJson = storeConfigService.getConfigValue(StoreConfigKey.virtual_goods);
        // 虚拟套餐和补差价套餐ID集合
        List<Integer> virtualCommodities = JSONArray.parseArray(virtualGoodsJson).stream().map(id -> (Integer) id).collect(Collectors.toList());
        // key -> orderId   value -> List<OrderItem>
        Map<Integer, List<OrderItem>> ordersById = orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOrder));
        // 全是虚拟商品的单
        List<Integer> ordersAllPickVirtualCommodity = ordersById.entrySet().stream()
                .filter(entry -> entry.getValue().stream()
                        .allMatch(orderItem -> virtualProducts.contains(orderItem.getProduct())
                                || virtualCommodities.contains(orderItem.getCommodity())))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        // remove掉
        if (CollectionUtils.isNotEmpty(ordersAllPickVirtualCommodity)) {
            orderIds.removeAll(ordersAllPickVirtualCommodity);
        }
        // orderIds为空，直接放行
        if (CollectionUtils.isEmpty(orderIds)) {
            return true;
        }

        // 去除掉售后单
        List<Integer> rmaOrderIds = rmaOrderService.listByOrderIds(orderIds).stream().map(RmaOrder::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rmaOrderIds)) {
            orderIds.removeAll(rmaOrderIds);
        }
        // 一通过滤之后，如果没有单了
        if (CollectionUtils.isEmpty(orderIds)) {
            return true;
        }

        // 判断是否有完全一样的套餐
        // 当前单的订单项
        List<OrderItem> currentOrderItem = orderItemService.getByOrder(order.getId());
        Set<Integer> currentCommodityIdSet = currentOrderItem.stream().map(OrderItem::getCommodity).collect(Collectors.toSet());
        // 历史单的订单项
        List<OrderItem> orderItemsInHistory = orderItemService.listByOrderIds(orderIds);
        // key -> orderId    value -> Set<OrderItem>
        Map<Integer, Set<OrderItem>> orderItemMap = orderItemsInHistory.stream().collect(groupingBy(OrderItem::getOrder, Collectors.toSet()));
        for (Map.Entry<Integer, Set<OrderItem>> entry : orderItemMap.entrySet()) {
            Set<Integer> commodityIdSet = entry.getValue().stream().map(OrderItem::getCommodity).collect(Collectors.toSet());
            if (currentCommodityIdSet.equals(commodityIdSet)) {
                this.sendFeiShuMessage(order);
                orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.REPEAT_ORDER, order.getId(), "套餐重复");
                return false;
            }
        }

        // 判断是否有过量的product
        // 当前单的产品ID合集
        List<Integer> productIds = currentOrderItem.stream().map(OrderItem::getProduct).collect(Collectors.toList());
        orderIds.add(order.getId());
        // 历史单加当前单的订单项
        List<OrderItem> orderItemList = orderItemService.listByOrderIds(orderIds);
        // 使用groupingBy按orderId分组，并收集每个orderId对应的OrderItem列表
        // key -> orderId   value -> List<OrderItem>
        Map<Integer, List<OrderItem>> orderItemMaps = orderItemList.stream()
                .collect(groupingBy(OrderItem::getOrder));
        // 使用groupingBy按productId分组，并收集每个productId对应的数量之和
        // key -> productId   value -> number
        Map<Integer, Integer> productIdToNumberMap = orderItemList.stream()
                .collect(groupingBy(OrderItem::getProduct, Collectors.summingInt(OrderItem::getNumber)));
        boolean ifProductNumsOver = productIds.stream().anyMatch(productId -> checkConditions(orderItemMaps, productIdToNumberMap, productId));
        if (ifProductNumsOver) {
            this.sendFeiShuMessage(order);
            orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.REPEAT_ORDER, order.getId(), "产品重复");
            return false;
        }
        return true;
    }

    /**
     * 判断订单池中包含指定产品的数量是否大于3
     *
     * @param orderItemMaps
     * @param productId
     * @return
     */
    private boolean checkConditions(Map<Integer, List<OrderItem>> orderItemMaps, Map<Integer, Integer> orderIdToNumberMap, Integer productId) {
        AtomicInteger count = new AtomicInteger(0);
        // 遍历map，遇到订单项中有指定productId的，+1
        for (Map.Entry<Integer, List<OrderItem>> entry : orderItemMaps.entrySet()) {
            if (entry.getValue().stream().anyMatch(orderItem -> productId.equals(orderItem.getProduct()))) {
                count.incrementAndGet();
            }
        }
        // 检查是否至少有3个这样的orderId，且对应的产品数是否大于等于4个
        return count.get() >= OrderAutoPushConstantPool.MIN_QUANTITY_IN_PAST && orderIdToNumberMap.get(productId) >= OrderAutoPushConstantPool.MIN_QUANTITY_THE_SAME_PRODUCT;
    }

    /**
     * 去除掉内部邮箱订单
     *
     * @param orderList
     * @return
     */
    private List<Integer> getNotInternalEmailOrders(List<Order> orderList) {
        return orderList.stream().filter(order -> !order.getContactEmail().endsWith(OrderAutoPushConstantPool.INTERNAL_EMAIL_SUFFIX))
                .map(Order::getId)
                .collect(Collectors.toList());
    }
}
