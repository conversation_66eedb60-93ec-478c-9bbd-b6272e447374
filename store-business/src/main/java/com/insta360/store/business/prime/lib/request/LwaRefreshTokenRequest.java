package com.insta360.store.business.prime.lib.request;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 * <p>
 * LWA(Live With Amazon)令牌请求参数对象
 * 用于获取访问Amazon服务的OAuth2.0访问令牌
 * 适用于授权码模式（authorization_code）的令牌获取场景
 */
public class LwaRefreshTokenRequest extends RestPrimeRequest implements PrimeRequest, Serializable {

    private static final long serialVersionUID = 1L;

    private static final String URL = "https://api.amazon.com/auth/o2/token";

    /**
     * 客户标识符。
     * 示例值：amzn1.application-oa2-client.87654321ddea48bc8916f62823a64321
     * 对应JSON字段名：client_id
     */
    @JSONField(name = "client_id")
    private String clientId;

    @JSONField(name = "grant_type")
    private String grantType;

    @JSONField(name = "client_secret")
    private String clientSecret;

    @JSONField(name = "refresh_token")
    private String refreshToken;

    public LwaRefreshTokenRequest() {
    }

    public LwaRefreshTokenRequest(String clientId, String grantType, String clientSecret, String refreshToken) {
        this.clientId = clientId;
        this.grantType = grantType;
        this.clientSecret = clientSecret;
        this.refreshToken = refreshToken;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    @Override
    public String getUrl() {
        return URL;
    }

}
