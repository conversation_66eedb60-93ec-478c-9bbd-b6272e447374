package com.insta360.store.business.integration.wto.oms.service.helper;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.aliyun.sms.SMSService;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceHelper;
import com.insta360.store.business.integration.oceanpayment.service.OceanPaymentSyncService;
import com.insta360.store.business.integration.wto.oms.bo.OmsOrderDeliveryDetailBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsOrderItemCameraSerialBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsOrderItemDeliveryBO;
import com.insta360.store.business.integration.wto.oms.lib.enums.OmsOrderDeliveryState;
import com.insta360.store.business.meta.model.MetaExpress;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.MetaExpressService;
import com.insta360.store.business.order.email.*;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderDeliveryPartly;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.*;
import com.insta360.store.business.order.service.impl.helper.OrderCronHelper;
import com.insta360.store.business.order.service.impl.helper.OrderEmailHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.rma.model.RmaOrder;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2024/12/10
 * @Description: 订单发货处理
 */
@Component
public class StoreOrderDeliveryHelper {

    public static final Logger LOGGER = LoggerFactory.getLogger(StoreOrderDeliveryHelper.class);

    /**
     * 管易的发货仓库(过滤)
     */
    private static final List<String> WAREHOUSE_NAME_FILTER_LIST = Arrays.asList("电商中山总仓", "电商京东美南仓_香港主体", "电商京东美南仓_顺势主体");

    /**
     * 用于组合商品文案
     */
    private static final String COMBINED_COMMODITY = "组合商品";

    @Autowired
    OrderService orderService;

    @Autowired
    ProductService productService;

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    SMSService smsService;

    @Autowired
    OrderEmailFactory orderEmailFactory;

    @Autowired
    OrderDeliveryUniqueCodeService orderDeliveryUniqueCodeService;

    @Autowired
    MetaExpressService metaExpressService;

    @Autowired
    OrderDeliveryPartlyService deliveryPartlyService;

    @Autowired
    InsuranceHelper insuranceHelper;

    @Autowired
    OrderEmailHelper orderEmailHelper;

    @Autowired
    OrderCronHelper orderCronHelper;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OceanPaymentSyncService oceanPaymentSyncService;

    /**
     * 处理订单发货
     *
     * @param order
     * @param orderDeliveryDetailBo
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void handleOrderDelivery(Order order, OmsOrderDeliveryDetailBO orderDeliveryDetailBo) {
        List<OmsOrderItemDeliveryBO> deliveryDetails = orderDeliveryDetailBo.getDeliveryDetails();
        // 更新第一次物流信息和同步钱海
        this.updateExpressInfo(order, orderDeliveryDetailBo.getExpressCode(), orderDeliveryDetailBo.getExpressCompanyCode());

        // 处理发货明细
        deliveryDetails.forEach(deliveryDetail -> this.handleOrderDeliveryPartly(order, orderDeliveryDetailBo, deliveryDetail));

        // 更新订单发货状态
        this.updateOrderState(order, orderDeliveryDetailBo.getDeliveryOrderState(), orderDeliveryDetailBo.getExpressCode());
    }

    /**
     * 更新物流信息和同步钱海
     *
     * @param order
     * @param expressCode
     * @param expressCompanyCode
     */
    public void updateExpressInfo(Order order, String expressCode, String expressCompanyCode) {
        // 保存物流信息
        OrderDelivery orderDelivery = orderDeliveryService.getById(order.getId());
        // 物流商匹配
        MetaExpress metaExpress = metaExpressService.getById(expressCompanyCode);
        if (Objects.isNull(metaExpress)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单{%s}发货物流商商城不存在！发货物流编码{%s}", order.getOrderNumber(),
                    expressCompanyCode), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        if (StringUtil.isBlank(orderDelivery.getExpressCode()) && StringUtil.isBlank(orderDelivery.getExpressCompany())) {
            LOGGER.info("OMS订单发货物流信息,订单子项发货处理... orderNumber:{}", order.getOrderNumber());
            // 订单第一次的物流信息保存在OrderDelivery
            orderDelivery.setExpressCompany(expressCompanyCode);
            orderDelivery.setExpressCode(expressCode);
            orderDelivery.setExpressTime(LocalDateTime.now());
            orderDeliveryService.updateById(orderDelivery);

            // 同步快递信息到钱海
            // 为了能够早点收到账款，第一次出现物流信息的时候就将订单推到钱海
            oceanPaymentSyncService.syncTrackingInfo(order, orderDelivery);
        }
    }

    /**
     * 发货明细处理
     *
     * @param order
     * @param orderDeliveryDetailBo
     * @param orderItemDeliveryBo
     */
    public void handleOrderDeliveryPartly(Order order, OmsOrderDeliveryDetailBO orderDeliveryDetailBo, OmsOrderItemDeliveryBO orderItemDeliveryBo) {
        LOGGER.info(String.format("OMS订单发货物流信息,订单子项发货处理... orderNumber:{%s},itemId:{%s}", order.getOrderNumber(), orderItemDeliveryBo.getItemId()));
        List<OmsOrderItemCameraSerialBO> cameraSerialLines = orderItemDeliveryBo.********************();
        OrderItem orderItem = orderItemService.getById(orderItemDeliveryBo.getItemId());
        Integer itemId = orderItem.getId();
        // 订单第一次以及后续的物流信息保存在OrderDeliveryPartly
        OrderDeliveryPartly orderDeliveryPartly = new OrderDeliveryPartly();
        orderDeliveryPartly.setOrderItem(itemId);
        orderDeliveryPartly.setSkuCode(orderItemDeliveryBo.getSkuCode().trim());
        orderDeliveryPartly.setQty(orderItemDeliveryBo.getItemNumber());
        orderDeliveryPartly.setOrderId(order.getId());
        orderDeliveryPartly.setExpressCompany(orderDeliveryDetailBo.getExpressCompanyCode());
        orderDeliveryPartly.setExpressCode(orderDeliveryDetailBo.getExpressCode());
        orderDeliveryPartly.setExpressTime(LocalDateTime.now());
        orderDeliveryPartly.setDeliveryOrderNumber(orderDeliveryDetailBo.getDeliveryOrderNumber());
        deliveryPartlyService.save(orderDeliveryPartly);

        // 保存具有uniqueCode的订单item
        List<String> uniqueCodes = cameraSerialLines.stream().map(OmsOrderItemCameraSerialBO::getCameraSerialNumber).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(uniqueCodes)) {
            orderDeliveryUniqueCodeService.saveUniqueCode(order, itemId, uniqueCodes);
        }

        // 序列号告警处理
        this.sendOrderMessage(orderDeliveryDetailBo.getWarehouseName(), orderItemDeliveryBo.getIsCombination(), order.getOrderNumber(), orderItemDeliveryBo.getSkuCode(), orderItem, uniqueCodes);

        //修改订单子项的状态为已发货。
        //与常规订单子项状态区别开来。因为涉及到退货退款问题，这样就无法区分是否已发货，后台的订单状态就无法显示出该子项是否已发货。
        orderItemService.shipOrderItem(itemId);
        LOGGER.info(String.format("OMS订单发货物流信息,订单子项发货状态更新成功... orderNumber:{%s},itemId:{%s}", order.getOrderNumber(), orderItemDeliveryBo.getItemId()));
    }

    /**
     * 更新订单状态，三种情况：
     * 1、已配货--》已发货
     * 2、已配货--》部分发货--》部分发货
     * 3、已配货--》部分发货--》部分发货 --》已发货
     *
     * @param order
     * @param omsOrderState
     * @param expressCode
     */
    public void updateOrderState(Order order, Integer omsOrderState, String expressCode) {
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());

        // 从已配货到全部发货
        if (OrderState.prepared.equals(order.orderState()) && OmsOrderDeliveryState.DELIVERY_ALL.getCode().equals(omsOrderState)) {
            // 全部发货并设置对应标识
            order.setAllAtOnceShip(true);
            orderCronHelper.shipOrder(order);

            // 校验仅虚拟商品订单（不发送邮件&短信）
            if (!insuranceHelper.checkVirtualGoods(order)) {
                if (orderEmailHelper.checkCloudSubscribeOnly(order)) {
                    // 邮件A 已配货到已发货状态
                    sendEmail(OrderShippedEmail.class, expressCode, order);
                }
                // 国内用户发送短信（A
                sendShortMessages("SMS_192532166", order, orderDelivery, expressCode);
            }
            return;
        }

        // OMS返回订单状态为部分发货
        if (OmsOrderDeliveryState.DELIVERY_PARTLY.getCode().equals(omsOrderState)) {
            // 部分发货
            orderCronHelper.partlyOrder(order);
            // 校验部分发货商品是否全为订阅商品且为续订
            if (orderEmailHelper.checkPartyCloudSubscribeOnly(order, expressCode)) {
                // 邮件B 已配货到部分发货状态
                sendEmail(OrderPartialShippedEmail.class, expressCode, order);
            }

            // 国内用户发送短信（B）
            sendShortMessages("SMS_192577294", order, orderDelivery, expressCode);
            return;
        }

        // 从部分发货到全部发货
        if (OrderState.part_delivery.equals(order.orderState()) && OmsOrderDeliveryState.DELIVERY_ALL.getCode().equals(omsOrderState)) {
            // 全部发货
            orderCronHelper.shipOrder(order);
            if (orderEmailHelper.checkPartyCloudSubscribeOnly(order, expressCode)) {
                // 邮件D 部分发货到全部发货
                sendEmail(OrderSurplusShippedEmail.class, expressCode, order);
            }

            // 国内用户发送短信（D）
            sendShortMessages("SMS_192532163", order, orderDelivery, expressCode);
        }
        LOGGER.info("商城同步OMS订单发货物流信息,订单发货状态更新成功... orderNumber:{},gyOrderState:{},expressCode:{}", order.getOrderNumber(), omsOrderState, expressCode);
    }

    /**
     * 由于OMS不会推送部分发货后子项产生售后完结，订单应流转为已发货
     * 售后订单完成后处理订单状态(只处理部分发货 -> 已发货)
     *
     * @param rmaOrder
     */
    @Async
    public void handleOrderDeliveryAll(RmaOrder rmaOrder) {
        Order order = orderService.getById(rmaOrder.getOrderId());
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        LOGGER.info("售后完结进入部分发货订单子项check。。。订单信息{}", order);
        // 只处理部分发货订单状态流转
        if (!OrderState.part_delivery.equals(order.orderState())) {
            return;
        }
        LOGGER.info("售后完结进入部分发货订单子项check。。。{}", rmaOrder);
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        // 收集当前售后是否是最后一个子项
        List<OrderItem> orderItems = orderItemList.stream().map(orderItem -> {
            // 过滤当前售后子项
            if (orderItem.getId().equals(rmaOrder.getOrderItemId())) {
                return null;
            }
            // 过滤已发货子项
            if (Objects.nonNull(orderItem.getDeliveryState()) && orderItem.getDeliveryState() == 4) {
                return null;
            }
            // 过滤已完成售后子项
            if (OrderItemState.isRmaRefundState(orderItem.orderItemState())) {
                return null;
            }
            return orderItem;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 不是最后一个完结子项
        if (CollectionUtils.isNotEmpty(orderItems)) {
            LOGGER.info("售后完结部分发货订单还存在子项状态未完结 {}", orderItems);
            return;
        }
        LOGGER.info("售后完结部分发货订单子项状态都已完结。。。流转订单状态未已发货");

        // 最后一个子项状态已完结
        // 流转订单状态从部分发货至已发货
        orderCronHelper.shipOrder(order);
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        if (Objects.isNull(orderDelivery)) {
            return;
        }
        // 国内用户发送短信（C）
        sendShortMessages("SMS_193242178", order, orderDelivery, orderDelivery.getExpressCode());
    }

    /**
     * 邮件发送
     *
     * @param clsEmail
     * @param expressCode
     * @param order
     */
    public void sendEmail(Class<? extends BaseOrderEmail> clsEmail, String expressCode, Order order) {
        BaseOrderEmail email = orderEmailFactory.getEmail(order, expressCode, clsEmail);
        email.doSend(order.getContactEmail());
    }

    /**
     * 短信发送
     *
     * @param templateCode
     * @param order
     * @param orderDelivery
     */
    private void sendShortMessages(String templateCode, Order order, OrderDelivery orderDelivery, String expressCode) {
        // 非大陆不发送短信
        if (!InstaCountry.CN.equals(order.country())) {
            return;
        }
        OrderDeliveryPartly deliveryPartly = deliveryPartlyService.getDeliveryPartly(expressCode).get(0);
        MetaExpress metaExpress = metaExpressService.getById(deliveryPartly.getExpressCompany());
        JSONObject para = new JSONObject();
        para.put("order_number", order.getOrderNumber());
        para.put("express_company", Objects.isNull(metaExpress) ? deliveryPartly.getExpressCompany() : metaExpress.getName());
        para.put("express_code", expressCode);
        smsService.sendMessage("Insta360影石", templateCode, para, orderDelivery.getPhone());
    }

    /**
     * 发送飞书通知
     *
     * @param warehouseName
     * @param isCombination
     * @param orderNumber
     * @param orderItem
     * @param uniqueCodes
     */
    private void sendOrderMessage(String warehouseName, Boolean isCombination, String orderNumber, String skuCode, OrderItem orderItem, List<String> uniqueCodes) {
        try {
            LOGGER.info(String.format("[海外仓发货飞书通知]开始判断是否相机,orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName));

            // 过滤发货仓
            if (WAREHOUSE_NAME_FILTER_LIST.stream().anyMatch(warehouseName::contains)) {
                LOGGER.info(String.format("[海外仓发货飞书通知]过滤发货仓,orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName));
                return;
            }

            // 存在序列号
            if (CollectionUtils.isNotEmpty(uniqueCodes)) {
                LOGGER.info(String.format("[海外仓发货飞书通知]存在回传序列号,结束流程.orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName));
                return;
            }

            // 过滤不包含care与延保订单
            List<OrderItem> orderItems = orderItemService.getByOrder(orderItem.getOrder());
            List<Integer> products = orderItems.stream().map(OrderItem::getProduct).collect(Collectors.toList());
            products.retainAll(Product.INSURANCE_SERVICE_PRODUCT);
            if (CollectionUtils.isEmpty(products)) {
                LOGGER.info(String.format("[海外仓发货飞书通知]属于不包含增值服务的订单,结束流程.orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName));
                return;
            }

            // 组合商品 通过sku获得真正的产品type
            List<CommodityCode> commodityCodes = commodityCodeService.listBySku(skuCode);
            if (CollectionUtils.isEmpty(commodityCodes)) {
                LOGGER.info(String.format("[海外仓发货飞书通知]sku错误,查不到数据.orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName));
                return;
            }

            List<Integer> commodityIds = commodityCodes.stream().map(CommodityCode::getCommodity).distinct().collect(Collectors.toList());
            List<Integer> productIds = commodityService.listByCommoditiesNormalSale(commodityIds).stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productIds)) {
                LOGGER.info(String.format("[海外仓发货飞书通知]根据正常销售的套餐id查询不到产品id,结束流程.orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName));
                return;
            }

            LOGGER.info(String.format("[海外仓发货飞书通知]相机套餐参数:orderNumber:%s,料号:%s,发货仓:%s,commodityIds:%s,productIds:%s", orderNumber, skuCode, warehouseName,
                    commodityIds, productIds));

            List<Product> productLists = productService.listEnableByProductIds(productIds).stream().filter(Product::whetherCamera).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productLists)) {
                LOGGER.info(String.format("[海外仓发货飞书通知]该sku查不到相机产品,结束流程.orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName));
                return;
            }

            // 组合商品判断
            if (!isCombination) {
                String msg = "相机非电商总仓发货，请及时处理。发货仓【" + warehouseName + "】" + "订单号【" + orderNumber + "】";
                FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.MainNotice, FeiShuAtUser.YCT, FeiShuAtUser.ZM, FeiShuAtUser.GQY, FeiShuAtUser.ZDX);
            } else {
                String msg = "相机非电商总仓发货，请及时处理。发货仓【" + warehouseName + "】" + "订单号【" + orderNumber + "】" + COMBINED_COMMODITY;
                FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.MainNotice, FeiShuAtUser.YCT, FeiShuAtUser.ZM, FeiShuAtUser.JHF, FeiShuAtUser.GQY, FeiShuAtUser.ZDX);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("商城同步OMS订单发货物流信息,非电商总仓发货场景飞书告警处理异常... orderNumber:%s,料号:%s,发货仓:%s", orderNumber, skuCode, warehouseName), e);
        }
    }
}
