package com.insta360.store.business.rma.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.google.common.collect.Lists;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-09-06
 * @Description:
 */
public class RmaOrder extends BaseModel<RmaOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 售后单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 售后单号
     */
    @JSONField(name = "rma_number")
    private String rmaNumber;

    /**
     * 售后类型
     *
     * @see RmaType
     */
    @JSONField(name = "rma_type")
    private String rmaType;

    /**
     * 联系邮箱
     */
    @JSONField(name = "contact_email")
    private String contactEmail;

    /**
     * 订单ID
     */
    @JSONField(name = "order_id")
    private Integer orderId;

    /**
     * 售后订单商品ID
     */
    @JSONField(name = "order_item_id")
    private Integer orderItemId;

    /**
     * 售后数量
     */
    private Integer quantity;

    /**
     * 售后创建时间
     */
    @JSONField(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 售后单状态
     *
     * @see RmaState
     */
    private Integer state;

    /**
     * 售后关闭状态
     */
    @JSONField(name = "close_state")
    private Integer closeState;

    /**
     * 是否退回
     */
    @JSONField(name = "need_return")
    private Boolean needReturn;

    /**
     * 退款金额
     */
    @JSONField(name = "refund_amount")
    private Float refundAmount;

    /**
     * 售后退款币种
     *
     * @see Currency
     */
    @JSONField(name = "refund_currency")
    private String refundCurrency;

    /**
     * 客户售后原因
     */
    private String reason;

    /**
     * 用户售后额外说明
     */
    @JSONField(name = "extra_reason")
    private String extraReason;

    /**
     * 客服备注
     */
    @JSONField(name = "admin_remark")
    private String adminRemark;

    /**
     * 客服确认原因
     */
    @JSONField(name = "admin_reason")
    private String adminReason;

    /**
     * 主责归因
     * 1-客户  2-产品  3-业务流程  4-商家  5-物流  6-其他  0-空
     */
    private Integer rmaMainDuty;
    /**
     * 售后完成时间
     */
    @JSONField(name = "finish_time")
    private LocalDateTime finishTime;

    /**
     * 协商选项类型
     *
     * @see com.insta360.store.business.rma.enums.RefundNegotiationOptionsType
     */
    private String negotiationOptionsType;

    /**
     * 平台来源
     */
    private Integer platformSource;

    /**
     * 挽单人
     */
    private String recoverUser;

    /**
     * 是否退回额度
     */
    private Boolean returnQuota;

    /**
     * 是否关闭自动订阅
     */
    private Boolean closeAutoSubscribe;

    /**
     * 是否终止权益（用于psp）
     */
    private Boolean terminationEquity;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;


    /**
     * 运输标签
     */
    @TableField(exist = false)
    private String shippingLabel;

    @JSONField(serialize = false)
    public boolean isRefund() {
        return Lists.newArrayList(RmaType.rma_refund, RmaType.rma_return).contains(rmaType()) && RmaState.success.equals(rmaState());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Boolean getNeedReturn() {
        return needReturn;
    }

    public void setNeedReturn(Boolean needReturn) {
        this.needReturn = needReturn;
    }

    public Float getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Float refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundCurrency() {
        return refundCurrency;
    }

    public void setRefundCurrency(String refundCurrency) {
        this.refundCurrency = refundCurrency;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getAdminRemark() {
        return adminRemark;
    }

    public void setAdminRemark(String adminRemark) {
        this.adminRemark = adminRemark;
    }

    public String getExtraReason() {
        return extraReason;
    }

    public void setExtraReason(String extraReason) {
        this.extraReason = extraReason;
    }

    public Integer getCloseState() {
        return closeState;
    }

    public void setCloseState(Integer closeState) {
        this.closeState = closeState;
    }

    public String getAdminReason() {
        return adminReason;
    }

    public void setAdminReason(String adminReason) {
        this.adminReason = adminReason;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getRmaMainDuty() {
        return rmaMainDuty;
    }

    public void setRmaMainDuty(Integer rmaMainDuty) {
        this.rmaMainDuty = rmaMainDuty;
    }

    public String getNegotiationOptionsType() {
        return negotiationOptionsType;
    }

    public void setNegotiationOptionsType(String negotiationOptionsType) {
        this.negotiationOptionsType = negotiationOptionsType;
    }

    public Integer getPlatformSource() {
        return platformSource;
    }

    public void setPlatformSource(Integer platformSource) {
        this.platformSource = platformSource;
    }

    public String getRecoverUser() {
        return recoverUser;
    }

    public void setRecoverUser(String recoverUser) {
        this.recoverUser = recoverUser;
    }

    public String getShippingLabel() {
        return shippingLabel;
    }

    public void setShippingLabel(String shippingLabel) {
        this.shippingLabel = shippingLabel;
    }

    public Boolean getReturnQuota() {
        return returnQuota;
    }

    public void setReturnQuota(Boolean returnQuota) {
        this.returnQuota = returnQuota;
    }

    public Boolean getCloseAutoSubscribe() {
        return closeAutoSubscribe;
    }

    public void setCloseAutoSubscribe(Boolean closeAutoSubscribe) {
        this.closeAutoSubscribe = closeAutoSubscribe;
    }

    public Boolean getTerminationEquity() {
        return terminationEquity;
    }

    public void setTerminationEquity(Boolean terminationEquity) {
        this.terminationEquity = terminationEquity;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public RmaType rmaType() {
        return RmaType.parse(rmaType);
    }

    public RmaState rmaState() {
        return RmaState.parse(state);
    }

    public Currency refundCurrency() {
        return Currency.parse(refundCurrency);
    }

    /**
     * 是否关闭
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isClose() {
        return Lists.newArrayList(RmaState.cancelled, RmaState.rejected).contains(rmaState());
    }

    @Override
    public String toString() {
        return "RmaOrder{" +
                "id=" + id +
                ", rmaNumber='" + rmaNumber + '\'' +
                ", rmaType='" + rmaType + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", orderId=" + orderId +
                ", orderItemId=" + orderItemId +
                ", quantity=" + quantity +
                ", createTime=" + createTime +
                ", state=" + state +
                ", closeState=" + closeState +
                ", needReturn=" + needReturn +
                ", refundAmount=" + refundAmount +
                ", refundCurrency='" + refundCurrency + '\'' +
                ", reason='" + reason + '\'' +
                ", extraReason='" + extraReason + '\'' +
                ", adminRemark='" + adminRemark + '\'' +
                ", adminReason='" + adminReason + '\'' +
                ", rmaMainDuty=" + rmaMainDuty +
                ", finishTime=" + finishTime +
                ", negotiationOptionsType='" + negotiationOptionsType + '\'' +
                ", platformSource=" + platformSource +
                ", recoverUser='" + recoverUser + '\'' +
                ", returnQuota=" + returnQuota +
                ", closeAutoSubscribe=" + closeAutoSubscribe +
                ", terminationEquity=" + terminationEquity +
                ", modifyTime=" + modifyTime +
                ", shippingLabel='" + shippingLabel + '\'' +
                "} " + super.toString();
    }
}