package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.faq;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.faq.model.FaqOtherQuestionBind;
import com.insta360.store.business.faq.service.FaqOtherQuestionBindService;
import com.insta360.store.business.outgoing.rpc.store.job.FaqCachePutService;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: wkx
 * @Date: 2023/11/16
 * @Description:
 */
@Component
public class FaqOtherCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(FaqOtherCachePutHandler.class);

    @Autowired
    FaqCachePutService faqCachePutService;

    @Autowired
    FaqOtherQuestionBindService faqOtherQuestionBindService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) {
        List<FaqOtherQuestionBind> otherQuestionBinds = faqOtherQuestionBindService.listQuestionBinds();
        if (CollectionUtils.isEmpty(otherQuestionBinds)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。questionBinds为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 更新页面绑定QA（类目结构）
        otherQuestionBinds.forEach(questionBind -> CacheConstant.COUNTIES.forEach(cacheCounties ->
                faqCachePutService.listOtherQuestionBind(questionBind.getType(), questionBind.getPageKey(),
                        cacheCounties.getCountry(), cacheCounties.getLanguage())));

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    public CachePutKeyParameterBO cacheParamParse(JoinPoint joinPoint) {
        return null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.FAQ_OTHER_QUESTION;
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.FALSE;
    }
}
