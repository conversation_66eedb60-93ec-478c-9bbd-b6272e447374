package com.insta360.store.admin.controller.test.wxq;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * tool/定制贴
 *
 * <AUTHOR>
 * @Description
 * @Date 2023/3/16
 */
@Validated
@RestController
public class OrderToolApi extends BaseAdminApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderToolApi.class);

    @Autowired
    OrderService orderService;

    @Autowired
    UserAccountHelper userAccountHelper;

    /**
     * 订单绑定用户
     *
     * @param orderNumber
     * @param email
     * @return
     */
    @PostMapping(value = "/admin/test/tool/bindUser")
    public Response<Object> bindUser(@RequestBody @NotBlank String orderNumber, @RequestParam @NotBlank String email) {
        StoreAccount storeAccount = userAccountHelper.getStoreAccountByEmail(email);
        if (storeAccount == null) {
            return Response.failed("用户不存在");
        }
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            return Response.failed("订单不存在");
        }
        order.setUserId(storeAccount.getId());
        orderService.updateById(order);
        FeiShuMessageUtil.storeGeneralMessage("订单绑定用户完毕", FeiShuGroupRobot.InternalWarning);
        return Response.ok();
    }

}
