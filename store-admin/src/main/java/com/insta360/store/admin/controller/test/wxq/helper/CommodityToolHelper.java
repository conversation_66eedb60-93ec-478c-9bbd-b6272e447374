package com.insta360.store.admin.controller.test.wxq.helper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.admin.controller.test.wxq.bo.CopyCommodityBO;
import com.insta360.store.admin.controller.test.wxq.dto.CopyCommodityDTO;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.product.model.FaqQuestion;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductCommodityOverview;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/17
 */
@Component
public class CommodityToolHelper {

    @Autowired
    private ProductService productService;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private FaqQuestionService faqQuestionService;

    @Autowired
    private ProductInfoService productInfoService;

    @Autowired
    private CommodityInfoService commodityInfoService;

    @Autowired
    private CommodityMetaService commodityMetaService;

    @Autowired
    private CommodityPriceService commodityPriceService;

    @Autowired
    private CommodityDisplayService commodityDisplayService;

    @Autowired
    private CommoditySaleStateService commoditySaleStateService;

    @Autowired
    private CommodityTradeRuleService commodityTradeRuleService;

    @Autowired
    private ProductCommodityOverviewService commodityOverviewService;

    @Autowired
    private ProductCommodityStockService productCommodityStockService;

    @Autowired
    private CommodityDeliveryTimeConfigService commodityDeliveryTimeConfigService;

    @Autowired
    private CommodityDifferenceService commodityDifferenceService;

    @Autowired
    private CommodityDifferencePointService commodityDifferencePointService;

    @Autowired
    private CommodityDifferencePointTextService commodityDifferencePointTextService;

    @Autowired
    private ProductCommodityOverviewContentService productCommodityOverviewContentService;

    public CopyCommodityBO getCopyCommodityBO(Integer productId, List<Integer> commodityIds) {
        CopyCommodityBO copyCommodityBO = new CopyCommodityBO();

        copyCommodityBO.setProduct(productService.getById(productId));
        copyCommodityBO.setFaqQuestions(faqQuestionService.listByProductId(productId));
        copyCommodityBO.setProductInfos(productInfoService.listInfoByProductId(productId));
        copyCommodityBO.setCommodities(commodityService.listCommodities(commodityIds));
        copyCommodityBO.setCommodityInfos(commodityInfoService.listByCommodityIds(commodityIds));
        copyCommodityBO.setCommodityMetas(commodityMetaService.listCommodityMetaByCommodityIds(commodityIds));
        copyCommodityBO.setCommodityPrices(commodityPriceService.listByCommodityIdEnabled(commodityIds));
        copyCommodityBO.setCommodityDisplays(commodityDisplayService.listByCommodityIds(commodityIds));
        copyCommodityBO.setCommoditySaleStates(commoditySaleStateService.listSaleStates(commodityIds));
        copyCommodityBO.setCommodityTradeRules(commodityTradeRuleService.listByCommodityIds(commodityIds));
        copyCommodityBO.setProductCommodityStocks(productCommodityStockService.listByCommodityIds(commodityIds));
        copyCommodityBO.setCommodityOverviews(commodityOverviewService.listByProductId(productId));
        List<ProductCommodityOverview> productCommodityOverviews = commodityOverviewService.listByProductId(productId);
        List<Integer> overviewIds = productCommodityOverviews.stream().map(ProductCommodityOverview::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(overviewIds)) {
            copyCommodityBO.setProductCommodityOverviewContents(productCommodityOverviewContentService.listByOverviewIdsData(overviewIds));
        }
        copyCommodityBO.setCommodityDifferences(commodityDifferenceService.listDifferences(commodityIds));
        List<CommodityDifference> commodityDifferences = commodityDifferenceService.listDifferences(commodityIds);
        List<Integer> differenceIds = commodityDifferences.stream().map(CommodityDifference::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(differenceIds)) {
            List<CommodityDifferencePoint> commodityDifferencePoints = (List<CommodityDifferencePoint>) commodityDifferencePointService.listByIds(differenceIds);
            copyCommodityBO.setCommodityDifferencePoints(commodityDifferencePoints);

            if (CollectionUtils.isNotEmpty(commodityDifferencePoints)) {
                List<Integer> pointIds = commodityDifferencePoints.stream().map(CommodityDifferencePoint::getId).collect(Collectors.toList());
                List<CommodityDifferencePointText> commodityDifferencePointTexts = (List<CommodityDifferencePointText>) commodityDifferencePointTextService.listByIds(pointIds);
                copyCommodityBO.setCommodityDifferencePointTexts(commodityDifferencePointTexts);
            }
        }
        return copyCommodityBO;
    }

    public CopyCommodityBO deleteCommodityInfo(CopyCommodityBO copyCommodityBO) {
        if (copyCommodityBO == null) {
            return null;
        }

        // 对象级判空保存
        Product product = copyCommodityBO.getProduct();
        Integer productId = product.getId();
        List<Commodity> commodities = copyCommodityBO.getCommodities();
        productService.removeById(productId);

        List<FaqQuestion> faqQuestions = copyCommodityBO.getFaqQuestions();
        if (CollectionUtils.isNotEmpty(faqQuestions)) {
            LambdaQueryWrapper<FaqQuestion> lqw = new LambdaQueryWrapper<>();
            lqw.eq(FaqQuestion::getProductId, productId);
            faqQuestionService.remove(lqw);
        }

        List<ProductInfo> productInfos = copyCommodityBO.getProductInfos();
        if (CollectionUtils.isNotEmpty(productInfos)) {
            LambdaQueryWrapper<ProductInfo> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ProductInfo::getProduct, productId);
            productInfoService.remove(lqw);
        }

        List<Integer> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(commodities)) {
            commodityService.removeByIds(commodityIds);
        }

        List<CommodityInfo> commodityInfos = copyCommodityBO.getCommodityInfos();
        if (CollectionUtils.isNotEmpty(commodityInfos)) {
            LambdaQueryWrapper<CommodityInfo> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommodityInfo::getCommodity, commodityIds);
            commodityInfoService.remove(lqw);
        }

        List<CommodityMeta> commodityMetas = copyCommodityBO.getCommodityMetas();
        if (CollectionUtils.isNotEmpty(commodityMetas)) {
            LambdaQueryWrapper<CommodityMeta> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommodityMeta::getCommodityId, commodityIds);
            commodityMetaService.remove(lqw);
        }

        List<CommodityPrice> commodityPrices = copyCommodityBO.getCommodityPrices();
        if (CollectionUtils.isNotEmpty(commodityPrices)) {
            LambdaQueryWrapper<CommodityPrice> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommodityPrice::getCommodityId, commodityIds);
            commodityPriceService.remove(lqw);
        }

        List<CommodityDisplay> commodityDisplays = copyCommodityBO.getCommodityDisplays();
        if (CollectionUtils.isNotEmpty(commodityDisplays)) {
            LambdaQueryWrapper<CommodityDisplay> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommodityDisplay::getCommodity, commodityIds);
            commodityDisplayService.remove(lqw);
        }

        List<CommoditySaleState> commoditySaleStates = copyCommodityBO.getCommoditySaleStates();
        if (CollectionUtils.isNotEmpty(commoditySaleStates)) {
            LambdaQueryWrapper<CommoditySaleState> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommoditySaleState::getCommodityId, commodityIds);
            commoditySaleStateService.remove(lqw);
        }

        List<CommodityTradeRule> commodityTradeRules = copyCommodityBO.getCommodityTradeRules();
        if (CollectionUtils.isNotEmpty(commodityTradeRules)) {
            LambdaQueryWrapper<CommodityTradeRule> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommodityTradeRule::getCommodityId, commodityIds);
            commodityTradeRuleService.remove(lqw);
        }

        List<ProductCommodityStock> productCommodityStocks = copyCommodityBO.getProductCommodityStocks();
        if (CollectionUtils.isNotEmpty(productCommodityStocks)) {
            LambdaQueryWrapper<ProductCommodityStock> lqw = new LambdaQueryWrapper<>();
            lqw.in(ProductCommodityStock::getCommodityId, commodityIds);
            productCommodityStockService.remove(lqw);
        }

        List<CommodityDeliveryTimeConfig> commodityDeliveryTimeConfigs = copyCommodityBO.getCommodityDeliveryTimeConfigs();
        if (CollectionUtils.isNotEmpty(commodityDeliveryTimeConfigs)) {
            LambdaQueryWrapper<CommodityDeliveryTimeConfig> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommodityDeliveryTimeConfig::getCommodity, commodityIds);
            commodityDeliveryTimeConfigService.remove(lqw);
        }

        List<ProductCommodityOverview> commodityOverviews = copyCommodityBO.getCommodityOverviews();
        if (CollectionUtils.isNotEmpty(commodityOverviews)) {
            LambdaQueryWrapper<ProductCommodityOverview> lqw = new LambdaQueryWrapper<>();
            lqw.in(ProductCommodityOverview::getCommodityId, commodityIds);
            commodityOverviewService.remove(lqw);
        }

        List<CommodityDifference> commodityDifferences = copyCommodityBO.getCommodityDifferences();
        if (CollectionUtils.isNotEmpty(commodityDifferences)) {
            LambdaQueryWrapper<CommodityDifference> lqw = new LambdaQueryWrapper<>();
            lqw.in(CommodityDifference::getCommodityId, commodityIds);
            commodityDifferenceService.remove(lqw);
        }

        List<CommodityDifferencePoint> commodityDifferencePoints = copyCommodityBO.getCommodityDifferencePoints();
        if (CollectionUtils.isNotEmpty(commodityDifferencePoints)) {
            LambdaQueryWrapper<CommodityDifferencePoint> lqw = new LambdaQueryWrapper<>();
            lqw.eq(CommodityDifferencePoint::getProductId, productId);
            commodityDifferencePointService.remove(lqw);
        }

        // 根据业务需求调整返回值
        return copyCommodityBO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void importCommodityInfo(CopyCommodityBO copyCommodityBO) {
        deleteCommodityInfo(copyCommodityBO);
        saveCommodityInfo(copyCommodityBO);
        deleteCache(copyCommodityBO);
    }

    public CopyCommodityBO saveCommodityInfo(CopyCommodityBO copyCommodityBO) {
        if (copyCommodityBO == null) {
            return null;
        }

        Product product = copyCommodityBO.getProduct();
        productService.importSave(product);

        List<ProductInfo> productInfos = copyCommodityBO.getProductInfos();
        if (CollectionUtils.isNotEmpty(productInfos)) {
            productInfoService.saveBatch(productInfos);
        }

        List<FaqQuestion> faqQuestions = copyCommodityBO.getFaqQuestions();
        if (CollectionUtils.isNotEmpty(faqQuestions)) {
            faqQuestions = faqQuestions.stream().peek(faqQuestion -> faqQuestion.setId(null)).collect(Collectors.toList());
            faqQuestionService.saveBatch(faqQuestions);
        }

        List<Commodity> commodities = copyCommodityBO.getCommodities();
        if (CollectionUtils.isNotEmpty(commodities)) {
            for (Commodity commodity : commodities) {
                commodityService.saveImport(commodity);
            }
        }

        List<CommodityInfo> commodityInfos = copyCommodityBO.getCommodityInfos();
        if (CollectionUtils.isNotEmpty(commodityInfos)) {
            commodityInfoService.saveBatch(commodityInfos);
        }

        List<CommodityMeta> commodityMetas = copyCommodityBO.getCommodityMetas();
        if (CollectionUtils.isNotEmpty(commodityMetas)) {
            commodityMetaService.saveBatch(commodityMetas);
        }

        List<CommodityPrice> commodityPrices = copyCommodityBO.getCommodityPrices();
        if (CollectionUtils.isNotEmpty(commodityPrices)) {
            commodityPrices = commodityPrices.stream().peek(commodityPrice -> commodityPrice.setId(null)).collect(Collectors.toList());
            commodityPriceService.saveBatch(commodityPrices, 300);
        }

        List<CommodityDisplay> commodityDisplays = copyCommodityBO.getCommodityDisplays();
        if (CollectionUtils.isNotEmpty(commodityDisplays)) {
            commodityDisplays = commodityDisplays.stream().peek(commodityDisplay -> commodityDisplay.setId(null)).collect(Collectors.toList());
            commodityDisplayService.saveBatch(commodityDisplays);
        }

        List<CommoditySaleState> commoditySaleStates = copyCommodityBO.getCommoditySaleStates();
        if (CollectionUtils.isNotEmpty(commoditySaleStates)) {
            commoditySaleStateService.saveBatch(commoditySaleStates);
        }

        List<CommodityTradeRule> commodityTradeRules = copyCommodityBO.getCommodityTradeRules();
        if (CollectionUtils.isNotEmpty(commodityTradeRules)) {
            commodityTradeRuleService.saveBatch(commodityTradeRules);
        }

        List<ProductCommodityStock> productCommodityStocks = copyCommodityBO.getProductCommodityStocks();
        if (CollectionUtils.isNotEmpty(productCommodityStocks)) {
            productCommodityStocks = productCommodityStocks.stream().peek(productCommodityStock -> productCommodityStock.setId(null)).collect(Collectors.toList());
            productCommodityStockService.saveBatch(productCommodityStocks);
        }

        List<CommodityDeliveryTimeConfig> commodityDeliveryTimeConfigs = copyCommodityBO.getCommodityDeliveryTimeConfigs();
        if (CollectionUtils.isNotEmpty(commodityDeliveryTimeConfigs)) {
            commodityDeliveryTimeConfigService.saveBatch(commodityDeliveryTimeConfigs);
        }

        List<ProductCommodityOverview> commodityOverviews = copyCommodityBO.getCommodityOverviews();
        if (CollectionUtils.isNotEmpty(commodityOverviews)) {
            commodityOverviews = commodityOverviews.stream().peek(commodityOverview -> commodityOverview.setId(null)).collect(Collectors.toList());
            commodityOverviewService.saveBatch(commodityOverviews);
        }

        List<CommodityDifference> commodityDifferences = copyCommodityBO.getCommodityDifferences();
        if (CollectionUtils.isNotEmpty(commodityDifferences)) {
            commodityDifferences = commodityDifferences.stream().peek(commodityDifference -> commodityDifference.setId(null)).collect(Collectors.toList());
            commodityDifferenceService.saveBatch(commodityDifferences);
        }

        List<CommodityDifferencePoint> commodityDifferencePoints = copyCommodityBO.getCommodityDifferencePoints();
        if (CollectionUtils.isNotEmpty(commodityDifferencePoints)) {
            commodityDifferencePoints = commodityDifferencePoints.stream().peek(commodityDifferencePoint -> commodityDifferencePoint.setId(null)).collect(Collectors.toList());
            commodityDifferencePointService.saveBatch(commodityDifferencePoints);
        }

        // 根据业务需求调整返回值
        return copyCommodityBO;

    }

    @CacheEvict(value = CacheableType.PRODUCT_INFO, key = "caches[0].name + '-ProductCachePack-' + methodName + '-productId-' + #productId + '-country-' + #country+ '-language-' + #language")
    public void deleteProductRedisCache(Integer productId, InstaCountry country, InstaLanguage language) {
    }

    @CacheEvict(value = CacheableType.COMMODITY_INFO, key = "caches[0].name + '-CommodityCachePack-' + methodName + '-commodityId-' + #commodityId + '-country-' + #country + '-language-' + #language")
    public void deleteCommodityRedisCache(Integer commodityId, InstaCountry country, InstaLanguage language) {
    }

    public CopyCommodityBO getCopyCommodityBO(CopyCommodityDTO copyCommodityDto) {
        List<Integer> commodityIds = copyCommodityDto.getCommodityIds();
        if (copyCommodityDto.getAllCommodity()) {
            List<Commodity> commodities = commodityService.getCommodities(copyCommodityDto.getProductId());
            commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
            copyCommodityDto.setCommodityIds(commodityIds);
        }
        return getCopyCommodityBO(copyCommodityDto.getProductId(), commodityIds);
    }

    private void deleteCache(CopyCommodityBO copyCommodityBO) {
        for (InstaCountry country : InstaCountry.values()) {
            for (InstaLanguage language : InstaLanguage.values()) {
                deleteProductRedisCache(copyCommodityBO.getProduct().getId(), country, language);
                List<Commodity> commodities = copyCommodityBO.getCommodities();
                commodities.forEach(commodity -> deleteCommodityRedisCache(commodity.getId(), country, language));
            }
        }
    }

}
