package com.insta360.store.service.controller.product.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.commodity.bo.GiftItem;
import com.insta360.store.business.commodity.model.AccessoryCompatibilityBind;
import com.insta360.store.business.commodity.service.AccessoryCompatibilityBindService;
import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.configuration.prerelease.annotation.ProductDataPreRelease;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.configuration.verification.annotation.StoreParameterVerification;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.service.HomepageItemService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.exception.ProductErrorCode;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.reseller.dto.ResellerGiftConfigBO;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.service.ResellerGiftConfigCommonService;
import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.trade.dto.condition.TradeCodeUseResult;
import com.insta360.store.business.trade.service.TradeCodeService;
import com.insta360.store.business.trade.service.impl.helper.gift.ActivityGiftProvider;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.product.cache.cacheable.ProductCachePack;
import com.insta360.store.service.controller.product.filter.ProductDataFilter;
import com.insta360.store.service.controller.product.format.CommodityPack;
import com.insta360.store.service.controller.product.format.GiftPack;
import com.insta360.store.service.controller.product.format.ProductGiftInfosPack;
import com.insta360.store.service.controller.product.format.ProductSearchResultFormatter;
import com.insta360.store.service.controller.product.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
@RestController
public class ProductApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductApi.class);

    @Autowired
    GiftPack giftPack;

    @Autowired
    ProductService productService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    ProductCachePack productCachePack;

    @Autowired
    TradeCodeService tradeCodeService;

    @Autowired
    ProductDataFilter productDataFilter;

    @Autowired
    HomepageItemService homeItemService;

    @Autowired
    ProductSearchResultFormatter productSearchResultFormatter;

    @Autowired
    AccessoryCompatibilityBindService accessoryCompatibilityBindService;

    @Autowired
    ResellerGiftConfigCommonService resellerGiftConfigCommonService;

    @Autowired
    ActivityGiftProvider activityGiftProvider;

    @Autowired
    ProductGiftInfosPack productGiftInfosPack;

    /**
     * 获取产品信息
     *
     * @param key
     * @return
     */
    @GrafanaDataStats(statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.PRODUCT_COMMODITY,
            keyType = GrafanaKeyType.PRODUCT_COMMODITY_PAGE)
    @ProductDataPreRelease
    @StoreParameterVerification
    @GetMapping("/store/product/product/getInfo")
    public Response<? extends Map> getProductInfo(@RequestParam(required = false, value = "id") String key) {
        // 判断key是否全部由数字组成（检测字符串是否只由数字组成）
        Product product = StringUtil.isNumeric(key) ? productService.getById(Integer.valueOf(key)) : productService.getByKey(key);
        if (product == null || !product.getEnabled()) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        // 灵活判断展示新品数据
        Boolean dataDisplay = getProductNewDataDisplay();
        if (!dataDisplay && product.getNewProduct()) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        ProductVO productVo = productCachePack.doPackCacheProductCacheInfo(product.getId(), getApiCountry(), getApiLanguage());
        // 分端数据处理
        productVo = productDataFilter.mobileDataFilter(productVo);
        return Response.ok("product", dataDisplay ? productVo : productDataFilter.getInfoDataFilter(productVo));
    }

    /**
     * 获取产品分销赠品
     *
     * @param productId
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @ProductDataPreRelease
    @StoreParameterVerification
    @GetMapping("/store/product/product/getResellerGifts")
    public Response<ProductInfoResponseVO> getProductResellerGifts(@RequestParam(required = false, value = "productId") Integer productId) {
        // 分销商校验
        Reseller reseller = resellerService.getByPromoCode(getResellerCode());
        if (reseller == null) {
            return Response.ok();
        }

        // 分销赠品校验
        List<ResellerGiftConfigBO> resellerGiftConfigBOList = resellerGiftConfigCommonService.getProductPageResellerGift(reseller.getId(), productId);
        if (CollectionUtils.isEmpty(resellerGiftConfigBOList)) {
            return Response.ok();
        }

        JSONArray commoditySpecifyGift = new JSONArray();
        ProductInfoResponseVO productInfoResponseVO = new ProductInfoResponseVO();
        resellerGiftConfigBOList.forEach(giftConfig -> {
            List<GiftItem> gifts = giftConfig.getGiftItemList();
            if (CollectionUtils.isNotEmpty(gifts)) {
                CommodityPack.PackSetting packSetting = new CommodityPack.PackSetting(this);
                packSetting.setWithCommodityInfo(true);
                packSetting.setWithPrice(true);
                packSetting.setWithDisplay(true);
                packSetting.setWithStock(true);
                packSetting.setWithProductName(true);
                packSetting.setWithProductUrlKey(true);

                List<GiftVO> giftVOList = giftPack.doPack(gifts, packSetting);
                Integer cId = giftConfig.getCommodityId();
                if (cId != null) {
                    // 按套餐来配赠品的情况
                    JSONObject giftJson = new JSONObject();
                    giftJson.put("gift", giftVOList);
                    giftJson.put("commodity_id", cId);
                    commoditySpecifyGift.add(giftJson);
                } else {
                    // 按产品来配产品的情况
                    productInfoResponseVO.setGifts(giftVOList);
                }
            }
        });

        if (!commoditySpecifyGift.isEmpty()) {
            productInfoResponseVO.setCommoditySpecificGifts(commoditySpecifyGift);
        }
        return Response.ok(productInfoResponseVO);
    }

    /**
     * 获取产品赠品（分销/活动）
     *
     * @param productId
     * @return
     */
//    @AvoidRepeatableCommit(timeOut = 500)
    @ProductDataPreRelease
    @StoreParameterVerification
    @GetMapping("/store/product/listProductGifts")
    public Response<ProductGiftInfosVO> listProductGifts(@RequestParam(value = "productId") Integer productId) {
        // 分销商校验
        Reseller reseller = resellerService.getByPromoCode(getResellerCode());
        // 分销赠品Map
        Map<Integer, List<GiftItem>> resellerGiftMap = null;
        if (Objects.nonNull(reseller)) {
            LOGGER.info("com.insta360.store.service.controller.product.controller.ProductApi.listProductGifts 分销赠品调用开始...");
            long startTime = System.currentTimeMillis();
            // 查询分销赠品
            resellerGiftMap = Optional.ofNullable(resellerGiftConfigCommonService.getProductPageResellerGift(reseller.getId(), productId))
                    .filter(CollectionUtils::isNotEmpty)
                    .map(list -> list.stream()
                            .filter(resellerGiftConfigBo -> CollectionUtils.isNotEmpty(resellerGiftConfigBo.getGiftItemList()))
                            .collect(Collectors.toMap(ResellerGiftConfigBO::getCommodityId, ResellerGiftConfigBO::getGiftItemList)))
                    .orElse(null);

            long endTime = System.currentTimeMillis();
            LOGGER.info("com.insta360.store.service.controller.product.controller.ProductApi.listProductGifts 分销赠品调用结束... 耗时:{}ms", endTime - startTime);
        }

        // 查询活动赠品
        LOGGER.info("com.insta360.store.service.controller.product.controller.ProductApi.listProductGifts 活动赠品调用开始...");
        long startTime = System.currentTimeMillis();
        Map<Integer, List<GiftItem>> activityGiftMap = activityGiftProvider.getProductGiftItems(productId, getApiCountry());
        long endTime = System.currentTimeMillis();
        LOGGER.info("com.insta360.store.service.controller.product.controller.ProductApi.listProductGifts 活动赠品调用结束... 耗时:{}ms", endTime - startTime);

        // 封装赠品信息
        LOGGER.info("com.insta360.store.service.controller.product.controller.ProductApi.listProductGifts 封装产品页赠品VO开始...");
        long startTime_ = System.currentTimeMillis();
        ProductGiftInfosVO ProductGiftInfos = productGiftInfosPack.packProductGiftInfos(activityGiftMap, resellerGiftMap, getApiCountry(), getApiLanguage());
        long endTime_ = System.currentTimeMillis();
        LOGGER.info("com.insta360.store.service.controller.product.controller.ProductApi.listProductGifts 封装产品页赠品VO结束... 耗时:{}ms", endTime_ - startTime_);
        return Response.ok(ProductGiftInfos);
    }

    /**
     * 获取产品分销折扣
     *
     * @param productId
     * @return
     */
//    @AvoidRepeatableCommit(timeOut = 500)
    @ProductDataPreRelease
    @StoreParameterVerification
    @GetMapping("/store/product/getResellerDiscount")
    public Response<? extends Map> getProductResellerDiscount(@RequestParam(required = false, value = "productId") Integer productId) {
        Map<String, Price> discountsSimple = new HashMap<>(1);
        Reseller reseller = resellerService.getByPromoCode(getResellerCode());
        if (reseller != null && StringUtil.isNotBlank(reseller.getRelatedCoupon())) {
            Map<Integer, TradeCodeUseResult> discounts = tradeCodeService.productDiscount(productId, reseller.getRelatedCoupon(), getApiCountry());
            discountsSimple = discounts.entrySet()
                    .stream().filter(o -> o.getValue()
                            .getDiscountCheckResult()
                            .getPrice() != null)
                    .collect(Collectors.toMap(k -> k.getKey().toString(), v -> v.getValue()
                            .getDiscountCheckResult()
                            .getPrice()));
        }
        return Response.ok("discounts", discountsSimple);
    }

    /**
     * 小程序模糊匹配产品名称
     *
     * @param keyword
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @ProductDataPreRelease
    @GetMapping("/store/product/product/search")
    public Response<? extends Map> searchProduct(@RequestParam(required = false, value = "keyword") String keyword) {
        // 灵活判断展示新品数据
        List<Product> products = productService.searchByProductName(keyword, getApiLanguage());
        products = getProductNewDataDisplay() ? products : products.stream().filter(product -> !product.getNewProduct()).collect(Collectors.toList());

        // 条件过滤
        products = products.stream().filter(p -> {
            // 过滤未启用的产品
            if (!p.getEnabled()) {
                return false;
            }

            // 过滤维修产品
            if (ProductCategoryMainType.CM_REPAIR_SERVICE.equals(ProductCategoryMainType.parse(p.getCategoryKey()))) {
                return false;
            }

            // 过滤出只配置在home item的产品
            return homeItemService.getByProduct(p.getId()) != null;
        }).collect(Collectors.toList());
        return Response.ok("products", productSearchResultFormatter.format(products, getApiLanguage(), getApiCountry()));
    }

    /**
     * 获取产品配件适配信息
     *
     * @param productId
     * @return
     */
    @ProductDataPreRelease
    @StoreParameterVerification
    @GetMapping("/store/product/getProductAccessoryCompatibility")
    public Response<? extends Map> getProductAccessoryCompatibility(@RequestParam Integer productId) {
        Product product = productService.getById(productId);
        if (product == null) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        // 该产品下的配件适配关系
        List<AccessoryCompatibilityBind> accessoryCompatibilityBinds = accessoryCompatibilityBindService.listAccessoryCompatibilityBind(productId);
        if (CollectionUtils.isEmpty(accessoryCompatibilityBinds)) {
            return Response.ok("productAccessoryCompatibilities", new ProductAccessoryCompatibilityVO());
        }

        // 数据封装
        ProductAccessoryCompatibilityVO productAccessoryCompatibilityVo = productCachePack.doPackCacheProductAccessoryCompatibility(productId, getApiLanguage());
        return Response.ok("productAccessoryCompatibilities", getProductNewDataDisplay()
                ? productAccessoryCompatibilityVo
                : productDataFilter.getProductAccessoryCompatibilityDataFilter(productAccessoryCompatibilityVo));
    }

    /**
     * 获取产品的overview信息
     *
     * @param productId
     * @return
     */
    @ProductDataPreRelease
    @GetMapping("/store/product/getOverview")
    public Response<? extends Map> getOverview(@RequestParam(value = "productId") Integer productId,
                                               @RequestParam(value = "isMobile", defaultValue = "false") Boolean isMobile) {
        ProductCommodityTemplateVO productCommodityTemplateVo;
        if (isMobile) {
            productCommodityTemplateVo = productCachePack.getStoreOverviewsMo(productId, getApiLanguage().name(), getApiCountry().name());
        } else {
            productCommodityTemplateVo = productCachePack.getStoreOverviewsPc(productId, getApiLanguage().name(), getApiCountry().name());
        }
        //获取数据
        return Response.ok("productOverviewTemplate", productCommodityTemplateVo);
    }
}
