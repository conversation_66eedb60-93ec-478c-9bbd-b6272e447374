package com.insta360.store.business.configuration.cache.monitor.redis.put.handler;

import java.util.concurrent.CountDownLatch;

/**
 * @Author: wbt
 * @Date: 2024/01/24
 * @Description:
 */
public interface CacheAsyncTaskService {

    /**
     * 任务一
     *
     * @param param
     * @param countDownLatch
     */
    void task1(Object param, CountDownLatch countDownLatch);

    /**
     * 任务二
     *
     * @param param
     * @param countDownLatch
     */
    void task2(Object param, CountDownLatch countDownLatch);

    /**
     * 任务三
     *
     * @param param
     * @param countDownLatch
     */
    void task3(Object param, CountDownLatch countDownLatch);

    /**
     * 是否开启异步任务执行
     *
     * @return
     */
    Boolean isAsyncTaskable();

    /**
     * 获取任务数量
     *
     * @return
     */
    Integer getTaskNumber();
}
