package com.insta360.store.service.controller.prime.notify;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.configuration.trace.TraceLogContext;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.model.StoreSdkCallRecord;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.prime.bo.PrimeWebhookNotifyBO;
import com.insta360.store.business.prime.constants.PrimeConstants;
import com.insta360.store.business.prime.enums.PrimeGraphqlOperation;
import com.insta360.store.business.prime.enums.PrimeProductStatus;
import com.insta360.store.business.prime.lib.handler.PrimeRequestHandler;
import com.insta360.store.business.prime.lib.response.BasePrimeResponse;
import com.insta360.store.business.prime.lib.response.ProductResponse;
import com.insta360.store.business.prime.lib.variables.ProductVariables;
import com.insta360.store.business.prime.model.PrimeCommodity;
import com.insta360.store.business.prime.service.PrimeCommodityService;
import com.insta360.store.service.common.BaseApi;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * service/prime/notify
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/30
 */

@RestController
public class PrimeNotifyApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrimeNotifyApi.class);

    @Autowired
    private PrimeCommodityService primeCommodityService;

    @Autowired
    private PrimeRequestHandler primeRequestHandler;

    @Autowired
    private StoreSdkCallRecordService sdkCallRecordService;

    // https://api-store-notify-test.insta360.cn
    @TraceLog(logPrefix = "prime 回调", logPrefixSub = "prime buyabilityChange")
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping("/store/openapi/prime/notify/buyabilityChange")
    public void buyabilityChange(@RequestBody String payload) {
        LOGGER.info("接收到buyabilityChange参数回调 参数为:{}", payload);
        String traceId = TraceLogContext.getTraceId();

        FeiShuMessageUtil.doSendTestProd(String.format("prime traceId[%s] %s", traceId, payload), FeiShuGroupRobot.DevNotice);
        PrimeWebhookNotifyBO primeWebhookNotifyBo = JSON.parseObject(payload, PrimeWebhookNotifyBO.class);
        LOGGER.info("buyabilityChange参数回调 封装参数为:{}", primeWebhookNotifyBo);
        String idempotencyKey = primeWebhookNotifyBo.getIdempotencyKey();

        StoreSdkCallRecord storeSdkCallRecord = new StoreSdkCallRecord();
        storeSdkCallRecord.setBusinessType(StoreSdkCallBusinessType.PRIME_API.getType());
        storeSdkCallRecord.setOrderNumber(idempotencyKey);
        storeSdkCallRecord.setTraceId(primeWebhookNotifyBo.getEventId());
        storeSdkCallRecord.setRequestJson(payload);
        storeSdkCallRecord.setApiType(StoreSdkCallApiType.PRIME_BUYABILITY_CHANGE.getType());
        storeSdkCallRecord.setCreateTime(LocalDateTime.now());
        storeSdkCallRecord.setModifyTime(LocalDateTime.now());
        sdkCallRecordService.save(storeSdkCallRecord);
        if (CollectionUtils.isEmpty(primeWebhookNotifyBo.getResources())) {
            LOGGER.error("获取到的resources为空");
            FeiShuMessageUtil.doSendTestProd("获取到的resources为空", FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
            return;
        }

        for (String resource : primeWebhookNotifyBo.getResources()) {
            LOGGER.info("获取到的resource为:{}", resource);
            if (StringUtils.isBlank(resource)) {
                LOGGER.error("获取到的resource为空");
                continue;
            }
            String[] split = StringUtils.split(resource, PrimeConstants.Webhook.SEPARATE);
            if (ArrayUtils.isEmpty(split)) {
                LOGGER.error("获取到的resource格式错误");
                FeiShuMessageUtil.doSendTestProd("获取到的resource格式错误", FeiShuGroupRobot.DevNotice, FeiShuAtUser.WXQ);
                return;
            }
            String productId = split[split.length - 1];
            PrimeCommodity primeCommodity = primeCommodityService.getByPrimeProductId(productId);
            if (primeCommodity == null) {
                LOGGER.error("prime套餐不存在");
                FeiShuMessageUtil.doSendTestProd("prime套餐不存在", FeiShuGroupRobot.DevNotice, FeiShuAtUser.WXQ);
                return;
            }
            LOGGER.info("获取到的primeCommodity为:{}", primeCommodity);
            ProductVariables productVariables = new ProductVariables();
            productVariables.setIdentifier(new ProductVariables.Identifier(primeCommodity.externalId()));
            LOGGER.info("发送查询primeCommodity请求");
            BasePrimeResponse basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.Product, productVariables);
            ProductResponse productResponse = basePrimeResponse.parsePrimeResponse(ProductResponse.class);
            String status = productResponse.getProduct().getBuyability().getStatus();
            PrimeProductStatus primeProductStatus = PrimeProductStatus.matchCode(status);
            LOGGER.info("获取到的primeCommodity状态为:{}", primeProductStatus);
            if (primeProductStatus == null || primeProductStatus.isUnknown()) {
                LOGGER.error("获取到的primeCommodity状态错误");
                return;
            }
            primeCommodity.setBuyable(primeProductStatus.isBuyable());
            primeCommodityService.updateById(primeCommodity);
            LOGGER.info("更新primeCommodity为:{}", primeCommodity);
        }

    }
}
