package com.insta360.store.business.admin.order.service.impl.handler;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.compass.libs.aliyun.oss.enums.EndpointEnum;
import com.insta360.compass.libs.aliyun.oss.enums.ModuleEnum;
import com.insta360.store.business.admin.order.dto.CnOrderPrintDTO;
import com.insta360.store.business.admin.order.enums.OrderExportType;
import com.insta360.store.business.admin.order.enums.ShipToCountry;
import com.insta360.store.business.admin.order.export.OrderCnCustomsClearanceExcelData;
import com.insta360.store.business.admin.order.model.OrderExportInfo;
import com.insta360.store.business.admin.order.print.bo.OrderPrintItemFilterResultBO;
import com.insta360.store.business.admin.order.print.helper.StoreOrderPrintInfoHelper;
import com.insta360.store.business.admin.order.print.spliter.StoreOrderSpliter;
import com.insta360.store.business.admin.order.service.OrderExportInfoService;
import com.insta360.store.business.admin.order.service.impl.handler.bo.CnCustomsClearanceBO;
import com.insta360.store.business.admin.order.service.impl.handler.constant.OrderExportConstant;
import com.insta360.store.business.admin.order.service.impl.helper.OrderCustomsDeclarationPrintHelper;
import com.insta360.store.business.admin.order.service.impl.helper.OrderExportCalculator;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.commodity.service.CommodityMetaService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.integration.avalara.enums.StoreTaxType;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.rma.exception.RmaErrorCode;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 国内清关服务-9610
 * @Date 2024/3/12
 */
@Component
public class CnCustomsClearanceExportHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CnCustomsClearanceExportHandler.class);

    private static final int MAX_SIZE = 1;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    StoreOrderPrintInfoHelper storeOrderPrintInfoHelper;

    @Autowired
    CommodityMetaService commodityMetaService;

    @Autowired
    StoreOrderSpliter storeOrderSpliter;

    @Autowired
    OrderExportCalculator orderExportHelper;

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderCustomsDeclarationPrintHelper orderCustomsDeclarationPrintHelper;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    OSSService ossService;

    @Autowired
    OrderExportInfoService orderExportInfoService;

    /**
     * 导出国内报关数据
     *
     * @param cnOrderPrintParam
     */
    public void exportCnCustomsClearanceData(CnOrderPrintDTO cnOrderPrintParam) {
        // 运抵国
        ShipToCountry shipToCountry = ShipToCountry.matchCode(cnOrderPrintParam.getShipToCountryCode());
        // 查询出需报关的订单列表
        List<Order> orderList = orderService.listByOrderNumber(cnOrderPrintParam.getOrderNumberList());
        // 报表数据
        List<OrderCnCustomsClearanceExcelData> cnCustomsClearanceExcelDataList = orderList.stream()
                .map(order -> this.doPotDhlOrderData(order, shipToCountry))
                .filter(CollectionUtils::isNotEmpty)
                .collect(Collectors.toList())
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(cnCustomsClearanceExcelDataList)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("[9610报关信息导出]集合单号[%s] 任务ID[%s] 执行失败,报表数据为空。",cnOrderPrintParam.getBusinessNumber(),cnOrderPrintParam.getTaskId()),FeiShuGroupRobot.ShippingLogistics,FeiShuAtUser.LJC,FeiShuAtUser.LB,FeiShuAtUser.ZJW);
            return;
        }

        // 二次封装
        cnCustomsClearanceExcelDataList = this.secondaryPackages(cnCustomsClearanceExcelDataList);
        // 报关数据写入Excel
        this.excelDataWrite(cnCustomsClearanceExcelDataList,cnOrderPrintParam,shipToCountry);
    }

    /**
     * Excel数据写入
     *
     * @param excelDataList
     * @param cnOrderPrintParam
     * @param shipToCountry
     */
    private void excelDataWrite(List<OrderCnCustomsClearanceExcelData> excelDataList, CnOrderPrintDTO cnOrderPrintParam, ShipToCountry shipToCountry) {
        if (!FileUtil.exist(OrderExportConstant.PATH)) {
            FileUtil.mkdir(OrderExportConstant.PATH);
        }
        String ossFileUrl = null;
        File targetFile = null;
        try {
            String fileName = "9610报关清单" + cnOrderPrintParam.getBusinessNumber() + "_深圳市顺势电子" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now()) + "_9610" + shipToCountry.getCountry() + '_' + shipToCountry.getZhName();
            targetFile = new File(OrderExportConstant.PATH + fileName + ".xlsx");
            // Excel写入
            EasyExcel.write(new FileOutputStream(targetFile), OrderCnCustomsClearanceExcelData.class)
                    .sheet("报关清单")
                    .doWrite(excelDataList);
            // 上传OSS
            ossFileUrl = ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, targetFile);
        } catch (Exception e) {
            LOGGER.error("[9610报关信息导出]报关清单数据写入Excel上传OSS时异常.", e);
        } finally {
            if (FileUtil.exist(targetFile)) {
                FileUtil.del(targetFile);
            }
        }

        // 是否上传成功
        boolean isSuccess = StringUtils.isNotBlank(ossFileUrl);

        OrderExportInfo orderExportInfo = new OrderExportInfo();
        orderExportInfo.setState(isSuccess);
        orderExportInfo.setCreateTime(cnOrderPrintParam.getUploadTime());
        orderExportInfo.setAccount(cnOrderPrintParam.getJobNumber());
        orderExportInfo.setType(OrderExportType.CN_CUSTOMS_CLEARANCE.getExportType());
        orderExportInfo.setUrl(ossFileUrl);
        orderExportInfo.setTaskId(cnOrderPrintParam.getTaskId());
        orderExportInfoService.save(orderExportInfo);

        // 格式化时间
        String uploadTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .format(cnOrderPrintParam.getUploadTime());

        // 发送飞书消息
        String msg = isSuccess ?
                String.format("[9610报关信息导出]集合单号[%s] 任务ID[%s] [%s]执行的导出任务已结束，请在对应界面列表进行文件下载。", cnOrderPrintParam.getBusinessNumber(), cnOrderPrintParam.getTaskId(), uploadTime) :
                String.format("[9610报关信息导出]集合单号[%s] 任务ID[%s] [%s]执行的导出任务已结束，上传OSS失败。", cnOrderPrintParam.getBusinessNumber(), cnOrderPrintParam.getTaskId(), uploadTime);
        FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.ShippingLogistics, FeiShuAtUser.LJC, FeiShuAtUser.LB, FeiShuAtUser.ZJW);
    }

    /**
     * 原订单逻辑
     * @param order
     * @return
     */
    private List<OrderCnCustomsClearanceExcelData> doPotDhlOrderData(Order order,ShipToCountry shipToCountry) {
        // 订单支付信息
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        // 订单商品
        List<OrderItem> orderItemList;
        // CA订单按照开始计税类型区分
        if (StoreTaxType.FINALLY.equals(order.storeTaxType()) && InstaCountry.CA.equals(order.country())) {
            // 订单商品过滤及金额分摊
            orderItemList = this.caOrderPaymentAmountApportion(order, orderPayment);
        }
        else if (PaymentChannel.usSubsidiaryPayChannel(orderPayment.paymentChannel())) {
            orderItemList = this.usOrderPaymentAmountApportion(order, orderPayment);
        }
        else {
            orderItemList = this.orderPaymentCommonApportion(order, orderPayment);
        }

        if(CollectionUtils.isEmpty(orderItemList)) {
            return null;
        }
        // 9610报表Excel映射实体构建
        return this.doPackOrderCnCustomsClearanceExcelData(orderItemList, order, shipToCountry);
    }

    /**
     * 二次封装
     * @param orderCnCustomsClearanceExcelDataList
     * @return
     */
    private List<OrderCnCustomsClearanceExcelData> secondaryPackages(List<OrderCnCustomsClearanceExcelData> orderCnCustomsClearanceExcelDataList) {
        // 重置后的Excel数据
        List<OrderCnCustomsClearanceExcelData> excelDataList = new ArrayList<>(orderCnCustomsClearanceExcelDataList.size());
        // 根据运单号分组，只保留一个订单号
        Map<String, List<OrderCnCustomsClearanceExcelData>> waybillNumberGroupMap = orderCnCustomsClearanceExcelDataList.stream().collect(Collectors.groupingBy(OrderCnCustomsClearanceExcelData::getWaybillNumber));
        for (Map.Entry<String, List<OrderCnCustomsClearanceExcelData>> entry : waybillNumberGroupMap.entrySet()) {
            // 根据订单号分组
            Map<String, List<OrderCnCustomsClearanceExcelData>> orderNumberGroupMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(OrderCnCustomsClearanceExcelData::getOrderNumber, Collectors.collectingAndThen(Collectors.toList(), d -> d.stream()
                            .sorted(Comparator.comparing(OrderCnCustomsClearanceExcelData::getSortId))
                            .collect(Collectors.toList()))));

            if (orderNumberGroupMap.size() > MAX_SIZE) {
                // 随机获取其中一笔订单数据
                OrderCnCustomsClearanceExcelData firstData = orderNumberGroupMap.entrySet()
                        .stream()
                        .findFirst()
                        .map(e -> e.getValue()
                                .stream()
                                .findFirst()
                                .get())
                        .orElse(null);

                // 序号
                AtomicInteger sortId = new AtomicInteger(1);
                // 将分组后的数据进行平铺，便于后续序号重置
                List<OrderCnCustomsClearanceExcelData> orderNumberGroupList = orderNumberGroupMap.values()
                        .stream()
                        .flatMap(List::stream)
                        .map(orderCnCustomsClearanceExcelData -> {
                            orderCnCustomsClearanceExcelData.setOrderNumber(firstData.getOrderNumber());
                            orderCnCustomsClearanceExcelData.setSortId(sortId.getAndIncrement());
                            return orderCnCustomsClearanceExcelData;
                        })
                        .collect(Collectors.toList());
                // 放入容器
                excelDataList.addAll(orderNumberGroupList);
            } else {
                entry.getValue().sort(Comparator.comparing(OrderCnCustomsClearanceExcelData::getSortId));
                excelDataList.addAll(entry.getValue());
            }
        }

        return excelDataList;
    }

    /**
     * 生成9610报表
     * @param orderItemList
     * @param order
     * @param shipToCountry
     * @return
     */
    private List<OrderCnCustomsClearanceExcelData> doPackOrderCnCustomsClearanceExcelData(List<OrderItem> orderItemList, Order order, ShipToCountry shipToCountry) {
        // 订单号
        String orderNumber = this.formatOrderNumber(orderHelper.orderCustomSerialSplice(order));
        // 运单号
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        // 套餐报关配置
        List<Integer> commodityIds = orderItemList.stream()
                .map(OrderItem::getCommodity)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, CommodityMeta> commodityMetaMap = commodityBatchHelper.commodityMetaMapByCommodityIds(commodityIds);

        // 序号
        AtomicInteger sortId = new AtomicInteger(1);

        return orderItemList.stream()
                .map(orderItem -> {
                    CommodityMeta commodityMeta = commodityMetaMap.get(orderItem.getCommodity());
                    try {
                        // 套餐报关配置校验
                        this.commodityMetaCheck(orderItem.getCommodity(), commodityMeta, order);
                    } catch (InstaException e) {
                        FeiShuMessageUtil.storeGeneralMessage(e.getMessage(), FeiShuGroupRobot.ShippingLogistics, FeiShuAtUser.LJC, FeiShuAtUser.LB, FeiShuAtUser.ZJW);
                        return null;
                    }

                    CnCustomsClearanceBO cnCustomsClearance = new CnCustomsClearanceBO();
                    cnCustomsClearance.setCommodityMeta(commodityMeta);
                    cnCustomsClearance.setOrderItem(orderItem);
                    cnCustomsClearance.setOrderNumber(orderNumber);
                    cnCustomsClearance.setWaybillNumber(orderDelivery.getExpressCode());
                    cnCustomsClearance.setShipToCountry(shipToCountry);
                    cnCustomsClearance.setSortId(sortId.getAndIncrement());
                    return OrderCnCustomsClearanceExcelData.build(cnCustomsClearance);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 套餐报关配置校验
     *
     * @param commodityId
     * @param commodityMeta
     * @param order
     */
    private void commodityMetaCheck(Integer commodityId, CommodityMeta commodityMeta, Order order) {
        if (Objects.isNull(commodityMeta)) {
            throw new InstaException(-1, String.format("[9610报关信息导出] 订单号[%s],套餐ID:[%s] 缺失报关配置。", order.getOrderNumber(), commodityId));
        }

        List<String> textList = Lists.newArrayListWithCapacity(10);
        if (StringUtils.isBlank(commodityMeta.getOfficialNameEn())) {
            textList.add("商品名称_英文");
        }
        if (StringUtils.isBlank(commodityMeta.getOfficialNameCn())) {
            textList.add("商品名称_中文");
        }
        JSONObject customCodeJson = StringUtils.isNotBlank(commodityMeta.getCustomCode()) ? JSON.parseObject(commodityMeta.getCustomCode()) : null;
        if (Objects.isNull(customCodeJson) || StringUtils.isBlank(customCodeJson.getString("default"))) {
            textList.add("海关编码");
        }
        if (StringUtils.isBlank(commodityMeta.getModel())) {
            textList.add("报关要素");
        }
        if (StringUtils.isBlank(commodityMeta.getUnit())) {
            textList.add("成交计量单位");
        }
        if (Objects.isNull(commodityMeta.getNw())) {
            textList.add("净重");
        }
        if (Objects.isNull(commodityMeta.getWeight())) {
            textList.add("重量");
        }

        if (CollectionUtils.isNotEmpty(textList)) {
            throw new InstaException(-1, String.format("[9610报关信息导出] 订单号[%s],套餐ID:[%s] 缺失以下报关配置属性 %s", order.getOrderNumber(), commodityId, StringUtils.join(textList)));
        }
    }

    /**
     * 常规收款主体订单商品金额分摊及过滤
     * @param order
     * @param orderPayment
     * @return
     */
    private List<OrderItem> orderPaymentCommonApportion(Order order,OrderPayment orderPayment) {
        // 订单商品
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        // 过滤增值服务商品
        orderItemList = storeOrderPrintInfoHelper.listItems(order, orderItemList);
        // 过滤已售后退款的商品
        OrderPrintItemFilterResultBO orderPrintItemFilterResult = orderCustomsDeclarationPrintHelper.itemRefundSceneFilter(orderItemList, order, false);
        // 拆分组合套餐
        List<OrderItem> splitOrderItemList = storeOrderSpliter.run(orderPrintItemFilterResult.getOrderItemList(), order.country());
        // 过滤不参与报关的套餐
        splitOrderItemList = orderExportHelper.checkOrderItem(splitOrderItemList);

        // 订单售后退款总金额
        BigDecimal totalRefundAmount = orderPrintItemFilterResult.getTotalRefundAmount();
        // 商品分摊总金额
        BigDecimal orderItemTotalAmount = new BigDecimal(String.valueOf(orderPayment.getTotalPayPrice().getAmount())).subtract(totalRefundAmount);
        // 价格分摊
        List<OrderItem> orderItems = orderExportHelper.runForOrderItems(splitOrderItemList, orderItemTotalAmount);

        return orderItems.stream()
                .filter(orderItem -> Objects.nonNull(orderItem.getDeliveryState()) && OrderItemState.on_delivery.equals(orderItem.deliveryState()))
                .collect(Collectors.toList());
    }

    /**
     * 美国收款主体订单商品金额分摊及过滤
     *
     * @param order
     * @param orderPayment
     * @return
     */
    private List<OrderItem> usOrderPaymentAmountApportion(Order order, OrderPayment orderPayment) {
        // 订单商品
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        // 过滤增值服务商品
        orderItemList = storeOrderPrintInfoHelper.listItems(order, orderItemList);
        // 过滤已售后退款的商品
        OrderPrintItemFilterResultBO orderPrintItemFilterResult = orderCustomsDeclarationPrintHelper.itemRefundSceneFilter(orderItemList, order, true);
        // 拆分组合套餐
        List<OrderItem> splitOrderItemList = storeOrderSpliter.run(orderPrintItemFilterResult.getOrderItemList(), order.country());
        // 过滤不参与报关的套餐
        splitOrderItemList = orderExportHelper.checkOrderItem(splitOrderItemList);

        // 订单售后退款总金额
        BigDecimal totalRefundAmount = orderPrintItemFilterResult.getTotalRefundAmount();
        // 订单售后退款总税额
        BigDecimal totalRefundTaxAmount = orderPrintItemFilterResult.getTotalRefundTaxAmount();

        // 商品分摊总金额
        BigDecimal orderItemTotalAmount = new BigDecimal(String.valueOf(orderPayment.getTotalPayPrice().getAmount()))
                .subtract(new BigDecimal(String.valueOf(orderPayment.getTax())))
                .subtract(totalRefundAmount)
                .add(totalRefundTaxAmount);

        // 价格分摊
        List<OrderItem> orderItems = orderExportHelper.runForOrderItems(splitOrderItemList, orderItemTotalAmount);

        return orderItems.stream()
                .filter(orderItem -> Objects.nonNull(orderItem.getDeliveryState()) && OrderItemState.on_delivery.equals(orderItem.deliveryState()))
                .collect(Collectors.toList());
    }

    /**
     * 加拿大订单商品金额分摊及过滤
     *
     * @param order
     * @param orderPayment
     * @return
     */
    private List<OrderItem> caOrderPaymentAmountApportion(Order order, OrderPayment orderPayment) {
        // 订单商品
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        // 过滤增值服务商品
        orderItemList = orderItemList.stream()
                .filter(item -> !Product.INSURANCE_SERVICE_PRODUCT.contains(item.getProduct())
                        || !Product.cloudProductId.equals(item.getProduct())
                        || !Product.PSP_CLOUD_ID.equals(item.getProduct()))
                .collect(Collectors.toList());

        // 过滤已售后退款的商品
        OrderPrintItemFilterResultBO orderPrintItemFilterResult = orderCustomsDeclarationPrintHelper.itemRefundSceneFilter(orderItemList, order, false);
        // 拆分组合套餐
        List<OrderItem> splitOrderItemList = storeOrderSpliter.run(orderPrintItemFilterResult.getOrderItemList(), order.country());
        // 过滤不参与报关的套餐
        splitOrderItemList = orderExportHelper.checkOrderItem(splitOrderItemList);

        // 订单售后退款总金额
        BigDecimal totalRefundAmount = orderPrintItemFilterResult.getTotalRefundAmount();
        // 商品分摊总金额
        BigDecimal orderItemTotalAmount = this.getCaTotalAmount(orderPayment, order, totalRefundAmount);
        // 价格分摊
        List<OrderItem> orderItems = orderExportHelper.runForOrderItems(splitOrderItemList, orderItemTotalAmount);

        return orderItems.stream()
                .filter(orderItem -> Objects.nonNull(orderItem.getDeliveryState()) && OrderItemState.on_delivery.equals(orderItem.deliveryState()))
                .collect(Collectors.toList());
    }

    /**
     * CA新主体订单支付总额
     *
     * @param orderPayment
     * @param order
     * @param refundedTotalAmount
     * @return
     */
    private BigDecimal getCaTotalAmount(OrderPayment orderPayment, Order order, BigDecimal refundedTotalAmount) {
        BigDecimal orderPayAmount = new BigDecimal(String.valueOf(orderPayment.getTotalPayPrice().getAmount()));

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalRmaAmount = BigDecimal.ZERO;
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        List<OrderItem> insuranceItems = orderItemList.stream().filter(item -> Product.INSURANCE_SERVICE_PRODUCT.contains(item.getProduct())
                || Product.cloudProductId.equals(item.getProduct())
                || Product.PSP_CLOUD_ID.equals(item.getProduct())).collect(Collectors.toList());
        // 增值服务子项
        if (CollectionUtils.isNotEmpty(insuranceItems)) {

            for (OrderItem insuranceItem : insuranceItems) {
                // 增值服务支付金额总额
                totalAmount = totalAmount.add(insuranceItem.getItemTotalAmountPaid());
                if (OrderItemState.refunded.equals(insuranceItem.orderItemState())) {
                    RmaOrder rmaOrder = rmaOrderService.getByOrderItem(insuranceItem.getId());
                    if (rmaOrder == null) {
                        FeiShuMessageUtil.storeGeneralMessage(String.format("【%s】子项已退款，没有售后单数据", insuranceItem.getId()), FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW);
                        throw new InstaException(RmaErrorCode.RmaNotFoundException);
                    }
                    // 记录增值服务退款总额
                    totalRmaAmount = totalRmaAmount.add(new BigDecimal(String.valueOf(rmaOrder.getRefundAmount())));
                }
            }
        }
        // 订单支付金额 - 增值服务金额 - (售后总金额 - 增值服务售后金额)
        return orderPayAmount.subtract(totalAmount)
                .subtract((refundedTotalAmount.subtract(totalRmaAmount)));
    }

    /**
     * 格式化订单号
     * @param orderNumber
     * @return
     */
    private String formatOrderNumber(String orderNumber) {
        return StringUtils.isNotBlank(orderNumber) && orderNumber.length() > 20 ? orderNumber.substring(orderNumber.length() - 20) : orderNumber;
    }
}
