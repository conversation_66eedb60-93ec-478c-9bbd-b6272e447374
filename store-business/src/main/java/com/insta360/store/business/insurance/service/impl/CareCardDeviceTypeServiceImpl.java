package com.insta360.store.business.insurance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.insurance.dao.CareCardDeviceTypeDao;
import com.insta360.store.business.insurance.model.CareCardDeviceType;
import com.insta360.store.business.insurance.service.CareCardDeviceTypeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-04-25
 * @Description:
 */
@Service
public class CareCardDeviceTypeServiceImpl extends BaseServiceImpl<CareCardDeviceTypeDao, CareCardDeviceType> implements CareCardDeviceTypeService {

    @Override
    public List<CareCardDeviceType> listDevice() {
        QueryWrapper<CareCardDeviceType> qw = new QueryWrapper<>();
        qw.orderByAsc("id");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<CareCardDeviceType> listDeviceByType(String cardType) {
        QueryWrapper<CareCardDeviceType> qw = new QueryWrapper<>();
        qw.eq("card_type", cardType);
        qw.orderByAsc("id");
        return baseMapper.selectList(qw);
    }
}
