package com.insta360.store.business.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.admin.commodity.service.impl.helper.ProductCommodityHelper;
import com.insta360.store.business.commodity.bo.CommodityLowInventoryBO;
import com.insta360.store.business.commodity.dao.CommodityDao;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.commodity.model.CommodityPlatformCodeBind;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.commodity.service.CommodityMetaService;
import com.insta360.store.business.commodity.service.CommodityPlatformCodeBindService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityStockHelper;
import com.insta360.store.business.commodity.service.impl.helper.CommodityTypeHelper;
import com.insta360.store.business.configuration.verification.enums.ParameterBusinessType;
import com.insta360.store.business.integration.google.dto.GoogleCommodityInsertQueryDTO;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.robot.FeiShuRobotConfiguration;
import com.insta360.store.business.meta.service.impl.helper.ScenerySectionHelper;
import com.insta360.store.business.outgoing.mq.verification.helper.ParameterVerificationSendHelper;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.reseller.config.ResellerConfig;
import com.insta360.store.business.reseller.model.ResellerProduct;
import com.insta360.store.business.reseller.service.ResellerProductService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
@Service
public class CommodityServiceImpl extends BaseServiceImpl<CommodityDao, Commodity> implements CommodityService {

    private static final int zero = 0;

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    CommodityPlatformCodeBindService platformCodeBindService;

    @Autowired
    ProductCommodityHelper productCommodityHelper;

    @Autowired
    ResellerProductService resellerProductService;

    @Autowired
    ResellerConfig resellerConfig;

    @Autowired
    ProductService productService;

    @Autowired
    ParameterVerificationSendHelper parameterVerificationSendHelper;

    @Autowired
    CommodityMetaService commodityMetaService;

    @Autowired
    FeiShuRobotConfiguration feiShuRobotConfiguration;

    @Autowired
    CommodityDao commodityDao;

    @Autowired
    CommodityStockHelper commodityStockHelper;

    @Autowired
    CommodityTypeHelper commodityTypeHelper;

    @Autowired
    ScenerySectionHelper scenerySectionHelper;

    @Override
    public List<Commodity> listCommodityIds(List<Long> commodityIds) {
        if (CollectionUtils.isEmpty(commodityIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("id", commodityIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public void saveImport(Commodity commodity) {
        baseMapper.saveImport(commodity);
    }

    @Override
    public List<Commodity> getCommodities(Integer productId) {
        return getCommodities(productId, null);
    }

    @Override
    public List<Commodity> getCommodities(Integer productId, Boolean enabled) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.eq("product", productId);
        qw.eq(enabled != null, "enabled", enabled);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Integer> getCommodityIds(Collection<Integer> productIds) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.select("id");
        qw.in("product", productIds);
        List<Commodity> idList = baseMapper.selectList(qw);
        return idList
                .stream()
                .map(Commodity::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Commodity> getCanBeGiftCommodity() {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.eq("can_be_gift", true);
        return baseMapper.selectList(qw);
    }

    @Override
    public Commodity getCommodityByPlatformCode(String skuCode) {
        // 查找商城sku映射关系
        String skuCodeTrim = skuCode.trim();
        if (StringUtils.isBlank(skuCodeTrim)) {
            throw new InstaException(-1, "为空");
        }
        List<CommodityCode> commodityCodes = commodityCodeService.listBySku(skuCodeTrim);
        if (CollectionUtils.isNotEmpty(commodityCodes)) {
            // 找到正常销售的套餐
            for (CommodityCode commodityCode : commodityCodes) {
                Commodity commodity = baseMapper.selectById(commodityCode.getCommodity());
                if (commodity.getNormalSale()) {
                    return commodity;
                }
            }
            // 未匹配到正常销售的套餐
            throw new InstaException(CommodityErrorCode.CommodityNotFoundNormalSaleException, commodityCodes.get(0).getCommodity().toString());
        }

        // 三方料号映射关系
        CommodityPlatformCodeBind platformCodeBind = platformCodeBindService.getBySkuCode(skuCodeTrim);
        if (platformCodeBind == null) {
            FeiShuMessageUtil.storeGeneralMessage("未找到三方平台Sku（" + skuCode + "）所对应的套餐信息。请及时更新。", FeiShuGroupRobot.MainNotice, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY, FeiShuAtUser.ZYX2);
            throw new InstaException(CommodityErrorCode.CommodityCodeNotFoundException);
        }

        Commodity commodity = baseMapper.selectById(platformCodeBind.getCommodityId());
        if (commodity.getNormalSale()) {
            return commodity;
        }

        // 未匹配到正常销售的套餐
        throw new InstaException(CommodityErrorCode.CommodityNotFoundNormalSaleException, commodity.getId().toString());
    }

    @Override
    public Commodity getCommodityBySku(String skuCode) {
        List<CommodityCode> commodityCodes = commodityCodeService.listBySku(skuCode.trim());
        if (commodityCodes.isEmpty()) {
            FeiShuMessageUtil.storeGeneralMessage("未找到Sku（" + skuCode + "）所对应的套餐信息。请及时更新。", FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY, FeiShuAtUser.ZYX2);
            throw new InstaException(CommodityErrorCode.CommodityCodeNotFoundException);
        }

        // 找到正常销售的套餐
        for (CommodityCode commodityCode : commodityCodes) {
            Commodity commodity = baseMapper.selectById(commodityCode.getCommodity());
            if (commodity.getNormalSale()) {
                return commodity;
            }
        }
        // 未匹配到正常销售的套餐
        List<Integer> commodityIds = commodityCodes.stream().map(CommodityCode::getCommodity).collect(Collectors.toList());
        FeiShuMessageUtil.storeGeneralMessage("未匹配到Sku（" + skuCode + "）所对应的正常销售套餐信息。请及时更新。套餐列表" + commodityIds, FeiShuGroupRobot.MainNotice);

        throw new InstaException(CommodityErrorCode.CommodityNotFoundException);
    }

    @Override
    public List<Commodity> listCommodities(List<Integer> commodities) {
        if (CollectionUtils.isEmpty(commodities)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("id", commodities);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Integer> listCameraCommodities(List<Integer> commodityIds) {
        return baseMapper.listCameraCommodities(commodityIds);
    }

    @Override
    public List<Commodity> listByCommodities(List<Integer> commodityIds) {
        if (CollectionUtils.isEmpty(commodityIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("id", commodityIds);
        qw.eq("enabled", true);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Commodity> getCommodities(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("product", productIds);
        qw.eq("enabled", true);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Commodity> listCommodityByTime(GoogleCommodityInsertQueryDTO googleCommodityInsertQueryDTO) {
        if (Objects.isNull(googleCommodityInsertQueryDTO)) {
            return null;
        }
        return baseMapper.listCommodityByTimeAndEnabled(googleCommodityInsertQueryDTO);
    }

    @Override
    public List<Commodity> listByProductIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("product", productIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Commodity> listCommodityByEnabled() {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.eq("enabled", true);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Commodity> listNewCommodities(Boolean enabled) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.eq("new_commodity", true);
        qw.eq(enabled, "enabled", true);
        return baseMapper.selectList(qw);
    }

    @Override
    public void upsertCommodity(Commodity commodityData) {
        if (Product.INSURANCE_SERVICE_PRODUCT.contains(commodityData.getProduct()) && commodityData.getId() != null) {
            Commodity oldCommodity = this.getById(commodityData.getId());
            // 状态改变异步处理
            if (!commodityData.getEnabled().equals(oldCommodity.getEnabled())) {
                productCommodityHelper.saveCommodityStateChange(commodityData, oldCommodity);
            }
        }

        boolean condition = Objects.isNull(commodityData.getId());
        boolean customsDeclaration = commodityData.getCustomsDeclaration() != null ? commodityData.getCustomsDeclaration() : false;

        // 更新&判断报关信息
        if (!condition && customsDeclaration) {
            // 查询报关信息
            CommodityMeta commodityMeta = commodityMetaService.getMeta(commodityData.getId());

            // 测试环境不做限制
            if (!feiShuRobotConfiguration.getTest()) {
                if (Objects.isNull(commodityMeta) && commodityData.getEnabled()) {
                    throw new InstaException(CommodityErrorCode.CommodityMetaNotFountException);
                }
            }

            if (Objects.isNull(commodityMeta) && !commodityData.getEnabled()) {
                // 飞书通知
                productCommodityHelper.customsDeclarationMsg(ProductCommodityHelper.CS_UPDATE_MSG, commodityData);
            }
        }

        // 更新或新增套餐信息
        boolean dbResult = this.saveOrUpdate(commodityData);
        if (condition && dbResult) {
            // 发送商城参数校验缓存同步刷新MQ消息
            parameterVerificationSendHelper.sendSyncRefreshCacheMsg(ParameterBusinessType.COMMODITY_ID.name(), String.valueOf(commodityData.getId()));
        }

        // 新增&报关
        if (condition && customsDeclaration) {
            // 飞书通知
            productCommodityHelper.customsDeclarationMsg(ProductCommodityHelper.CS_MSG, commodityData);
        }
    }

    @Override
    public List<Commodity> listEnabledCommodities(Integer productId) {
        List<Integer> list = Lists.newArrayList();
        if (productId.equals(resellerConfig.getDefaultAccessoriesId())) {
            List<Product> productList = productService.listProductsByKey(ProductCategoryMainType.CM_ACCESSORY.name());
            if (CollectionUtils.isNotEmpty(productList)) {
                List<Integer> productIds = productList.stream().map(Product::getId).collect(Collectors.toList());
                List<ResellerProduct> resellerProductList = resellerProductService.getResellerProductList(productIds);
                if (CollectionUtils.isNotEmpty(resellerProductList)) {
                    resellerProductList.forEach(resellerProduct -> list.add(resellerProduct.getProductId()));
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                return this.getCommodities(list);
            }
        } else {
            return this.getCommodities(productId, true);
        }
        return null;
    }

    @Override
    public List<Commodity> getCommodityAll() {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.gt("id", zero);
        return baseMapper.selectList(qw);
    }

    @Override
    public Commodity getCommodityBySkuCode(String skuCode) {
        List<CommodityCode> commodityCodes = commodityCodeService.listBySku(skuCode.trim());
        if (commodityCodes.isEmpty()) {
            FeiShuMessageUtil.storeGeneralMessage("未找到Sku（" + skuCode + "）所对应的套餐信息。请及时更新。", FeiShuGroupRobot.MainNotice, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY, FeiShuAtUser.ZYX2);
            throw new InstaException(CommodityErrorCode.CommodityCodeNotFoundException);
        }

        // sku对应的正常销售的套餐
        List<Integer> commodityIds = commodityCodes.stream()
                .map(CommodityCode::getCommodity).collect(Collectors.toList());
        List<Commodity> normalSaleCommodityList = this.listByCommoditiesNormalSale(commodityIds);
        if (CollectionUtils.isEmpty(normalSaleCommodityList)) {
            // 没有找到正常销售的套餐
            throw new InstaException(CommodityErrorCode.CommodityNotFoundException);
        }

        // 如果正常销售的只有一个 就直接返回
        if (normalSaleCommodityList.size() == 1) {
            return normalSaleCommodityList.get(0);
        }

        // 内部链接产品类型
        List<Integer> productIds = normalSaleCommodityList.stream().map(Commodity::getProduct).collect(Collectors.toList());
        List<Integer> noInteriorProductIds = productService.listByIds(productIds)
                .stream()
                .filter(product -> !ProductCategoryMainType.CM_INTERIOR.equals(ProductCategoryMainType.parse(product.getCategoryKey())))
                .map(Product::getId)
                .collect(Collectors.toList());

        // 没有内部链接的套餐
        List<Commodity> noInteriorCommodities = normalSaleCommodityList
                .stream()
                .filter(commodity -> noInteriorProductIds.contains(commodity.getProduct()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(noInteriorCommodities)) {
            return normalSaleCommodityList.get(0);
        }

        return noInteriorCommodities.get(0);
    }

    @Override
    public List<Commodity> listByCommoditiesNormalSale(List<Integer> commodityIds) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("id", commodityIds);
        qw.eq("normal_sale", true);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Commodity> listByProductIdsEnable(List<Integer> productIdList) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("product", productIdList);
        qw.eq("enabled", true);
        return baseMapper.selectList(qw);
    }

    @Override
    public void doInsertCommodity(Commodity commodityData) {
        boolean condition = Objects.isNull(commodityData.getId());
        boolean customsDeclaration = commodityData.getCustomsDeclaration() != null ? commodityData.getCustomsDeclaration() : false;

        // 更新或新增套餐信息
        int insert = baseMapper.insert(commodityData);
        if (condition && insert == 1) {
            // 初始化套餐类型为独立商品
            commodityTypeHelper.commodityTypeInit(commodityData.getProduct(), commodityData.getId());
            // 发送商城参数校验缓存同步刷新MQ消息
            parameterVerificationSendHelper.sendSyncRefreshCacheMsg(ParameterBusinessType.COMMODITY_ID.name(), String.valueOf(commodityData.getId()));
        }

        Product product = productService.getById(commodityData.getProduct());
        // 新增成功，且是工单套餐
        if (condition && insert == 1 && product.getIsRepairService()) {
            // 工单套餐库存初始化
            commodityStockHelper.repairCommodityStockInit(commodityData.getId());
        }

        // 非工单套餐填充可发货地区
        if (condition && insert == 1 && !product.getIsRepairService()) {
            commodityStockHelper.fillDeliveryArea(commodityData.getId());
        }

        // 新增&报关
        if (condition && customsDeclaration) {
            // 飞书通知
            productCommodityHelper.customsDeclarationMsg(ProductCommodityHelper.CS_MSG, commodityData);
        }
    }

    @Override
    public List<Commodity> listByRange(Integer orderIndex, Integer product) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.eq("product", product);
        qw.ge("order_index", orderIndex);
        return baseMapper.selectList(qw);
    }

    @Override
    public void updateOrderIndexByIds(List<Commodity> commodities) {
        if (CollectionUtils.isEmpty(commodities)) {
            return;
        }
        baseMapper.updateOrderIndexByIds(commodities);
    }

    @Override
    public List<Commodity> listByAllRange(Integer product, Integer firstIndex, Integer lastIndex) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.eq("product", product);
        qw.ge("order_index", firstIndex);
        qw.le("order_index", lastIndex);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public void doUpdateCommodity(Commodity commodityData) {
        // 同步修改场景专区内部名称
        scenerySectionHelper.updateByName(commodityData.getId(), commodityData.getName());

        if (Product.INSURANCE_SERVICE_PRODUCT.contains(commodityData.getProduct()) && commodityData.getId() != null) {
            Commodity oldCommodity = this.getById(commodityData.getId());
            // 状态改变异步处理
            if (!commodityData.getEnabled().equals(oldCommodity.getEnabled())) {
                productCommodityHelper.saveCommodityStateChange(commodityData, oldCommodity);
            }
        }

        boolean condition = Objects.isNull(commodityData.getId());
        boolean customsDeclaration = commodityData.getCustomsDeclaration() != null ? commodityData.getCustomsDeclaration() : false;

        // 更新&判断报关信息
        if (!condition && customsDeclaration) {
            // 查询报关信息
            CommodityMeta commodityMeta = commodityMetaService.getMeta(commodityData.getId());

            // 测试环境不做限制
            if (!feiShuRobotConfiguration.getTest()) {
                if (Objects.isNull(commodityMeta) && commodityData.getEnabled()) {
                    throw new InstaException(CommodityErrorCode.CommodityMetaNotFountException);
                }
            }

            if (Objects.isNull(commodityMeta) && !commodityData.getEnabled()) {
                // 飞书通知
                productCommodityHelper.customsDeclarationMsg(ProductCommodityHelper.CS_UPDATE_MSG, commodityData);
            }
        }

        // 更新或新增套餐信息
        int dbResult = baseMapper.updateById(commodityData);
        if (condition && dbResult == 1) {
            // 发送商城参数校验缓存同步刷新MQ消息
            parameterVerificationSendHelper.sendSyncRefreshCacheMsg(ParameterBusinessType.COMMODITY_ID.name(), String.valueOf(commodityData.getId()));
        }

        // 新增&报关
        if (condition && customsDeclaration) {
            // 飞书通知
            productCommodityHelper.customsDeclarationMsg(ProductCommodityHelper.CS_MSG, commodityData);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void batchSave(List<Commodity> commodityList) {
        if (CollectionUtils.isEmpty(commodityList)) {
            return;
        }
        baseMapper.batchImportScenesSave(commodityList);
    }

    @Override
    public List<Commodity> listByNames(List<String> commodityNames, Integer productId) {
        if (CollectionUtils.isEmpty(commodityNames) || Objects.isNull(productId)) {
            return Lists.newArrayList();
        }
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("name", commodityNames);
        qw.eq("product", productId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<CommodityLowInventoryBO> listByCommodityLowInventory(Integer stock, SaleState saleState) {
        if (Objects.isNull(stock) || Objects.isNull(saleState)) {
            return null;
        }

        return baseMapper.listByCommodityLowInventory(stock, saleState.getCode());
    }

    @Override
    public List<Commodity> getCommodityByProductList(List<Integer> productIdList) {
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("product", productIdList);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Commodity> listByCommodityIdIgnoreEnable(List<Integer> commodityIdList) {
        if (CollectionUtils.isEmpty(commodityIdList)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Commodity> qw = new QueryWrapper<>();
        qw.in("id", commodityIdList);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Integer> batchInsertCommodities(List<Commodity> commodityList) {
        commodityDao.batchInsertCommodities(commodityList);
        return commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
    }
}
