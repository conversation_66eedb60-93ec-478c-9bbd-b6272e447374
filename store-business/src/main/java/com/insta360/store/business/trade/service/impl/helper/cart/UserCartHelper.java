package com.insta360.store.business.trade.service.impl.helper.cart;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONArray;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.configuration.check.bo.CheckResultBO;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.configuration.check.handler.UserCartEmailCheckHandler;
import com.insta360.store.business.discount.utils.MathUtil;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.outgoing.mq.check.bo.UserCartEmailCheckBO;
import com.insta360.store.business.outgoing.mq.trade.dto.UserCartMessageDTO;
import com.insta360.store.business.outgoing.mq.trade.helper.UserCartMessageHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.trade.bo.UserCartCommodityInfoBO;
import com.insta360.store.business.trade.dto.condition.TradeCodeUseResult;
import com.insta360.store.business.trade.email.BaseTradeEmail;
import com.insta360.store.business.trade.email.TradeEmailFactory;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import com.insta360.store.business.trade.enums.UserCartEmailSendStatus;
import com.insta360.store.business.trade.enums.UserCartEmailTypeEnum;
import com.insta360.store.business.trade.model.UserCart;
import com.insta360.store.business.trade.model.UserCartEmailSendRecord;
import com.insta360.store.business.trade.model.UserCartEmailSendRule;
import com.insta360.store.business.trade.service.TradeCodeService;
import com.insta360.store.business.trade.service.UserCartEmailSendRecordService;
import com.insta360.store.business.trade.service.UserCartEmailSendRuleService;
import com.insta360.store.business.trade.service.UserCartService;
import com.insta360.store.business.trade.service.impl.helper.CartItemsParser;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.EmailSubscribeService;
import com.insta360.store.business.utils.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2020/10/23
 * @Description:
 */
@Component
public class UserCartHelper {

    public static final Logger LOGGER = LoggerFactory.getLogger(UserCartHelper.class);

    @Autowired
    ResellerService resellerService;

    @Autowired
    CartItemsParser cartItemsParser;

    @Autowired
    UserCartService userCartService;

    @Autowired
    TradeCodeService tradeCodeService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    TradeEmailFactory tradeEmailFactory;


    @Autowired
    ProductService productService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    UserCartMessageHelper userCartMessageHelper;

    @Autowired
    UserCartEmailCheckHandler userCartEmailCheckHandler;

    @Autowired
    ProductRecommendationService productRecommendationService;

    @Autowired
    UserCartEmailSendRuleService userCartEmailSendRuleService;

    @Autowired
    CommodityRecommendationService commodityRecommendationService;

    @Autowired
    UserCartEmailSendRecordService userCartEmailSendRecordService;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    /**
     * 封装新品标签信息
     *
     * @param commodityId
     * @param tagBindMap
     * @param newTagIds
     * @return
     */
    public Boolean packCommodityTagInfo(Integer commodityId, Map<Integer, List<CommodityTagBind>> tagBindMap,
                                        List<Integer> newTagIds) {
        // 没有绑定tag 则不进行展示
        List<CommodityTagBind> commodityTagBinds = tagBindMap.get(commodityId);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(commodityTagBinds)) {
            return false;
        }

        // 只选择新品标签
        List<CommodityTagBind> newCommodityTagBinds = commodityTagBinds.stream()
                .filter(commodityTagBind -> newTagIds.contains(commodityTagBind.getTagId()))
                .collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(newCommodityTagBinds);
    }

    /**
     * 获取推荐配件的套餐信息
     * 1、相机之前取价格最高的
     * 2、配件之间取价格最高的
     * 3、存在相机则不用理会配件（即使只有一个相机）
     *
     * @param commodityIds
     * @param country
     * @return
     */
    public List<Commodity> listRecommendCommodity(List<Integer> commodityIds, InstaCountry country) {
        try {
            // 所有相机套餐的价格信息（按当前地区启用的那个价格作为基准）
            CommodityPrice commodityPrice = commodityPriceService.getMaxPrice(commodityIds, country);

            // 套餐维度的推荐配件
            List<CommodityRecommendation> commodityRecommendations = commodityRecommendationService.listByCommodityId(commodityPrice.getCommodityId());
            List<Integer> commodityRecommendationIds = commodityRecommendations.stream().map(CommodityRecommendation::getRecommendCommodityId).collect(Collectors.toList());
            Set<Integer> commodityRecommendationIdSets = new LinkedHashSet<>(commodityRecommendationIds);

            // 产品维度的推荐配件
            Commodity commodity = commodityService.getById(commodityPrice.getCommodityId());
            List<ProductRecommendation> productRecommendations = productRecommendationService.listByProductId(commodity.getProduct());
            productRecommendations.forEach(productRecommendation -> commodityRecommendationIdSets.add(productRecommendation.getRecommendCommodityId()));
            if (commodityRecommendationIdSets.isEmpty()) {
                return new ArrayList<>();
            }

            // 重排序以后的套餐列表
            List<Commodity> commodities = commodityService.listCommodities(new ArrayList<>(commodityRecommendationIdSets));
            return sortCommodityRecommendation(commodities, commodityRecommendationIdSets);
        } catch (Exception e) {
            // 没有配置的暂时不显示
            return new ArrayList<>();
        }
    }

    /**
     * 分销减价
     *
     * @param commodities
     * @param promoCome
     * @param country
     * @return
     */
    public Map<String, Price> resellerDiscount(List<Commodity> commodities, String promoCome, InstaCountry country) {
        Reseller reseller = resellerService.getByPromoCode(promoCome);
        Map<String, Price> discountsSimple = new HashMap<>(1);
        if (reseller != null && StringUtil.isNotBlank(reseller.getRelatedCoupon())) {
            List<Integer> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
            Map<Integer, TradeCodeUseResult> discounts = tradeCodeService.commodityDiscount(commodityIds, reseller.getRelatedCoupon(), country);

            discountsSimple = discounts.entrySet()
                    .stream().filter(o -> o.getValue()
                            .getDiscountCheckResult()
                            .getPrice() != null)
                    .collect(Collectors.toMap(k -> k.getKey().toString(), v -> v.getValue()
                            .getDiscountCheckResult()
                            .getPrice()));
        }
        return discountsSimple;
    }

    /**
     * 发送购物车促销邮件
     *
     * @param userCartMessageParam 用户购物车消息参数
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void sendTradeUserCartEmail(UserCartMessageDTO userCartMessageParam) {
        String uuid = userCartMessageParam.getUuid();

        // 如果商城账户不存在 则记录错误日志终止流程
        StoreAccount storeAccount = userCartMessageParam.getStoreAccount();
        if (Objects.isNull(storeAccount)) {
            LOGGER.error("[购物车邮件链路-用户不存在流程终止] uuid:{}", uuid);
            return;
        }

        // 前置数据获取
        UserCart userCart = userCartService.getByUserId(storeAccount.getInstaAccount());

        UserCartEmailEnum userCartEmailEnum = userCartMessageParam.getUserCartEmailEnum();
        Integer sendRecordId = userCartMessageParam.getUserSendRecordId();
        UserCartEmailSendRecord userCartEmailSendRecord = userCartEmailSendRecordService.getById(sendRecordId);
        if (userCartEmailSendRecord == null || !UserCartEmailSendStatus.Ready.equals(userCartEmailSendRecord.status())) {
            LOGGER.info("[购物车邮件发送记录不存在或状态不符合] uuid:{} recordId:{} userCartEmailSendRecord:{}", uuid, sendRecordId, userCartEmailSendRecord);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[购物车邮件发送记录不存在或状态不符合] uuid:{%s} recordId:{%s} userCartEmailSendRecord:{%s}", uuid, sendRecordId, userCartEmailSendRecord), FeiShuGroupRobot.InternalWarning);
            return;
        }

        // 获取邮件信息 检查发送邮件的条件是否满足
        String email = storeAccount.getUsername();
        CheckResultBO checkResult = checkEmailSendConditions(uuid, sendRecordId, userCart, email, userCartEmailEnum);

        // 如果发送条件不通过，则更新记录状态并终止流程
        if (checkResult.isNotPass()) {
            LOGGER.info("[购物车邮件发送条件检查未通过] uuid:{} storeAccount:{} email:{} type:{} reason:{}", uuid, storeAccount, email, userCartEmailEnum, checkResult.getName());
            userCartEmailSendRecord.setStatus(UserCartEmailSendStatus.Fail.getCode());
            userCartEmailSendRecord.setReason(checkResult.getName());
            LOGGER.info("[购物车邮件发送条件检查未通过] uuid:{} 删除记录:{}", uuid, userCartEmailSendRecord);
            userCartEmailSendRecordService.removeById(sendRecordId);
            return;
        }

        EmailSubscribe emailSubscribe = emailSubscribeService.getByEmailLast(email);
        UserCartEmailSendRule userCartEmailSendRule = userCartEmailSendRuleService.getByCountry(emailSubscribe.country());

        // 解析购物车商品项
        List<OrderSheet.SheetItem> items = cartItemsParser.parse(JSONArray.parseArray(userCart.getItems()));
        List<Integer> commodities = items.stream().map(OrderSheet.SheetItem::getCommodityId).collect(Collectors.toList());
        Map<Integer, Integer> commodityIdNumberMap = new HashMap<>(items.size());
        items.forEach(item -> commodityIdNumberMap.put(item.getCommodityId(), item.getNumber()));
        LOGGER.info("[购物车邮件套餐数据] uuid:{} email:{} commodityIdNumberMap:{}", uuid, email, commodityIdNumberMap);

        Currency currency = countryConfigService.getCountryCurrency(emailSubscribe.country());

        String cartEmailEnumType = userCartEmailEnum.getType();
        List<UserCartCommodityInfoBO> userCartCommodityInfoBos = new ArrayList<>();
        if (UserCartEmailTypeEnum.normal.name().equals(cartEmailEnumType)) {
            userCartCommodityInfoBos = packCartCommodityInfo(uuid, userCartEmailSendRule.language(), commodities, emailSubscribe.country(), commodityIdNumberMap);
        }
        if (UserCartEmailTypeEnum.a3.name().equals(cartEmailEnumType)) {
            userCartCommodityInfoBos = packParticularCommodityInfo(uuid, userCartEmailSendRule.language(), commodities, Product.X5_ID, emailSubscribe.country(), commodityIdNumberMap);
        }

        if (UserCartEmailTypeEnum.flow2pro.name().equals(cartEmailEnumType)) {
            userCartCommodityInfoBos = packParticularCommodityInfo(uuid, userCartEmailSendRule.language(), commodities, Product.FLOW2PRO_ID, emailSubscribe.country(), commodityIdNumberMap);
        }

        if (CollectionUtils.isEmpty(userCartCommodityInfoBos)) {
            LOGGER.info("[购物车邮件套餐过滤后数据为空] uuid:{} email:{}", uuid, email);
            return;
        }

        // 根据商品和套餐信息发送邮件
        BaseTradeEmail userCartEmail = tradeEmailFactory.getEmail(uuid,
                userCartEmailSendRule.language(),
                emailSubscribe.country(),
                currency,
                userCartEmailEnum,
                userCartCommodityInfoBos,
                userCartEmailEnum.getTemplate());
        userCartEmail.doSend(email);

        // 语言取userCartEmailSendRule
        userCartEmailSendRecord.setLanguage(userCartEmailSendRule.language().name());
        userCartEmailSendRecord.setCountry(emailSubscribe.getCountry());
        userCartEmailSendRecord.setCartItem(userCart.getItems());
        userCartEmailSendRecord.setSendTime(LocalDateTime.now());
        userCartEmailSendRecord.setStatus(UserCartEmailSendStatus.Send.getCode());
        userCartEmailSendRecordService.updateById(userCartEmailSendRecord);
        LOGGER.info("[购物车促销邮件发送完毕] uuid:{} email:{}", uuid, email);

        // 发送下一封邮件
        UserCartEmailEnum nextEmail = userCartEmailEnum.getNextEmail();
        this.sendNewEmailMessage(nextEmail, storeAccount, uuid);
    }

    /**
     * 解决mybatisPlus根据id重排序的问题
     *
     * @param commodities
     * @param commodityIds
     * @return
     */
    private List<Commodity> sortCommodity(List<Commodity> commodities, List<Integer> commodityIds) {
        Map<Integer, Commodity> commodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, o -> o));
        return commodityIds.stream().map(commodityMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 封装指定套餐数据
     *
     * @param uuid
     * @param language
     * @param commodityIds
     * @param productId
     * @param country
     * @param commodityIdNumberMap
     * @return
     */
    public List<UserCartCommodityInfoBO> packParticularCommodityInfo(String uuid,
                                                                     InstaLanguage language,
                                                                     List<Integer> commodityIds,
                                                                     Integer productId,
                                                                     InstaCountry country,
                                                                     Map<Integer, Integer> commodityIdNumberMap) {
        // 查询和组装产品套餐、价格、主图、销售状态等数据
        List<Commodity> commodityList = commodityService.listByCommodities(commodityIds);

        // A3套餐重排序
        commodityList = this.sortCommodity(commodityList, commodityIds);
        commodityList = this.prioritizeCommodities(commodityList, productId);

        List<Integer> productIds = commodityList.stream().map(Commodity::getProduct).collect(Collectors.toList());
        List<Product> productList = productService.listEnableByProductIds(productIds);
        List<Integer> productIdsEnabled = productList.stream().map(Product::getId).collect(Collectors.toList());
        List<Integer> commodityIdsEnabled = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());

        List<CommoditySaleState> commoditySaleStateList = commoditySaleStateService.listSaleStateByCommodityIds(commodityIdsEnabled, country);
        List<ProductInfo> productInfoList = productInfoService.listInfoByProductIdsAndLanguage(productIdsEnabled, language);
        List<CommodityInfo> commodityInfoList = commodityInfoService.listInfoByIdsAndLanguage(commodityIdsEnabled, language);
        List<CommodityDisplay> commodityDisplayList = commodityDisplayService.listByCommodityIds(commodityIdsEnabled);
        List<CommodityPrice> commodityPriceList = commodityPriceService.getPriceByCommodityIds(commodityIdsEnabled, country);

        // 选择新品标签
        Map<Integer, CommodityTagGroup> newCommodityTagGroupMap = commodityBatchHelper.newTagGroupMapTagIds();
        Map<Integer, List<CommodityTagBind>> tagBindMap = commodityBatchHelper.commodityTagBindMapCommodityIds(commodityIds);
        List<Integer> newTagIds = new ArrayList<>(newCommodityTagGroupMap.keySet());

        // 将数据封装为map
        Map<Integer, SaleState> commodityStateMap = commoditySaleStateList.stream().collect(Collectors.toMap(CommoditySaleState::getCommodityId, CommoditySaleState::parseSaleState));
        Map<Integer, ProductInfo> productInfoMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getProduct, productInfo -> productInfo));
        Map<Integer, CommodityInfo> commodityInfoMap = commodityInfoList.stream().collect(Collectors.toMap(CommodityInfo::getCommodity, commodityInfo -> commodityInfo));
        Map<Integer, CommodityDisplay> commodityDisplayMap = commodityDisplayList.stream().collect(Collectors.toMap(CommodityDisplay::getCommodity, commodityDisplay -> commodityDisplay, (k1, k2) -> k1));
        Map<Integer, CommodityPrice> commodityPriceMap = commodityPriceList.stream().collect(Collectors.toMap(CommodityPrice::getCommodityId, commodityPrice -> commodityPrice));

        List<UserCartCommodityInfoBO> cartInfoList = commodityList.stream()
                // 只要销售状态为 正常的套餐信息
                .filter(commodity -> {
                    SaleState saleState = commodityStateMap.get(commodity.getId());
                    return SaleState.normal.equals(saleState);
                })
                // 封装数据
                .map(commodity -> {
                    UserCartCommodityInfoBO userCartCommodityInfo = buildUserCartCommodityInfo(commodity, productInfoMap, commodityInfoMap, commodityDisplayMap, commodityPriceMap, commodityIdNumberMap);

                    userCartCommodityInfo.setNewTag(packCommodityTagInfo(commodity.getId(), tagBindMap, newTagIds));

                    return userCartCommodityInfo;
                })
                // 过滤无效数据
                .filter(this::doFiledFilter)
                // 如果超过3个，则只要前三个数据
                .limit(3)
                .collect(Collectors.toList());

        LOGGER.info("uuid:{} cartInfoList:{}", uuid, cartInfoList);
        return cartInfoList;
    }

    /**
     * 发送新的邮件消息
     *
     * @param userCartEmailEnum 邮件枚举类型
     * @param account           商城账户信息
     * @param uuid              uuid
     */
    public void sendNewEmailMessage(UserCartEmailEnum userCartEmailEnum, StoreAccount account, String uuid) {
        LOGGER.info("name:{} uuid:{} type:{} account:{}", "发送新购物车邮件-start", uuid, userCartEmailEnum, account);
        if (userCartEmailEnum == null || account == null) {
            return;
        }

        String email = account.getUsername();
        EmailSubscribe emailSubscribe = emailSubscribeService.getByEmailLast(email);
        if (emailSubscribe == null) {
            LOGGER.info("name:{} uuid:{} type:{} email:{} account:{}", "邮件订阅不存在-流程终止", uuid, userCartEmailEnum, email, account);
            return;
        }
        if (StringUtils.isBlank(emailSubscribe.getCountry())) {
            LOGGER.info("name:{} uuid:{} type:{} email:{} account:{}", "邮件订阅国家为空-流程终止", uuid, userCartEmailEnum, email, account);
            return;
        }
        UserCartEmailSendRule userCartEmailSendRule = userCartEmailSendRuleService.getByCountry(emailSubscribe.country());
        if (userCartEmailSendRule == null) {
            LOGGER.info("name:{} uuid:{} type:{} email:{} account:{}", "邮件发送规则不存在-流程终止", uuid, userCartEmailEnum, email, account);
            FeiShuMessageUtil.storeGeneralMessage(String.format("name:{%s} uuid:{%s} type:{%s} email:{%s} account:{%s}", "邮件发送规则不存在-流程终止", uuid, userCartEmailEnum, email, account), FeiShuGroupRobot.InternalWarning);
            return;
        }

        // 计算实际邮件应该发送的时间
        LocalTime time = userCartEmailSendRule.getSendTime();
        LocalDate localDate = LocalDate.now().plusDays(userCartEmailEnum.getDelayTime());
        LocalDateTime planSendTime = localDate.atTime(time);

        // 计算预计等待时间 分钟
        long awaitMillis = Math.abs(Duration.between(planSendTime, LocalDateTime.now()).toMillis());

        // 增加 随机时间，分散发送时间
        awaitMillis = awaitMillis + TimeUnit.MINUTES.toMillis(RandomUtil.randomLong(1, 10));

        // 创建一个新的用户购物车邮件发送记录
        UserCartEmailSendRecord userCartEmailSendRecord = new UserCartEmailSendRecord();
        userCartEmailSendRecord.setTemplateKey(userCartEmailEnum.getTemplateKey());
        userCartEmailSendRecord.setEmailType(userCartEmailEnum.getCode());
        userCartEmailSendRecord.setEmail(email);
        userCartEmailSendRecord.setUuid(uuid);
        userCartEmailSendRecord.setPlanSendTime(planSendTime);
        userCartEmailSendRecord.calculateIntervalCartTime(awaitMillis);
        userCartEmailSendRecord.setStatus(UserCartEmailSendStatus.Ready.getCode());
        userCartEmailSendRecordService.save(userCartEmailSendRecord);
        LOGGER.info("name:{} uuid:{} type:{} email:{} userCartEmailSendRecord:{}", "提前保存发送记录-userCartEmailSendRecord", uuid, userCartEmailEnum, email, userCartEmailSendRecord);

        UserCartMessageDTO userCartMessageDto = new UserCartMessageDTO();
        userCartMessageDto.setStoreAccount(account);
        userCartMessageDto.setUserSendRecordId(userCartEmailSendRecord.getId());
        userCartMessageDto.setUserCartEmailEnum(userCartEmailEnum);
        userCartMessageDto.setAwaitMillis(awaitMillis);
        userCartMessageDto.setUuid(uuid);
        userCartMessageHelper.sendUserCartEmailMessage(userCartMessageDto);

        // 记录日志，包含邮件发送记录的相关信息
        LOGGER.info("name:{} uuid:{} type:{} email:{} account:{} record:{}", "发送新购物车邮件-end", uuid, userCartEmailEnum, email, account, userCartEmailSendRecord);
    }

    /**
     * 解析封装购物车信息数据
     *
     * @return
     */
    public List<UserCartCommodityInfoBO> packCartCommodityInfo(String uuid,
                                                               InstaLanguage language,
                                                               List<Integer> commodityIds,
                                                               InstaCountry country,
                                                               Map<Integer, Integer> commodityIdNumberMap) {
        // 查询和组装产品套餐、价格、主图、销售状态等数据
        List<Commodity> commodityList = commodityService.listByCommodities(commodityIds);

        List<Integer> productIds = commodityList.stream().map(Commodity::getProduct).collect(Collectors.toList());
        List<Product> productList = productService.listEnableByProductIds(productIds);
        List<Integer> productIdsEnabled = productList.stream().map(Product::getId).collect(Collectors.toList());
        List<Integer> commodityIdsEnabled = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());

        List<CommoditySaleState> commoditySaleStateList = commoditySaleStateService.listSaleStateByCommodityIds(commodityIdsEnabled, country);
        List<ProductInfo> productInfoList = productInfoService.listInfoByProductIdsAndLanguage(productIdsEnabled, language);
        List<CommodityInfo> commodityInfoList = commodityInfoService.listInfoByIdsAndLanguage(commodityIdsEnabled, language);
        List<CommodityDisplay> commodityDisplayList = commodityDisplayService.listByCommodityIds(commodityIdsEnabled);
        List<CommodityPrice> commodityPriceList = commodityPriceService.getPriceByCommodityIds(commodityIdsEnabled, country);

        // 将数据封装为map
        Map<Integer, SaleState> commodityStateMap = commoditySaleStateList.stream().collect(Collectors.toMap(CommoditySaleState::getCommodityId, CommoditySaleState::parseSaleState));
        Map<Integer, ProductInfo> productInfoMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getProduct, productInfo -> productInfo));
        Map<Integer, CommodityInfo> commodityInfoMap = commodityInfoList.stream().collect(Collectors.toMap(CommodityInfo::getCommodity, commodityInfo -> commodityInfo));
        Map<Integer, CommodityDisplay> commodityDisplayMap = commodityDisplayList.stream().collect(Collectors.toMap(CommodityDisplay::getCommodity, commodityDisplay -> commodityDisplay, (k1, k2) -> k1));
        Map<Integer, CommodityPrice> commodityPriceMap = commodityPriceList.stream().collect(Collectors.toMap(CommodityPrice::getCommodityId, commodityPrice -> commodityPrice));

        List<UserCartCommodityInfoBO> cartInfoList = commodityList.stream()
                // 只要销售状态为 正常的套餐信息
                .filter(commodity -> {
                    SaleState saleState = commodityStateMap.get(commodity.getId());
                    return SaleState.normal.equals(saleState);
                })
                // 封装数据
                .map(commodity -> buildUserCartCommodityInfo(commodity, productInfoMap, commodityInfoMap, commodityDisplayMap, commodityPriceMap, commodityIdNumberMap))
                // 过滤无效数据
                .filter(this::doFiledFilter)
                // 如果超过3个，则只要前三个数据
                .limit(3)
                .collect(Collectors.toList());

        LOGGER.info("uuid:{} cartInfoList:{}", uuid, cartInfoList);
        return cartInfoList;

    }

    /**
     * 套餐重排序（将指定产品的商品排在前面）
     *
     * @param commodityList 原始商品列表
     * @param productId     需要前置的产品ID
     * @return 重新排序后的商品列表（指定产品在前，其他商品在后）
     */
    private List<Commodity> prioritizeCommodities(List<Commodity> commodityList, Integer productId) {
        // 快速返回空列表或null的情况
        if (CollectionUtils.isEmpty(commodityList)) {
            return commodityList;
        }

        // 获取有效的目标产品商品列表
        List<Integer> targetCommodityIds = commodityService.listEnabledCommodities(productId)
                .stream()
                .map(Commodity::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(targetCommodityIds)) {
            return commodityList;
        }

        // 使用partitioningBy一次性分割列表，避免多次遍历
        Map<Boolean, List<Commodity>> partitionedMap = commodityList.stream()
                .collect(Collectors.partitioningBy(
                        commodity -> targetCommodityIds.contains(commodity.getId())
                ));

        // 合并结果：目标商品在前，其他商品在后
        List<Commodity> sortedCommodities = new ArrayList<>(commodityList.size());
        sortedCommodities.addAll(partitionedMap.get(true));
        sortedCommodities.addAll(partitionedMap.get(false));

        return sortedCommodities;
    }

    /**
     * 检查发送邮件的条件是否满足
     *
     * @param uuid              uuid
     * @param recordId          记录ID
     * @param userCart          购物车信息
     * @param email             邮件
     * @param userCartEmailEnum 用户购物车邮件枚举，用于指定邮件类型
     * @return 返回一个检查结果对象，用于判断是否可以发送邮件
     */
    private CheckResultBO checkEmailSendConditions(String uuid, Integer recordId, UserCart userCart, String email, UserCartEmailEnum userCartEmailEnum) {
        LOGGER.info("[购物车促销邮件发送条件检查开始] uuid:{} email:{} recordId:{} type:{}", uuid, email, recordId, userCartEmailEnum);
        DoubleCheckBO doubleCheckBo = new DoubleCheckBO();
        doubleCheckBo.setBusinessId(recordId);
        doubleCheckBo.setUserCartEmailCheckBo(new UserCartEmailCheckBO(email, userCart, userCartEmailEnum));
        doubleCheckBo.setUuid(uuid);
        doubleCheckBo.setCheckType(DoubleCheckEnum.UserCartEmailCheck);
        List<CheckResultBO> checkResults = userCartEmailCheckHandler.doubleCheck(doubleCheckBo);

        // 如果有未通过的检查结果，则返回该结果；否则创建并返回一个新的通过结果
        return checkResults.stream().filter(CheckResultBO::isNotPass).findAny().orElse(new CheckResultBO(StringUtils.EMPTY, true));
    }

    /**
     * 解决Mybatis Plus会自动根据id排序的机制
     *
     * @param commodities                商品ID
     * @param commodityRecommendationIds 商品推荐id
     * @return {@link List}<{@link Commodity}>
     */
    private List<Commodity> sortCommodityRecommendation(List<Commodity> commodities, Set<Integer> commodityRecommendationIds) {
        Map<Integer, Commodity> commodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, o -> o));
        return commodityRecommendationIds.stream().map(commodityMap::get).collect(Collectors.toList());
    }

    /**
     * 封装购物车信息
     *
     * @param commodity
     * @param productInfoMap
     * @param commodityInfoMap
     * @param commodityDisplayMap
     * @param commodityPriceMap
     * @return
     */
    private UserCartCommodityInfoBO buildUserCartCommodityInfo(Commodity commodity,
                                                               Map<Integer, ProductInfo> productInfoMap,
                                                               Map<Integer, CommodityInfo> commodityInfoMap,
                                                               Map<Integer, CommodityDisplay> commodityDisplayMap,
                                                               Map<Integer, CommodityPrice> commodityPriceMap,
                                                               Map<Integer, Integer> commodityIdNumberMap) {
        CommodityInfo commodityInfo = commodityInfoMap.get(commodity.getId());
        CommodityPrice commodityPrice = commodityPriceMap.get(commodity.getId());
        ProductInfo productInfo = productInfoMap.get(commodity.getProduct());
        CommodityDisplay commodityDisplay = commodityDisplayMap.get(commodity.getId());
        Integer number = commodityIdNumberMap.get(commodity.getId());

        // 组装各个字段的数据，避免空指针
        UserCartCommodityInfoBO userCartCommodityInfoBo = new UserCartCommodityInfoBO();
        Optional.ofNullable(productInfo).map(ProductInfo::getName).ifPresent(userCartCommodityInfoBo::setProductName);
        Optional.ofNullable(commodityInfo).map(CommodityInfo::getName).ifPresent(userCartCommodityInfoBo::setCommodityName);
        Optional.ofNullable(commodityDisplay).map(CommodityDisplay::getUrl).ifPresent(userCartCommodityInfoBo::setProductImage);
        Optional.ofNullable(commodityPrice).map(CommodityPrice::getOriginAmount).map(MathUtil::getBigDecimal).map(BigDecimal::doubleValue).ifPresent(userCartCommodityInfoBo::setOriginPrice);
        Optional.ofNullable(commodityPrice).map(CommodityPrice::getAmount).map(MathUtil::getBigDecimal).map(BigDecimal::doubleValue).ifPresent(userCartCommodityInfoBo::setCurrentPrice);
        Optional.ofNullable(number).ifPresent(userCartCommodityInfoBo::setNumber);
        return userCartCommodityInfoBo;
    }

    /**
     * 过滤无效数据
     *
     * @param userCartCommodityInfoBo
     * @return
     */
    private boolean doFiledFilter(UserCartCommodityInfoBO userCartCommodityInfoBo) {
        return CommonUtil.checkValidationObject(userCartCommodityInfoBo);
    }
}
