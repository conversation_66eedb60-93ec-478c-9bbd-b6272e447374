package com.insta360.store.service.controller.order.format;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.service.CreditCardPaymentInfoService;
import com.insta360.store.business.tradeup.model.TradeupDevice;
import com.insta360.store.business.tradeup.model.TradeupEvaluationRule;
import com.insta360.store.business.tradeup.model.TradeupOrder;
import com.insta360.store.business.tradeup.service.TradeupDeviceService;
import com.insta360.store.business.tradeup.service.TradeupEvaluationRuleService;
import com.insta360.store.business.tradeup.service.TradeupOrderService;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.StoreAccountService;
import com.insta360.store.business.user.service.UserPayInfoService;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import com.insta360.store.service.controller.order.vo.info.OrderInfoVO;
import com.insta360.store.service.controller.order.vo.info.OrderItemInfoVO;
import com.insta360.store.service.controller.order.vo.info.OrderPaymentInfoVO;
import com.insta360.store.service.controller.tradeup.vo.info.TradeupDeviceVO;
import com.insta360.store.service.controller.tradeup.vo.info.TradeupEvaluationRuleVO;
import com.insta360.store.service.controller.tradeup.vo.info.TradeupOrderInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/10/18
 * @Description: 精简版
 */
@Component
public class OrderSimpleInfoPack {

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    TradeupOrderService tradeupOrderService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    TradeupDeviceService tradeupDeviceService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    TradeupEvaluationRuleService tradeupEvaluationRuleService;

    @Autowired
    CreditCardPaymentInfoService creditCardPaymentInfoService;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    StoreAccountService storeAccountService;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    UserAccountHelper userAccountHelper;

    /**
     * 封装订单信息
     *
     * @param order
     * @param language
     * @return
     */
    public OrderInfoVO doPackSimpleOrderInfo(Order order, InstaLanguage language) {
        Integer orderId = order.getId();

        // 订单收货地址
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(orderId);

        // 数据封装
        OrderInfoVO orderInfoVo = new OrderInfoVO();
        orderInfoVo.setOrderId(orderId);
        orderInfoVo.setArea(order.getArea());
        orderInfoVo.setOrderState(order.getState());
        orderInfoVo.setPhone(orderDelivery.getPhone());
        orderInfoVo.setZipCode(orderDelivery.getZipCode());
        orderInfoVo.setCity(orderDelivery.getCity());
        orderInfoVo.setLastName(orderDelivery.getLastName());
        orderInfoVo.setFirstName(orderDelivery.getFirstName());
        orderInfoVo.setPhoneCode(orderDelivery.getPhoneCode());
        orderInfoVo.setProvince(orderDelivery.getProvince());
        orderInfoVo.setOrderNumber(order.getOrderNumber());
        orderInfoVo.setContactEmail(order.getContactEmail());
        orderInfoVo.setOrderCreateTime(order.getCreateTime());
        orderInfoVo.setSubscribeScenesType(order.getSubscribeScenesType());
        orderInfoVo.setOrderPaymentInfo(this.getOrderPaymentInfo(order));
        orderInfoVo.setOrderItemInfos(this.listOrderItemInfos(order, language));
        orderInfoVo.setTradeupOrderInfo(this.getTradeOrderInfo(order));
        orderInfoVo.setPayFailedInfo(this.getPayFailedInfo(order));
        return orderInfoVo;
    }

    /**
     * 校验是否需要展示失败指引
     * 前端自行判断是否需要展示指引
     *
     * @param order
     * @return
     */
    private String getPayFailedInfo(Order order) {
        CreditCardPaymentInfo creditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(order.getOrderNumber());
        return Objects.isNull(creditCardPaymentInfo) ? StringUtils.EMPTY : creditCardPaymentInfo.getPayMethod();
    }

    /**
     * 以旧换新订单信息
     *
     * @param order
     * @return
     */
    private TradeupOrderInfoVO getTradeOrderInfo(Order order) {
        TradeupOrder tradeupOrder = tradeupOrderService.getByOrder(order.getId());
        if (tradeupOrder == null) {
            return null;
        }
        // 置换设备评估信息
        TradeupEvaluationRule tradeupEvaluationRule = tradeupEvaluationRuleService.getById(tradeupOrder.getEvaluationRule());
        TradeupEvaluationRuleVO tradeupEvaluationRuleVo = new TradeupEvaluationRuleVO();
        tradeupEvaluationRuleVo.setId(tradeupEvaluationRuleVo.getId());
        tradeupEvaluationRuleVo.setDevice(tradeupEvaluationRule.getDevice());
        tradeupEvaluationRuleVo.setPriceMap(tradeupEvaluationRule.getPriceMap());
        tradeupEvaluationRuleVo.setPriceMap(tradeupEvaluationRule.getPromoPriceMap());

        // 置换设备信息
        TradeupDevice tradeupDevice = tradeupDeviceService.getById(tradeupEvaluationRule.getDevice());
        TradeupDeviceVO tradeupDeviceVo = new TradeupDeviceVO();
        tradeupDeviceVo.setId(tradeupDevice.getId());
        tradeupDeviceVo.setBrandId(tradeupDevice.getBrandId());
        tradeupDeviceVo.setName(tradeupDevice.getName());
        tradeupDeviceVo.setNameEn(tradeupDevice.getNameEn());

        // 以旧换新订单详情
        TradeupOrderInfoVO tradeupOrderVO = new TradeupOrderInfoVO();
        tradeupOrderVO.setId(tradeupOrder.getId());
        tradeupOrderVO.setTradeupOrderNumber(tradeupOrder.getOrderNumber());
        tradeupOrderVO.setCountry(tradeupOrder.getCountry());
        tradeupOrderVO.setTypeId(tradeupOrder.getTypeId());
        tradeupOrderVO.setCurrency(tradeupOrder.getCurrency());
        tradeupOrderVO.setTradeupDevice(tradeupDeviceVo);
        tradeupOrderVO.setTradeupEvaluationRule(tradeupEvaluationRuleVo);
        return tradeupOrderVO;
    }

    /**
     * 订单子项信息
     *
     * @param order
     * @param language
     * @return
     */
    private List<OrderItemInfoVO> listOrderItemInfos(Order order, InstaLanguage language) {
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        return orderItems.stream().map(orderItem -> {
            // 产品多语言信息
            ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(orderItem.getProduct(), language);

            // 套餐多语言信息
            CommodityInfo commodityInfo = commodityInfoService.getInfoDefaultEnglish(orderItem.getCommodity(), language);

            // 套餐详情图
            CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(orderItem.getCommodity());

            OrderItemInfoVO orderItemInfo = new OrderItemInfoVO();
            orderItemInfo.setId(orderItem.getId());
            orderItemInfo.setNumber(orderItem.getNumber());
            orderItemInfo.setIsGift(orderItem.getIsGift());
            orderItemInfo.setProductId(orderItem.getProduct());
            orderItemInfo.setCommodityId(orderItem.getCommodity());
            orderItemInfo.setProductName(productInfo.getName());
            orderItemInfo.setCommodityName(commodityInfo.getName());
            orderItemInfo.setImageUrl(commodityDisplay.getUrl());
            orderItemInfo.setAmount(orderItem.getPrice());
            orderItemInfo.setDiscountFee(orderItem.getDiscountFee());
            Currency currency = orderItem.currency();
            orderItemInfo.setCurrency(currency.name());
            orderItemInfo.setSignal(currency.getSignal());
            return orderItemInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 订单支付信息
     *
     * @param order
     * @return
     */
    private OrderPaymentInfoVO getOrderPaymentInfo(Order order) {
        // 订单支付信息
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());

        OrderPaymentInfoVO orderPaymentInfo = new OrderPaymentInfoVO();
        orderPaymentInfo.setAmount(orderPayment.getAmount());
        orderPaymentInfo.setCurrency(orderPayment.getCurrency());
        orderPaymentInfo.setTaxFee(orderPayment.getTax());
        orderPaymentInfo.setShippingFee(orderPayment.getShippingCost());
        orderPaymentInfo.setTotalAmount(orderPayment.getTotalPayPrice().getAmount());
        orderPaymentInfo.setSignal(orderPayment.currency().getSignal());
        orderPaymentInfo.setCouponFee(orderPayment.getTotalDiscountFee());

        // 非游客订单 && 云服务订阅升级订单
        if (!order.isGuestOrder() && ServiceScenesType.UPGRADE.name().equals(order.getSubscribeScenesType()) && order.getCloudSubscribeMark()) {
            this.doPackSubscribePayment(order, orderPaymentInfo);
        }

        return orderPaymentInfo;
    }

    /**
     * 封装订单云服务订阅相关支付信息
     *
     * @param order
     * @return
     */
    private void doPackSubscribePayment(Order order, OrderPaymentInfoVO orderPaymentInfo) {
        // 获取用户信息
        Integer userId = order.getUserId();
        if (Objects.isNull(userId)) {
            return;
        }

        // 获取用户当前订阅生效记录
        CloudStorageSubscribe cloudStorageSubscribe = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(userId);
        if (Objects.isNull(cloudStorageSubscribe)) {
            return;
        }

        // 卡支付信息校验（不限制PayChannelId）
        CreditCardPaymentInfo cardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(cloudStorageSubscribe.getOrderNumber());
        if (Objects.isNull(cardPaymentInfo)) {
            return;
        }

        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(userId);
        if (Objects.isNull(userPayInfo)) {
            return;
        }

        // 支付方式
        String paymentMethod = userPayInfo.getPaymentMethod();
        // 支付方式枚举
        StorePaymentMethodEnum paymentMethodEnum = userPayInfo.parsePaymentMethod();
        // Apple pay & Google pay 也属于cko支付方式
        if (StorePaymentMethodEnum.CKO_PAYMENT.equals(paymentMethodEnum) && StringUtils.isNotBlank(userPayInfo.getPaymentSubMethod())) {
            paymentMethod = userPayInfo.getPaymentSubMethod();
        }

        orderPaymentInfo.setPaymentChannelId(cardPaymentInfo.getPayChannelId());
        orderPaymentInfo.setPaymentMethod(paymentMethod);
    }
}
