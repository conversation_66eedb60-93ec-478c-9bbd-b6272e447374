package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.outgoing.rpc.store.job.CategoryPageCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.NavigationBarCachePutService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import feign.RetryableException;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @description:
 * @author: py
 * @create: 2024-12-20 16:10
 */
@Component
public class SceneryTagMainCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(SceneryTagMainCachePutHandler.class);

    @Autowired
    CategoryPageCachePutService categoryPageCachePutService;

    @Autowired
    NavigationBarCachePutService navigationBarCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws Exception {

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}。", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(null, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.SCENERY_TAG_MAIN;
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            CacheConstant.COUNTIES.forEach(cacheCounties -> {
                // 更新导航栏
                navigationBarCachePutService.listNavigationBarCategoryInfos(cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新配件类目页
                categoryPageCachePutService.listCategory(ProductCategoryMainType.CM_ACCESSORY.name(), cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新配件类目页筛选器
                categoryPageCachePutService.listCategoryFilter(ProductCategoryMainType.CM_ACCESSORY.name(), cacheCounties.getCountry(), cacheCounties.getLanguage());
            });
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.CATEGORY_PAGE, CacheableType.CATEGORY_PAGE_FILTER, CacheableType.NAVIGATION_BAR_CATEGORY_KEY);
    }

    @Override
    public CachePutKeyParameterBO cacheParamParse(JoinPoint joinPoint) {
        return null;
    }

    @Override
    public Integer getTaskNumber() {
        return super.getTaskNumber() - 2;
    }
}
