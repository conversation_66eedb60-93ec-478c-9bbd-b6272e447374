package com.insta360.store.business.user.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: py
 * @Date: 2023/6/12
 * @Description: 所有的活动邮件
 */
@Scope("prototype")
@Component
public class ActivitySubscribeEmail extends BaseUserEmail {

    @Override
    public String getTemplateName() {
        return getTemplateKey();
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        // 代金券
        templateParams.addBodyParam("gift_code", getGiftCode());

        // 用户邮箱
        templateParams.addBodyParam("email", this.getEmail());
    }

    @Override
    protected InstaLanguage getLanguage() {
        if (InstaCountry.HK.equals(getInstaCountry())) {
            return InstaLanguage.zh_TW;
        }
        return super.getLanguage();
    }
}