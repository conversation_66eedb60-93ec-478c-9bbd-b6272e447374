package com.insta360.store.service.controller.review.controller;

import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.outgoing.mq.review.helper.ReviewMessageHelper;
import com.insta360.store.business.product.exception.ProductErrorCode;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.review.dto.ReviewDTO;
import com.insta360.store.business.review.enums.ReviewLikeEnum;
import com.insta360.store.business.review.enums.ReviewStateEnum;
import com.insta360.store.business.review.exception.ReviewErrorCode;
import com.insta360.store.business.review.model.Review;
import com.insta360.store.business.review.service.ReviewService;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.review.cache.cacheable.ReviewCachePack;
import com.insta360.store.service.controller.review.format.ReviewPack;
import com.insta360.store.service.controller.review.vo.ReviewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2022/07/05
 * @Description:
 */
@RestController
public class ReviewApi extends BaseApi {

    @Autowired
    ReviewPack reviewPack;

    @Autowired
    ReviewService reviewService;

    @Autowired
    ProductService productService;

    @Autowired
    ReviewCachePack reviewCachePack;

    @Autowired
    ReviewMessageHelper reviewMessageHelper;


    /**
     * 获取产品的评论信息
     *
     * @param productId
     * @param sortKey
     * @param rate
     * @param pageNumber
     * @param pageSize
     * @return
     */
//    @AvoidRepeatableCommit(timeOut = 200)
    @GetMapping("/store/review/listReviews")
    public Response<? extends Map> listReviews(@RequestParam Integer productId,
                                               @RequestParam String sortKey,
                                               @RequestParam(required = false, defaultValue = "0") Integer rate,
                                               @RequestParam Integer pageNumber,
                                               @RequestParam Integer pageSize) {
        // 产品需要真实有效
        Product product = productService.getById(productId);
        if (product == null) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber, 1);
        pageQuery.setPageSize(pageSize, 10);

        // 分页查询 & 数据封装
        PageResult<Review> reviewPageResult = reviewService.listReviews(productId, rate, sortKey, pageQuery);
        PageResult<ReviewVO> reviewVoPageResult = reviewPageResult.replaceList(reviewPack.doPackReviewInfos(reviewPageResult.getList(), getApiCountry(), getApiLanguage()));

        return Response.ok(reviewVoPageResult);
    }

    /**
     * 获取产品的总评论数和平均分
     *
     * @param productId
     * @return
     */
    @GetMapping("/store/review/getProductReviewRateInfo")
    public Response<Object> getProductReviewRateInfo(@RequestParam Integer productId) {
        // 产品需要真实有效
        Product product = productService.getById(productId);
        if (product == null) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        // 获取产品的总评论数和平均分
        return Response.ok(reviewCachePack.doPackCacheReviewRateInfo(productId));
    }

    /**
     * 获取某个产品的所有评论星级的评论数量
     *
     * @param productId
     * @return
     */
    @GetMapping("/store/review/getProductReviewRateLevelInfo")
    public Response<Object> getProductReviewRateLevelInfo(@RequestParam Integer productId) {
        // 产品需要真实有效
        Product product = productService.getById(productId);
        if (product == null) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        // 获取某个产品的所有评论星级的数量
        return Response.ok(reviewCachePack.doPackCacheReviewRateLevelInfo(productId));
    }

    /**
     * 点赞
     *
     * @param reviewParam
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping(path = "/store/review/like", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> like(@RequestBody ReviewDTO reviewParam) {
        if (reviewParam.getReviewNumber() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        // 点赞
        Review review = reviewService.getByReviewNumber(reviewParam.getReviewNumber());
        if (review == null) {
            throw new InstaException(ReviewErrorCode.ReviewNotFoundException);
        }

        // 已发布才允许点赞
        if (!ReviewStateEnum.released.equals(review.reviewState())) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }
        // 评论点赞事件通知
        reviewMessageHelper.sendReviewLikeMessage(review, ReviewLikeEnum.LIKE);
        return Response.ok();
    }

    /**
     * 取消点赞
     *
     * @param reviewParam
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping(path = "/store/review/dislike", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> dislike(@RequestBody ReviewDTO reviewParam) {
        // 新版取消点赞
        if (StringUtil.isBlank(reviewParam.getReviewNumber())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        Review review = reviewService.getByReviewNumber(reviewParam.getReviewNumber());
        if (review == null) {
            throw new InstaException(ReviewErrorCode.ReviewNotFoundException);
        }

        // 已发布才允许取消点赞
        if (!ReviewStateEnum.released.equals(review.reviewState())) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }

        // 评论取消点赞事件通知
        reviewMessageHelper.sendReviewLikeMessage(review, ReviewLikeEnum.DISLIKE);
        return Response.ok();
    }
}
