package com.insta360.store.business.admin.upload.service.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.admin.upload.enums.UploadBusinessType;
import com.insta360.store.business.admin.upload.service.StoreAdminUploadService;
import com.insta360.store.business.admin.upload.service.handler.*;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 平台Excel上传服务工厂
 * @Date 2023/10/11
 */
@Component
public class AdminUploadServiceFactory {

    /**
     * 获取上传服务
     *
     * @param type
     * @return
     */
    public StoreAdminUploadService getService(Integer type) {
        UploadBusinessType businessType = UploadBusinessType.matchType(type);
        if (Objects.isNull(businessType)) {
            throw new InstaException(-1, "非法的上传数据业务类型");
        }

        // 平台文件上传服务
        StoreAdminUploadService adminUploadService = null;

        switch (businessType) {
            case product_multi_language:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(ProductMultiLanguageExcelHandler.class);
                break;
            case product_base_info:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(ProductInfoExcelHandler.class);
                break;
            case product_pack_list:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(ProductPackageRelatedHandler.class);
                break;
            case commodity_multi_language:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(CommodityMultiLanguageExcelHandler.class);
                break;
            case commodity_base_info:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(CommodityInfoExcelHandler.class);
                break;
            case current_price:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(CommodityCurrentPriceExcelHandler.class);
                break;
            case original_price:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(CommodityOriginalPriceExcelHandler.class);
                break;
            case new_product_original_price:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(CommodityPriceActivityExcelHandler.class);
                break;
            case logistics_quotation:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(LogisticsQuotationFileHandler.class);
                break;
            case new_product_materials:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(NewProductMaterialsExcelHandler.class);
                break;
            case care_card_bind:
                adminUploadService = ApplicationContextHolder.getApplicationContext().getBean(InsuranceCareCardBindHandler.class);
                break;
            default:
                throw new InstaException(-1, "非法的上传数据业务类型");
        }

        return adminUploadService;
    }
}
