package com.insta360.store.business.trade.email;

import com.insta360.store.business.meta.bo.EmailTemplateParams;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 用户架构忘记下单提醒邮件
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Scope("prototype")
@Component
public class TradeUserCartAbandonA3TWOEmail extends BaseTradeEmail {

    /**
     * 邮件模版名称
     */
    protected String templateName;

    @Override
    public String getTemplateName() {
        return getUserCartEmailEnum().getTemplateKey();
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {

        // 购物车信息币种
        templateParams.addBodyParam("signal", getCartSignal());

        // 是否标志前缀
        templateParams.addBodyParam("isSuffix", getSuffix());

        // x4价格
        templateParams.addBodyParam("x4_price", getPrice(Product.X4_ID));

        // x5价格
        templateParams.addBodyParam("x5_price", getPrice(Product.X5_ID));

        // 购物车链接
        templateParams.addBodyParam("cartLink", getCartLink(UserCartEmailEnum.A3_SECOND_EMAIL.getLinkName()));

        // x5产品页
        templateParams.addBodyParam("productLink", getProductLink());
    }


}
