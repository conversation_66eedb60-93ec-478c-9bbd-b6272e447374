package com.insta360.store.admin.controller.prime.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.business.prime.bo.PrimeCommodityDomainBO;
import com.insta360.store.business.prime.dto.PrimeCommodityDTO;
import com.insta360.store.business.prime.enums.PrimeCommodityType;
import com.insta360.store.business.prime.enums.PrimeGraphqlOperation;
import com.insta360.store.business.prime.error.PrimeInstaErrorCode;
import com.insta360.store.business.prime.lib.handler.PrimeRequestHandler;
import com.insta360.store.business.prime.lib.response.BasePrimeResponse;
import com.insta360.store.business.prime.lib.response.ProductResponse;
import com.insta360.store.business.prime.lib.variables.ProductVariables;
import com.insta360.store.business.prime.service.PrimeCommodityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * admin/prime
 * prime套餐信息配置
 *
 * @author: glowxq
 * @date: 2025-06-11
 */
@RestController
// @PermissionResource(code = "commodity", desc = "套餐基本信息配置")
public class AdPrimeCommodityApi extends BaseAdminApi {

    @Autowired
    PrimeRequestHandler primeRequestHandler;

    @Autowired
    PrimeCommodityService primeCommodityService;

    /**
     * 查询prime产品套餐信息
     *
     * @param commodityId
     */
    @LogAttr(desc = "查询prime产品套餐信息")
    @Permission(code = "store.commodity.commodity.getPrimeCommodity", desc = "查询prime产品套餐信息")
    @GetMapping(path = "/admin/commodity/getPrimeCommodity")
    public Response<Object> getPrimeCommodity(@RequestParam Long commodityId) {
        PrimeCommodityDomainBO primeCommodityDomainBO = primeCommodityService.getPrimeDetail(commodityId);
        return Response.ok(primeCommodityDomainBO);
    }

    /**
     * 创建prime产品套餐信息
     *
     * @param commodityParam
     */
    @LogAttr(desc = "创建prime产品套餐信息")
    @Permission(code = "store.commodity.commodity.createPrimeCommodity", desc = "创建prime产品套餐信息")
    @PostMapping(path = "/admin/commodity/createPrimeCommodity", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> createPrimeCommodity(@RequestBody PrimeCommodityDTO commodityParam) {
        if (CollectionUtils.isEmpty(commodityParam.getIncludeCommodities()) && PrimeCommodityType.Bundle.equals(commodityParam.getPrimeCommodityType())) {
            throw new InstaException(PrimeInstaErrorCode.PRIME_COMMODITY_PARAMS_FAILED, "组合商品必须包含子商品");
        }
        primeCommodityService.createPrimeCommodity(commodityParam);
        return Response.ok();
    }

    /**
     * 更新prime产品套餐信息
     *
     * @param commodityParam
     */
    @LogAttr(desc = "更新prime产品套餐信息")
    @Permission(code = "store.commodity.commodity.updatePrimeCommodity", desc = "更新prime产品套餐信息")
    @PostMapping(path = "/admin/commodity/updatePrimeCommodity", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updatePrimeCommodity(@RequestBody PrimeCommodityDTO commodityParam) {
        primeCommodityService.updatePrimeCommodity(commodityParam);
        return Response.ok();
    }

    /**
     * 删除prime产品套餐信息
     *
     * @param commodityParam
     */
    @LogAttr(desc = "删除prime产品套餐信息")
    @Permission(code = "store.commodity.commodity.deletePrimeCommodity", desc = "创建prime产品套餐信息")
    @PostMapping(path = "/admin/commodity/deletePrimeCommodity", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> deletePrimeCommodity(@RequestBody PrimeCommodityDTO commodityParam) {
        primeCommodityService.deletePrimeCommodity(commodityParam);
        return Response.ok();
    }

    /**
     * 向prime查询产品套餐信息
     *
     * @param commodityParam
     */
    @LogAttr(desc = "向prime查询产品套餐信息")
    @Permission(code = "store.commodity.commodity.getPrimeProduct", desc = "向prime查询产品套餐信息")
    @PostMapping(path = "/admin/commodity/getPrimeProduct", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> getPrimeProduct(@RequestBody PrimeCommodityDTO commodityParam) {
        String externalId = commodityParam.externalId();
        ProductVariables productVariables = new ProductVariables();
        productVariables.setIdentifier(new ProductVariables.Identifier(externalId));
        BasePrimeResponse basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.Product, productVariables);
        ProductResponse productResponse = basePrimeResponse.parsePrimeResponse(ProductResponse.class);
        return Response.ok(productResponse);
    }

}
