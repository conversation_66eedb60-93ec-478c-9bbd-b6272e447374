package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.commodity.service.impl.helper.CommodityStockHelper;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.job.controller.cache.JobCommodityStockCachePack;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 套餐库存扣减消费类
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderCommodityStockDeductionMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCommodityStockDeductionMqConsumer.class);

    @Autowired
    CommodityStockHelper commodityStockHelper;

    @Autowired
    JobCommodityStockCachePack jobCommodityStockCachePack;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_commodity_stock_deduction_notify)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.error("库存扣减失败。订单不存在。订单id:{}", messageBody);
                return Action.CommitMessage;
            }

            LOGGER.info("[库存扣减]套餐库存，扣减开始。order_number:{}", order.getOrderNumber());

            List<Integer> needUpdateCacheCommodityIds = commodityStockHelper.stockDeduction(order);

            LOGGER.info("[库存扣减]套餐库存，扣减结束。order_number:{}", order.getOrderNumber());

            if (CollectionUtils.isEmpty(needUpdateCacheCommodityIds)) {
                return Action.CommitMessage;
            }
            // 更新套餐库存缓存
            CachePutKeyParameterBO cachePutKeyParameterBo = new CachePutKeyParameterBO();
            cachePutKeyParameterBo.setCommodityIds(needUpdateCacheCommodityIds);
            jobCommodityStockCachePack.handleStock(cachePutKeyParameterBo);

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[库存扣减]库存扣减,消费异常,消息体[%s],原因[%s]", messageBody, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[库存扣减]库存扣减失败,消息体[%s],原因[%s]", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return Action.CommitMessage;
        }
    }
}
