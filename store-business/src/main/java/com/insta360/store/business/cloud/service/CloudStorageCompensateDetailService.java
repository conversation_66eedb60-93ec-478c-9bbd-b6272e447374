package com.insta360.store.business.cloud.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.model.CloudStorageCompensateDetail;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitDetail;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-06-03
 * @Description:
 */
public interface CloudStorageCompensateDetailService extends BaseService<CloudStorageCompensateDetail> {

    /**
     * 根据序列号查询补偿详情
     *
     * @param serialNumber
     * @param benefitType
     * @return
     */
    CloudStorageCompensateDetail getDetailBySerialNumber(Integer userId, String serialNumber, BenefitType benefitType);

    /**
     * 根据类型查询补偿详情
     *
     * @param userId
     * @param benefitType
     * @return
     */
    CloudStorageCompensateDetail getDetailByType(Integer userId, BenefitType benefitType);

    /**
     * 绑定
     *
     * @param compensateDetail
     * @param storeBenefitDetail
     */
    void bind(CloudStorageCompensateDetail compensateDetail, CloudStorageStoreBenefitDetail storeBenefitDetail);

    /**
     * 根据用户id查询该用户所有已绑定的代金券code
     *
     * @param userId
     * @return
     */
    List<String> listBindCodeByUserId(Integer userId);
}
