package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CacheCountiesBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.meta.enums.OrderEndpoint;
import com.insta360.store.business.outgoing.rpc.store.job.GraphicNavigationCachePutService;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: wkx
 * @Date: 2023/11/15
 * @Description:
 */
@Component
public class GraphicNavigationCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GraphicNavigationCachePutHandler.class);

    @Autowired
    GraphicNavigationCachePutService graphicNavigationCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        Boolean isMobile = cachePutKeyParameter.getIsMobile();
        if (isMobile == null) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。分端信息为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}", this.getCachePutType(), this.isAsyncTaskable()));
        OrderEndpoint endpoint = isMobile ? OrderEndpoint.mobile : OrderEndpoint.pc;
        CountDownLatch countDownLatch = this.getCountDownLatch();
        CacheConstant.PARTITION_COUNTIES.forEach(cacheCounties -> {
            Map<String, Object> paramMap = new HashMap<>(2);
            paramMap.put("cacheCounties", cacheCounties);
            paramMap.put("endpoint", endpoint.getCode());
            cachePutThreadPool.execute(() -> this.task1(paramMap, countDownLatch));
        });
        countDownLatch.await();

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.GRAPHIC_NAVIGATION;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.GRAPHIC_NAVIGATION);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            Map<String, Object> paramMap = (Map<String, Object>) param;
            for (CacheCountiesBO cacheCounties : (List<CacheCountiesBO>) paramMap.get("cacheCounties")) {
                graphicNavigationCachePutService.listGraphicNavigation(cacheCounties.getCountry(), cacheCounties.getLanguage(),
                        (Integer) paramMap.get("endpoint"));
            }
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }

    @Override
    public Integer getTaskNumber() {
        return super.getTaskNumber() - 2;
    }
}
