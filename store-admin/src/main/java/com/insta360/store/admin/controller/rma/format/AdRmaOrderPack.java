package com.insta360.store.admin.controller.rma.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.admin.controller.product.format.AdProductCategoryPack;
import com.insta360.store.admin.controller.rma.vo.AdReturnOrderDetailVO;
import com.insta360.store.admin.controller.rma.vo.AdReturnOrderVO;
import com.insta360.store.admin.controller.rma.vo.AdRmaItemVO;
import com.insta360.store.admin.controller.rma.vo.AdRmaOrderVO;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.discount.enums.DiscountSource;
import com.insta360.store.business.order.enums.OrderItemType;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.*;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.rma.constant.RmaConstant;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.model.RmaDelivery;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.model.RmaOrderDetail;
import com.insta360.store.business.rma.service.RmaDeliveryService;
import com.insta360.store.business.rma.service.impl.helper.RmaCoreAmountCalculationHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: hyc
 * @Date: 2019-11-19
 * @Description:
 */
@Component
public class AdRmaOrderPack {
    @Autowired
    RmaDeliveryService rmaDeliveryService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    ProductService productService;

    @Autowired
    RmaCoreAmountCalculationHelper rmaCoreAmountCalculationHelper;

    @Autowired
    AdProductCategoryPack adProductCategoryPack;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderItemCustomsTaxRateService orderItemCustomsTaxRateService;

    /**
     * 售后单VO封装
     *
     * @param rmaOrder
     * @param language
     * @return
     */
    public AdRmaOrderVO doPack(RmaOrder rmaOrder, InstaLanguage language) {
        AdRmaOrderVO rmaOrderVO = new AdRmaOrderVO(rmaOrder);
        RmaDelivery delivery = rmaDeliveryService.getByRmaOrder(rmaOrder.getId());
        rmaOrderVO.setDelivery(delivery);

        Optional.ofNullable(orderItemService.getById(rmaOrder.getOrderItemId()))
                .ifPresent(item -> {
                    AdRmaItemVO adRmaItemVo = new AdRmaItemVO(item);
                    // 商品总关税
                    BigDecimal totalCustomsTax = Optional.ofNullable(orderItemCustomsTaxRateService.getCustomsTaxByOrderItemId(item.getId())).map(orderItemCustomsTaxRate -> new BigDecimal(String.valueOf(orderItemCustomsTaxRate.getTotalTax()))).orElse(BigDecimal.ZERO);
                    adRmaItemVo.setTotalCustomsTax(totalCustomsTax);
                    rmaOrderVO.setOrderItem(adRmaItemVo);
                    Optional.ofNullable(productInfoService.getInfo(item.getProduct(), language))
                            .ifPresent(productInfo -> rmaOrderVO.setProductName(productInfo.getName()));

                    Optional.ofNullable(productService.getById(item.getProduct()))
                            .ifPresent(product -> {
                                rmaOrderVO.setProductType(product.getType());
                                rmaOrderVO.setProductCategory(adProductCategoryPack.getProductCategory(product.getCategoryKey()));
                                if (product.isCloudSubscribeItem()) {
                                    rmaOrderVO.setItemType(OrderItemType.cloud_subscription.getCode());
                                }
                            });

                    Optional.ofNullable(commodityInfoService.getInfo(item.getCommodity(), language))
                            .ifPresent(commodityInfo -> rmaOrderVO.setCommodityName(commodityInfo.getName()));

                    Optional.ofNullable(commodityDisplayService.getFirstDisplay(item.getCommodity()))
                            .ifPresent(commodityDisplay -> rmaOrderVO.setCommodityDisplay(commodityDisplay.getUrl()));

                    if (DiscountSource.CLOUD_SUBSCRIBE.name().equals(item.getDiscountSource())) {
                        rmaOrderVO.setItemType(OrderItemType.special_discount.getCode());
                    }
                });

        Optional.ofNullable(orderService.getById(rmaOrder.getOrderId()))
                .ifPresent(order -> {
                    rmaOrderVO.setOrderNumber(order.getOrderNumber());
                    rmaOrderVO.setOrderState(order.getState());
                    rmaOrderVO.setOrderArea(order.getArea());
                    rmaOrderVO.setPushPlatform(order.getPushPlatform());
                    Optional.ofNullable(orderPaymentService.getByOrder(order.getId()))
                            .ifPresent(orderPayment -> rmaOrderVO.setOrderTotalPrice(orderPayment.getTotalPayPrice()));
                });

        // ShippingLabel开具按钮
        rmaOrderVO.setSlButton(this.shippingLabelButton(rmaOrder, delivery));

        return rmaOrderVO;
    }

    /**
     * 开具运输标签按钮展示逻辑处理
     *
     * @param rmaOrder    代表返修订单的对象，包含返修单的各种状态和类型信息。
     * @param rmaDelivery 代表返修订单的配送信息，包括退货的物流信息。
     * @return 返回一个布尔值，如果满足所有条件，则返回true，否则返回false。
     */
    private Boolean shippingLabelButton(RmaOrder rmaOrder, RmaDelivery rmaDelivery) {
        // 检查售后类型是否为'退货退款'、'换货'
        if (!RmaType.isNeedLogisticsType(rmaOrder.rmaType())) {
            return false;
        }
        // 检查是否需要退货
        if (Objects.isNull(rmaOrder.getNeedReturn()) || !rmaOrder.getNeedReturn()) {
            return false;
        }
        // 检查RMA的状态是否为'待客户寄回'
        if (!RmaState.pending_for_customer_send.equals(rmaOrder.rmaState())) {
            return false;
        }

        // 检查是否已经存在寄出的物流信息
        if (Objects.nonNull(rmaDelivery) && (StringUtils.isNotBlank(rmaDelivery.getExpressFromCompany()) || StringUtils.isNotBlank(rmaDelivery.getExpressFromNumber()))) {
            return false;
        }

        // 获取订单配送信息，并根据国家和省份判断是否可以生成运输标签
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(rmaOrder.getOrderId());
        InstaCountry country = orderDelivery.country();
        String provinceCode = orderDelivery.getProvinceCode();
//        // 是否为美国且州代码为关岛、波多黎各地区
//        if (InstaCountry.US.equals(country) && (InstaCountry.GU.name().equals(provinceCode) || InstaCountry.PR.name().equals(provinceCode))) {
//            return false;
//        }
//        // 非美国地区且不属于欧盟的国家处理
//        if (!InstaCountry.US.equals(country) && !RmaConstant.EU_CODES.contains(country.name())) {
//            return false;
//        }
        if (!InstaCountry.US.equals(country) && !RmaConstant.EU_CODES.contains(country.name())) {
            return false;
        } else {
            if (InstaCountry.GU.name().equals(provinceCode) || InstaCountry.PR.name().equals(provinceCode)) {
                return false;
            }
        }

        return true;
    }


    /**
     * 构建售后单VO
     *
     * @param rmaOrder
     * @param rmaOrderDetail
     * @return
     */
    public AdReturnOrderVO doPack(RmaOrder rmaOrder, RmaOrderDetail rmaOrderDetail) {
        // 构建售后单
        AdReturnOrderVO adReturnOrder = new AdReturnOrderVO(rmaOrder);
        // 售后商品
        OrderItem orderItem = orderItemService.getById(rmaOrder.getOrderItemId());
        // 对应产品
        Product product = productService.getById(orderItem.getProduct());

        // 设置售后商品类型
        if (product.isCloudSubscribeItem()) {
            adReturnOrder.setItemType(OrderItemType.cloud_subscription.getCode());
        } else if (DiscountSource.CLOUD_SUBSCRIBE.name().equals(orderItem.getDiscountSource())) {
            adReturnOrder.setItemType(OrderItemType.special_discount.getCode());
        }

        // 对应订单
        Order order = orderService.getById(rmaOrder.getOrderId());
        if (Objects.nonNull(order)) {
            adReturnOrder.setArea(order.getArea());
        }

        // 构建售后单明细
        AdReturnOrderDetailVO returnOrderDetail;
        if (Objects.isNull(rmaOrderDetail)) {
            returnOrderDetail = this.buildDefaultReturnOrderDetail(rmaOrder);
        } else {
            returnOrderDetail = this.returnOrderDetailMoneyReset(rmaOrderDetail);
        }

        adReturnOrder.setReturnOrderDetail(returnOrderDetail);
        return adReturnOrder;
    }

    /**
     * 售后订单明细金额重置
     *
     * @param rmaOrderDetail
     */
    private AdReturnOrderDetailVO returnOrderDetailMoneyReset(RmaOrderDetail rmaOrderDetail) {
        // 获取售后商品信息
        OrderItem orderItem = orderItemService.getById(rmaOrderDetail.getOrderItemId());
        // 商品总优惠金额
        BigDecimal itemTotalDiscountAmount = rmaCoreAmountCalculationHelper.getItemTotalDiscountAmount(orderItem);
        // 商品总金额
        BigDecimal itemTotalAmount = orderItem.getItemTotalAmount();
        // 商品最大实退金额
        BigDecimal itemMaxActualRefundAmount = itemTotalAmount.subtract(itemTotalDiscountAmount);
        // 商品总税费
        BigDecimal totalTax = orderItem.getTotalTax();
        // 商品最大可退关税
        BigDecimal refundableMaxCustomsTax = Optional.ofNullable(orderItemCustomsTaxRateService.getCustomsTaxByOrderItemId(orderItem.getId())).map(orderItemCustomsTaxRate -> new BigDecimal(String.valueOf(orderItemCustomsTaxRate.getTotalTax()))).orElse(BigDecimal.ZERO);

        AdReturnOrderDetailVO returnOrderDetailVo = new AdReturnOrderDetailVO(rmaOrderDetail);
        returnOrderDetailVo.setReturnMaxNum(orderItem.getNumber());
        returnOrderDetailVo.setRefundableMaxTax(totalTax);
        returnOrderDetailVo.setItemMaxActualRefundAmount(itemMaxActualRefundAmount);
        returnOrderDetailVo.setItemTotalDiscountAmount(itemTotalDiscountAmount);
        returnOrderDetailVo.setItemTotalAmount(itemMaxActualRefundAmount);
        returnOrderDetailVo.setRefundableMaxCustomsTax(refundableMaxCustomsTax);

        return returnOrderDetailVo;
    }

    /**
     * 构建默认售后单明细
     *
     * @param rmaOrder
     * @return
     */
    private AdReturnOrderDetailVO buildDefaultReturnOrderDetail(RmaOrder rmaOrder) {
        // 获取售后商品
        OrderItem orderItem = orderItemService.getById(rmaOrder.getOrderItemId());
        // 售后数量
        Integer returnNum = rmaOrder.getQuantity();

        // 商品总金额
        BigDecimal itemTotalAmount = orderItem.getItemTotalAmount();
        // 商品总优惠金额
        BigDecimal itemTotalDiscountAmount = rmaCoreAmountCalculationHelper.getItemTotalDiscountAmount(orderItem);
        // 商品应退金额
        BigDecimal itemRefundableAmount = new BigDecimal(String.valueOf(orderItem.getPrice())).multiply(new BigDecimal(String.valueOf(returnNum)));
        // 售后单退款金额
        BigDecimal refundAmount = new BigDecimal(String.valueOf(rmaOrder.getRefundAmount()));
        // 商品最大实退金额
        BigDecimal itemMaxActualRefundAmount = itemTotalAmount.subtract(itemTotalDiscountAmount);
        // 售后单优惠扣减金额
        BigDecimal itemDiscountAmount = new BigDecimal(String.valueOf(orderItem.getDiscountFee())).multiply(new BigDecimal(String.valueOf(returnNum)));

        AdReturnOrderDetailVO adReturnOrderDetail = new AdReturnOrderDetailVO();
        adReturnOrderDetail.setRmaOrderId(rmaOrder.getId());
        adReturnOrderDetail.setOrderItemId(rmaOrder.getOrderItemId());
        adReturnOrderDetail.setReturnTaxAmount(BigDecimal.ZERO);
        adReturnOrderDetail.setReturnFreight(BigDecimal.ZERO);
        adReturnOrderDetail.setDiscountAmount(itemDiscountAmount);
        adReturnOrderDetail.setItemRefundableAmount(itemRefundableAmount);
        adReturnOrderDetail.setItemActualRefundAmount(refundAmount);
        adReturnOrderDetail.setRefundAmount(refundAmount);
        adReturnOrderDetail.setReturnNum(returnNum);
        adReturnOrderDetail.setReturnMaxNum(orderItem.getNumber());
        adReturnOrderDetail.setItemMaxActualRefundAmount(itemMaxActualRefundAmount);
        adReturnOrderDetail.setRefundableMaxTax(orderItem.getTotalTax());
        adReturnOrderDetail.setItemTotalAmount(itemMaxActualRefundAmount);
        adReturnOrderDetail.setItemTotalDiscountAmount(itemTotalDiscountAmount);

        BigDecimal refundableMaxCustomsTax = Optional.ofNullable(orderItemCustomsTaxRateService.getCustomsTaxByOrderItemId(orderItem.getId())).map(orderItemCustomsTaxRate -> new BigDecimal(String.valueOf(orderItemCustomsTaxRate.getTotalTax()))).orElse(BigDecimal.ZERO);
        adReturnOrderDetail.setRefundCustomsTaxAmount(BigDecimal.ZERO);
        adReturnOrderDetail.setRefundableMaxCustomsTax(refundableMaxCustomsTax);

        return adReturnOrderDetail;
    }
}
