package com.insta360.store.admin.controller.commodity.controller;

import cn.hutool.core.lang.ObjectId;
import com.google.common.collect.Lists;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.commodity.controller.cache.AdCommodityDeliveryTimeCachePack;
import com.insta360.store.admin.controller.commodity.controller.cache.AdCommodityInfoCachePack;
import com.insta360.store.admin.controller.commodity.controller.cache.AdCommodityTradeRuleCachePack;
import com.insta360.store.admin.controller.commodity.format.AdCommodityPack;
import com.insta360.store.admin.controller.commodity.vo.AdCommodityBaseVo;
import com.insta360.store.business.admin.commodity.dto.RepairCommoditiesDTO;
import com.insta360.store.business.admin.upload.bo.UploadExcelBO;
import com.insta360.store.business.admin.upload.enums.UploadBusinessType;
import com.insta360.store.business.admin.upload.enums.UploadTemplateType;
import com.insta360.store.business.admin.upload.service.helper.AdminUploadHelper;
import com.insta360.store.business.cloud.dto.CloudSkuCreateDTO;
import com.insta360.store.business.commodity.dto.CommodityDTO;
import com.insta360.store.business.commodity.dto.CommodityDeliveryTimeConfigDTO;
import com.insta360.store.business.commodity.dto.CommodityTradeRuleDTO;
import com.insta360.store.business.commodity.enums.CommodityStockLimitType;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityDeliveryTimeConfig;
import com.insta360.store.business.commodity.model.CommodityTradeRule;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.ProductCommodityStockService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityHelper;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import com.insta360.store.business.configuration.search.enums.BatchActionType;
import com.insta360.store.business.prime.lib.handler.PrimeRequestHandler;
import com.insta360.store.business.prime.service.PrimeCommodityService;
import com.insta360.store.business.product.exception.ProductErrorCode;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * admin/commodity
 *
 * @Author: hyc
 * @Date: 2019-05-17
 * @Description: 套餐基本信息
 */
@RestController
@PermissionResource(code = "commodity", desc = "套餐基本信息配置")
public class AdCommodityApi extends BaseAdminApi {

    @Autowired
    ProductService productService;

    @Autowired
    AdCommodityPack adCommodityPack;

    @Autowired
    CommodityService commodityService;

    @Autowired
    AdCommodityInfoCachePack adCommodityInfoCachePack;

    @Autowired
    AdCommodityTradeRuleCachePack adCommodityTradeRuleCachePack;

    @Autowired
    AdCommodityDeliveryTimeCachePack adCommodityDeliveryTimeCachePack;

    @Autowired
    CommodityHelper commodityHelper;

    @Autowired
    PrimeCommodityService primeCommodityService;

    @Autowired
    AdminUploadHelper adminUploadHelper;

    @Autowired
    ProductCommodityStockService productCommodityStockService;

    @Autowired
    PrimeRequestHandler primeRequestHandler;

    /**
     * 套餐赠品
     */
    @LogAttr(desc = "套餐赠品", logType = LogType.query)
    @Permission(code = "store.commodity.commodity.listGifts", desc = "获取套餐赠品标记项")
    @GetMapping("/admin/commodity/listGifts")
    public Response<? extends Map> listGifts() {
        List<Commodity> commodities = commodityService.getCanBeGiftCommodity();
        if (CollectionUtils.isEmpty(commodities)) {
            return Response.ok();
        }

        AdCommodityPack.PackSetting setting = new AdCommodityPack.PackSetting(this);
        setting.setWithProductName(true);
        setting.setWithProductInfoName(true);
        setting.setWithInfo(true);

        List<Commodity> commodityList = commodities.stream().filter(commodity -> {
            Product product = productService.getById(commodity.getProduct());
            return commodity.getEnabled() && product.getEnabled();
        }).collect(Collectors.toList());

        return Response.ok("commodities", adCommodityPack.adminPack(commodityList, setting));
    }

    /**
     * 获取某个产品的所有套餐信息
     *
     * @param productId
     */
    @LogAttr(desc = "获取某个产品的所有套餐信息", logType = LogType.query)
    @Permission(code = "store.commodity.commodity.listCommodities", desc = "获取某个产品的所有套餐信息")
    @GetMapping("/admin/commodity/listCommodities")
    public Response<? extends Map> listCommodities(@RequestParam(required = false, value = "product_id") Integer productId) {
        Product product = productService.getById(productId);
        if (product == null) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        AdCommodityPack.PackSetting setting = new AdCommodityPack.PackSetting(this);
        setting.setWithTotalSubscribeQuantity(true);
        setting.setWithProductName(true);
        setting.setWithTradeRule(true);
        setting.setWithStock(true);
        setting.setWithProductInfoName(true);
        setting.setWithCommodityInfoName(true);
        setting.setWithPrime(true);

        List<Commodity> commodities = commodityService.getCommodities(productId);
        return Response.ok("commodities", adCommodityPack.adminPack(commodities, setting));
    }

    /**
     * 获取套餐的详细信息
     *
     * @param commodityId
     */
    @LogAttr(desc = "获取套餐的详细信息", logType = LogType.query)
    @Permission(code = "store.commodity.commodity.getDetail", desc = "获取套餐的详细信息")
    @GetMapping("/admin/commodity/getDetail")
    public Response<? extends Map> getDetail(@RequestParam(required = false, value = "commodity_id") Integer commodityId) {
        Commodity commodity = commodityService.getById(commodityId);
        if (commodity == null) {
            throw new InstaException(CommodityErrorCode.CommodityNotFoundException);
        }

        AdCommodityPack.PackSetting setting = new AdCommodityPack.PackSetting(this);
        setting.setWithStock(true);
        setting.setWithTradeRule(true);
        setting.setWithProductName(true);
        setting.setWithDeliveryTimeConfig(true);
        setting.setWithCloudSubscribeSku(true);

        return Response.ok("commodity", adCommodityPack.doPack(commodity, setting));
    }

    /**
     * upsert 套餐信息
     *
     * @param commodityParam
     * @deprecated todo:报关信息-套餐报关信息-子产品关联 还在用
     */
    @LogAttr(desc = "upsert 套餐信息")
    @Permission(code = "store.commodity.commodity.upsertCommodity", desc = "新增或更新套餐信息")
    @PostMapping(path = "/admin/commodity/upsertCommodity", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> upsertCommodity(@RequestBody CommodityDTO commodityParam) {
        Commodity commodityData = commodityParam.getPojoObject();

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityData.getId()));

        // upsert commodity
        adCommodityInfoCachePack.upsertCommodity(cachePutKeyParameter, commodityData);
        return Response.ok("commodity", commodityData);
    }

    /**
     * 新增套餐信息
     *
     * @param commodityParam
     */
    @LogAttr(desc = "新增套餐信息")
    @Permission(code = "store.commodity.commodity.insertCommodity", desc = "新增套餐信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY, actionType = BatchActionType.ADD)
    @PostMapping(path = "/admin/commodity/insertCommodity", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> insertCommodity(@RequestBody CommodityDTO commodityParam) {
        Commodity commodityData = commodityParam.getPojoObject();
        // 判断是否云服务套餐
        CloudSkuCreateDTO cloudSkuCreateDto = null;
        if (Objects.nonNull(commodityParam.getCloudService()) && commodityParam.getCloudService() && Objects.nonNull(commodityParam.getCloudSku())) {
            cloudSkuCreateDto = commodityParam.getCloudSku();
        }
        commodityHelper.insertCommodity(commodityData, cloudSkuCreateDto);

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityData.getId()));
        SearchDataChangeContext.set(searchDataChangeParams);

        return Response.ok();
    }

    /**
     * 更新套餐信息
     *
     * @param commodityParam
     */
    @LogAttr(desc = "更新套餐信息")
    @Permission(code = "store.commodity.commodity.updateCommodity", desc = "更新套餐信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/updateCommodity", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateCommodity(@RequestBody CommodityDTO commodityParam) {
        Commodity commodityData = commodityParam.getPojoObject();

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityData.getId()));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityData.getId()));
        SearchDataChangeContext.set(searchDataChangeParams);

        adCommodityInfoCachePack.updateCommodity(cachePutKeyParameter, commodityData);

        return Response.ok();
    }

    /**
     * 拖拽排序套餐信息
     *
     * @param commodityParamList
     */
    @LogAttr(desc = "拖拽排序套餐信息")
    @Permission(code = "store.commodity.commodity.orderCommodity", desc = "拖拽排序套餐信息")
    @PostMapping(path = "/admin/commodity/orderCommodity", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> orderCommodity(@RequestBody List<CommodityDTO> commodityParamList) {
        List<Commodity> commodities = commodityParamList.stream().map(CommodityDTO::getPojoObject).collect(Collectors.toList());

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(commodities.stream().map(Commodity::getId).collect(Collectors.toList()));

        adCommodityInfoCachePack.orderCommodity(cachePutKeyParameter, commodities);
        return Response.ok();
    }

    /**
     * upsert 交易规则
     *
     * @param commodityTradeRuleParam
     */
    @LogAttr(desc = "upsert 交易规则")
    @Permission(code = "store.commodity.commodity.upsertTradeRule", desc = "新增或修改套餐交易规则")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/upsertTradeRule", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> upsertTradeRule(@Validated @RequestBody CommodityTradeRuleDTO commodityTradeRuleParam) {
        CommodityStockLimitType stockLimitType = CommodityStockLimitType.matchType(commodityTradeRuleParam.getStockLimitType());
        if (Objects.isNull(stockLimitType)) {
            throw new InstaException(-1, "非法的库存类型");
        }

        // '无限库存'类型下设置库存数量
        if (CommodityStockLimitType.unlimited_stock.equals(stockLimitType)) {
            productCommodityStockService.updateUnlimitedStock(commodityTradeRuleParam.getCommodityId());
        }

        CommodityTradeRule commodityTradeRuleData = commodityTradeRuleParam.getPojoObjects();

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityTradeRuleData.getCommodityId()));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityTradeRuleData.getCommodityId()));
        SearchDataChangeContext.set(searchDataChangeParams);

        adCommodityTradeRuleCachePack.doUpsertTradeRule(cachePutKeyParameter, commodityTradeRuleData);
        return Response.ok("trade_rule", commodityTradeRuleData);
    }

    /**
     * 套餐发货时间和优先级配置
     *
     * @param deliveryTimeConfigParam
     */
    @LogAttr(desc = "套餐发货时间和优先级配置")
    @Permission(code = "store.commodity.commodity.upsertDeliveryTimeConfig", desc = "新增或修改套餐发货时间")
    @PostMapping(path = "/admin/commodity/upsertDeliveryTime", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> upsertDeliveryTimeConfig(@RequestBody CommodityDeliveryTimeConfigDTO deliveryTimeConfigParam) {
        CommodityDeliveryTimeConfig deliveryTimeConfig = deliveryTimeConfigParam.getPojoObject();

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(deliveryTimeConfig.getCommodity()));

        adCommodityDeliveryTimeCachePack.upsertDeliveryTimeConfig(cachePutKeyParameter, deliveryTimeConfig);
        return Response.ok("delivery_time_config", deliveryTimeConfig);
    }

    /**
     * 获取产品对应的已启用套餐
     *
     * @param productId
     */
    @LogAttr(desc = "获取产品对应的已启用套餐", logType = LogType.query)
    @Permission(code = "store.commodity.commodity.listEnabledCommodities", desc = "获取产品对应的已启用套餐")
    @GetMapping("/admin/commodity/listEnabledCommodities")
    public Response<? extends Map> listEnabledCommodities(@RequestParam(required = false, value = "product_id") Integer productId) {
        Product product = productService.getById(productId);
        if (product == null) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        List<Commodity> commodities = commodityService.listEnabledCommodities(productId);
        List<AdCommodityBaseVo> baseV0List = null;
        if (CollectionUtils.isNotEmpty(commodities)) {
            baseV0List = commodities.stream().map(commodity -> {
                AdCommodityBaseVo commodityBaseV0 = new AdCommodityBaseVo();
                BeanUtils.copyProperties(commodity, commodityBaseV0);
                commodityBaseV0.setCommodityName(commodity.getName());
                return commodityBaseV0;
            }).collect(Collectors.toList());
        }
        return Response.ok("commodities", baseV0List);
    }

    /**
     * 获取已启用的套餐信息
     *
     * @return
     */
    @LogAttr(desc = "获取已启用的套餐信息", logType = LogType.query)
    @Permission(code = "store.commodity.commodity.getCommodityEnabled", desc = "获取已启用的套餐信息")
    @GetMapping("/admin/commodity/getCommodityEnabled")
    public Response<List<AdCommodityBaseVo>> getCommodityEnabled() {
        List<Commodity> commodities = commodityService.listCommodityByEnabled();
        if (CollectionUtils.isEmpty(commodities)) {
            return Response.ok();
        }
        List<AdCommodityBaseVo> commodityBaseVos = commodities.stream().map(commodity -> {
            AdCommodityBaseVo commodityBaseVo = new AdCommodityBaseVo();
            BeanUtils.copyProperties(commodity, commodityBaseVo);
            commodityBaseVo.setCommodityName(commodity.getName());
            return commodityBaseVo;
        }).collect(Collectors.toList());
        return Response.ok(commodityBaseVos);
    }

    /**
     * 批量上传新品物料
     *
     * @param repairCommoditiesDTO
     * @return
     */
    @LogAttr(desc = "批量上传新品物料", logType = LogType.query)
    @Permission(code = "store.commodity.commodity.batchInsertCommodities", desc = "批量上传新品物料")
    @PostMapping(path = "/admin/commodity/batchInsertCommodities", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> batchInsertCommodities(@RequestBody RepairCommoditiesDTO repairCommoditiesDTO) {
        String ossLink = repairCommoditiesDTO.getOssLink();
        if (StringUtil.isBlank(ossLink)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "文件链接不存在");
        }

        String[] filenameArr = ossLink.split("\\.");
        String suffix = filenameArr[filenameArr.length - 1];
        if (!"xlsx".equals(suffix)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "不支持的文件格式");
        }

        // 发送上传通知
        UploadExcelBO uploadExcelBo = new UploadExcelBO();
        uploadExcelBo.setTaskId(ObjectId.next());
        uploadExcelBo.setExcelOssUrl(ossLink);
        uploadExcelBo.setUploadBusinessType(UploadBusinessType.new_product_materials.getType());
        uploadExcelBo.setJobNumber(getAdminJobNumber());
        uploadExcelBo.setUploadTemplateType(UploadTemplateType.MULTIPLE.getType());
        adminUploadHelper.uploadTransit(uploadExcelBo);
        return Response.ok();
    }
}
