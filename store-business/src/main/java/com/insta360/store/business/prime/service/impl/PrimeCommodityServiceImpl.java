package com.insta360.store.business.prime.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.dao.CommodityDao;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.prime.bo.PrimeCommodityDomainBO;
import com.insta360.store.business.prime.bo.PrimeCreateCommodityBO;
import com.insta360.store.business.prime.constants.PrimeConstants;
import com.insta360.store.business.prime.dao.PrimeCommodityDao;
import com.insta360.store.business.prime.dto.PrimeCommodityDTO;
import com.insta360.store.business.prime.enums.PrimeCommodityType;
import com.insta360.store.business.prime.enums.PrimeGraphqlOperation;
import com.insta360.store.business.prime.error.PrimeInstaErrorCode;
import com.insta360.store.business.prime.lib.handler.PrimeRequestHandler;
import com.insta360.store.business.prime.lib.response.*;
import com.insta360.store.business.prime.lib.variables.*;
import com.insta360.store.business.prime.lib.variables.UpdatePurchaseGroupVariables.InputDTO.RepresentativeProductIdDTO;
import com.insta360.store.business.prime.model.PrimeCommodity;
import com.insta360.store.business.prime.model.PrimeCommodityInclude;
import com.insta360.store.business.prime.service.PrimeCommodityIncludeService;
import com.insta360.store.business.prime.service.PrimeCommodityService;
import com.insta360.store.business.prime.service.helper.PrimeCommodityHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Prime商品服务实现类
 * <p>
 * 该类实现了PrimeCommodityService接口，提供创建Prime商品的核心业务逻辑。
 * 支持两种类型的Prime商品创建：
 * 1. Individual（单个商品）：直接创建单个Prime商品
 * 2. Bundle（套餐组合）：创建包含多个商品的套餐组合
 * </p>
 *
 * <AUTHOR> Plus Generator
 * @date 2025-06-04
 */
@Service
public class PrimeCommodityServiceImpl extends BaseServiceImpl<PrimeCommodityDao, PrimeCommodity> implements PrimeCommodityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrimeCommodityServiceImpl.class);

    @Autowired
    CommodityDao commodityDao;

    @Autowired
    PrimeRequestHandler primeRequestHandler;

    @Autowired
    PrimeCommodityHelper primeCommodityHelper;

    @Autowired
    PrimeCommodityIncludeService primeCommodityIncludeService;

    @Override
    public List<PrimeCommodity> listByCommodityIdsType(List<Long> commodityIds, PrimeCommodityType primeCommodityType) {
        if (commodityIds.isEmpty()) {
            return new ArrayList<>(0);
        }
        QueryWrapper<PrimeCommodity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("commodity_id", commodityIds);
        queryWrapper.eq("prime_commodity_type", primeCommodityType.getCode());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PrimeCommodity> listByCommodityIds(List<Long> commodityIds) {
        if (commodityIds.isEmpty()) {
            return new ArrayList<>(0);
        }
        QueryWrapper<PrimeCommodity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("commodity_id", commodityIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PrimeCommodity> listByType(PrimeCommodityType primeCommodityType) {
        QueryWrapper<PrimeCommodity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prime_commodity_type", primeCommodityType.getCode());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public PrimeCommodity getByCommodityId(Integer commodityId) {
        QueryWrapper<PrimeCommodity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("commodity_id", commodityId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public PrimeCommodity getByPrimeProductId(String productId) {
        QueryWrapper<PrimeCommodity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prime_product_id", productId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    @TraceLog(logPrefix = "[删除prime商品]", logPrefixSub = "删除入口")
    public void deletePrimeCommodity(PrimeCommodityDTO commodityParam) {
        DeleteProductVariables deleteProductVariables = new DeleteProductVariables();
        deleteProductVariables.setIdentifier(new DeleteProductVariables.Identifier(commodityParam.externalId()));
        BasePrimeResponse response = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.DeleteProduct, deleteProductVariables);
        DeleteProductResponse deleteProductResponse = response.parsePrimeResponse(DeleteProductResponse.class);
        if (StringUtils.isBlank(deleteProductResponse.primeProductId())) {
            throw new InstaException(PrimeInstaErrorCode.PRIME_PRODUCT_ID_IS_NULL);
        }
        this.removeByPrimeCommodityId(commodityParam.getCommodityId(), commodityParam.getPrimeCommodityType());

    }

    @Override
    @TraceLog(logPrefix = "[更新prime商品]", logPrefixSub = "更新入口")
    public void updatePrimeCommodity(PrimeCommodityDTO commodityParam) {
        // 验证商品是否存在
        Commodity commodity = commodityDao.selectById(commodityParam.getCommodityId());
        if (commodity == null) {
            LOGGER.error("商品不存在，ID: {}", commodityParam.getCommodityId());
            throw new InstaException(PrimeInstaErrorCode.PRIME_COMMODITY_PARAMS_FAILED);
        }

        String externalId = commodityParam.externalId();
        PrimeCommodity primeCommodity = this.getPrimeCommodity(commodityParam.getCommodityId());
        PrimeCommodityType primeCommodityType = primeCommodity.primeCommodityType();
        // 打包Prime商品创建所需的业务对象（标题和图片URL）
        PrimeCreateCommodityBO primeCreateCommodityBO = primeCommodityHelper.packPrimeCreateCommodityBO(commodity);
        String primedProductId = null;
        BasePrimeResponse response = null;

        UpdateProductVariables updateProductVariables = new UpdateProductVariables();
        updateProductVariables.setIdentifier(new UpdateProductVariables.Identifier(externalId));
        UpdateProductVariables.Input input = new UpdateProductVariables.Input(
                commodityParam.getAmazonSku(),
                commodityParam.getOfferPrime(),
                primeCreateCommodityBO.getTitle(),
                primeCreateCommodityBO.getDetailUrl(),
                primeCreateCommodityBO.getSourceImage()
        );
        if (PrimeCommodityType.Bundle.equals(commodityParam.getPrimeCommodityType())) {
            input.setAmazonSku(null);
        }

        updateProductVariables.setInput(input);
        response = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.UpdateProduct, updateProductVariables);
        UpdateProductResponse updateProductResponse = response.parsePrimeResponse(UpdateProductResponse.class);
        primedProductId = updateProductResponse.primeProductId();
        if (Objects.requireNonNull(primeCommodityType) == PrimeCommodityType.Bundle) {
            UpdatePurchaseGroupVariables updatePurchaseGroupVariables = new UpdatePurchaseGroupVariables();
            UpdatePurchaseGroupVariables.InputDTO inputDTO = new UpdatePurchaseGroupVariables.InputDTO();
            inputDTO.setRepresentativeProductId(new RepresentativeProductIdDTO(externalId));
            List<PrimeCommodityDTO.IncludeCommodity> includeCommodities = commodityParam.getIncludeCommodities();
            if (CollectionUtils.isNotEmpty(includeCommodities)) {
                includeCommodities.forEach(includeCommodity -> inputDTO.addMember(includeCommodity.getCommodityId().toString(), includeCommodity.getQuantity(), PrimeConstants.Commodity.DEFAULT_UNIT));
            }
            updatePurchaseGroupVariables.setInput(inputDTO);
            response = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.UpdatePurchaseGroup, updatePurchaseGroupVariables);
            UpdateProductGroupResponse updateProductGroupResponse = response.parsePrimeResponse(UpdateProductGroupResponse.class);
            primedProductId = updateProductGroupResponse.primeProductId();
        }
        if (StringUtils.isBlank(primedProductId)) {
            throw new InstaException(PrimeInstaErrorCode.PRIME_PRODUCT_ID_IS_NULL);
        }

        primeCommodityHelper.updateGroupCommodity(primeCommodity, primedProductId, commodityParam);
    }

    /**
     * 创建Prime商品
     * <p>
     * 该方法根据传入的商品参数创建Prime商品，支持两种类型：
     * 1. Individual（单个商品）：直接创建单个Prime商品
     * 2. Bundle（套餐组合）：创建包含多个商品的套餐组合
     * </p>
     *
     * @param commodityParam Prime商品创建参数
     * @throws InstaException 当参数不正确或创建过程中出错时抛出异常
     */
    @Override
    @TraceLog(logPrefix = "[创建prime商品]", logPrefixSub = "创建入口")
    public void createPrimeCommodity(PrimeCommodityDTO commodityParam) {
        if (commodityParam.getReBuild()) {
            LOGGER.info("开始重建Prime商品，商品ID: {}, 类型: {}", commodityParam.getCommodityId(), commodityParam.getPrimeCommodityType());
            try {
                deletePrimeCommodity(commodityParam);
            } catch (Exception e) {
                LOGGER.error("删除Prime商品失败，商品ID: {}, 类型: {}", commodityParam.getCommodityId(), commodityParam.getPrimeCommodityType(), e);
            }
        }
        PrimeCommodity primeCommodity = this.getPrimeCommodity(commodityParam.getCommodityId());
        if (primeCommodity != null) {
            LOGGER.info("商品已存在，ID: {}", commodityParam.getCommodityId());
            throw new InstaException(PrimeInstaErrorCode.PRIME_COMMODITY_EXIST);
        }
        LOGGER.info("开始创建Prime商品，商品ID: {}, 类型: {}", commodityParam.getCommodityId(), commodityParam.getPrimeCommodityType());

        // 验证商品是否存在
        Commodity commodity = commodityDao.selectById(commodityParam.getCommodityId());
        if (commodity == null) {
            LOGGER.error("商品不存在，ID: {}", commodityParam.getCommodityId());
            throw new InstaException(PrimeInstaErrorCode.PRIME_COMMODITY_PARAMS_FAILED);
        }
        // 打包Prime商品创建所需的业务对象（标题和图片URL）
        PrimeCreateCommodityBO primeCreateCommodityBO = primeCommodityHelper.packPrimeCreateCommodityBO(commodity);
        PrimeCommodityType primeCommodityType = commodityParam.getPrimeCommodityType();
        String primedProductId = null;

        String externalId = commodityParam.externalId();

        // 根据商品类型执行不同的创建逻辑
        switch (primeCommodityType) {
            case Individual:
                LOGGER.info("创建Individual类型Prime商品，商品ID: {}", commodityParam.getCommodityId());

                // 使用商品ID作为SKU标识符
                String commoditySku = commodityParam.getAmazonSku();
                LOGGER.debug("使用商品ID作为SKU: {}", commoditySku);

                // 构建创建产品的输入参数
                CreateProductVariables.CreateProductInput createProductInput = CreateProductVariables.CreateProductInput.builder()
                        .externalId(externalId)  // 外部ID（用于与商城系统关联）
                        .amazonSkuValue(commoditySku)  // 使用相同的sku作为amazonSku
                        .offerPrime(commodityParam.getOfferPrime())  // 是否提供Prime服务
                        .productDetailPageUrl(primeCreateCommodityBO.getDetailUrl())  // 商品详情页URL
                        .titleValue(PrimeConstants.Commodity.DEFAULT_LANGUAGE, primeCreateCommodityBO.getTitle())  // 商品标题
                        .imageSourceUrl(primeCreateCommodityBO.getSourceImage())  // 商品图片URL
                        .build();
                LOGGER.debug("创建产品输入参数构建完成");

                // 创建请求变量并发送GraphQL请求
                CreateProductVariables createProductVariables = new CreateProductVariables(createProductInput);
                LOGGER.info("发送创建产品GraphQL请求");
                BasePrimeResponse responseCreateProduct = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.CreateProduct, createProductVariables);

                // 解析响应获取Prime产品ID
                CreateProductResponse createProductResponse = responseCreateProduct.parsePrimeResponse(CreateProductResponse.class);
                primedProductId = createProductResponse.primeProductId();
                LOGGER.info("成功创建Individual类型Prime商品，Prime产品ID: {}", primedProductId);

                break;
            case Bundle:
                LOGGER.info("创建Bundle类型Prime商品，商品ID: {}", commodityParam.getCommodityId());

                // 构建购买组输入参数
                CreatePurchaseGroupVariables.CreatePurchaseGroupInput.Builder bundleBuilder = CreatePurchaseGroupVariables.CreatePurchaseGroupInput.builder()
                        // 设置代表性产品详细信息
                        .representativeProductDetails(
                                externalId,  // 套餐ID+bundle后缀
                                commodityParam.getOfferPrime(),  // 是否启用Prime服务
                                primeCreateCommodityBO.getDetailUrl()  // 商品详情页URL
                        );

                // 添加包含的商品作为成员
                if (CollectionUtils.isNotEmpty(commodityParam.getIncludeCommodities())) {
                    commodityParam.getIncludeCommodities().forEach(include -> bundleBuilder.addMember(
                            include.getCommodityId().toString(),  // 商品ID作为外部ID
                            include.getQuantity(),  // 数量
                            PrimeConstants.Commodity.DEFAULT_UNIT  // 单位固定为UNIT
                    ));
                }

                // 创建请求变量
                CreatePurchaseGroupVariables createPurchaseGroupVariables = new CreatePurchaseGroupVariables(bundleBuilder.build());

                // 发送请求
                LOGGER.info("发送创建购买组GraphQL请求");
                BasePrimeResponse responseCreatePurchaseGroup = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.CreatePurchaseGroup, createPurchaseGroupVariables);

                // 解析响应
                CreatePurchaseGroupResponse createPurchaseGroupResponse =
                        responseCreatePurchaseGroup.parsePrimeResponse(CreatePurchaseGroupResponse.class);
                primedProductId = createPurchaseGroupResponse.purchaseGroupId();
                LOGGER.info("成功创建Bundle类型Prime商品，购买组ID: {}", primedProductId);
                break;

        }
        if (StringUtils.isBlank(primedProductId)) {
            LOGGER.error("创建Prime商品失败，请检查参数");
            throw new InstaException(PrimeInstaErrorCode.PRIME_PRODUCT_ID_IS_NULL);
        }

        // 构建并保存Prime商品记录
        LOGGER.info("开始保存Prime商品记录到数据库");
        primeCommodity = commodityParam.buildPrimeCommodity(primedProductId);
        baseMapper.insert(primeCommodity);
        LOGGER.info("Prime商品记录保存成功，ID: {}", primeCommodity.getId());

        // 如果是Bundle类型，额外保存包含的商品记录
        if (PrimeCommodityType.Bundle.equals(primeCommodityType)) {
            LOGGER.info("Bundle类型商品，开始保存包含的商品记录");
            List<PrimeCommodityInclude> primeCommodityIncludes =
                    commodityParam.buildPrimeCommodityIncludeList(primeCommodity.getId(), primeCommodity.getCommodityId());

            if (CollectionUtils.isNotEmpty(primeCommodityIncludes)) {
                primeCommodityIncludeService.saveBatch(primeCommodityIncludes);
                LOGGER.info("Bundle包含商品记录保存成功，数量: {}", primeCommodityIncludes.size());
            } else {
                LOGGER.error("Bundle类型商品没有包含商品记录保存");
            }
        }

        LOGGER.info("Prime商品创建完成，商品ID: {}, Prime产品ID: {}", commodityParam.getCommodityId(), primedProductId);
    }

    @Override
    public PrimeCommodityDomainBO getPrimeDetail(Long commodityId) {
        PrimeCommodity primeCommodity = this.getPrimeCommodity(commodityId);
        List<PrimeCommodityInclude> primeCommodityIncludes = primeCommodityIncludeService.listByPrimeCommodityId(primeCommodity.getId());
        return new PrimeCommodityDomainBO(primeCommodity, primeCommodityIncludes);
    }

    /**
     * 根据商品ID获取Prime商品信息
     *
     * @param commodityId 套餐ID
     * @return
     */
    public PrimeCommodity getPrimeCommodity(Long commodityId) {
        QueryWrapper<PrimeCommodity> qw = new QueryWrapper<>();
        qw.eq("commodity_id", commodityId);
        return baseMapper.selectOne(qw);
    }

    public void removeByPrimeCommodityId(Long commodityId, PrimeCommodityType primeCommodityType) {
        QueryWrapper<PrimeCommodity> wrapper = new QueryWrapper<>();
        wrapper.eq("commodity_id", commodityId);
        wrapper.eq("prime_commodity_type", primeCommodityType.getCode());
        baseMapper.delete(wrapper);
    }
}
