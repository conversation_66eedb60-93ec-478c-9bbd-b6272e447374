package com.insta360.store.business.outgoing.rpc.user.service;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.outgoing.common.ServiceName;
import com.insta360.store.business.outgoing.rpc.user.dto.UserAccount;
import com.insta360.store.business.outgoing.rpc.user.service.fallback.UserAccountServiceFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: wbt
 * @Date: 2021/05/10
 * @Description:
 */
@Primary
@FeignClient(contextId = "userAccountService", value = ServiceName.USER_SERVICE, fallback = UserAccountServiceFallBack.class)
public interface UserAccountService {

    /**
     * 根据邮箱获取用户信息
     *
     * @param username
     * @return
     */
    @GetMapping("/rpc/user/service/account/getProfileByUsername")
    Response<UserAccount> getByUsername(@RequestParam(value = "username") String username);

    /**
     * 根据token获取用户信息
     *
     * @param token
     * @return
     */
    @GetMapping("/rpc/user/service/account/getProfileByToken")
    Response<UserAccount> getByUserToken(@RequestParam(value = "token") String token);

    /**
     * 根据用户ID查询用户账户信息
     *
     * @param userId
     * @return
     */
    @GetMapping("/rpc/user/service/account/getProfileByUserId")
    Response<UserAccount> getProfileByUserId(@RequestParam(value = "userId") Integer userId);
}
