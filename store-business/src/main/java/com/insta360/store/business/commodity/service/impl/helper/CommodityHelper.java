package com.insta360.store.business.commodity.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.cloud.dto.CloudSkuCreateDTO;
import com.insta360.store.business.cloud.service.impl.helper.CloudServiceSubscribeEngineHelper;
import com.insta360.store.business.commodity.bo.CommodityDeliveryTimeConfigBO;
import com.insta360.store.business.commodity.bo.CommodityRelatedBO;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityDeliveryTimeConfig;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.model.CommodityTradeRule;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.configuration.verification.enums.ParameterBusinessType;
import com.insta360.store.business.insurance.model.ClimbServiceCommodity;
import com.insta360.store.business.insurance.service.ClimbServiceCommodityService;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.outgoing.mq.verification.helper.ParameterVerificationSendHelper;
import com.insta360.store.business.product.enums.ProductCategoryFinalType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2023-10-19 15:49
 */
@Component
public class CommodityHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommodityHelper.class);

    /**
     * 特殊处理发货时间地区列表
     */
    public static final List<String> JAPAC_COUNTRY = Arrays.asList("KR", "JP", "TW", "HK", "MO", "VN", "TH", "SG", "PH", "MY", "ID", "BN");

    /**
     * 特殊处理发货时间套餐列表
     */
    public static final List<Integer> ACE_PRO2_COMMODITY_IDS = Arrays.asList(3611, 3612, 3613, 3614, 3615, 3616, 3617, 3618);

    /**
     * JAPAC开始时间
     */
    private static final String JAPAC_COUNTRY_START_TIME = "2024-12-25T16:00:00";

    /**
     * JAPAC结束时间
     */
    private static final String JAPAC_COUNTRY_END_TIME = "2025-01-14T16:00:00";

    /**
     * JAPAC 元旦开始时间
     */
    private static final String JAPAC_COUNTRY_NEW_YEAR_START_TIME = "2024-12-30T10:00:00";

    /**
     * JAPAC 元旦结束时间
     */
    private static final String JAPAC_COUNTRY_NEW_YEAR_END_TIME = "2024-12-31T09:59:59";

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommodityTradeRuleService commodityTradeRuleService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityDeliveryTimeConfigService commodityDeliveryTimeConfigService;

    @Autowired
    ParameterVerificationSendHelper parameterVerificationSendHelper;

    @Autowired
    CloudServiceSubscribeEngineHelper cloudServiceSubscribeEngineHelper;

    @Autowired
    ProductCommodityStockService productCommodityStockService;

    @Autowired
    ProductService productService;

    @Autowired
    ClimbServiceCommodityService climbServiceCommodityService;

    @Autowired
    CommodityStockHelper commodityStockHelper;

    /**
     * 新增套餐信息
     *
     * @param commodityData
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void insertCommodity(Commodity commodityData, CloudSkuCreateDTO cloudSkuCreateDto) {
        Integer orderIndex = commodityData.getOrderIndex();
        Integer product = commodityData.getProduct();
        List<Commodity> standerdCommodityList = commodityService.getCommodities(product).stream().filter(Commodity::getStandard).collect(Collectors.toList());
        Boolean isStandard = Optional.ofNullable(commodityData.getStandard()).orElse(Boolean.FALSE);
        if (isStandard && CollectionUtils.isNotEmpty(standerdCommodityList)) {
            throw new InstaException(-1, String.format("产品ID：%d 套餐：%s已是标准套餐无法新增", product, standerdCommodityList.get(0).getName()));
        }
        List<Commodity> commodities = commodityService.listByRange(orderIndex, product);
        commodities.forEach(o -> {
            Integer index = o.getOrderIndex();
            o.setOrderIndex(++index);
        });

        commodityService.updateOrderIndexByIds(commodities);
        commodityService.doInsertCommodity(commodityData);

        // 创建商城云服务套餐相关信息
        if (Objects.nonNull(cloudSkuCreateDto)) {
            cloudSkuCreateDto.setProductId(commodityData.getProduct());
            cloudSkuCreateDto.setCommodityId(commodityData.getId());
            cloudServiceSubscribeEngineHelper.createCloudSubscribeSku(cloudSkuCreateDto);
        }

        // 新增套餐的商品类⽬=虚拟服务/增值服务时，增值服务套餐表同时新增记录
        Product productData = productService.getById(product);
        ProductCategoryFinalType categoryFinalType = ProductCategoryFinalType.parse(productData.getCategoryKey());
        if (ProductCategoryFinalType.CF_CLIMB_SERVICE.equals(categoryFinalType)) {
            ClimbServiceCommodity climbServiceCommodity = new ClimbServiceCommodity(product, commodityData.getId());
            climbServiceCommodityService.save(climbServiceCommodity);
        }
    }

    /**
     * 更新套餐信息
     *
     * @param commodityData
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateCommodity(Commodity commodityData) {
        Integer commodityId = commodityData.getId();
        Commodity commodity = commodityService.getById(commodityId);
        if (Objects.isNull(commodity)) {
            throw new InstaException(CommodityErrorCode.CommodityNotFoundException);
        }

        Integer fromIndex = commodity.getOrderIndex();
        Integer toIndex = commodityData.getOrderIndex();
        Integer product = commodity.getProduct();

        // 报关子产品-没有排序只更新数据
        if (Objects.isNull(toIndex)) {
            commodityService.doUpdateCommodity(commodityData);
            return;
        }

        // 序号相等只更新数据
        if (toIndex.equals(fromIndex)) {
            commodityService.doUpdateCommodity(commodityData);
            return;
        }

        List<Commodity> commodities = null;
        // 大 -> 小
        if (fromIndex > toIndex) {
            commodities = commodityService.listByAllRange(product, toIndex, fromIndex);
            commodities.remove(commodities.size() - 1);
            commodities.forEach(o -> {
                Integer orderIndex = o.getOrderIndex();
                o.setOrderIndex(++orderIndex);
            });
        }

        // 小 -> 大
        if (fromIndex < toIndex) {
            commodities = commodityService.listByAllRange(product, fromIndex, toIndex);
            commodities.remove(0);
            commodities.forEach(o -> {
                Integer orderIndex = o.getOrderIndex();
                o.setOrderIndex(--orderIndex);
            });
        }

        // update
        commodityService.updateOrderIndexByIds(commodities);
        commodityService.doUpdateCommodity(commodityData);
    }

    /**
     * 保存套餐相关构成数据
     *
     * @param commodityRelatedBo
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public Commodity saveCommodityRelatedData(CommodityRelatedBO commodityRelatedBo) {
        // 套餐基础数据
        Commodity commodity = commodityRelatedBo.getCommodity();
        // 套餐库存
        CommodityTradeRule commodityTradeRule = commodityRelatedBo.getCommodityTradeRule();
        // 套餐预计发货天数
        CommodityDeliveryTimeConfig commodityDeliveryTimeConfig = commodityRelatedBo.getCommodityDeliveryTimeConfig();

        if (Objects.isNull(commodity) || Objects.isNull(commodityTradeRule) || Objects.isNull(commodityDeliveryTimeConfig)) {
            return null;
        }

        // 保存套餐
        boolean result = commodityService.save(commodity);

        if (result) {
            commodityTradeRule.setCommodityId(commodity.getId());
            commodityDeliveryTimeConfig.setCommodity(commodity.getId());
            commodityTradeRuleService.save(commodityTradeRule);
            commodityDeliveryTimeConfigService.save(commodityDeliveryTimeConfig);
            commodityStockHelper.fillDeliveryArea(commodity.getId());
        }

        // 发送商城参数校验缓存同步刷新MQ消息
        parameterVerificationSendHelper.sendSyncRefreshCacheMsg(ParameterBusinessType.COMMODITY_ID.name(), String.valueOf(commodity.getId()));
        return commodity;
    }

    /**
     * 批量更新套餐价格
     *
     * @param cachePutKeyParameter
     * @param commodityPriceList
     */
    @CacheEvict(value = {CacheableType.HOME_ITEM_KEY, CacheableType.HOME_PAGE_KEY, CacheableType.NAVIGATION_BAR_CATEGORY_KEY, CacheableType.COMMODITY_LIST_INFO,
            CacheableType.PRODUCT_INFO, CacheableType.COMMODITY_INFO, CacheableType.COMMODITY_DIFFERENCE, CacheableType.GRAPHIC_NAVIGATION,
            CacheableType.COMMODITY_RECOMMENDATION, CacheableType.COMMODITY_RECOMMENDATION_CART, CacheableType.CATEGORY_PAGE, CacheableType.CATEGORY_PAGE_FILTER}, allEntries = true)
    @CachePutMonitor(cacheableType = CachePutType.COMMODITY_PRICE_BATCH)
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    public void batchUpdateCommodityPrice(CachePutKeyParameterBO cachePutKeyParameter, List<CommodityPrice> commodityPriceList) {
        List<Integer> commodityIds = commodityPriceList.stream().map(CommodityPrice::getCommodityId).distinct().collect(Collectors.toList());
        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(commodityIds);
        SearchDataChangeContext.set(searchDataChangeParams);

        commodityPriceService.batchUpdatePrice(commodityPriceList);
    }

    /**
     * 获取发货时间配置
     *
     * @param country
     * @param commodityId
     * @param deliveryTimeConfig
     * @return
     */
    public CommodityDeliveryTimeConfig getCommodityDeliveryTimeConfig(InstaCountry country, Integer commodityId, CommodityDeliveryTimeConfig deliveryTimeConfig) {
        // 如果没有配置发货时间则保持默认2天
        if (Objects.isNull(deliveryTimeConfig)) {
            LOGGER.info(String.format("发货配置/地区为空 套餐{%s}, 地区{%s}", commodityId, country));
            deliveryTimeConfig = new CommodityDeliveryTimeConfig();
            deliveryTimeConfig.setEstimateDays(2);
            return deliveryTimeConfig;
        }

        if (Objects.isNull(country)) {
            LOGGER.info(String.format("地区为空 发货信息{%s}, 套餐{%s}", deliveryTimeConfig, commodityId));
            return deliveryTimeConfig;
        }

        if (InstaCountry.US.equals(country)) {
            return this.getUsDeliveryTimeConfig(country, commodityId, deliveryTimeConfig);
        }
        // 只针对符合指定‘地区’、‘套餐’特殊处理
        else if (JAPAC_COUNTRY.contains(country.name()) && ACE_PRO2_COMMODITY_IDS.contains(commodityId)) {
            return this.getJapacDeliveryTimeConfig(country, commodityId, deliveryTimeConfig);
        }

        return deliveryTimeConfig;
    }

    /**
     * JAPAC_COUNTRY 预估发货天数特殊处理
     *
     * @param country
     * @param commodityId
     * @param deliveryTimeConfig
     * @return
     */
    private CommodityDeliveryTimeConfig getJapacDeliveryTimeConfig(InstaCountry country, Integer commodityId, CommodityDeliveryTimeConfig deliveryTimeConfig) {
        try {
            // 缓存中存在
            String japacCountryStartTime = (String) RedisTemplateUtil.getValue("JAPAC_COUNTRY_START_TIME");
            String japacCountryEndTime = (String) RedisTemplateUtil.getValue("JAPAC_COUNTRY_END_TIME");
            // JAPAC发货时间处理时间
            LocalDateTime nowTime = LocalDateTime.now();
            LocalDateTime startTime = Objects.nonNull(japacCountryStartTime) ? LocalDateTime.parse(japacCountryStartTime) : LocalDateTime.parse(JAPAC_COUNTRY_START_TIME);
            LocalDateTime endTime = Objects.nonNull(japacCountryEndTime) ? LocalDateTime.parse(japacCountryEndTime) : LocalDateTime.parse(JAPAC_COUNTRY_END_TIME);
            if (!(nowTime.isAfter(startTime) && nowTime.isBefore(endTime))) {
                LOGGER.info(String.format("不在时间内，当前时间%s,地区{%s} 套餐{%s}", nowTime, country, commodityId));
                return deliveryTimeConfig;
            }

            // 缓存中存在
            String countryNewYearStartTime = (String) RedisTemplateUtil.getValue("JAPAC_COUNTRY_NEW_YEAR_START_TIME");
            String countryNewYearEndTime = (String) RedisTemplateUtil.getValue("JAPAC_COUNTRY_NEW_YEAR_END_TIME");
            // JAPAC发货时间处理时间
            LocalDateTime japacCountryNewYearStartTime = Objects.nonNull(countryNewYearStartTime) ? LocalDateTime.parse(countryNewYearStartTime) : LocalDateTime.parse(JAPAC_COUNTRY_NEW_YEAR_START_TIME);
            LocalDateTime japacCountryNewYearEndTime = Objects.nonNull(countryNewYearEndTime) ? LocalDateTime.parse(countryNewYearEndTime) : LocalDateTime.parse(JAPAC_COUNTRY_NEW_YEAR_END_TIME);
            // 元旦期间发货时间3天
            if ((nowTime.isAfter(japacCountryNewYearStartTime) && nowTime.isBefore(japacCountryNewYearEndTime))) {
                deliveryTimeConfig.setEstimateDays(3);
                return deliveryTimeConfig;
            }
        } catch (Exception e) {
            LOGGER.info(String.format("发货时间配置异常，地区{%s} 套餐{%s}", country, commodityId), e);
        }

        // 其他生效时间段2天
        deliveryTimeConfig.setEstimateDays(2);
        return deliveryTimeConfig;
    }

    /**
     * US发货时间特殊处理
     *
     * @param country
     * @param commodityId
     * @param deliveryTimeConfig
     * @return
     */
    private CommodityDeliveryTimeConfig getUsDeliveryTimeConfig(InstaCountry country, Integer commodityId, CommodityDeliveryTimeConfig deliveryTimeConfig) {
        // 获取配置信息
        String configValue = storeConfigService.getConfigValue(StoreConfigKey.commodity_delivery_time_config_key);
        if (StringUtils.isBlank(configValue)) {
            return deliveryTimeConfig;
        }

        // 特殊套餐预估发货天数配置信息
        List<CommodityDeliveryTimeConfigBO> commodityDeliveryTimeConfigList = JSON.parseArray(configValue, CommodityDeliveryTimeConfigBO.class);
        Map<String, List<CommodityDeliveryTimeConfigBO.ConfigDetail>> deliveryTimeConfigCacheMap = commodityDeliveryTimeConfigList.stream()
                .collect(Collectors.toMap(CommodityDeliveryTimeConfigBO::getCountryCode, CommodityDeliveryTimeConfigBO::getDetails));

        List<CommodityDeliveryTimeConfigBO.ConfigDetail> cacheDetailList = deliveryTimeConfigCacheMap.get(country.name());
        if (CollectionUtils.isEmpty(cacheDetailList)) {
            return deliveryTimeConfig;
        }

        for (CommodityDeliveryTimeConfigBO.ConfigDetail cacheDetail : cacheDetailList) {
            if (commodityId.equals(cacheDetail.getCommodityId())) {
                deliveryTimeConfig.setEstimateDays(cacheDetail.getEstimateDays());
                return deliveryTimeConfig;
            }
        }

        return deliveryTimeConfig;
    }
}
