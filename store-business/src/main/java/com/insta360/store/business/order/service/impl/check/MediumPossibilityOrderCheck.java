package com.insta360.store.business.order.service.impl.check;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.OrderPushBlockReasonTypeEnum;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.dto.OrderEoriVerificationDTO;
import com.insta360.store.business.order.enums.OrderMonitorState;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.order.service.*;
import com.insta360.store.business.order.service.constant.OrderAutoPushConstantPool;
import com.insta360.store.business.order.service.impl.helper.OrderPushAutoHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对订单检查的具体实现类
 *
 * <AUTHOR>
 */
@Component
public class MediumPossibilityOrderCheck extends BaseOrderPushAutoCheckChain {

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderAdminRemarkService orderAdminRemarkService;

    @Autowired
    OrderCheckRuleService orderCheckRuleService;

    @Autowired
    OrderPushAutoHelper orderPushAutoHelper;

    @Autowired
    OrderItemService orderItemService;

    @Override
    public boolean doCheck(Order order) {
        return ifEoriOrder(order) && ifContainRocKeyWord(order) && isAdminRemarkNull(order) && ifLowDiscountOrGiftRule(order) && isAbnormalAmount(order);
    }

    /**
     * e.是否德国EORI订单
     *
     * @param order
     * @return
     */
    private boolean ifEoriOrder(Order order) {
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        float result = orderPayment.getTotalPayPrice().getAmount();
        OrderEoriVerificationDTO orderEoriVerificationDTO = eoriVerification(orderDelivery);
        // 德国的单，包含关键词，且金额大于150
        if (InstaCountry.DE.equals(order.country()) && orderEoriVerificationDTO.getPickEoriKeyWord() && result > OrderAutoPushConstantPool.GERMANY_COMPANY_MIN_PRICE) {
            String message = "【" + orderEoriVerificationDTO.getItem() + "】" + "匹配到关键字：" + orderEoriVerificationDTO.getKeyWord();
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.EORI, message, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * f.是否含有ROC关键词
     *
     * @param order
     * @return
     */
    private boolean ifContainRocKeyWord(Order order) {
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        if (InstaCountry.TW.equals(order.country()) && ifContainAnyKeyword(orderDelivery)) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.ROC, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * g.订单“卖家备注”不为空
     *
     * @param order
     * @return
     */
    private boolean isAdminRemarkNull(Order order) {
        OrderAdminRemark adminRemark = orderAdminRemarkService.getByOrder(order.getId());
        if (adminRemark == null) {
            return true;
        }
        if (StringUtil.isNotBlank(adminRemark.getContent())) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.ADMIN_REMARK, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * h.订单折扣&赠品价值风控
     *
     * @param order
     * @return
     */
    private boolean ifLowDiscountOrGiftRule(Order order) {
        OrderCheckRule checkRule = orderCheckRuleService.getOrderRule(order.getId());
        if (checkRule == null) {
            return true;
        }
        String orderMonitorState = checkRule.getOrderMonitorState();
        if (StringUtil.isBlank(orderMonitorState)) {
            return true;
        }
        if (orderMonitorState.contains(OrderMonitorState.ORDER_RULE.getCodeAsString()) || orderMonitorState.contains(OrderMonitorState.GIFT_RULE.getCodeAsString())) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.LOW_DISCOUNT_OR_GIFT_RULE, "订单监控状态：" + orderMonitorState, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * i.订单金额是否触发“异常金额风控”(飞书告警)
     *
     * @param order
     * @return
     */
    private boolean isAbnormalAmount(Order order) {
        // 先进行小数点校验
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        // 当前订单币种
        Currency currency = orderPayment.currency();
        // 订单实付金额
        float payableAmount = orderPayment.getTotalPayPrice().getAmount();
        String formattedAmount = Float.toString(payableAmount);
        boolean exceedThreeDecimalPlaces = ifExceedThreeDecimalPlaces(formattedAmount);
        // 小数点超过三位
        if (exceedThreeDecimalPlaces) {
            sendFeiShuMessage(String.format("订单{%s}，订单总金额为{%s%s}，小数点超过三位，请检查并手动备货。", order.getOrderNumber(), currency.name(), formattedAmount));
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.EXCEEDS_THREE_DIGITS, String.valueOf(payableAmount), order.getOrderNumber(), order.getId());
            return false;
        }

        // 低价校验，排除套餐仅含指定低价云服务套餐或补差价套餐
        List<Integer> commodityIds = orderItemService.getByOrder(order.getId()).stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        boolean allBelongToLowPriceCommodities = new HashSet<>(OrderAutoPushConstantPool.LOW_PRICE_COMMODITY_IDS).containsAll(commodityIds);
        if (allBelongToLowPriceCommodities) {
            return true;
        }

        // 最后进行低价校验
        // key -> Currency     value -> 最小金额
        Map<Currency, Float> minPriceMap = OrderAutoPushConstantPool.getMinPriceMap();
        // 低于设定阈值
        if (currency != null && minPriceMap.containsKey(currency) &&
                minPriceMap.get(currency) != null && payableAmount <= minPriceMap.get(currency)) {
            sendFeiShuMessage(String.format("订单{%s}，订单总金额为{%s%s}，低于设定阈值，请检查并手动备货。", order.getOrderNumber(), currency.name(), formattedAmount));
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.BELOW_MIN_PRICE, null, order.getOrderNumber(), order.getId());
            return false;
        }

        return true;
    }

    /**
     * 飞书告警
     *
     * @param context 告警内容
     */
    private void sendFeiShuMessage(String context) {
        FeiShuMessageUtil.storeGeneralMessage(context, FeiShuGroupRobot.PriceChange, FeiShuAtUser.ZM);
    }

    /**
     * 去空格并转换成大写
     *
     * @param info
     * @return
     */
    private String removeBlankAndUpperCase(String info) {
        return StringUtil.isBlank(info) ? "" : info.replaceAll("\\s+", "").toUpperCase();
    }

    /**
     * 转换成大写
     *
     * @param info
     * @return
     */
    private String doUpperCase(String info) {
        return StringUtil.isBlank(info) ? "" : info.toUpperCase();
    }

    /**
     * 判断是否包含关键词
     *
     * @param orderDelivery
     * @return
     */
    private boolean ifContainAnyKeyword(OrderDelivery orderDelivery) {
        String address = removeBlankAndUpperCase(orderDelivery.getAddress());
        String subAddress = removeBlankAndUpperCase(orderDelivery.getSubAddress());
        String firstName = removeBlankAndUpperCase(orderDelivery.getFirstName());
        String lastName = removeBlankAndUpperCase(orderDelivery.getLastName());
        String city = removeBlankAndUpperCase(orderDelivery.getCity());
        return OrderAutoPushConstantPool.ROC_ARRAY.stream().filter(StringUtil::isNotBlank)
                .map(this::removeBlankAndUpperCase)
                .anyMatch(keyWord -> address.contains(keyWord) ||
                        subAddress.contains(keyWord) ||
                        firstName.contains(keyWord) ||
                        lastName.contains(keyWord) ||
                        city.contains(keyWord));
    }

    /**
     * 金额是否超过三位小数（含三位）
     *
     * @param price
     * @return
     */
    private boolean ifExceedThreeDecimalPlaces(String price) {
        if (!price.contains(".")) {
            return false;
        } else {
            //小数点位数
            int decimalPlaces = price.length() - price.indexOf(".") - 1;
            return decimalPlaces > 2;
        }
    }

    /**
     * 返回是否命中关键字，及命中时具体信息
     *
     * @param orderDelivery
     * @return
     */
    private OrderEoriVerificationDTO eoriVerification(OrderDelivery orderDelivery) {
        // 获取各个target
        String address = orderDelivery.getAddress();
        String subAddress = orderDelivery.getSubAddress();
        String firstName = orderDelivery.getFirstName();
        String lastName = orderDelivery.getLastName();
        String city = orderDelivery.getCity();

        // key -> 目标信息   value -> 对应的哪一项
        Map<String, String> targetInfoToItemMap = new HashMap<>();
        targetInfoToItemMap.put(address, "地址");
        targetInfoToItemMap.put(subAddress, "详细地址");
        targetInfoToItemMap.put(firstName, "名");
        targetInfoToItemMap.put(lastName, "姓");
        targetInfoToItemMap.put(city, "市");

        List<String> deliveryInfos = Arrays.asList(address, subAddress, firstName, lastName, city);
        for (String targetInfo : deliveryInfos) {
            String keyWord = getEoriKeyWord(targetInfo);
            if (StringUtil.isNotBlank(keyWord)) {
                // 踩中了关键词，包装之后返回
                return new OrderEoriVerificationDTO(true, keyWord, targetInfoToItemMap.get(targetInfo), targetInfo);
            }
        }
        return new OrderEoriVerificationDTO(false);
    }

    /**
     * 返回eori订单踩中的关键词
     *
     * @param target
     * @return
     */
    private String getEoriKeyWord(String target) {
        List<String> shortKeyWords = OrderAutoPushConstantPool.GERMANY_COMPANY_MSG_SHORT;
        List<String> longKeyWords = OrderAutoPushConstantPool.GERMANY_COMPANY_MSG;
        // 先判断短字符
        String word = shortKeyWords.stream()
                .map(this::doUpperCase)
                .filter(keyWord -> ifCharacterBoundary(keyWord, doUpperCase(target)))
                .findFirst()
                .orElse(null);
        // 再判断长字符
        return StringUtil.isNotBlank(word) ? word : longKeyWords.stream().map(this::removeBlankAndUpperCase)
                .filter(removeBlankAndUpperCase(target)::contains)
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断目标字符串是否包含关键词(只针对短字符)
     *
     * @param keyWord
     * @param target
     * @return
     */
    private Boolean ifCharacterBoundary(String keyWord, String target) {
        // 不包含关键词
        if (!target.contains(keyWord)) {
            return Boolean.FALSE;
        }
        // 与关键词完全相等
        if (target.equals(keyWord)) {
            return Boolean.TRUE;
        }

        // 目标字符以关键词结尾
        if (target.endsWith(keyWord)) {
            int endIndex = target.length() - keyWord.length();
            if (!endsWithLetterOrDigit(target.substring(0, endIndex))) {
                return Boolean.TRUE;
            }
        }
        // 以关键词为参数，分割目标字符
        // 若字符数组中，第i个元素不以字母或者数字结尾，且第i+1个元素不以字母或者数字开头，返回true
        String[] splitArray = target.split(keyWord);
        for (int i = 0; i < splitArray.length - 1; i++) {
            if (!endsWithLetterOrDigit(splitArray[i]) && !startsWithLetterOrDigit(splitArray[i + 1])) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 判断目标字符是否以字母或者数字结尾
     *
     * @param info
     * @return
     */
    private boolean endsWithLetterOrDigit(String info) {
        if (StringUtil.isBlank(info)) {
            return false;
        }
        return info.matches(".*[a-zA-Z0-9]");
    }

    /**
     * 判断目标字符是否以字母或者数字开头
     *
     * @param info
     * @return
     */
    private boolean startsWithLetterOrDigit(String info) {
        if (StringUtil.isBlank(info)) {
            return false;
        }
        return info.matches("^[a-zA-Z0-9].*");
    }
}
