package com.insta360.store.business.reseller.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.reseller.enums.ResellerGiftModuleType;
import com.insta360.store.business.reseller.model.ResellerGiftConfigNew;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-25
 * @Description:
 */
public interface ResellerGiftConfigNewService extends BaseService<ResellerGiftConfigNew> {

    /**
     * 根据分销码获取
     *
     * @param resellerCode 分销码
     * @return {@link List }<{@link ResellerGiftConfigNew }>
     */
    List<ResellerGiftConfigNew> listByPromoCode(String resellerCode);

    /**
     * 根据 产品ID、套餐ID、分销商ID查询对应分销活动配置
     *
     *
     * @param productId
     * @param commodityIdList
     * @param resellerIds
     * @return
     */
    List<ResellerGiftConfigNew> listByResellerGiftConfig(Integer productId, List<Integer> commodityIdList, List<Integer> resellerIds);

    /**
     * 根据产品ID、分销商ID 查询分销赠品活动配置主体
     *
     * @param productId
     * @param resellerIds
     * @param defaultGiftMark
     * @return
     */
    List<ResellerGiftConfigNew> getConfigByReseller(Integer productId, List<Integer> resellerIds, Integer defaultGiftMark);

    /**
     * 查询分销赠品默认配置
     *
     * @param productId
     * @param commodityIdList
     * @param defaultGiftMark
     * @return
     */
    List<ResellerGiftConfigNew> getDefaultConfig(Integer productId, List<Integer> commodityIdList, Integer defaultGiftMark);

    /**
     * 批量保存
     *
     * @param resellerGiftConfigList
     */
    boolean batchSaveResellerGiftConfig(List<ResellerGiftConfigNew> resellerGiftConfigList, ResellerGiftModuleType moduleType);
}
