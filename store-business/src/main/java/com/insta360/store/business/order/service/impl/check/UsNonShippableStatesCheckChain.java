package com.insta360.store.business.order.service.impl.check;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.enums.OrderPushBlockReasonTypeEnum;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.constant.OrderAutoPushConstantPool;
import com.insta360.store.business.order.service.impl.helper.OrderPushAutoHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description US地区不可发货州检查
 * @Date 2025/4/30 下午2:19
 */
@Component
public class UsNonShippableStatesCheckChain extends BaseOrderPushAutoCheckChain {

    public static final Logger LOGGER = LoggerFactory.getLogger(UsNonShippableStatesCheckChain.class);

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderPushAutoHelper orderPushAutoHelper;

    /**
     * 判断下单地区为美国不可发货州时，拦截
     *
     * @param order
     * @return
     */
    @Override
    public boolean doCheck(Order order) {
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        // 下单地区
        String country = orderDelivery.getCountryCode();
        // 下单邮编
        String zipCode = orderDelivery.getZipCode();
        // 非美国地区，直接退出
        if (!InstaCountry.US.equals(InstaCountry.parse(country))) {
            return true;
        }

        // 对美属维京群岛进行字符串判断
        if (OrderAutoPushConstantPool.VIRGIN_ISLAND.contains(zipCode)) {
            LOGGER.info(String.format("[自动推单]美国不可发货州拦截，邮政编码：%s", zipCode));
            orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.US_NON_SHIPPABLE_STATE, order.getId(), zipCode);
            return false;
        }

        // 解析zipCode
        int zip;
        try {
            zip = Integer.parseInt(zipCode);
        } catch (NumberFormatException e) {
            // 无法解析，直接返回
            return true;
        }

        // 对其他州进行范围判断
        Optional<String> nonShippableState = OrderAutoPushConstantPool.US_STATE_ZIP_RANGES.entrySet()
                .stream()
                .filter(
                        entry -> entry.getValue()
                                .stream()
                                .anyMatch(zipRange -> zipRange.contains(zip))
                )
                .map(Map.Entry::getKey)
                .findFirst();
        nonShippableState.ifPresent(state -> {
            LOGGER.info(String.format("[自动推单]美国不可发货州拦截，邮政编码：%s，州简写：%s", zipCode, state));
            orderPushAutoHelper.addOrderPushFailedLog(OrderPushBlockReasonTypeEnum.US_NON_SHIPPABLE_STATE, order.getId(), zipCode);
        });
        return !nonShippableState.isPresent();
    }
}
