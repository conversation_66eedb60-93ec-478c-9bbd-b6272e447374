package com.insta360.store.business.insurance.enums;

/**
 * @description: care卡的类型
 * @author: py
 * @create: 2023-12-15 14:56
 */
public enum CareCardType {

    /**
     * 实体卡
     */
    physical("实体卡"),

    /**
     * 虚拟卡
     */
    virtual("虚拟卡"),

    ;

    /**
     * 描述
     */
    private final String message;


    CareCardType(String message) {
        this.message = message;
    }

    /**
     * 解析
     *
     * @param cardType
     * @return
     */
    public static CareCardType parse(String cardType) {
        for (CareCardType careCardType : CareCardType.values()) {
            if (careCardType.name().equals(cardType)) {
                return careCardType;
            }
        }
        return null;
    }

    public String getMessage() {
        return message;
    }
}
