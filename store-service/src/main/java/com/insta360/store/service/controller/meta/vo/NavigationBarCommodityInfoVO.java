package com.insta360.store.service.controller.meta.vo;

import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.service.controller.product.vo.CommodityVO;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2021/08/27
 * @Description:
 */
public class NavigationBarCommodityInfoVO implements Serializable {

    /**
     * 套餐 id
     */
    private Integer commodityId;

    /**
     * 套餐内部名称
     */
    private String commodityInnerName;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 产品内部名称
     */
    private String productInnerName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品跳转链接key
     */
    private String productUrlKey;

    /**
     * 销售状态
     */
    private Integer saleState;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 启用状态
     */
    private Boolean enabled;

    /**
     * 展示图
     */
    private String displayUrl;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 现价
     */
    private Price price;

    /**
     * 原价
     */
    private Price originPrice;

    /**
     * 解析参数填充
     *
     * @param commodityVO
     * @return
     */
    public NavigationBarCommodityInfoVO parse(CommodityVO commodityVO) {
        if (Objects.isNull(commodityVO)) {
            return null;
        }
        List<CommodityDisplay> displays = commodityVO.getDisplays();
        if (CollectionUtils.isEmpty(displays)) {
            return null;
        }
        CommodityInfo commodityInfo = commodityVO.getInfo();
        if (Objects.isNull(commodityInfo)) {
            return null;
        }
        this.setCommodityId(commodityVO.getId());
        this.setDisplayUrl(displays.get(0).getUrl());
        this.setProductInnerName(commodityVO.getProductInnerName());
        this.setProductName(commodityVO.getProductName());
        this.setSaleState(commodityVO.getSaleState());
        this.setStock(commodityVO.getStock());
        this.setEnabled(commodityVO.getEnabled());
        this.setProductUrlKey(commodityVO.getProductUrlKey());
        this.setProductId(commodityVO.getProductId());
        this.setPrice(commodityVO.getPrice());
        this.setOriginPrice(commodityVO.getOriginPrice());
        this.setCommodityInnerName(commodityVO.getName());
        this.setCommodityName(commodityInfo.getName());
        return this;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityInnerName() {
        return commodityInnerName;
    }

    public void setCommodityInnerName(String commodityInnerName) {
        this.commodityInnerName = commodityInnerName;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getProductInnerName() {
        return productInnerName;
    }

    public void setProductInnerName(String productInnerName) {
        this.productInnerName = productInnerName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductUrlKey() {
        return productUrlKey;
    }

    public void setProductUrlKey(String productUrlKey) {
        this.productUrlKey = productUrlKey;
    }

    public Integer getSaleState() {
        return saleState;
    }

    public void setSaleState(Integer saleState) {
        this.saleState = saleState;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getDisplayUrl() {
        return displayUrl;
    }

    public void setDisplayUrl(String displayUrl) {
        this.displayUrl = displayUrl;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Price getPrice() {
        return price;
    }

    public void setPrice(Price price) {
        this.price = price;
    }

    public Price getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Price originPrice) {
        this.originPrice = originPrice;
    }

    @Override
    public String toString() {
        return "NavigationBarCommodityInfoVO{" +
                "commodityId=" + commodityId +
                ", commodityInnerName='" + commodityInnerName + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", productInnerName='" + productInnerName + '\'' +
                ", productName='" + productName + '\'' +
                ", productUrlKey='" + productUrlKey + '\'' +
                ", saleState=" + saleState +
                ", stock=" + stock +
                ", enabled=" + enabled +
                ", displayUrl='" + displayUrl + '\'' +
                ", orderIndex=" + orderIndex +
                ", productId=" + productId +
                ", price=" + price +
                ", originPrice=" + originPrice +
                '}';
    }
}
