package com.insta360.store.business.payment.bo;

import com.insta360.store.business.cloud.enums.SubscribeActionType;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2024/06/21
 * @Description:
 */
public class PaymentSubscribeBO implements Serializable {

    /**
     * 订单信息
     */
    private Order order;

    /**
     * 订单支付信息
     */
    private OrderPayment orderPayment;

    /**
     * 信用卡卡号
     */
    @NotNull(message = "信用卡卡号不允许为null")
    @NotBlank(message = "信用卡卡号不允许为空")
    private String cardNumber;

    /**
     * 信用卡年份
     */
    @NotNull(message = "信用卡年份不允许为null")
    private Integer cardYear;

    /**
     * 信用卡月份
     */
    @NotNull(message = "信用卡月份不允许为null")
    private Integer cardMonth;

    /**
     * 信用卡CVV
     */
    @NotNull(message = "信用卡CVV不允许为null")
    @NotBlank(message = "信用卡CVV不允许为空")
    private String cardCvv;

    /**
     * 持卡人姓名
     */
    @NotNull(message = "持卡人名称不允许为null")
    @NotBlank(message = "持卡人名称不允许为空")
    private String nameOnCard;

    /**
     * 是否走3D校验（信用卡支付）
     */
    private Boolean threeDomainTrade;

    /**
     * 是否3d豁免（信用卡支付）
     */
    private Boolean exemption;

    /**
     * 订阅动作类型
     */
    private SubscribeActionType subscribeActionType;

    /**
     * 订阅支付凭证
     */
    private String payId;

    /**
     * insta account
     */
    private StoreAccount storeAccount;

    /**
     * 订阅pay info
     */
    private UserPayInfo userPayInfo;

    /*********************************** klarna ********************************

     /**
     * kp session
     */
    private String kpSid;

    /**
     * klarna 交易号
     */
    private String klarnaOrderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * klarna auth token
     */
    private String authToken;

    /*********************************** checkout ********************************

     /**
     * 支付通道id
     */
    private Integer paymentChannelId;

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public OrderPayment getOrderPayment() {
        return orderPayment;
    }

    public void setOrderPayment(OrderPayment orderPayment) {
        this.orderPayment = orderPayment;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public Integer getCardYear() {
        return cardYear;
    }

    public void setCardYear(Integer cardYear) {
        this.cardYear = cardYear;
    }

    public Integer getCardMonth() {
        return cardMonth;
    }

    public void setCardMonth(Integer cardMonth) {
        this.cardMonth = cardMonth;
    }

    public String getCardCvv() {
        return cardCvv;
    }

    public void setCardCvv(String cardCvv) {
        this.cardCvv = cardCvv;
    }

    public String getNameOnCard() {
        return nameOnCard;
    }

    public void setNameOnCard(String nameOnCard) {
        this.nameOnCard = nameOnCard;
    }

    public Boolean getThreeDomainTrade() {
        return threeDomainTrade;
    }

    public void setThreeDomainTrade(Boolean threeDomainTrade) {
        this.threeDomainTrade = threeDomainTrade;
    }

    public Boolean getExemption() {
        return exemption;
    }

    public void setExemption(Boolean exemption) {
        this.exemption = exemption;
    }

    public SubscribeActionType getSubscribeActionType() {
        return subscribeActionType;
    }

    public void setSubscribeActionType(SubscribeActionType subscribeActionType) {
        this.subscribeActionType = subscribeActionType;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public StoreAccount getStoreAccount() {
        return storeAccount;
    }

    public void setStoreAccount(StoreAccount storeAccount) {
        this.storeAccount = storeAccount;
    }

    public String getKpSid() {
        return kpSid;
    }

    public void setKpSid(String kpSid) {
        this.kpSid = kpSid;
    }

    public String getKlarnaOrderId() {
        return klarnaOrderId;
    }

    public void setKlarnaOrderId(String klarnaOrderId) {
        this.klarnaOrderId = klarnaOrderId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public UserPayInfo getUserPayInfo() {
        return userPayInfo;
    }

    public void setUserPayInfo(UserPayInfo userPayInfo) {
        this.userPayInfo = userPayInfo;
    }

    public Integer getPaymentChannelId() {
        return paymentChannelId;
    }

    public void setPaymentChannelId(Integer paymentChannelId) {
        this.paymentChannelId = paymentChannelId;
    }

    @Override
    public String toString() {
        return "PaymentSubscribeBO{" +
                "order=" + order +
                ", orderPayment=" + orderPayment +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardYear=" + cardYear +
                ", cardMonth=" + cardMonth +
                ", cardCvv='" + cardCvv + '\'' +
                ", nameOnCard='" + nameOnCard + '\'' +
                ", threeDomainTrade=" + threeDomainTrade +
                ", exemption=" + exemption +
                ", subscribeActionType=" + subscribeActionType +
                ", payId='" + payId + '\'' +
                ", storeAccount=" + storeAccount +
                ", userPayInfo=" + userPayInfo +
                ", kpSid='" + kpSid + '\'' +
                ", klarnaOrderId='" + klarnaOrderId + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", authToken='" + authToken + '\'' +
                ", paymentChannelId=" + paymentChannelId +
                '}';
    }
}
