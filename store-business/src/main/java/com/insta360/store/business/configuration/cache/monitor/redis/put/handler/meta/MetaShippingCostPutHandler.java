package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.outgoing.rpc.store.job.MetaShippingCostCachePutService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 物流信息put处理程序
 *
 * <AUTHOR>
 * @Description:
 * @date 2024/04/07
 */
@Component
public class MetaShippingCostPutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetaShippingCostPutHandler.class);

    @Autowired
    MetaShippingCostCachePutService metaShippingCostCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) {
        List<String> countries = cachePutKeyParameter.getCountries();
        if (CollectionUtils.isEmpty(countries)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。地区集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 更新物流运费信息数据
        countries.forEach(county -> metaShippingCostCachePutService.getShippingCost(InstaCountry.parse(county)));

        // 构造前端缓存更新参数
        StoreCacheDataChangeEventBO storeCacheDataChangeEventBo = new StoreCacheDataChangeEventBO();
        storeCacheDataChangeEventBo.setCountries(countries);
        return storeCacheDataChangeEventBo;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.META_SHIPPING_COST;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Collections.singletonList(CachePutType.META_SHIPPING_COST);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.FALSE;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }
}
