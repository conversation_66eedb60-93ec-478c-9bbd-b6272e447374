package com.insta360.store.service.controller.prime.auth;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.prime.bo.LwaLoginBO;
import com.insta360.store.business.prime.dto.LwaAuthLoginDTO;
import com.insta360.store.business.prime.service.PrimeDataRecordService;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.prime.vo.LwaLoginVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * service/prime/auth
 * <p>
 * 描述: 获取LWA授权令
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/16
 */
@RestController
public class PrimeAuthApi extends BaseApi {

    @Autowired
    PrimeDataRecordService primeDataRecordService;

    /**
     * 获取LWA授权
     *
     * @param lwaAuthLoginParam
     * @return
     */
    @PostMapping("/store/prime/user/lwaLogin")
    @TraceLog(logPrefix = "[prime auth]", logPrefixSub = "获取LWA授权")
    public Response<LwaLoginVO> lwaLogin(@RequestBody LwaAuthLoginDTO lwaAuthLoginParam) {
        LwaLoginBO lwaLoginBo = primeDataRecordService.lwaLogin(lwaAuthLoginParam);
        LwaLoginVO lwaLoginVo = new LwaLoginVO(lwaLoginBo);
        FeiShuMessageUtil.doSendTestProd(String.format("prime auth lwaLogin成功,data:\n%s", JSON.toJSONString(lwaLoginVo)), FeiShuGroupRobot.DevNotice);
        return Response.ok(lwaLoginVo);
    }

    /**
     * 获取LWA授权用户信息
     *
     * @param amazonUserId Amazon 用户 ID
     * @return
     */
    @GetMapping("/store/prime/user/amazonProfile")
    public Response<Object> amazonProfile(@RequestParam String amazonUserId) {
        JSONObject jsonObject = primeDataRecordService.amazonProfile(amazonUserId);
        return Response.ok(jsonObject);
    }

    /**
     * 获取token
     *
     * @param amazonUserId Amazon 用户 ID
     * @return
     */
    @GetMapping("/store/prime/user/getLwaToken")
    public Response<Object> getLwaToken(@RequestParam String amazonUserId) {
        String lwaToken = primeDataRecordService.getLwaToken(amazonUserId);
        return Response.ok(lwaToken);
    }

    /**
     * 登出
     *
     * @param amazonUserId Amazon 用户 ID
     * @return
     */
    @PostMapping("/store/prime/user/lwaLogout")
    public Response<Object> lwaLogout(@RequestParam String amazonUserId) {
        primeDataRecordService.lwaLogout(amazonUserId);
        return Response.ok();
    }

}
