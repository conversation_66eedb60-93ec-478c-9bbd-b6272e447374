package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.product;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.outgoing.rpc.store.job.ProductCachePutService;
import com.insta360.store.business.product.model.Product;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: wbt
 * @Date: 2023/11/17
 * @Description:
 */
@Component
public class ProductVideoCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductVideoCachePutHandler.class);

    @Autowired
    ProductCachePutService productCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) {
        List<Integer> productIds = cachePutKeyParameter.getProductIds();
        if (CollectionUtils.isEmpty(productIds)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。产品id集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 如果没有产品数据，则中断流程
        Collection<Product> products = productService.listByIds(productIds);
        if (CollectionUtils.isEmpty(products)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。产品集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}", this.getCachePutType(), this.isAsyncTaskable()));
        for (Product product : products) {
            // 更新产品页
            CacheConstant.COUNTIES.forEach(cacheCounties -> productCachePutService.getInfo(product.getId(),
                    cacheCounties.getCountry(), cacheCounties.getLanguage()));
        }

        // 构造前端缓存更新参数
        StoreCacheDataChangeEventBO storeCacheDataChangeEvent = new StoreCacheDataChangeEventBO();
        storeCacheDataChangeEvent.setProductEvents(this.parseProductEvent(products));
        return storeCacheDataChangeEvent;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.PRODUCT_VIDEO;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.PRODUCT_INFO);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.FALSE;
    }
}
