package com.insta360.store.service.controller.cloud.controller;

import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.security.annotation.Authorization;
import com.insta360.store.business.cloud.bo.BenefitDiscountItemBO;
import com.insta360.store.business.cloud.dto.BenefitDiscountDTO;
import com.insta360.store.business.cloud.service.StoreBenefitUseService;
import com.insta360.store.business.cloud.service.impl.helper.CloudStorageOpenPlatformHelper;
import com.insta360.store.business.cloud.service.impl.helper.StoreBenefitCheckHelper;
import com.insta360.store.business.common.constants.RedisKeyConstant;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.outgoing.rpc.cloud.dto.CloudUserConfig;
import com.insta360.store.business.outgoing.rpc.cloud.service.CloudStoragePlatformService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.cloud.format.StoreBenefitPack;
import com.insta360.store.service.controller.cloud.vo.BenefitDiscountInfoVO;
import com.insta360.store.service.controller.cloud.vo.CloudSubscribeUserCacheVO;
import com.insta360.store.service.controller.cloud.vo.StoreCloudStorageInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/16
 */
@RestController
public class StoreBenefitApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreBenefitApi.class);

    @Autowired
    StoreBenefitUseService storeBenefitUseService;

    @Autowired
    StoreBenefitPack storeBenefitPack;

    @Autowired
    UserAccountHelper userAccountHelper;

    @Autowired
    CloudStoragePlatformService cloudStoragePlatformService;

    @Autowired
    CloudStorageOpenPlatformHelper cloudStorageOpenPlatformHelper;

    @Autowired
    StoreBenefitCheckHelper storeBenefitCheckHelper;

    /**
     * 获取用户云存储订阅权益信息
     * 该接口不需要参数，通过用户登录态获取当前用户的云存储订阅权益信息。
     * 如果用户未登录，或没有云存储订阅权益记录，将返回相应的错误信息。
     *
     * @return Response<StoreCloudStorageInfoVO> 返回用户云存储订阅权益信息的响应体，
     * 如果用户有订阅权益，则返回权益信息；否则，返回空的响应体。
     */
    @Authorization
    @GetMapping("/store/cloud/getCloudStorageSubscribeInfo")
    public Response<StoreCloudStorageInfoVO> getCloudStorageSubscribeInfo() {
        // 获取当前登录的用户
        StoreAccount storeAccount = getAccessUser();

        if (Objects.isNull(storeAccount)) {
            // 如果用户未登录，返回失败响应
            return Response.failed();
        }
        // 如果存在云存储订阅权益记录，返回包含权益信息的成功响应
        return Response.ok(storeBenefitPack.doPackStoreBenefitInfo(storeAccount, getApiCountry(), getApiLanguage()));
    }

    /**
     * 请求订阅优惠信息。
     *
     * @param benefitDiscountParam 包含订阅优惠请求参数的DTO，经过验证确保数据有效性。
     * @return 返回包含优惠信息的响应体，如果请求成功，响应状态为OK，数据为包装后的优惠信息。
     */
    @Authorization
    @PostMapping(path = "/store/cloud/getSubscribeDiscount", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<BenefitDiscountInfoVO> getSubscribeDiscount(@RequestBody @Validated BenefitDiscountDTO benefitDiscountParam) {
        // 设置访问用户信息和国家信息到请求参数中
        benefitDiscountParam.setAccessUser(getAccessUser());
        benefitDiscountParam.setCountry(getApiCountry());
        // 调用服务层方法，根据传入参数获取订单项的优惠信息
        List<BenefitDiscountItemBO> benefitDiscountItemList = storeBenefitUseService.getOrderItemBenefitDiscount(benefitDiscountParam);
        // 对获取的优惠信息进行包装，然后返回
        return Response.ok(storeBenefitPack.doPackBenefitDiscountInfo(benefitDiscountItemList));
    }

    /**
     * 游客身份下单加购云服务检查
     *
     * @param email 邮箱地址
     * @return 返回响应体，如果请求成功，响应状态为OK，数据为空。
     */
    @GetMapping("/store/cloud/cloudSubscribeVisitorCheck")
    public Response<Object> cloudSubscribeVisitorCheck(@RequestParam("email") String email) {
        // 检查访客是否能购买云服务
        storeBenefitCheckHelper.allowPurchasesCheck(null, email);
        return Response.ok();
    }

    /**
     * 获取当前登录用户是否已订阅缓存结果信息
     *
     * @return 返回响应体，如果请求成功，响应状态为OK，数据为包装后的用户云服务是否已订阅信息。
     */
    @Authorization
    @GetMapping("/store/cloud/cloudSubscribeUserCheck")
    public Response<CloudSubscribeUserCacheVO> cloudSubscribeUserCheck() {
        // 获取当前登陆用户
        Integer instaAccountId = getAccessUser().getInstaAccount();
        // 查询当前登录用户是否为云服务订阅用户，如缓存中存在值则为'权益生效中'的订阅用户，反之则是'未订阅'或'已失效'
        Object value = null;
        try {
            value = RedisTemplateUtil.getValue(RedisKeyConstant.STORE_CLOUD_SUBSCRIBE_USER_KEY + instaAccountId);
        } catch (Exception e) {
            LOGGER.error(String.format("获取商城订阅用户缓存结果异常. userId:{%s}", instaAccountId), e);
        }
        // 是否已订阅
        Boolean isSubscribed = true;
        if (Objects.isNull(value)) {
            isSubscribed = cloudStorageOpenPlatformHelper.isSubscribeUser(instaAccountId);
        }
        return Response.ok(new CloudSubscribeUserCacheVO(isSubscribed));
    }

    /**
     * 获取用户数据存储地地区配置
     *
     * @return
     */
    @GetMapping("/store/cloud/getUserRegionConfig")
    public Response<CloudUserConfig> getUserRegionConfig(@RequestParam(required = false) String email) {
        // 当前登陆用户
        StoreAccount accessUser = getAccessUser();
        // 用户ID
        Integer userId = Objects.nonNull(accessUser) ? accessUser.getInstaAccount() : null;
        if (Objects.isNull(userId)) {
            userId = userAccountHelper.getInstaAccountId(email);
        }
        // 获取用户存储地区配置
        return cloudStoragePlatformService.getUserConfig(userId);
    }
}
