package com.insta360.store.business.cloud.service.impl.handler.benefit;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.cloud.constant.StoreBenefitConstant;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.enums.SkuSubscribeType;
import com.insta360.store.business.cloud.model.CloudStorageInsuranceBenefitBind;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 月刊权益处理类
 * @Date 2024/5/14
 */
@Scope("prototype")
@Component
public class MonthlyBenefitHandler extends BaseStoreBenefitCoreHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(MonthlyBenefitHandler.class);

    @Override
    public void handle(StoreBenefitContext context) {
        super.handle(context);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void purchaseSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 月度首购场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 非延保上线地区不下发权益
        if (!extendOnlineRegionCheck(storeBenefit.parseRegion())) {
            LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 月度首购场景业务处理,非延保上线地区不下发权益", storeBenefit.getUserId());
            return;
        }
        // 停用现在的所有权益
        this.stopAllBenefit();
        // 下发延保权益
        cloudStorageStoreBenefitDetailService.save(this.buildExtendBenefitDetail());
        // 邮件发送
        this.sendBenefitEmail();
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 月度首购场景业务处理结束...", storeBenefit.getUserId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void renewSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 月度续订场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 获取期数，从第13次开始的每隔12次（13、25、37等）都需重新下发延保权益
        Integer period = storeBenefit.getPeriodNumber();
        if ((period - StoreBenefitConstant.INITIAL_PERIOD) % StoreBenefitConstant.ONE_CYCLE == 0) {
            if (!extendOnlineRegionCheck(storeBenefit.parseRegion())) {
                LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 月度续订场景业务处理,非延保上线地区不下发权益", storeBenefit.getUserId());
                return;
            }
            // 停用现在的所有权益
            this.stopAllBenefit();
            // 下发新的延保权益
            cloudStorageStoreBenefitDetailService.save(this.buildExtendBenefitDetail());
            // 邮件发送
            this.sendBenefitEmail();
            LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 月度续订场景业务处理结束...", storeBenefit.getUserId());
        }
    }

    @Override
    public void upgradeSceneHandle(StoreBenefitContext storeBenefitContext) {
        // 月度已是当前最低等级
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 月度升级业务不予处理...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void downgradeSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 月度降级场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 年度降级 -> 月度
        if (SkuSubscribeType.YEARLY.getType().equals(benefitLastChange.getBeforeSubscribeType())) {
            // 第一、回收配件折扣权益
            this.stopOldBenefitDetail(subscribeDiscountBenefitDetail);
            // 第二、回收care权益
            if (Objects.nonNull(careBenefitDetail)) {
                // 停用旧的care权益
                this.stopOldBenefitDetail(careBenefitDetail);
                // 禁用已绑定的代金券
                this.disableGiftCard(storeBenefit.getUserId());
                // 如有绑定care则进行作废
                CloudStorageInsuranceBenefitBind insuranceBenefitBind = cloudStorageInsuranceBenefitBindService.getInsuranceBenefitBind(careBenefitDetail.getId(), BenefitType.CARE.getType());
                if (Objects.nonNull(insuranceBenefitBind)) {
                    this.insuranceInvalid(insuranceBenefitBind);
                }
            }
        }
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 月度降级场景业务处理结束...", storeBenefit.getUserId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void refundSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 月度退款场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 权益未过期则设置为'过期'
        this.monthlyBenefitEnd(true);
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 月度退款场景业务处理结束...", storeBenefit.getUserId());
    }

    @Override
    public void expiredSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 月度过期场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 权益未过期则设置为'过期'
        Integer periodNumber = storeBenefit.getPeriodNumber();
        // 月度订阅自然过期权益回收规则：连续订阅期数为12的倍数，则回收权益时无需扣减延保的赠送时长，反之则需扣减延保的赠送时长
        boolean recycleNow = periodNumber % StoreBenefitConstant.ONE_CYCLE != 0;
        // 回收权益
        this.monthlyBenefitEnd(recycleNow);
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 月度过期场景业务处理结束...", storeBenefit.getUserId());
    }

    /**
     * 月度权益终止
     */
    private void monthlyBenefitEnd(Boolean recycleNow) {
        // 停用现在的所有权益
        this.stopAllBenefit();
        // 扣减已赠送的延保时长
        if (Objects.nonNull(extendBenefitDetail) && recycleNow) {
            Optional.ofNullable(cloudStorageInsuranceBenefitBindService.getInsuranceBenefitBind(extendBenefitDetail.getId(), BenefitType.EXTEND.getType()))
                    .ifPresent(this::insuranceInvalid);
        }
    }
}
