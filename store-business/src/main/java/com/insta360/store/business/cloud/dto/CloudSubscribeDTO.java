package com.insta360.store.business.cloud.dto;

import com.insta360.store.business.cloud.enums.SubscribeActionType;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2024/05/24
 * @Description:
 */
public class CloudSubscribeDTO implements Serializable {

    /**
     * 信用卡名称
     */
    private String creditCardName;

    /**
     * 卡信息
     */
    private String cardData;

    /**
     * 订阅动作类型
     */
    private SubscribeActionType subscribeActionType;

    /**
     * 信用卡卡号
     */
    private String cardNumber;

    /**
     * 信用卡年份
     */
    private Integer cardYear;

    /**
     * 信用卡月份
     */
    private Integer cardMonth;

    /**
     * 信用卡CVV
     */
    private String cardCvv;

    /**
     * 是否走3D校验（信用卡支付）
     */
    private Boolean threeDomainTrade;

    /**
     * 是否豁免
     */
    private Boolean exemption;

    /**
     * 取消订阅原因
     */
    private String cancelReason;

    /**
     * 令牌信息
     */
    private String forterTokenCookie;

    /**
     * 持卡人姓名
     */
    private String nameOnCard;

    /**
     * 信用卡前六位
     */
    private String bin;

    /**
     * 信用卡后四位
     */
    private String lastFourDigits;

    /**
     * 卡种
     */
    private String cardType;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * auth 类型
     */
    private String adaptiveAuthType;

    /**
     * 支付方式
     */
    private Integer payMode;

    /**
     * 订阅地址
     */
    private SubscribeBillingAddressDTO subscribeBillingAddress;

    public String getCreditCardName() {
        return creditCardName;
    }

    public void setCreditCardName(String creditCardName) {
        this.creditCardName = creditCardName;
    }

    public String getCardData() {
        return cardData;
    }

    public void setCardData(String cardData) {
        this.cardData = cardData;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public Integer getCardYear() {
        return cardYear;
    }

    public void setCardYear(Integer cardYear) {
        this.cardYear = cardYear;
    }

    public Integer getCardMonth() {
        return cardMonth;
    }

    public void setCardMonth(Integer cardMonth) {
        this.cardMonth = cardMonth;
    }

    public String getCardCvv() {
        return cardCvv;
    }

    public void setCardCvv(String cardCvv) {
        this.cardCvv = cardCvv;
    }

    public Boolean getThreeDomainTrade() {
        return threeDomainTrade;
    }

    public void setThreeDomainTrade(Boolean threeDomainTrade) {
        this.threeDomainTrade = threeDomainTrade;
    }

    public Boolean getExemption() {
        return exemption;
    }

    public void setExemption(Boolean exemption) {
        this.exemption = exemption;
    }

    public SubscribeActionType getSubscribeActionType() {
        return subscribeActionType;
    }

    public void setSubscribeActionType(SubscribeActionType subscribeActionType) {
        this.subscribeActionType = subscribeActionType;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public String getForterTokenCookie() {
        return forterTokenCookie;
    }

    public void setForterTokenCookie(String forterTokenCookie) {
        this.forterTokenCookie = forterTokenCookie;
    }

    public String getNameOnCard() {
        return nameOnCard;
    }

    public void setNameOnCard(String nameOnCard) {
        this.nameOnCard = nameOnCard;
    }

    public String getBin() {
        return bin;
    }

    public void setBin(String bin) {
        this.bin = bin;
    }

    public String getLastFourDigits() {
        return lastFourDigits;
    }

    public void setLastFourDigits(String lastFourDigits) {
        this.lastFourDigits = lastFourDigits;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getAdaptiveAuthType() {
        return adaptiveAuthType;
    }

    public void setAdaptiveAuthType(String adaptiveAuthType) {
        this.adaptiveAuthType = adaptiveAuthType;
    }

    public SubscribeBillingAddressDTO getSubscribeBillingAddress() {
        return subscribeBillingAddress;
    }

    public void setSubscribeBillingAddress(SubscribeBillingAddressDTO subscribeBillingAddress) {
        this.subscribeBillingAddress = subscribeBillingAddress;
    }

    public Integer getPayMode() {
        return payMode;
    }

    public void setPayMode(Integer payMode) {
        this.payMode = payMode;
    }

    @Override
    public String toString() {
        return "CloudSubscribeDTO{" +
                "creditCardName='" + creditCardName + '\'' +
                ", cardData='" + cardData + '\'' +
                ", subscribeActionType=" + subscribeActionType +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardYear=" + cardYear +
                ", cardMonth=" + cardMonth +
                ", cardCvv='" + cardCvv + '\'' +
                ", threeDomainTrade=" + threeDomainTrade +
                ", exemption=" + exemption +
                ", cancelReason='" + cancelReason + '\'' +
                ", forterTokenCookie='" + forterTokenCookie + '\'' +
                ", nameOnCard='" + nameOnCard + '\'' +
                ", bin='" + bin + '\'' +
                ", lastFourDigits='" + lastFourDigits + '\'' +
                ", cardType='" + cardType + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", adaptiveAuthType='" + adaptiveAuthType + '\'' +
                ", payMode=" + payMode +
                ", subscribeBillingAddress=" + subscribeBillingAddress +
                '}';
    }
}
