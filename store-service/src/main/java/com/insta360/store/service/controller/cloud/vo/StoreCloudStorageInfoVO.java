package com.insta360.store.service.controller.cloud.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 商城权益明细VO
 * @Date 2024/5/16
 */
public class StoreCloudStorageInfoVO implements Serializable {

    /**
     * 权益状态
     * @see com.insta360.store.business.cloud.enums.BenefitStatus
     */
    private String benefitStatus;

    /**
     * 平台来源
     */
    private String platformSource;

    /**
     * 用户数据存储地区
     */
    private String storageRegion;

    /**
     * 容量类型
     * @see com.insta360.store.business.cloud.enums.BenefitCapacityType
     */
    private Integer capacityType;

    /**
     * 配件折扣权益信息
     */
    private StoreBenefitInfoVO discountBenefitInfo;

    /**
     * care权益信息
     */
    private StoreBenefitInfoVO careBenefitInfo;

    /**
     * 延保权益信息
     */
    private StoreBenefitInfoVO extendBenefitInfo;

    /**
     * 云服务订阅信息
     */
    private CloudSubscribeInfoVO cloudSubscribeInfo;

    /**
     * 下一台相机优惠标记
     * true：存在 false：不存在
     */
    private Boolean nextOneCameraMark;

    /**
     * 缺失支付ID
     */
    private Boolean notPayIdMark;

    public String getBenefitStatus() {
        return benefitStatus;
    }

    public void setBenefitStatus(String benefitStatus) {
        this.benefitStatus = benefitStatus;
    }

    public String getPlatformSource() {
        return platformSource;
    }

    public void setPlatformSource(String platformSource) {
        this.platformSource = platformSource;
    }

    public StoreBenefitInfoVO getDiscountBenefitInfo() {
        return discountBenefitInfo;
    }

    public void setDiscountBenefitInfo(StoreBenefitInfoVO discountBenefitInfo) {
        this.discountBenefitInfo = discountBenefitInfo;
    }

    public StoreBenefitInfoVO getCareBenefitInfo() {
        return careBenefitInfo;
    }

    public void setCareBenefitInfo(StoreBenefitInfoVO careBenefitInfo) {
        this.careBenefitInfo = careBenefitInfo;
    }

    public StoreBenefitInfoVO getExtendBenefitInfo() {
        return extendBenefitInfo;
    }

    public void setExtendBenefitInfo(StoreBenefitInfoVO extendBenefitInfo) {
        this.extendBenefitInfo = extendBenefitInfo;
    }

    public CloudSubscribeInfoVO getCloudSubscribeInfo() {
        return cloudSubscribeInfo;
    }

    public void setCloudSubscribeInfo(CloudSubscribeInfoVO cloudSubscribeInfo) {
        this.cloudSubscribeInfo = cloudSubscribeInfo;
    }

    public String getStorageRegion() {
        return storageRegion;
    }

    public void setStorageRegion(String storageRegion) {
        this.storageRegion = storageRegion;
    }

    public Integer getCapacityType() {
        return capacityType;
    }

    public void setCapacityType(Integer capacityType) {
        this.capacityType = capacityType;
    }

    public Boolean getNextOneCameraMark() {
        return nextOneCameraMark;
    }

    public void setNextOneCameraMark(Boolean nextOneCameraMark) {
        this.nextOneCameraMark = nextOneCameraMark;
    }

    public Boolean getNotPayIdMark() {
        return notPayIdMark;
    }

    public void setNotPayIdMark(Boolean notPayIdMark) {
        this.notPayIdMark = notPayIdMark;
    }

    @Override
    public String toString() {
        return "StoreCloudStorageInfoVO{" +
                "benefitStatus='" + benefitStatus + '\'' +
                ", platformSource='" + platformSource + '\'' +
                ", storageRegion='" + storageRegion + '\'' +
                ", capacityType=" + capacityType +
                ", discountBenefitInfo=" + discountBenefitInfo +
                ", careBenefitInfo=" + careBenefitInfo +
                ", extendBenefitInfo=" + extendBenefitInfo +
                ", cloudSubscribeInfo=" + cloudSubscribeInfo +
                ", nextOneCameraMark=" + nextOneCameraMark +
                ", hasPayIdMark=" + notPayIdMark +
                '}';
    }
}
