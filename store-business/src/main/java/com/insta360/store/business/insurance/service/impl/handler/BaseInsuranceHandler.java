package com.insta360.store.business.insurance.service.impl.handler;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.cloud.enums.BenefitBusinessTypeType;
import com.insta360.store.business.cloud.exception.CloudStorageBenefitErrorCode;
import com.insta360.store.business.cloud.service.CloudStorageInsuranceBenefitBindService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitDetailService;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.bo.InsuranceBO;
import com.insta360.store.business.insurance.bo.InsuranceInfoBO;
import com.insta360.store.business.insurance.config.InsuranceCommonConfiguration;
import com.insta360.store.business.insurance.email.InsuranceEmailFactory;
import com.insta360.store.business.insurance.enums.InsuranceOriginType;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.insurance.model.*;
import com.insta360.store.business.insurance.service.*;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceCheckHelper;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceHelper;
import com.insta360.store.business.insurance.service.impl.helper.generator.ActivationCardCodeGenerator;
import com.insta360.store.business.insurance.service.impl.helper.http.CustomerServiceHelper;
import com.insta360.store.business.insurance.service.impl.helper.http.DeviceInfoHelper;
import com.insta360.store.business.order.email.BaseOrderEmail;
import com.insta360.store.business.order.email.OrderEmailFactory;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDeliveryUniqueCode;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderItemBind;
import com.insta360.store.business.order.service.*;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceActivationInfo;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceInfo;
import com.insta360.store.business.trade.model.StoreEmailSendRule;
import com.insta360.store.business.trade.service.StoreEmailSendRuleService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2024-02-01 12:05
 */
public abstract class BaseInsuranceHandler implements InsuranceBindInterface, InsuranceCheckInterface, InsuranceActivationInterface, BusinessServiceInterface {

    public static final Logger LOGGER = LoggerFactory.getLogger(BaseInsuranceHandler.class);

    @Autowired
    protected OrderDeliveryUniqueCodeService orderDeliveryUniqueCodeService;

    @Autowired
    protected InsuranceHelper insuranceHelper;

    @Autowired
    protected InsuranceCheckHelper insuranceCheckHelper;

    @Autowired
    protected DeviceInfoHelper deviceInfoHelper;

    @Autowired
    protected CareInsuranceService careInsuranceService;

    @Autowired
    protected CarePlusInsuranceService carePlusInsuranceService;

    @Autowired
    protected InsuranceEmailFactory insuranceEmailFactory;

    @Autowired
    protected InsuranceCommonConfiguration insuranceCommonConfig;

    @Autowired
    protected OrderDeliveryService orderDeliveryService;

    @Autowired
    protected OrderService orderService;

    @Autowired
    protected InsuranceServiceTypeService insuranceServiceTypeService;

    @Autowired
    protected CareInsuranceActivationCardService careInsuranceActivationCardService;

    @Autowired
    protected OrderItemService orderItemService;

    @Autowired
    protected ServiceCommodityBindService serviceCommodityBindService;

    @Autowired
    protected OrderPaymentService orderPaymentService;

    @Autowired
    protected OrderEmailFactory orderEmailFactory;

    @Autowired
    protected StoreEmailSendRuleService storeEmailSendRuleService;

    @Autowired
    protected CloudStorageStoreBenefitDetailService cloudStorageStoreBenefitDetailService;

    @Autowired
    protected CloudStorageInsuranceBenefitBindService cloudStorageInsuranceBenefitBindService;

    @Autowired
    protected CustomerServiceHelper customerServiceHelper;

    @Autowired
    protected ActivationCardCodeGenerator activationCardCodeGenerator;

    @Autowired
    protected ServiceCommodityRuleService serviceCommodityRuleService;

    @Override
    public void checkInsurance(InsuranceBO insuranceParam) {
        String deviceSerial = insuranceParam.getDeviceSerial();

        // 校验是否为二手机 &  验证序列号是否存在
        String appDeviceType = this.checkCamera(deviceSerial);

        // 是否为相机 -判断是什么相机类型
        if (!insuranceCheckHelper.isCamera(insuranceParam.getDeviceSerial(), insuranceParam.getInsuranceServiceCommodityBind())) {
            LOGGER.error(String.format("单独购买增值服务-校验相机类型失败,序列号：%s", insuranceParam.getDeviceSerial()));
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }

        // 校验是否在上线国家或者地区
        insuranceParam.setOrigin(InsuranceOriginType.store.name());
        this.checkOnLine(insuranceParam);

        String serviceType = insuranceParam.getInsuranceType();
        // check下单没支付
        insuranceHelper.checkAlreadyNotPay(deviceSerial, serviceType);

        OrderDeliveryUniqueCode orderDeliveryUniqueCode = orderDeliveryUniqueCodeService.getByUniqueCode(deviceSerial);
        // 过滤随机购买的增值服务
        insuranceCheckHelper.checkBuyWithCamera(serviceType, orderDeliveryUniqueCode);

        // 增值服务服务 一个序列号只让绑定一次
        this.checkSerial(deviceSerial);

        InsuranceServiceCommodityBind commodityBind = insuranceParam.getInsuranceServiceCommodityBind();
        String deviceType = commodityBind.getDeviceType();
        // 是否属于该保险机型的序列号
        if (!deviceType.equals(appDeviceType)) {
            throw new InstaException(InsuranceErrorCode.DeviceMismatchException);
        }

        // 激活时间校验
        Integer activationDay = commodityBind.getActivationDay();

        // 判断是否为不需要激活的相机
        if (insuranceCheckHelper.notNeedActivation(insuranceParam.getPurchaseTime(),
                orderDeliveryUniqueCode, deviceType, activationDay)) {
            return;
        }

        // 需要激活的相机
        DeviceActivationInfo deviceActivationInfo = deviceInfoHelper.getDeviceActivationInfo(deviceSerial);
        if (Objects.isNull(deviceActivationInfo)) {
            return;
        }
        // 激活时间
        if (!deviceActivationInfo.getCreateTime().plusDays(activationDay).isAfter(LocalDateTime.now())) {
            throw new InstaException(InsuranceErrorCode.DeviceActivationTimeException);
        }
    }

    @Override
    public Boolean checkAllowBinding(DeviceActivationInfo deviceActivationInfo) {
        // 未激活直接返回false
        if (deviceActivationInfo == null) {
            return false;
        }

        InsuranceServiceType insuranceServiceType = insuranceServiceTypeService.getByServiceType(getServiceType().name());
        if (Objects.isNull(insuranceServiceType)) {
            return true;
        }
        List<InsuranceServiceCommodityBind> insuranceServiceCommodityBindList =
                serviceCommodityBindService.getByDeviceTypeAndService(deviceActivationInfo.getDeviceType(), insuranceServiceType.getId());
        if (CollectionUtils.isEmpty(insuranceServiceCommodityBindList)) {
            return true;
        }
        InsuranceServiceCommodityBind insuranceServiceCommodityBind = insuranceServiceCommodityBindList.get(0);
        Integer activationTimeLimit = insuranceServiceCommodityBind.getActivationDay();
        return !deviceActivationInfo.getCreateTime().plusDays(activationTimeLimit).isAfter(LocalDateTime.now());
    }

    @Override
    public void CheckDeviceSerialExist(String deviceSerial, String insuranceType, String deviceType) {
        String appDeviceType = deviceInfoHelper.getDeviceType(deviceSerial);

        InsuranceServiceType insuranceServiceType = insuranceServiceTypeService.getByServiceType(insuranceType);
        if (Objects.isNull(insuranceServiceType)) {
            throw new InstaException(InsuranceErrorCode.InsuranceNotFoundException);
        }

        List<InsuranceServiceCommodityBind> commodityBindList = serviceCommodityBindService.getByDeviceTypeAndService(appDeviceType, insuranceServiceType.getId());
        if (CollectionUtils.isEmpty(commodityBindList)) {
            throw new InstaException(InsuranceErrorCode.DeviceNotBindInsuranceException);
        }

        if (!deviceType.equals(appDeviceType)) {
            throw new InstaException(InsuranceErrorCode.DeviceMismatchException);
        }
    }

    @Override
    public void checkSerial(String deviceSerial) {
        // Care服务 一个序列号只让绑定一次
        CareInsurance careInsurance = careInsuranceService.getCareInsurance(deviceSerial);
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(deviceSerial);
        CareInsuranceActivationCard careInsuranceActivationCard = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
        if (Objects.nonNull(careInsurance) || Objects.nonNull(carePlusInsurance) || Objects.nonNull(careInsuranceActivationCard)) {
            LOGGER.error(String.format("序列号:[%s]已经绑定过care,不可再次绑定[%s]。", deviceSerial, getServiceType().name()));
            throw new InstaException(InsuranceErrorCode.DeviceAlreadyBindException);
        }
    }

    /**
     * 获取激活时间
     *
     * @param deviceSerial
     * @param store
     * @return
     */
    public LocalDateTime getCreateTime(String deviceSerial, Boolean store) {
        DeviceActivationInfo deviceActivationInfo = deviceInfoHelper.getDeviceActivationInfo(deviceSerial);
        if (store && this.checkAllowBinding(deviceActivationInfo)) {
            throw new InstaException(InsuranceErrorCode.DeviceActivationTimeException);
        }

        return Objects.isNull(deviceActivationInfo) ? LocalDateTime.now() : deviceActivationInfo.getCreateTime();
    }

    /**
     * 单独绑定通用校验
     *
     * @param deviceSerial
     */
    public String checkCamera(String deviceSerial) {
        // 校验是否为二手机
        if (customerServiceHelper.getSecondHandCamera(deviceSerial)) {
            throw new InstaException(InsuranceErrorCode.DeviceSecondHandCameraException);
        }

        DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(deviceSerial);
        if (deviceInfo == null) {
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }

        // 二手机不允许购买
        if (deviceInfo.getSecondhandFlag()) {
            throw new InstaException(InsuranceErrorCode.DeviceSecondHandCameraException);
        }

        return deviceInfo.getDeviceType();
    }

    /**
     * 序列号校验
     *
     * @param deviceSerial
     * @param deviceType
     * @param serviceType
     */
    public void isCameraCheck(String deviceSerial, String deviceType, ServiceType serviceType) {
        List<InsuranceServiceCommodityBind> commodityBinds = serviceCommodityBindService.getByDeviceTypeAndService(deviceType, serviceType.getServiceId());
        if (CollectionUtils.isEmpty(commodityBinds)) {
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotSupportException);
        }

        InsuranceServiceCommodityBind insuranceServiceCommodityBind = commodityBinds.get(0);

        // 判断是否为主机
        LOGGER.info(String.format("[手动激活]序列号校验是否为主机,deviceType:%s,serviceType:%s", deviceType, serviceType));

        // 是否为相机 -判断是什么相机类型
        if (!insuranceCheckHelper.isCamera(deviceSerial, insuranceServiceCommodityBind)) {
            LOGGER.error(String.format("[手动激活]校验相机类型失败,deviceType:%s,serviceType:%s", deviceType, serviceType));
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }
    }

    /**
     * 保存商城邮件发送规则，并发送邮件
     *
     * @param order      订单
     * @param orderEmail 订单邮件
     */
    public void saveStoreEmailSendRuleSendEmail(Order order, BaseOrderEmail orderEmail) {
        String templateName = orderEmail.getTemplateName();
        StoreEmailSendRule storeEmailSendRule = storeEmailSendRuleService.getTemplateSendRuleByOrderId(order.getId(), templateName);
        if (storeEmailSendRule == null) {
            LOGGER.info(String.format("[发送推广邮件]规则验证通过,发送模板[%s],邮箱[%s]",
                    orderEmail.getTemplateName(), order.getContactEmail()));
            orderEmail.doSend(order.getContactEmail());
            storeEmailSendRule = new StoreEmailSendRule();
            storeEmailSendRule.setOrderId(order.getId());
            storeEmailSendRule.setTemplateKey(orderEmail.getTemplateName());
            storeEmailSendRule.setCreateTime(LocalDateTime.now());
            storeEmailSendRule.setUpdateTime(LocalDateTime.now());
            storeEmailSendRuleService.save(storeEmailSendRule);
        } else {
            storeEmailSendRule.setUpdateTime(LocalDateTime.now());
            storeEmailSendRuleService.updateById(storeEmailSendRule);
            LOGGER.info(String.format("[发送推广邮件]已经发送过,storeEmailSendRule存在,本次不再发送模板[%s],邮箱[%s]",
                    orderEmail.getTemplateName(), order.getContactEmail()));
        }
    }

    /**
     * 构建care绑定信息
     *
     * @param insuranceParam
     * @param cloudBind
     * @param insuranceNumber
     * @param activateCode
     * @return
     */
    public CareInsurance buildCareInsurance(InsuranceBO insuranceParam, Boolean cloudBind, String insuranceNumber, String activateCode) {
        CareInsurance careInsurance = new CareInsurance();
        careInsurance.setOrderNumber(insuranceParam.getOrderNumber());
        careInsurance.setPhone(insuranceParam.getPhone());
        careInsurance.setEmail(insuranceParam.getEmail());
        careInsurance.setArea(insuranceParam.getArea());
        careInsurance.setDeviceSerial(insuranceParam.getDeviceSerial());
        careInsurance.setDeviceType(insuranceParam.getDeviceType());
        careInsurance.setOrderOrigin(insuranceParam.getOrderOrigin());
        careInsurance.setActivateCode(activateCode);
        careInsurance.setInsuranceNumber(insuranceNumber);
        careInsurance.setInsuranceType(getServiceType().name());
        careInsurance.setInsuranceEnabled(true);
        careInsurance.setAutoActivation(true);
        careInsurance.setDisabled(false);
        careInsurance.setCloudBind(cloudBind);
        careInsurance.setCreateTime(LocalDateTime.now());
        careInsurance.setBindTime(insuranceParam.getActivateTime());
        careInsurance.setExpireTime(insuranceServiceTypeService.getExpireDay(getServiceType().name(), insuranceParam.getActivateTime()));
        return careInsurance;
    }

    /**
     * 构建care+信息
     *
     * @param insuranceParam
     * @param cloudBind
     * @param insuranceNumber
     * @param activateCode
     * @return
     */
    public CarePlusInsurance buildCarePlusInsurance(InsuranceBO insuranceParam, Boolean cloudBind, String insuranceNumber, String activateCode) {
        CarePlusInsurance carePlusInsurance = new CarePlusInsurance();
        carePlusInsurance.setOrderNumber(insuranceParam.getOrderNumber());
        carePlusInsurance.setPhone(insuranceParam.getPhone());
        carePlusInsurance.setEmail(insuranceParam.getEmail());
        carePlusInsurance.setArea(insuranceParam.getArea());
        carePlusInsurance.setDeviceSerial(insuranceParam.getDeviceSerial());
        carePlusInsurance.setDeviceType(insuranceParam.getDeviceType());
        carePlusInsurance.setOrderOrigin(insuranceParam.getOrderOrigin());
        carePlusInsurance.setActivateCode(activateCode);
        carePlusInsurance.setInsuranceNumber(insuranceNumber);
        carePlusInsurance.setInsuranceType(getServiceType().name());
        carePlusInsurance.setAutoActivation(true);
        carePlusInsurance.setDisabled(false);
        carePlusInsurance.setCloudBind(cloudBind);
        carePlusInsurance.setCreateTime(LocalDateTime.now());
        carePlusInsurance.setUpdateTime(LocalDateTime.now());
        carePlusInsurance.setBindTime(insuranceParam.getActivateTime());
        carePlusInsurance.setExpireTime(insuranceServiceTypeService.getExpireDay(getServiceType().name(), insuranceParam.getActivateTime()));
        return carePlusInsurance;
    }

    /**
     * 构建延保信息
     *
     * @param insuranceParam
     * @param cloudBind
     * @param insuranceNumber
     * @return
     */
    public ExtendInsurance buildExtendInsurance(InsuranceBO insuranceParam, Boolean cloudBind, String insuranceNumber) {
        ExtendInsurance extendInsurance = new ExtendInsurance();
        extendInsurance.setOrderNumber(insuranceParam.getOrderNumber());
        extendInsurance.setPhone(insuranceParam.getPhone());
        extendInsurance.setEmail(insuranceParam.getEmail());
        extendInsurance.setArea(insuranceParam.getArea());
        extendInsurance.setDeviceSerial(insuranceParam.getDeviceSerial());
        extendInsurance.setDeviceType(insuranceParam.getDeviceType());
        extendInsurance.setInsuranceNumber(insuranceNumber);
        extendInsurance.setEnabled(true);
        extendInsurance.setCloudBind(cloudBind);
        extendInsurance.setBindTime(insuranceParam.getActivateTime());
        extendInsurance.setExpireTime(insuranceServiceTypeService.getExpireDay(getServiceType().name(), insuranceParam.getActivateTime()));
        extendInsurance.setCreateTime(LocalDateTime.now());
        extendInsurance.setUpdateTime(LocalDateTime.now());
        return extendInsurance;
    }

    /**
     * 构建实体卡信息
     *
     * @param insuranceType
     * @param deviceType
     * @param insuranceParam
     * @param createTime
     * @return
     */
    public CareInsuranceActivationCard buildCareInsuranceActivationCard(String insuranceType, InsuranceBO insuranceParam, String deviceType, LocalDateTime createTime) {
        CareInsuranceActivationCard careInsuranceActivationCard = new CareInsuranceActivationCard();
        careInsuranceActivationCard.setInsuranceNumber(activationCardCodeGenerator.generateInsuranceNumber());
        careInsuranceActivationCard.setInsuranceType(insuranceType);
        careInsuranceActivationCard.setActivationCode(insuranceParam.getActivationCode());
        careInsuranceActivationCard.setDeviceSerial(insuranceParam.getDeviceSerial());
        careInsuranceActivationCard.setDeviceType(deviceType);
        careInsuranceActivationCard.setCountry(insuranceParam.getArea());
        careInsuranceActivationCard.setName(insuranceParam.getName());
        careInsuranceActivationCard.setPhone(insuranceParam.getPhone());
        careInsuranceActivationCard.setEmail(insuranceParam.getEmail());
        careInsuranceActivationCard.setBindTime(createTime);
        careInsuranceActivationCard.setExpireTime(insuranceServiceTypeService.getExpireDay(insuranceType, createTime));
        careInsuranceActivationCard.setCreateTime(LocalDateTime.now());
        return careInsuranceActivationCard;
    }

    /**
     * 校验care绑定
     *
     * @param serial
     */
    public void checkSerialBind(String serial, BenefitBusinessTypeType businessType) {
        CareInsurance careInsurance = careInsuranceService.getCareInsurance(serial);
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(serial);
        CareInsuranceActivationCard careInsuranceActivationCard = careInsuranceActivationCardService.getByDeviceSerial(serial);
        if (Objects.nonNull(carePlusInsurance)) {
            InstaException instaException = new InstaException(InsuranceErrorCode.DeviceAlreadyBindCareException);
            instaException.putErrorData("deviceType", carePlusInsurance.getDeviceType());
            instaException.putErrorData("insuranceType", carePlusInsurance.getInsuranceType());
            instaException.putErrorData("cloudType", businessType.name());
            throw instaException;
        }

        if (Objects.nonNull(careInsurance)) {
            InstaException instaException = new InstaException(InsuranceErrorCode.DeviceAlreadyBindCareException);
            instaException.putErrorData("deviceType", careInsurance.getDeviceType());
            instaException.putErrorData("insuranceType", careInsurance.getInsuranceType());
            instaException.putErrorData("cloudType", businessType.name());
            throw instaException;
        }

        if (Objects.nonNull(careInsuranceActivationCard)) {
            InstaException instaException = new InstaException(InsuranceErrorCode.DeviceAlreadyBindCareException);
            instaException.putErrorData("deviceType", careInsuranceActivationCard.getDeviceType());
            instaException.putErrorData("insuranceType", careInsuranceActivationCard.getInsuranceType());
            instaException.putErrorData("cloudType", businessType.name());
            throw instaException;
        }
    }

    @Override
    public void presentExtendGift(String deviceSerial, String area, String email, String deviceType, Integer extendDays) {

    }

    @Override
    public InsuranceBO checkActivationCode(String deviceSerial, String activationCode) {
        return null;
    }

    @Override
    public String createUrl(InsuranceBO insuranceParam) {
        return null;
    }

    @Override
    public List<InsuranceInfoBO> getBindInsuranceInfoList(OrderItemBind orderItemBind) {
        return null;
    }

    @Override
    public void autoActivationInsurance(InsuranceBO insuranceParam) {

    }

    @Override
    public void autoInvalidInsurance(String insuranceType, List<String> serials, Integer orderId) {

    }

    @Override
    public void checkOnLine(InsuranceBO insuranceParam) {

    }

    @Override
    public void useCard(CareInsuranceActivationCard careInsuranceActivationCard, LocalDateTime useTime) {

    }

    @Override
    public void sendPromoteEmail(OrderDeliveryUniqueCode orderDeliveryUniqueCode, List<OrderItem> orderItems, Order order) {

    }

    @Override
    public void useVirtualService(String deviceSerial, LocalDateTime useTime) {

    }

    @Override
    public Boolean checkInsuranceBySerials(List<String> serials) {
        return null;
    }

    @Override
    public void businessBindService(InsuranceBO insuranceParam) {
    }

    @Override
    public void cloudCancelService(String serial) {

    }
}
