package com.insta360.store.service.controller.review.format;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.configuration.utils.StrUtils;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.review.model.*;
import com.insta360.store.business.review.service.ReviewRateCountService;
import com.insta360.store.business.review.service.ReviewRateLevelCountService;
import com.insta360.store.business.review.service.ReviewRateLevelService;
import com.insta360.store.business.review.service.ReviewResourceService;
import com.insta360.store.business.review.service.impl.hepler.ReviewResourceHelper;
import com.insta360.store.service.controller.review.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/07/05
 * @Description:
 */
@Component
public class ReviewPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReviewPack.class);

    private static final String REVIEW_NICK_NAME_PRIVATE = "**";

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    ReviewRateCountService reviewRateCountService;

    @Autowired
    ReviewRateLevelService reviewRateLevelService;

    @Autowired
    ReviewRateLevelCountService reviewRateLevelCountService;

    @Autowired
    ReviewResourceService reviewResourceService;

    @Autowired
    ReviewResourceHelper reviewResourceHelper;

    /**
     * 封装评论信息
     *
     * @param reviews
     * @return
     */
    public  List<ReviewVO> doPackReviewInfos(List<Review> reviews, InstaCountry country, InstaLanguage language) {
        List<ReviewBO> reviewBoList = secretNickName(reviews, country)
                .stream()
                .map(review -> {
                    CommodityInfo commodityInfo = commodityInfoService.getInfoDefaultEnglish(review.getCommodityId(), language);
                    if (commodityInfo == null) {
                        String message = "套餐多语言配置信息缺失。产品Id:" + review.getProductId() + ", 套餐Id: " + review.getCommodityId() + ", 语言:" + language;
                        FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY, FeiShuAtUser.LCN, FeiShuAtUser.ZXY,
                                FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
                    }

                    ReviewBO reviewBO = new ReviewBO(review);
                    reviewBO.setReviewTime(review.getCreateTime());
                    reviewBO.setCommodityName(commodityInfo == null ? "" : commodityInfo.getName());
                    return reviewBO;
                }).collect(Collectors.toList());

        if (CollUtil.isEmpty(reviewBoList)) {
            return reviewBoList.stream().map(ReviewVO::new).collect(Collectors.toList());
        }

        // 获取评论的媒体资源
        List<Integer> reviewIds = reviewBoList.stream().map(ReviewBO::getId).collect(Collectors.toList());
        List<ReviewResource> reviewResourceList = reviewResourceService.listAllowResourceByReviewIds(reviewIds);

        if (CollectionUtils.isEmpty(reviewResourceList)) {
            return reviewBoList.stream().map(ReviewVO::new).collect(Collectors.toList());
        }

        // 将 reviewResourceList 按照 reviewId 分组并转换为映射，方便后续根据 reviewId 查找对应的 ReviewResource
        Map<Integer, List<ReviewResource>> reviewResourceMap = reviewResourceList.stream().collect(Collectors.groupingBy(ReviewResource::getReviewId));

        // 遍历每一个 reviewVO 对象 组装媒体资源信息
        reviewBoList.forEach(reviewBO -> packReviewResourceInfo(reviewBO, reviewResourceMap));
        return reviewBoList.stream().map(ReviewVO::new).collect(Collectors.toList());

    }

    /**
     * nick name 做匿名处理
     *
     * @param reviews
     * @return
     */
    private List<Review> secretNickName(List<Review> reviews, InstaCountry country) {
        // 中国大陆页面，所有的评论都需要做昵称匿名处理
        if (InstaCountry.CN.equals(country)) {
            reviews.forEach(review -> {
                String secretNickName = StrUtils.secretStr(review.getNickName(), REVIEW_NICK_NAME_PRIVATE, 3);
                review.setNickName(secretNickName);
            });
        }

        // 其他国家地区页面，属于中国大陆的订单评论需要做昵称匿名处理
        else {
            reviews.forEach(review -> {
                if (InstaCountry.CN.equals(review.country())) {
                    String secretNickName = StrUtils.secretStr(review.getNickName(), REVIEW_NICK_NAME_PRIVATE, 3);
                    review.setNickName(secretNickName);
                }
            });
        }
        return reviews;
    }

    /**
     * 组装媒体资源信息
     *
     * @param reviewBO          评论vo
     * @param reviewResourceMap 评估资源映射
     */
    private void packReviewResourceInfo(ReviewBO reviewBO, Map<Integer, List<ReviewResource>> reviewResourceMap) {
        List<ReviewResource> reviewResourceList = reviewResourceMap.get(reviewBO.getId());
        if (CollectionUtils.isEmpty(reviewResourceList)) {
            return;
        }
        List<ReviewResourceVO> reviewResourceVoList = reviewResourceList.stream().map(resource -> {
            ReviewResourceVO reviewResourceVO = new ReviewResourceVO();
            BeanUtils.copyProperties(resource, reviewResourceVO);
            return reviewResourceVO;
        }).collect(Collectors.toList());
        reviewBO.setReviewResourceList(reviewResourceVoList);
    }

    /**
     * 获取产品的总评论数和平均分
     *
     * @param productId
     * @return
     */
    public ReviewRateInfoVO doPackReviewRateInfo(Integer productId) {
        ReviewRateInfoVO reviewRateInfo = new ReviewRateInfoVO();
        ReviewRateCount reviewRateCount = reviewRateCountService.getByProductId(productId);
        if (reviewRateCount != null) {
            reviewRateInfo.setProductId(reviewRateCount.getProductId());
            // 如果是加权产品，则返回加权均分，否则返回常规均分。
            reviewRateInfo.setReviewRateAvg(this.isWeightProduct(productId) ? reviewRateCount.getReviewRateWeightAvg() : reviewRateCount.getReviewTateAvg());
            reviewRateInfo.setReviewCountSum(reviewRateCount.getReviewCountSum());
        }
        return reviewRateInfo;
    }

    /**
     * 判断是否是加权产品
     *
     * @param productId
     * @return
     */
    private Boolean isWeightProduct(Integer productId) {
        String weightProdcutStr = storeConfigService.getConfigValue(StoreConfigKey.review_weight_products);
        if (StringUtil.isBlank(weightProdcutStr)) {
            LOGGER.info("评论产品权重未配置。");
            return false;
        }

        List<Integer> weightProducts = JSONArray.parseArray(weightProdcutStr).stream().map(productStr -> (Integer) productStr).collect(Collectors.toList());
        return weightProducts.contains(productId);
    }

    /**
     * 获取产品的总评论数和平均分
     *
     * @param productId
     * @return
     */
    public ReviewRateInfoVO doPackReviewRateInfo(Integer productId, Map<Integer, ReviewRateCount> reviewRateCountMap) {
        ReviewRateInfoVO reviewRateInfo = new ReviewRateInfoVO();
        ReviewRateCount reviewRateCount = reviewRateCountMap.get(productId);
        if (reviewRateCount != null) {
            reviewRateInfo.setProductId(reviewRateCount.getProductId());
            // 如果是加权产品，则返回加权均分，否则返回常规均分。
            reviewRateInfo.setReviewRateAvg(this.isWeightProduct(productId) ? reviewRateCount.getReviewRateWeightAvg() : reviewRateCount.getReviewTateAvg());
            reviewRateInfo.setReviewCountSum(reviewRateCount.getReviewCountSum());
        }
        return reviewRateInfo;
    }

    /**
     * 获取某个产品的所有评论星级的数量
     *
     * @param productId
     * @return
     */
    public ReviewRateLevelInfoVO doPackReviewRateLevelInfo(Integer productId) {
        List<ReviewRateLevelCount> reviewRateLevelCounts = reviewRateLevelCountService.listByProductId(productId);

        // key：评分级别
        // value：评论数量
        Map<String, Integer> rateLevelInfoMap = new HashMap<>();
        reviewRateLevelCounts.forEach(reviewRateLevelCount -> {
            ReviewRateLevel reviewRateLevel = reviewRateLevelService.getById(reviewRateLevelCount.getReviewRateLevelId());
            if (reviewRateLevel != null) {
                rateLevelInfoMap.put(reviewRateLevel.getLevelKey(), reviewRateLevelCount.getReviewRateCount());
            }
        });

        // 数据封装
        ReviewRateLevelInfoVO reviewRateLevelInfo = new ReviewRateLevelInfoVO();
        reviewRateLevelInfo.setProductId(productId);
        reviewRateLevelInfo.setRateLevelInfoMap(rateLevelInfoMap);
        return reviewRateLevelInfo;
    }
}
