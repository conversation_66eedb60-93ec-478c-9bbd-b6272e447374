package com.insta360.store.business.configuration.cache.monitor.redis.put.runner;

import cn.hutool.core.collection.CollUtil;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CacheCountiesBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.meta.service.CountryConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/09/11
 * @Description:
 */
@Component
public class MetaConfigurationRunner implements ApplicationRunner {

    @Autowired
    CountryConfigService countryConfigService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        countryConfigService.listConfig()
                .forEach(countryConfig -> {
                    // 排除禁用的国家地区 和 工单地区
                    if (countryConfig.getDisabled() || "MF".equals(countryConfig.getCountryCode())) {
                        return;
                    }
                    CacheConstant.COUNTIES.add(new CacheCountiesBO(countryConfig.country(), countryConfig.language()));
                    CacheConstant.LANGUAGES.add(countryConfig.language());
                });

        // 以任务数进行分割
        CacheConstant.PARTITION_COUNTIES.addAll(CollUtil.split(CacheConstant.COUNTIES, 20));

        // 以任务数进行分割
        CacheConstant.PARTITION_LANGUAGES.addAll(CollUtil.split(CacheConstant.LANGUAGES, 3));
    }
}
