package com.insta360.store.business.prime.service.helper;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.configuration.trace.TraceLogContext;
import com.insta360.store.business.configuration.utils.StreamUtils;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.prime.bo.*;
import com.insta360.store.business.prime.constants.PrimeConstants;
import com.insta360.store.business.prime.enums.DeliveryProvider;
import com.insta360.store.business.prime.enums.PrimeCommodityType;
import com.insta360.store.business.prime.enums.PrimeGraphqlOperation;
import com.insta360.store.business.prime.enums.PrimeParseCommodityType;
import com.insta360.store.business.prime.error.PrimeInstaErrorCode;
import com.insta360.store.business.prime.lib.error.PrimeRequestErrorCode;
import com.insta360.store.business.prime.lib.handler.PrimeRequestHandler;
import com.insta360.store.business.prime.lib.response.BasePrimeResponse;
import com.insta360.store.business.prime.lib.response.CreateOrderResponse;
import com.insta360.store.business.prime.lib.response.ErrorResponse;
import com.insta360.store.business.prime.lib.response.OffersResponse;
import com.insta360.store.business.prime.lib.variables.offers.OffersVariables;
import com.insta360.store.business.prime.lib.variables.offers.OffersVariables.InputDTO.LocationDTO;
import com.insta360.store.business.prime.lib.variables.order.*;
import com.insta360.store.business.prime.lib.variables.order.variables.CreateOrderVariables;
import com.insta360.store.business.prime.lib.variables.order.variables.UpdateOrderVariables;
import com.insta360.store.business.prime.model.PrimeCommodity;
import com.insta360.store.business.prime.model.PrimeCommodityInclude;
import com.insta360.store.business.prime.model.PrimeDataRecord;
import com.insta360.store.business.prime.service.PrimeCommodityIncludeService;
import com.insta360.store.business.prime.service.PrimeCommodityService;
import com.insta360.store.business.prime.service.PrimeDataRecordService;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.utils.MathUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 主要订单助手
 *
 * <AUTHOR>
 * @date 2025/06/22
 */
@Component
public class PrimeOrderHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrimeOrderHelper.class);

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    PrimeRequestHandler primeRequestHandler;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    PrimeCommodityService primeCommodityService;

    @Autowired
    PrimeDataRecordService primeDataRecordService;

    @Autowired
    PrimeCommodityIncludeService primeCommodityIncludeService;

    /**
     * 合并商品
     *
     * @param buyCommodities
     * @return
     */
    public static List<PrimeBuyCommodityBO> mergeCommodities(List<PrimeBuyCommodityBO> buyCommodities) {
        return buyCommodities.stream()
                .collect(Collectors.groupingBy(
                        PrimeBuyCommodityBO::getCommodityId,
                        Collectors.summingInt(PrimeBuyCommodityBO::getQuantity)))
                .entrySet().stream()
                .map(entry -> {
                    PrimeBuyCommodityBO merged = new PrimeBuyCommodityBO();
                    merged.setCommodityId(entry.getKey());
                    merged.setQuantity(entry.getValue());
                    return merged;
                })
                .collect(Collectors.toList());
    }

    /**
     * 封装offers入参数
     *
     * @param primeOffersBo
     * @return
     */
    public OffersVariables packOffersVariables(PrimeOffersBO primeOffersBo) {
        LOGGER.info("0.开始封装offers入参数 primeOffersBo:{}", primeOffersBo);
        // 获取用户lwaToken
        String amazonUserId = primeOffersBo.getAmazonUserId();
        // todo prime 去掉测试方法
        String lwaToken = primeDataRecordService.getTestLwaToken(amazonUserId);
        List<PrimeBuyCommodityBO> buyCommodities = primeOffersBo.getBuyCommodities();
        LOGGER.info("1.解析入参 amazonUserId:{} 获取用户lwaToken: {} buyCommodities:{}", amazonUserId, lwaToken, buyCommodities);

        buyCommodities = mergeCommodities(buyCommodities);
        LOGGER.info("2.合并相同commodityId的套餐购买数量 amazonUserId:{} 合并相同商品的购买数量 buyCommodities:{}", amazonUserId, buyCommodities);

        List<Long> buyCommodityIds = StreamUtils.toList(buyCommodities, PrimeBuyCommodityBO::getCommodityId);
        List<PrimeCommodity> primeCommodities = primeCommodityService.listByCommodityIds(buyCommodityIds);
        Map<Long, PrimeCommodity> primeCommodityMap = StreamUtils.toMap(primeCommodities, PrimeCommodity::getCommodityId, Function.identity());

        List<PrimeCommodity> primeBundleCommodities = primeCommodities.stream().filter(e -> e.primeCommodityType().isBundle()).collect(Collectors.toList());
        List<Long> bundlePrimeIds = primeBundleCommodities.stream().map(PrimeCommodity::getId).collect(Collectors.toList());
        List<PrimeCommodityInclude> primeCommodityIncludes = primeCommodityIncludeService.listByPrimeCommodityIds(bundlePrimeIds);
        Map<Long, List<PrimeCommodityInclude>> primeCommodityGroups = primeCommodityIncludes.stream().collect(Collectors.groupingBy(PrimeCommodityInclude::getId));
        LOGGER.info("3.根据套餐ID 获取prime商品信息 primeCommodities:{} primeCommodityGroups:{}", primeCommodities, primeCommodityGroups);

        List<PrimeOfferLineBO> offerLineBoList = new ArrayList<>();
        for (PrimeBuyCommodityBO buyCommodity : buyCommodities) {
            Long commodityId = buyCommodity.getCommodityId();
            PrimeCommodity primeCommodity = primeCommodityMap.get(commodityId);
            // 非prime商品
            if (primeCommodity == null) {
                PrimeOfferLineBO primeOfferLineBO = new PrimeOfferLineBO();
                primeOfferLineBO.setCommodityId(commodityId);
                primeOfferLineBO.setQuantity(buyCommodity.getQuantity());
                primeOfferLineBO.setCommodityType(PrimeParseCommodityType.Insta);
                primeOfferLineBO.setExternalId(commodityId.toString());
                primeOfferLineBO.setPrime(false);
                offerLineBoList.add(primeOfferLineBO);
            }
            // prime 商品
            else {
                PrimeCommodityType primeCommodityType = primeCommodity.primeCommodityType();
                switch (primeCommodityType) {
                    case Individual:
                        PrimeOfferLineBO primeOfferLineBO = new PrimeOfferLineBO();
                        primeOfferLineBO.setCommodityId(commodityId);
                        primeOfferLineBO.setQuantity(buyCommodity.getQuantity());
                        primeOfferLineBO.setCommodityType(PrimeParseCommodityType.Individual);
                        primeOfferLineBO.setExternalId(commodityId.toString());
                        primeOfferLineBO.setPrime(true);
                        offerLineBoList.add(primeOfferLineBO);
                        break;
                    case Bundle:
                        List<PrimeCommodityInclude> primeCommodityIncludeList = primeCommodityGroups.get(primeCommodity.getId());
                        List<PrimeOfferLineBO> bundleIncludeLines = primeCommodityIncludeList.stream().map(primeCommodityInclude -> {
                            PrimeOfferLineBO primeOfferLineIncludeBO = new PrimeOfferLineBO();
                            primeOfferLineIncludeBO.setCommodityId(commodityId);
                            primeOfferLineIncludeBO.setQuantity(buyCommodity.getQuantity());
                            primeOfferLineIncludeBO.setCommodityType(PrimeParseCommodityType.BundleOnInclude);
                            primeOfferLineIncludeBO.setExternalId(commodityId.toString());
                            primeOfferLineIncludeBO.setBundleQuantity(MathUtil.getBigDecimal(primeCommodityInclude.getQuantity() * buyCommodity.getQuantity()));
                            primeOfferLineIncludeBO.setBundleExternalId(primeCommodity.externalId());
                            primeOfferLineIncludeBO.setPrime(true);
                            return primeOfferLineIncludeBO;
                        }).collect(Collectors.toList());
                        offerLineBoList.addAll(bundleIncludeLines);
                        break;
                }
            }
        }
        LOGGER.info("4.组装商品行明细 offerLineBoList:{}", offerLineBoList);

        // 7. 转为订单行graphql参数
        List<OffersVariables.InputDTO.LineItemsDTO> lineItems = offerLineBoList.stream()
                .map(primeOfferLineBo -> OffersVariables.InputDTO.LineItemsDTO.init(
                        primeOfferLineBo.getExternalId(),
                        primeOfferLineBo.getQuantity(),
                        primeOfferLineBo.getBundleExternalId(),
                        primeOfferLineBo.getPrime())
                )
                .collect(Collectors.toList());

        LOGGER.info("5.组装offers商品行参数 lineItems:{}", lineItems);
        OffersVariables offersVariables = new OffersVariables();
        OffersVariables.InputDTO input = new OffersVariables.InputDTO();
        input.setLineItems(lineItems);
        input.setShopperIdentity(new ShopperIdentityDTO(lwaToken));
        input.setLocation(new LocationDTO(primeOffersBo.getShippingAddress()));
        offersVariables.setInput(input);
        return offersVariables;
    }

    @TraceLog(logPrefix = "[prime offers]", logPrefixSub = "offers具体逻辑")
    public PrimeOffersResponseBO offers(PrimeOffersBO primeOffersBO) {
        String amazonUserId = primeOffersBO.getAmazonUserId();
        PrimeDataRecord primeDataRecord = Optional.ofNullable(primeDataRecordService.getPrimeData(amazonUserId)).orElse(new PrimeDataRecord());
        PrimeOffersResponseBO primeOffersResponseBo = doOffers(primeOffersBO);
        primeDataRecord.setAmazonUserId(amazonUserId);
        primeDataRecord.setOfferId(primeOffersResponseBo.getOfferId());
        primeDataRecord.setProviders(JSON.toJSONString(primeOffersResponseBo.getCommodityDeliveryProviderList()));
        primeDataRecordService.saveOrUpdate(primeDataRecord);
        return primeOffersResponseBo;
    }

    /**
     * 创建prime订单
     *
     * @param primeOrderCreateBO 订单创建请求参数
     * @return 订单创建结果
     */
    public PrimeCreateOrderBO createOrder(PrimeOrderCreateBO primeOrderCreateBO) {
        String amazonUserId = primeOrderCreateBO.getAmazonUserId();
        String offerId = primeOrderCreateBO.getOfferId();
        PrimeDataRecord primeDataRecord = primeDataRecordService.getPrimeData(amazonUserId, offerId);

        List<PrimeOffersLineResBO> primeOffersLineResBoList = null;
        List<PrimeCommodityDeliveryProviderBO> commodityDeliveryProviderList = null;
        if (primeDataRecord == null) {
            PrimeOffersBO primeOffersBO = new PrimeOffersBO();
            primeOffersBO.setAmazonUserId(amazonUserId);
            primeOffersBO.setBuyCommodities(primeOrderCreateBO.getBuyCommodities());
            primeOffersBO.setShippingAddress(primeOrderCreateBO.getShippingAddress());
            PrimeOffersResponseBO primeOffersResponseBO = doOffers(primeOffersBO);
            primeOffersLineResBoList = primeOffersResponseBO.getPrimeOffersLineResBoList();
            commodityDeliveryProviderList = primeOffersResponseBO.getCommodityDeliveryProviderList();
        } else {
            primeOffersLineResBoList = primeDataRecord.parseOfferLine();
            commodityDeliveryProviderList = primeDataRecord.parseProviders();
        }
        CreateOrderVariables createOrderVariables = packCreateOrderVariables(primeOrderCreateBO, primeOffersLineResBoList, commodityDeliveryProviderList);
        BasePrimeResponse basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.CreateOrder, createOrderVariables);
        if (basePrimeResponse.notOk()) {
            LOGGER.info("amazonUserId:{}创建prime订单失败: {}", amazonUserId, basePrimeResponse.getResponseBody());
            FeiShuMessageUtil.storeGeneralMessage(String.format("prime 创建订单失败 traceId: %s", TraceLogContext.getTraceId()), FeiShuGroupRobot.DevNotice, FeiShuAtUser.WXQ);
            ErrorResponse errorResponse = basePrimeResponse.parseErrorResponse();
            if (errorResponse == null) {
                throw new InstaException(PrimeInstaErrorCode.PRIME_ORDER_CREATE_FAILED_NOT_PRIME);
            }
            List<PrimeRequestErrorCode> primeRequestErrorCodes = errorResponse.parseErrorCode();
            Optional<PrimeInstaErrorCode> primeInstaErrorCode = primeRequestErrorCodes.stream().map(PrimeRequestErrorCode::getPrimeInstaErrorCode).filter(Objects::nonNull).findFirst();
            if (primeInstaErrorCode.isPresent()) {
                throw new InstaException(primeInstaErrorCode.get());
            }
        }
        CreateOrderResponse createOrderResponse = basePrimeResponse.parsePrimeResponse(CreateOrderResponse.class);
        PrimeCreateOrderBO createOrderBO = new PrimeCreateOrderBO();
        CreateOrderResponse.CreateOrderDTO.OrderDTO primeOrder = createOrderResponse.getCreateOrder().getOrder();
        createOrderBO.setPrimeOrderId(primeOrder.getId());
        createOrderBO.setOutOrderLink(Optional.ofNullable(primeOrder.getOrderLinks()).map(orderLinks -> CollectionUtils.isNotEmpty(orderLinks) ? orderLinks.get(0).getUrl() : StringUtils.EMPTY).orElse(null));
        createOrderBO.setDeliveryProviderBos(commodityDeliveryProviderList);
        return createOrderBO;

    }

    /**
     * offers操作 并解析对应bo
     *
     * @param primeOffersBo
     * @return
     */
    public PrimeOffersResponseBO doOffers(PrimeOffersBO primeOffersBo) {
        String amazonUserId = primeOffersBo.getAmazonUserId();
        OffersVariables offersVariables = packOffersVariables(primeOffersBo);
        BasePrimeResponse basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.Offers, offersVariables);
        if (basePrimeResponse.notOk()) {
            LOGGER.info("amazonUserId:{} prime offers失败 error: {}", amazonUserId, basePrimeResponse.getResponseBody());
            PrimeOffersResponseBO primeOffersResponseBo = new PrimeOffersResponseBO();
            List<PrimeBuyCommodityBO> buyCommodities = primeOffersBo.getBuyCommodities();
            List<PrimeCommodityDeliveryProviderBO> primeCommodityDeliveryProviderBoList = buyCommodities
                    .stream()
                    .map(buyCommodity -> new PrimeCommodityDeliveryProviderBO(buyCommodity.getCommodityId(), DeliveryProvider.MERCHANT))
                    .collect(Collectors.toList());
            primeOffersResponseBo.setCommodityDeliveryProviderList(primeCommodityDeliveryProviderBoList);
            return primeOffersResponseBo;
        }
        OffersResponse offersResponse = basePrimeResponse.parsePrimeResponse(OffersResponse.class, "data", "offers");
        LOGGER.info("amazonUserId:{} offersResponse: {}", amazonUserId, offersResponse);
        return packPrimeOffersBo(amazonUserId, offersResponse);
    }

    /**
     * 更新prime订单税费
     *
     * @param orderNumber
     * @param totalTax
     */
    public void updateTax(String orderNumber, BigDecimal totalTax) {
        LOGGER.info("orderNumber:{} 更新prime订单税费开始...", orderNumber);
        UpdateOrderVariables updateOrderVariables = new UpdateOrderVariables();
        updateOrderVariables.setOrderIdentifier(new OrderIdentifierDTO(orderNumber));
        updateOrderVariables.setInput(new UpdateOrderVariables.InputDTO(totalTax));
        BasePrimeResponse basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.UpdateOrder, updateOrderVariables);
        if (basePrimeResponse.notOk()) {
            LOGGER.info("orderNumber:{} 更新prime订单税费失败: {}", orderNumber, basePrimeResponse.getResponseBody());
            FeiShuMessageUtil.storeGeneralMessage(String.format("prime 更新订单失败 traceId: %s", TraceLogContext.getTraceId()), FeiShuGroupRobot.DevNotice, FeiShuAtUser.WXQ);
        } else {
            LOGGER.info("orderNumber:{} 订单税费更新成功...", orderNumber);
        }
    }

    /**
     * 创建prime订单参数封装
     *
     * @return
     */
    public void packPrimeOrderCreationBo(OrderCreation orderCreation, PrimeCreateOrderBO primeCreateOrderBo) {
        orderCreation.setPrimeCreateOrderBo(primeCreateOrderBo);
        List<PrimeCommodityDeliveryProviderBO> deliveryProviderBos = primeCreateOrderBo.getDeliveryProviderBos();
        List<OrderItem> orderItems = orderCreation.getOrderItems();
        LOGGER.info("封装履约前 原始orderItems:{}", orderItems);
        Map<Long, DeliveryProvider> deliveryProviderBoMap = deliveryProviderBos.stream().collect(Collectors.toMap(PrimeCommodityDeliveryProviderBO::getCommodityId, PrimeCommodityDeliveryProviderBO::getDeliveryProvider, (k1, k2) -> k1));
        orderItems = orderItems.stream().map(orderItem -> {
            DeliveryProvider deliveryProvider = deliveryProviderBoMap.get(Long.valueOf(orderItem.getCommodity()));
            orderItem.setDeliveryProvider(deliveryProvider.getType());
            return orderItem;
        }).collect(Collectors.toList());
        LOGGER.info("封装履约后 orderItems:{}", orderItems);
        orderCreation.setOrderItems(orderItems);
    }

    /**
     * 创建prime订单参数封装
     *
     * @param primeOrderCreateBO
     * @param primeOffersLineResBoList
     * @param commodityDeliveryProviderList
     * @return
     */
    @TraceLog(logPrefixSub = "创建prime订单参数封装")
    private CreateOrderVariables packCreateOrderVariables(PrimeOrderCreateBO primeOrderCreateBO,
                                                          List<PrimeOffersLineResBO> primeOffersLineResBoList,
                                                          List<PrimeCommodityDeliveryProviderBO> commodityDeliveryProviderList) {
        String amazonUserId = primeOrderCreateBO.getAmazonUserId();
        Currency currency = primeOrderCreateBO.getCurrency();

        List<PrimeBuyCommodityBO> buyCommodities = primeOrderCreateBO.getBuyCommodities();
        LOGGER.info("进入封装 OrderVariables参数 amazonUserId:{} buyCommodities:{}", amazonUserId, buyCommodities);

        List<Long> commodityIds = buyCommodities.stream().map(PrimeBuyCommodityBO::getCommodityId).collect(Collectors.toList());
        List<Integer> compatibilityCommodityIds = commodityIds.stream().map(Long::intValue).collect(Collectors.toList());

        // 获取prime套餐
        List<PrimeCommodity> primeCommodityList = primeCommodityService.listByCommodityIds(commodityIds);

        // 获取bundle套餐包含的子套餐
        Map<Long, PrimeCommodity> primeCommodityMap = StreamUtils.toMap(primeCommodityList, PrimeCommodity::getCommodityId, Function.identity());
        List<PrimeCommodity> primeBundleCommodityList = primeCommodityList.stream().filter(primeCommodity -> primeCommodity.primeCommodityType().isBundle()).collect(Collectors.toList());
        List<Long> primeCommodityIds = primeBundleCommodityList.stream().map(PrimeCommodity::getId).collect(Collectors.toList());
        List<PrimeCommodityInclude> primeCommodityIncludes = primeCommodityIncludeService.listByPrimeCommodityIds(primeCommodityIds);
        Map<Long, List<PrimeCommodityInclude>> primeBundleCommodityIncludeGroups = StreamUtils.groupByKey(primeCommodityIncludes, PrimeCommodityInclude::getPrimeCommodityId);

        List<Long> bundleIncludeCommodityIds = primeCommodityIncludes.stream().map(PrimeCommodityInclude::getCommodityId).collect(Collectors.toList());
        commodityIds.addAll(bundleIncludeCommodityIds);
        LOGGER.info("添加bundle包含套餐ID后 amazonUserId:{} commodityIds:{} commodityDeliveryProviderList:{}", amazonUserId, commodityIds, commodityDeliveryProviderList);

        List<CommodityPrice> commodityPrices = commodityPriceService.listByCommodityIds(compatibilityCommodityIds);
        Map<Integer, CommodityPrice> commodityPriceMap = StreamUtils.toMap(commodityPrices, CommodityPrice::getCommodityId, Function.identity());
        LOGGER.info("amazonUserId:{} commodityPriceMap:{}", amazonUserId, commodityPriceMap);

        List<CommodityInfo> commodityInfos = commodityInfoService.listByCommodityIds(compatibilityCommodityIds);
        Map<Integer, CommodityInfo> commodityInfoMap = StreamUtils.toMap(commodityInfos, CommodityInfo::getCommodity, Function.identity());
        LOGGER.info("amazonUserId:{} commodityInfoMap:{}", amazonUserId, commodityInfoMap);

        List<Commodity> commodities = commodityService.listCommodities(compatibilityCommodityIds);
        Map<Integer, Integer> commodityProductMap = StreamUtils.toMap(commodities, Commodity::getId, Commodity::getProduct);
        LOGGER.info("amazonUserId:{} commodityProductMap:{}", amazonUserId, commodityProductMap);

        List<Integer> productIds = commodities.stream().map(Commodity::getProduct).collect(Collectors.toList());
        List<ProductInfo> products = productInfoService.listInfoByProductIds(productIds, InstaLanguage.en_US);
        Map<Integer, ProductInfo> productInfoMap = StreamUtils.toMap(products, ProductInfo::getProduct, Function.identity());
        LOGGER.info("amazonUserId:{} productInfoMap:{}", amazonUserId, productInfoMap);

        Map<Long, PrimeCommodityDeliveryProviderBO> primeCommodityDeliveryProviderBoMap = commodityDeliveryProviderList.stream().collect(Collectors.toMap(PrimeCommodityDeliveryProviderBO::getCommodityId, Function.identity()));
        List<PrimeOrderCreateLineBO> primeOrderCreateLineBoList = new ArrayList<>();

        for (PrimeBuyCommodityBO buyCommodity : buyCommodities) {
            Long commodityId = buyCommodity.getCommodityId();
            int convertedCommodityId = commodityId.intValue();
            PrimeCommodity primeCommodity = primeCommodityMap.get(commodityId);
            CommodityPrice commodityPrice = commodityPriceMap.get(convertedCommodityId);
            PrimeCommodityDeliveryProviderBO primeCommodityDeliveryProviderBO = primeCommodityDeliveryProviderBoMap.get(commodityId);
            // Insta360 履约
            if (primeCommodityDeliveryProviderBO.getDeliveryProvider().isMerchant()) {
                PrimeOrderCreateLineBO primeOrderCreateLineBO = new PrimeOrderCreateLineBO();
                ProductInfo productInfo = Optional.ofNullable(commodityProductMap.get(convertedCommodityId)).map(productInfoMap::get).orElse(new ProductInfo());
                CommodityInfo commodityInfo = commodityInfoMap.get(convertedCommodityId);
                primeOrderCreateLineBO.setExternalId(String.valueOf(buyCommodity.getCommodityId()));
                primeOrderCreateLineBO.setBundleExternalId("");
                primeOrderCreateLineBO.packTitle(productInfo, commodityInfo);
                primeOrderCreateLineBO.setOfferPrime(false);
                primeOrderCreateLineBO.setGift(buyCommodity.getGift());
                primeOrderCreateLineBO.setPriceAmount(MathUtil.getBigDecimal(commodityPrice.getAmount()));
                primeOrderCreateLineBO.setMemberAmount(MathUtil.getBigDecimal(buyCommodity.getQuantity()));
                primeOrderCreateLineBO.setCurrencyCode(Currency.USD.name());
                primeOrderCreateLineBO.setPrimeEligible(false);
                primeOrderCreateLineBO.setDeliveryProvider(DeliveryProvider.MERCHANT);
                primeOrderCreateLineBoList.add(primeOrderCreateLineBO);
            }
            // amazon 履约
            else {
                PrimeCommodityType primeCommodityType = primeCommodity.primeCommodityType();
                switch (primeCommodityType) {
                    case Individual:
                        PrimeOrderCreateLineBO primeOrderCreateLineBO = new PrimeOrderCreateLineBO();
                        ProductInfo productInfo = Optional.ofNullable(commodityProductMap.get(convertedCommodityId)).map(productInfoMap::get).orElse(new ProductInfo());
                        CommodityInfo commodityInfo = commodityInfoMap.get(convertedCommodityId);
                        primeOrderCreateLineBO.setExternalId(primeCommodity.getCommodityId().toString());
                        primeOrderCreateLineBO.setBundleExternalId("");
                        primeOrderCreateLineBO.packTitle(productInfo, commodityInfo);
                        primeOrderCreateLineBO.setOfferPrime(true);
                        primeOrderCreateLineBO.setGift(buyCommodity.getGift());
                        primeOrderCreateLineBO.setPriceAmount(MathUtil.getBigDecimal(commodityPrice.getAmount()));
                        primeOrderCreateLineBO.setMemberAmount(MathUtil.getBigDecimal(buyCommodity.getQuantity()));
                        primeOrderCreateLineBO.setCurrencyCode(Currency.USD.name());
                        primeOrderCreateLineBO.setPrimeEligible(true);
                        primeOrderCreateLineBO.setDeliveryProvider(DeliveryProvider.AMAZON);
                        primeOrderCreateLineBoList.add(primeOrderCreateLineBO);
                        break;
                    case Bundle:
                        List<PrimeCommodityInclude> primeCommodityIncludeList = primeBundleCommodityIncludeGroups.get(commodityId);
                        List<PrimeOrderCreateLineBO> primeOrderCreateLineBos = primeCommodityIncludeList.stream().map(primeCommodityInclude -> {
                            Long includeCommodityId = primeCommodityInclude.getCommodityId();
                            int convertedIncludeCommodityId = includeCommodityId.intValue();
                            PrimeOrderCreateLineBO primeOrderCreateLineBoInclude = new PrimeOrderCreateLineBO();
                            ProductInfo productInfoInclude = Optional.ofNullable(commodityProductMap.get(convertedIncludeCommodityId)).map(productInfoMap::get).orElse(new ProductInfo());
                            CommodityInfo commodityInfoInclude = commodityInfoMap.get(convertedIncludeCommodityId);
                            CommodityPrice commodityPriceInclude = commodityPriceMap.get(convertedIncludeCommodityId);

                            primeOrderCreateLineBoInclude.setExternalId(primeCommodity.getCommodityId().toString());
                            primeOrderCreateLineBoInclude.setBundleExternalId(primeCommodity.externalId());
                            primeOrderCreateLineBoInclude.packTitle(productInfoInclude, commodityInfoInclude);
                            primeOrderCreateLineBoInclude.setOfferPrime(true);
                            primeOrderCreateLineBoInclude.setGift(buyCommodity.getGift());
                            primeOrderCreateLineBoInclude.setPriceAmount(MathUtil.getBigDecimal(commodityPriceInclude.getAmount()));
                            primeOrderCreateLineBoInclude.setMemberAmount(MathUtil.getBigDecimal(primeCommodityInclude.getQuantity() * buyCommodity.getQuantity()));
                            primeOrderCreateLineBoInclude.setCurrencyCode(Currency.USD.name());
                            primeOrderCreateLineBoInclude.setPrimeEligible(true);
                            primeOrderCreateLineBoInclude.setDeliveryProvider(DeliveryProvider.AMAZON);
                            return primeOrderCreateLineBoInclude;
                        }).collect(Collectors.toList());
                        primeOrderCreateLineBoList.addAll(primeOrderCreateLineBos);
                }
            }

        }

        // todo prime 去掉测试的
        String lwaToken = primeDataRecordService.getTestLwaToken(amazonUserId);

        CreateOrderVariables createOrderVariables = new CreateOrderVariables();
        InputDTO inputDTO = new InputDTO();
        inputDTO.setLineItems(doPackOrderLineItems(primeOrderCreateLineBoList));
        inputDTO.setDesiredExecutionState(primeOrderCreateBO.getDesiredExecutionState());
        inputDTO.setCustomer(CustomerDTO.init(primeOrderCreateBO.getName(), primeOrderCreateBO.getEmail()));
        inputDTO.setRecipient(new RecipientDTO(primeOrderCreateBO.getShippingAddress()));
        inputDTO.setTotalPrice(new TotalPriceDTO(primeOrderCreateBO.getPriceAmount(), currency));
        inputDTO.setShopperIdentity(new ShopperIdentityDTO(lwaToken));
        inputDTO.setTaxes(new TaxTotalAmountDTO(primeOrderCreateBO.getTaxAmount(), currency.name()));
        inputDTO.setDiscounts(new CurrencyTotalAmountDTO(primeOrderCreateBO.getDiscountsAmount(), currency.name()));
        createOrderVariables.setInput(inputDTO);
        return createOrderVariables;
    }

    /**
     * 创建订单参数行
     *
     * @param primeOrderCreateLineBos
     * @return
     */
    private List<LineItemsDTO> doPackOrderLineItems(List<PrimeOrderCreateLineBO> primeOrderCreateLineBos) {
        return primeOrderCreateLineBos.stream().map(primeOrderCreateLineBO -> {
            LineItemsDTO lineItemsDTO = new LineItemsDTO();
            lineItemsDTO.setProduct(new ProductDTO(primeOrderCreateLineBO.getExternalId(),
                    primeOrderCreateLineBO.getTitle(),
                    primeOrderCreateLineBO.getPriceAmount(),
                    primeOrderCreateLineBO.getGift(),
                    primeOrderCreateLineBO.getCurrencyCode(),
                    primeOrderCreateLineBO.getOfferPrime(),
                    primeOrderCreateLineBO.getBundleExternalId()));

            lineItemsDTO.setAmount(new AmountDTO(primeOrderCreateLineBO.getMemberAmount()));
            lineItemsDTO.setSelectedDeliveryOffer(new SelectedDeliveryOfferDTO(primeOrderCreateLineBO.getDeliveryProvider(), primeOrderCreateLineBO.getPrimeEligible()));
            return lineItemsDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 封装 offers 响应
     *
     * @param amazonUserId
     * @param offersResponse
     * @return
     */
    private PrimeOffersResponseBO packPrimeOffersBo(String amazonUserId, OffersResponse offersResponse) {
        PrimeOffersResponseBO primeOffersResponseBo = new PrimeOffersResponseBO();
        for (OffersResponse.EdgesDTO edge : offersResponse.getEdges()) {
            OffersResponse.EdgesDTO.NodeDTO node = edge.getNode();
            String id = node.getId();
            LOGGER.info(" amazonUserId:{} 获取ID等基本信息 id:{}", amazonUserId, id);
            primeOffersResponseBo.setOfferId(id);

            List<OffersResponse.EdgesDTO.NodeDTO.LineItemsDTO> lineItems = node.getLineItems();
            List<PrimeOffersLineResBO> primeOffersLineResBoList = lineItems.stream().map(lineItem -> {
                PrimeOffersLineResBO primeOffersLineResBO = new PrimeOffersLineResBO();
                primeOffersLineResBO.setCommodityId(Long.valueOf(lineItem.getProduct().getExternalId().getValue()));
                String primeProductId = Optional.ofNullable(lineItem.getProduct())
                        .map(OffersResponse.EdgesDTO.NodeDTO.LineItemsDTO.ProductDTO::getExternalId)
                        .map(OffersResponse.EdgesDTO.NodeDTO.LineItemsDTO.ProductDTO.ExternalIdDTO::getValue)
                        .orElse(StringUtils.EMPTY);

                primeOffersLineResBO.setPrimeProductId(primeProductId);
                PrimeCommodityType primeCommodityType = lineItem.getProduct().getPurchaseGroupMembership() == null ? PrimeCommodityType.Individual : PrimeCommodityType.Bundle;
                primeOffersLineResBO.setPrimeCommodityType(primeCommodityType);
                primeOffersLineResBO.setExternalId(lineItem.getProduct().getExternalId().getValue());
                if (primeCommodityType.isBundle()) {
                    String bundleExternalId = lineItem.getProduct().getPurchaseGroupMembership().getProduct().getExternalId().getValue();
                    primeOffersLineResBO.setBundleExternalId(bundleExternalId);
                    primeOffersLineResBO.setExternalId(bundleExternalId);
                    primeOffersLineResBO.setBundleCommodityId(Long.valueOf(StringUtils.remove(bundleExternalId, PrimeConstants.Commodity.BUNDLE_SUFFIX)));
                }

                // deliveryProvider
                DeliveryProvider deliveryProvider = Optional.ofNullable(lineItem.getDeliveryOffer())
                        .map(e -> e.getDetails())
                        .map(e -> e.getDeliveryProvider())
                        .map(DeliveryProvider::matchCode)
                        .orElse(DeliveryProvider.MERCHANT);
                primeOffersLineResBO.setDeliveryProvider(deliveryProvider);

                // deliveryPreviewId
                String deliveryPreviewId = Optional.ofNullable(lineItem.getDeliveryOffer())
                        .map(e -> e.getDetails())
                        .map(e -> e.getDeliveryPreviewId())
                        .orElse(StringUtils.EMPTY);
                primeOffersLineResBO.setDeliveryPreviewId(deliveryPreviewId);

                // latest
                LocalDateTime latest = Optional.ofNullable(lineItem.getDeliveryOffer())
                        .map(OffersResponse.EdgesDTO.NodeDTO.LineItemsDTO.DeliveryOfferDTO::getDetails)
                        .map(OffersResponse.EdgesDTO.NodeDTO.LineItemsDTO.DeliveryOfferDTO.DetailsDTO::getDate)
                        .map(OffersResponse.EdgesDTO.NodeDTO.LineItemsDTO.DeliveryOfferDTO.DetailsDTO.DateDTO::getLatest)
                        .map(utcDateTime -> ZonedDateTime.parse(utcDateTime, DateTimeFormatter.ISO_DATE_TIME).toLocalDateTime())
                        .orElse(null);
                primeOffersLineResBO.setLatest(latest);

                return primeOffersLineResBO;
            }).collect(Collectors.toList());
            LOGGER.info("amazonUserId:{} 原始offers lineitem分组信息 primeOffersLineResBoList: {}", amazonUserId, primeOffersLineResBoList);

            // 封装订单需要的分组信息
            List<PrimeOffersLineResBO> individualCommodityList = StreamUtils.filter(primeOffersLineResBoList, primeOffersLineResBO -> PrimeCommodityType.Individual.equals(primeOffersLineResBO.getPrimeCommodityType()));
            LOGGER.info("amazonUserId:{} 提取individual的商品信息 individualCommodityList:{}", amazonUserId, individualCommodityList);
            List<PrimeCommodityDeliveryProviderBO> individualProviders = individualCommodityList
                    .stream()
                    .map(lineItem -> new PrimeCommodityDeliveryProviderBO(lineItem.getCommodityId(), lineItem.getDeliveryProvider()))
                    .collect(Collectors.toList());

            List<PrimeOffersLineResBO> bundleCommodityList = StreamUtils.filter(primeOffersLineResBoList, primeOffersLineResBO -> PrimeCommodityType.Bundle.equals(primeOffersLineResBO.getPrimeCommodityType()));
            Map<Long, List<PrimeOffersLineResBO>> bundleCommodityGroups = bundleCommodityList.stream().collect(Collectors.groupingBy(PrimeOffersLineResBO::getBundleCommodityId));
            List<PrimeCommodityDeliveryProviderBO> bundleProviders = bundleCommodityGroups.entrySet().stream().map(entry -> {
                // bundleCommodityId 为bundle的externalId
                Long bundleCommodityId = entry.getKey();
                List<PrimeOffersLineResBO> bundleIncludes = entry.getValue();

                // 解析是否存在商家配送MERCHANT
                Optional<DeliveryProvider> deliveryProviderMatch = bundleIncludes.stream()
                        .map(PrimeOffersLineResBO::getDeliveryProvider)
                        .filter(DeliveryProvider::isMerchant)
                        .findAny();

                PrimeCommodityDeliveryProviderBO primeCommodityDeliveryProviderBO = new PrimeCommodityDeliveryProviderBO();
                primeCommodityDeliveryProviderBO.setCommodityId(bundleCommodityId);
                primeCommodityDeliveryProviderBO.setDeliveryProvider(deliveryProviderMatch.isPresent() ? DeliveryProvider.MERCHANT : DeliveryProvider.AMAZON);
                return primeCommodityDeliveryProviderBO;
            }).collect(Collectors.toList());
            LOGGER.info("amazonUserId:{} 提取bundle商品信息组 bundleCommodityList:{} bundleCommodityGroups:{} bundleProviders:{}", amazonUserId, bundleCommodityList, bundleCommodityGroups, bundleProviders);

            List<PrimeCommodityDeliveryProviderBO> allProviders = new ArrayList<>(individualProviders.size() + bundleProviders.size());
            allProviders.addAll(individualProviders);
            allProviders.addAll(bundleProviders);
            LOGGER.info("amazonUserId:{} 合并信息 allProviders:{}", amazonUserId, JSON.toJSONString(allProviders));

            List<LocalDateTime> latestDates = primeOffersLineResBoList.stream()
                    .map(PrimeOffersLineResBO::getLatest)
                    .filter(Objects::nonNull)
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());

            LOGGER.info("amazonUserId:{} 将获取到的latest进行排序，得到最早和最晚的latest【earliestStart-earliestEnd】latestDates:{}", allProviders, latestDates);
            if (CollectionUtils.isNotEmpty(latestDates)) {
                primeOffersResponseBo.setEarliestStart(latestDates.get(0));
                primeOffersResponseBo.setEarliestEnd(latestDates.get(latestDates.size() - 1));
            }

            primeOffersResponseBo.setPrimeOffersLineResBoList(primeOffersLineResBoList);
            primeOffersResponseBo.setCommodityDeliveryProviderList(allProviders);
            LOGGER.info("amazonUserId:{} 最终组装数据 primeOffersResBo:{}", amazonUserId, primeOffersResponseBo);
        }
        return primeOffersResponseBo;
    }

}
