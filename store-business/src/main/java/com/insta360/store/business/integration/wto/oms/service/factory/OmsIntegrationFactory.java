package com.insta360.store.business.integration.wto.oms.service.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.integration.wto.enums.OmsIntegrationBusinessType;
import com.insta360.store.business.integration.wto.oms.service.handler.OmsService;
import com.insta360.store.business.integration.wto.oms.service.handler.admin.BaseStoreAdminService;
import com.insta360.store.business.integration.wto.oms.service.handler.commodity.StoreCommodityCombineItemHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.order.StoreOrderDeliveryDetailHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.order.StoreOrderLogisticsChangeHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.order.StorePushOrderHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.rma.StoreChangeOrderSyncHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.rma.StoreRefundInterceptSyncHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.rma.StoreRefundOrderSyncHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.rma.StoreReturnItemStockSyncHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.stock.StoreSkuCodeSyncHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.stock.StoreSkuStockGetHandler;
import com.insta360.store.business.integration.wto.oms.service.handler.stock.StoreSkuStockSyncHandler;
import com.insta360.store.business.integration.wto.service.factory.IntegrationFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2024/11/29
 * @Description:
 */
@Component
public class OmsIntegrationFactory implements IntegrationFactory<OmsIntegrationBusinessType> {

    @Override
    public OmsService getIntegrationService(OmsIntegrationBusinessType omsIntegrationBusinessType) {
        ApplicationContext applicationContext = ApplicationContextHolder.getApplicationContext();

        switch (omsIntegrationBusinessType) {
            case STORE_PUSH_ORDER:
                return applicationContext.getBean(StorePushOrderHandler.class);
            case STORE_ORDER_DELIVERY:
                return applicationContext.getBean(StoreOrderDeliveryDetailHandler.class);
            case STORE_ADMIN_HANDLER:
                return applicationContext.getBean(BaseStoreAdminService.class);
            case STORE_RMA_REFUND_ORDER_INFO_SYNC:
                return applicationContext.getBean(StoreRefundOrderSyncHandler.class);
            case STORE_RMA_CHANGE_ORDER_INFO_SYNC:
                return applicationContext.getBean(StoreChangeOrderSyncHandler.class);
            case OMS_REFUND_INTERCEPT_NOTIFY:
                return applicationContext.getBean(StoreRefundInterceptSyncHandler.class);
            case OMS_RETURN_ITEM_STOCK_NOTIFY:
                return applicationContext.getBean(StoreReturnItemStockSyncHandler.class);
            case STORE_BUNDLE_COMMODITY_CODE_QUERY:
                return applicationContext.getBean(StoreCommodityCombineItemHandler.class);
            case OMS_SKU_STOCK_GET:
                return applicationContext.getBean(StoreSkuStockGetHandler.class);
            case STORE_SKU_CODE_SYNC:
                return applicationContext.getBean(StoreSkuCodeSyncHandler.class);
            case OMS_SKU_STOCK_SYNC_CALLBACK:
                return applicationContext.getBean(StoreSkuStockSyncHandler.class);
            case STORE_ORDER_DELIVERY_LOGISTICS_CHANGE_CALLBACK:
                return applicationContext.getBean(StoreOrderLogisticsChangeHandler.class);
            default:
                throw new InstaException(CommonErrorCode.InvalidParameter);
        }
    }


}