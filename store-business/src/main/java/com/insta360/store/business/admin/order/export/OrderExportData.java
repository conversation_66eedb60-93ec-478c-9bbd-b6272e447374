package com.insta360.store.business.admin.order.export;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @Author: hyc
 * @Date: 2019-11-28
 * @Description:
 */
public class OrderExportData {

    @ExcelProperty("提现渠道")
    private String withdrawChannel;

    @ExcelProperty("分销商邮箱号")
    private String resellerEmail;

    @ExcelProperty("提现账户姓名")
    private String resellerName;

    @ExcelProperty("订单号")
    private String orderNumber;

    @ExcelProperty("支付渠道")
    private String payChannel;

    @ExcelProperty("订单状态")
    private String orderState;

    @ExcelProperty("RMA类型")
    private String rmaType;

    @ExcelProperty("RMA状态")
    private String rmaState;

    @ExcelProperty("RMA原因")
    private String rmaReason;

    @ExcelProperty("RMA其他原因")
    private String rmaExtraReason;

    @ExcelProperty("RMA客服确认原因")
    private String rmaAdminReason;

    @ExcelProperty("RMA客服说明")
    private String rmaAdminRemark;

    @ExcelProperty("RMA创建时间")
    private String rmaCreatTime;

    @ExcelProperty("RMA完成时间")
    private String rmaFinishTime;

    @ExcelProperty("RMA退款总金额")
    private Double rmaRefundTotalAmount;

    @ExcelProperty("邮箱")
    private String email;

    @ExcelProperty("游客订单")
    private String isGuest;

    @ExcelProperty("商品名")
    private String itemName;

    @ExcelProperty("相机")
    private String isCamera;

    @ExcelProperty("数量")
    private Integer itemQuantity;

    @ExcelProperty("国家")
    private String country;

    @ExcelProperty("货币")
    private String currency;

    @ExcelProperty("商品单价")
    private Double price;

    @ExcelProperty("商品原价")
    private Double originAccount;

    @ExcelProperty("单项减价")
    private Double discount;

    @ExcelProperty("支付总额（原货币）")
    private Double totalPrice;

    @ExcelProperty("支付总额（人民币）")
    private Double totalPriceCny;

    @ExcelProperty("套餐实付金额（原币种）")
    private Double comboTotalPrice;

    @ExcelProperty("税费")
    private Double tax;

    @ExcelProperty("运费")
    private Double shippingCost;

    @ExcelProperty("总优惠减价")
    private Double totalDiscountPrice;

    @ExcelProperty("初始佣金率")
    private Double commissionRate;

    @ExcelProperty("支付货币初始汇率")
    private Double payCurrencyUsdRate;

    @ExcelProperty("提现货币初始汇率")
    private Double withdrawCurrencyUsdRate;

    @ExcelProperty("套餐佣金")
    private Double estimatedIncome;

    @ExcelProperty("是否为历史分销订单")
    private String historyTag;

    @ExcelProperty("分销码")
    private String resellerCode;

    @ExcelProperty("分销转化方式")
    private String utmSource;

    @ExcelProperty("代金券")
    private String giftCardCode;

    @ExcelProperty("优惠码")
    private String couponCode;

    @ExcelProperty("模版代金券")
    private String giftCardTemplateCode;

    @ExcelProperty("商家备注")
    private String adminRemark;

    @ExcelProperty("买家备注")
    private String buyerRemark;

    @ExcelProperty("买家姓名")
    private String customerName;

    @ExcelProperty("详细地址")
    private String address;

    @ExcelProperty("电话区号")
    private String phoneCode;

    @ExcelProperty("电话号")
    private String phone;

    @ExcelProperty("下单时间(北京)")
    private String createTime;

    @ExcelProperty("支付时间(北京)")
    private String payTime;

    @ExcelProperty("预计发货日期")
    private String estimateDeliveryDate;

    @ExcelProperty("快递公司")
    private String expressCompany;

    @ExcelProperty("快递单号")
    private String expressNumber;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public String getRmaState() {
        return rmaState;
    }

    public void setRmaState(String rmaState) {
        this.rmaState = rmaState;
    }

    public String getRmaReason() {
        return rmaReason;
    }

    public void setRmaReason(String rmaReason) {
        this.rmaReason = rmaReason;
    }

    public String getRmaExtraReason() {
        return rmaExtraReason;
    }

    public void setRmaExtraReason(String rmaExtraReason) {
        this.rmaExtraReason = rmaExtraReason;
    }

    public String getRmaCreatTime() {
        return rmaCreatTime;
    }

    public void setRmaCreatTime(String rmaCreatTime) {
        this.rmaCreatTime = rmaCreatTime;
    }

    public String getRmaFinishTime() {
        return rmaFinishTime;
    }

    public void setRmaFinishTime(String rmaFinishTime) {
        this.rmaFinishTime = rmaFinishTime;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Double getRmaRefundTotalAmount() {
        return rmaRefundTotalAmount;
    }

    public void setRmaRefundTotalAmount(Double rmaRefundTotalAmount) {
        this.rmaRefundTotalAmount = rmaRefundTotalAmount;
    }

    public Double getComboTotalPrice() {
        return comboTotalPrice;
    }

    public void setComboTotalPrice(Double comboTotalPrice) {
        this.comboTotalPrice = comboTotalPrice;
    }

    public Double getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(Double commissionRate) {
        this.commissionRate = commissionRate;
    }

    public Double getEstimatedIncome() {
        return estimatedIncome;
    }

    public void setEstimatedIncome(Double estimatedIncome) {
        this.estimatedIncome = estimatedIncome;
    }

    public String getIsGuest() {
        return isGuest;
    }

    public void setIsGuest(String isGuest) {
        this.isGuest = isGuest;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getIsCamera() {
        return isCamera;
    }

    public void setIsCamera(String isCamera) {
        this.isCamera = isCamera;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(Integer itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Double getShippingCost() {
        return shippingCost;
    }

    public void setShippingCost(Double shippingCost) {
        this.shippingCost = shippingCost;
    }

    public Double getTotalDiscountPrice() {
        return totalDiscountPrice;
    }

    public void setTotalDiscountPrice(Double totalDiscountPrice) {
        this.totalDiscountPrice = totalDiscountPrice;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getAdminRemark() {
        return adminRemark;
    }

    public void setAdminRemark(String adminRemark) {
        this.adminRemark = adminRemark;
    }

    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getEstimateDeliveryDate() {
        return estimateDeliveryDate;
    }

    public void setEstimateDeliveryDate(String estimateDeliveryDate) {
        this.estimateDeliveryDate = estimateDeliveryDate;
    }

    public String getExpressCompany() {
        return expressCompany;
    }

    public void setExpressCompany(String expressCompany) {
        this.expressCompany = expressCompany;
    }

    public String getExpressNumber() {
        return expressNumber;
    }

    public void setExpressNumber(String expressNumber) {
        this.expressNumber = expressNumber;
    }

    public String getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(String resellerCode) {
        this.resellerCode = resellerCode;
    }

    public Double getTotalPriceCny() {
        return totalPriceCny;
    }

    public void setTotalPriceCny(Double totalPriceCny) {
        this.totalPriceCny = totalPriceCny;
    }

    public String getGiftCardCode() {
        return giftCardCode;
    }

    public void setGiftCardCode(String giftCardCode) {
        this.giftCardCode = giftCardCode;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getGiftCardTemplateCode() {
        return giftCardTemplateCode;
    }

    public void setGiftCardTemplateCode(String giftCardTemplateCode) {
        this.giftCardTemplateCode = giftCardTemplateCode;
    }

    public String getRmaAdminReason() {
        return rmaAdminReason;
    }

    public void setRmaAdminReason(String rmaAdminReason) {
        this.rmaAdminReason = rmaAdminReason;
    }

    public String getRmaAdminRemark() {
        return rmaAdminRemark;
    }

    public void setRmaAdminRemark(String rmaAdminRemark) {
        this.rmaAdminRemark = rmaAdminRemark;
    }

    public Double getOriginAccount() {
        return originAccount;
    }

    public void setOriginAccount(Double originAccount) {
        this.originAccount = originAccount;
    }

    public Double getPayCurrencyUsdRate() {
        return payCurrencyUsdRate;
    }

    public void setPayCurrencyUsdRate(Double payCurrencyUsdRate) {
        this.payCurrencyUsdRate = payCurrencyUsdRate;
    }

    public Double getWithdrawCurrencyUsdRate() {
        return withdrawCurrencyUsdRate;
    }

    public void setWithdrawCurrencyUsdRate(Double withdrawCurrencyUsdRate) {
        this.withdrawCurrencyUsdRate = withdrawCurrencyUsdRate;
    }

    public String getHistoryTag() {
        return historyTag;
    }

    public void setHistoryTag(String historyTag) {
        this.historyTag = historyTag;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    public String getWithdrawChannel() {
        return withdrawChannel;
    }

    public void setWithdrawChannel(String withdrawChannel) {
        this.withdrawChannel = withdrawChannel;
    }

    public String getResellerEmail() {
        return resellerEmail;
    }

    public void setResellerEmail(String resellerEmail) {
        this.resellerEmail = resellerEmail;
    }

    public String getResellerName() {
        return resellerName;
    }

    public void setResellerName(String resellerName) {
        this.resellerName = resellerName;
    }
}
