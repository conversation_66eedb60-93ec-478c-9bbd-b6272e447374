package com.insta360.store.business.outgoing.mq.email.sender;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.cloud.email.CloudSubscribeRenewCallEmail;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2021/06/10
 * @Description:
 */
@Component
public class EmailSendMq {

    public static final Logger LOGGER = LoggerFactory.getLogger(EmailSendMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.send_email_delay, messageType = MessageSenderType.time)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * 邮件消息发送
     *
     * @param emailTemplateParams
     */
    public void sendEmailMessage(EmailTemplateParams emailTemplateParams) {
        if (emailTemplateParams != null && emailTemplateParams.getEmail() != null) {
            Map<String, Object> bodyParams = emailTemplateParams.getBodyParams();
            // 延迟消息
            String messageId = null;
            try {
                messageId = rocketTcpMessageSender.sendDelayMessage(JSON.toJSONString(emailTemplateParams), emailTemplateParams.getDelayTime());
            } catch (Exception e) {
                LOGGER.error(String.format("发送邮件服务mq消息失败。 emailTemplateParams: %s", JSON.toJSONString(emailTemplateParams)), e);
                String templateKey = emailTemplateParams.getTemplateKey();
                if (CloudSubscribeRenewCallEmail.templateName.equals(templateKey)) {
                    FeiShuMessageUtil.storeGeneralMessage(String.format("云服务订阅续费提醒邮件发送消息失败. 用户邮箱: [%s]", emailTemplateParams.getEmail()), FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.CYJ);
                }
            }
            MqUtils.isBlankMessageIdHandle(messageId, this, emailTemplateParams);

            LOGGER.info("send message success. email:" + emailTemplateParams.getEmail() + " bodyParams:" + bodyParams.toString() + ",template:" + emailTemplateParams.getTemplateKey() + ",time(+0800):" + LocalDateTime.now());
        }
    }
}
