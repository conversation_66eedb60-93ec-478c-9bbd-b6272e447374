package com.insta360.store.business.prime.lib.response;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10
 */
public class CreateOrderResponse implements PrimeResponse {

    private CreateOrderDTO createOrder;

    public CreateOrderDTO getCreateOrder() {
        return createOrder;
    }

    public void setCreateOrder(CreateOrderDTO createOrder) {
        this.createOrder = createOrder;
    }

    public Boolean ok() {
        String primeOrderId = Optional.ofNullable(createOrder).map(CreateOrderDTO::getOrder).map(CreateOrderDTO.OrderDTO::getId).orElse(StringUtils.EMPTY);
        return StringUtils.isNotBlank(primeOrderId);
    }

    public Boolean notOk() {
        return !ok();
    }

    public static class CreateOrderDTO {

        private OrderDTO order;

        public OrderDTO getOrder() {
            return order;
        }

        public void setOrder(OrderDTO order) {
            this.order = order;
        }

        public static class OrderDTO {

            private String id;

            private List<OrderLinksDTO> orderLinks;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public List<OrderLinksDTO> getOrderLinks() {
                return orderLinks;
            }

            public void setOrderLinks(List<OrderLinksDTO> orderLinks) {
                this.orderLinks = orderLinks;
            }

            public static class OrderLinksDTO {

                private String destinationType;

                private String url;

                public String getDestinationType() {
                    return destinationType;
                }

                public void setDestinationType(String destinationType) {
                    this.destinationType = destinationType;
                }

                public String getUrl() {
                    return url;
                }

                public void setUrl(String url) {
                    this.url = url;
                }
            }
        }
    }
}
