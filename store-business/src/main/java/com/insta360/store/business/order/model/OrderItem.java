package com.insta360.store.business.order.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.prime.enums.DeliveryProvider;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Author: hyc
 * @Date: 2019/2/11
 * @Description:
 */
@TableName("order_item")
public class OrderItem extends BaseModel<OrderItem> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单id
     */
    @TableField(value = "`order`")
    private Integer order;

    /**
     * 产品id
     */
    private Integer product;

    /**
     * 套餐id
     */
    private Integer commodity;

    /**
     * 下单现价
     */
    private Float price;

    /**
     * 下单原价
     */
    private Float originAmount;

    /**
     * 子项折扣
     */
    @JSONField(name = "discount_fee")
    private Float discountFee;

    /**
     * 子项货币
     */
    private String currency;

    /**
     * 子项数量
     */
    private Integer number;

    /**
     * 是否是赠品
     */
    @JSONField(name = "is_gift")
    private Boolean isGift;

    /**
     * 子项状态
     */
    private Integer state;

    /**
     * 评论状态（0：未评论；1：已评论）
     */
    private Boolean reviewState;

    /**
     * 绑定服务对应的套餐id
     */
    @TableField(exist = false)
    @JSONField(name = "belong_to_commodity_id")
    private Integer belongToCommodityId;

    /**
     * 子项发货状态（避免与子项售后发生冲突，所以独立于state字段）
     */
    @JSONField(name = "delivery_state")
    private Integer deliveryState;

    /**
     * 是否发送发货邮件
     */
    @JSONField(name = "send_email_rule")
    private Boolean sendEmailRule;

    /**
     * 商品总税费
     */
    private BigDecimal totalTax;

    /**
     * 商品总折扣
     */
    private BigDecimal totalDiscount;

    /**
     * 优惠来源
     *
     * @see com.insta360.store.business.discount.enums.DiscountSource
     */
    private String discountSource;

    /**
     * 优惠来源
     *
     * @see com.insta360.store.business.prime.enums.DeliveryProvider
     */
    private Integer deliveryProvider;

    /**
     * 已使用额度
     */
    private Integer usedQuota;

    /**
     * 总支付渠道折扣(非持久化属性)
     */
    @TableField(exist = false)
    private BigDecimal totalPayChannelDiscount;

    /**
     * 套餐 (非持久化属性)
     */
    @TableField(exist = false)
    private Commodity itemCommodity;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getCommodity() {
        return commodity;
    }

    public void setCommodity(Integer commodity) {
        this.commodity = commodity;
    }

    public Float getPrice() {
        return price;
    }

    public void setPrice(Float price) {
        this.price = price;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Boolean getIsGift() {
        return isGift;
    }

    public void setIsGift(Boolean isGift) {
        this.isGift = isGift;
    }

    public Float getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(Float discountFee) {
        this.discountFee = discountFee;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Boolean getReviewState() {
        return reviewState;
    }

    public void setReviewState(Boolean reviewState) {
        this.reviewState = reviewState;
    }

    public Integer getBelongToCommodityId() {
        return belongToCommodityId;
    }

    public void setBelongToCommodityId(Integer belongToCommodityId) {
        this.belongToCommodityId = belongToCommodityId;
    }

    public Integer getDeliveryState() {
        return deliveryState;
    }

    public void setDeliveryState(Integer deliveryState) {
        this.deliveryState = deliveryState;
    }

    public Boolean getSendEmailRule() {
        return sendEmailRule;
    }

    public void setSendEmailRule(Boolean sendEmailRule) {
        this.sendEmailRule = sendEmailRule;
    }

    public Float getOriginAmount() {
        return originAmount;
    }

    public void setOriginAmount(Float originAmount) {
        this.originAmount = originAmount;
    }

    public Commodity getItemCommodity() {
        return itemCommodity;
    }

    public void setItemCommodity(Commodity itemCommodity) {
        this.itemCommodity = itemCommodity;
    }

    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public BigDecimal getTotalPayChannelDiscount() {
        return totalPayChannelDiscount;
    }

    public void setTotalPayChannelDiscount(BigDecimal totalPayChannelDiscount) {
        this.totalPayChannelDiscount = totalPayChannelDiscount;
    }

    public Price price() {
        return new Price(currency(), price);
    }

    public Price discountPrice() {
        return new Price(currency(), discountFee);
    }

    public Currency currency() {
        return Currency.parse(currency);
    }

    public String getDiscountSource() {
        return discountSource;
    }

    public Integer getDeliveryProvider() {
        return deliveryProvider;
    }

    public void setDeliveryProvider(Integer deliveryProvider) {
        this.deliveryProvider = deliveryProvider;
    }

    public void setDiscountSource(String discountSource) {
        this.discountSource = discountSource;
    }

    public Integer getUsedQuota() {
        return usedQuota;
    }

    public void setUsedQuota(Integer usedQuota) {
        this.usedQuota = usedQuota;
    }

    /**
     * 子项状态
     *
     * @return
     */
    public OrderItemState orderItemState() {
        return OrderItemState.parse(state);
    }

    /**
     * 获取子项商品实付金额（含税）
     * 计算公式：商品现价 * 数量 + 税费 - 优惠金额
     *
     * @return
     */
    @JSONField(serialize = false)
    public BigDecimal getTotalPaidAmount() {
        return this.getItemTotalAmount().add(new BigDecimal(String.valueOf(totalTax))).subtract(new BigDecimal(String.valueOf(totalDiscount)));
    }

    /**
     * 获取商品实付金额（不含税）
     * 计算公式：商品现价 * 数量 - 优惠金额
     *
     * @return
     */
    @JSONField(serialize = false)
    public BigDecimal getNetPaymentAmount() {
        return this.getItemTotalAmount().subtract(new BigDecimal(String.valueOf(totalDiscount)));
    }

    /**
     * 获取商品现价总额
     *
     * @return
     */
    @JSONField(serialize = false)
    public BigDecimal getItemTotalAmount() {
        return new BigDecimal(String.valueOf(price)).multiply(new BigDecimal(String.valueOf(number)));
    }

    /**
     * 获取商品原价总额
     *
     * @return
     */
    @JSONField(serialize = false)
    public BigDecimal getItemTotalOriginAmount() {
        return new BigDecimal(String.valueOf(originAmount)).multiply(new BigDecimal(String.valueOf(number)));
    }

    /**
     * 获取商品优惠总额（旧的方式,不建议使用这个,可能存在误差）
     *
     * @return
     */
    @JSONField(serialize = false)
    public BigDecimal getTotalOldDiscountAmount() {
        return Objects.isNull(discountFee) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(discountFee)).multiply(new BigDecimal(String.valueOf(number)));
    }

    /**
     * 获取商品实际支付总额
     *
     * @return
     */
    @JSONField(serialize = false)
    public BigDecimal getItemTotalAmountPaid() {
        // 商品优惠折扣金额
        BigDecimal itemDiscount = Objects.isNull(totalDiscount) || totalDiscount.compareTo(BigDecimal.ZERO) == 0 ? this.getTotalOldDiscountAmount() : totalDiscount;
        // 商品支付优惠折扣金额
        BigDecimal paymentDiscount = Objects.isNull(totalPayChannelDiscount) ? BigDecimal.ZERO : totalPayChannelDiscount;
        return this.getItemTotalAmount().subtract(itemDiscount).subtract(paymentDiscount);
    }

    /**
     * 发货状态
     *
     * @return
     */
    public OrderItemState deliveryState() {
        return OrderItemState.parse(deliveryState);
    }

    /**
     * 获取配送商
     * @return
     */
    public DeliveryProvider deliveryProvider() {
        return DeliveryProvider.matchType(deliveryProvider);
    }

    /**
     * 单个子项的应付价格
     *
     * @return
     */
    @JSONField(serialize = false)
    public Price getTotalItemPayPrice() {
        return new Price(currency(), price - discountFee);
    }

    @JSONField(serialize = false)
    public String getUniqueKey() {
        return commodity + "_" + (Objects.isNull(belongToCommodityId) ? commodity : belongToCommodityId);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OrderItem orderItem = (OrderItem) o;
        return Objects.equals(getId(), orderItem.getId())
                && Objects.equals(getOrder(), orderItem.getOrder())
                && Objects.equals(getProduct(), orderItem.getProduct())
                && Objects.equals(getCommodity(), orderItem.getCommodity())
                && Objects.equals(getPrice(), orderItem.getPrice())
                && Objects.equals(getDiscountFee(), orderItem.getDiscountFee())
                && Objects.equals(getCurrency(), orderItem.getCurrency())
                && Objects.equals(getNumber(), orderItem.getNumber())
                && Objects.equals(getIsGift(), orderItem.getIsGift())
                && Objects.equals(getState(), orderItem.getState())
                && Objects.equals(getReviewState(), orderItem.getReviewState())
                && Objects.equals(getBelongToCommodityId(), orderItem.getBelongToCommodityId())
                && Objects.equals(getDeliveryState(), orderItem.getDeliveryState())
                && Objects.equals(getSendEmailRule(), orderItem.getSendEmailRule())
                && Objects.equals(getTotalTax(), orderItem.getTotalTax())
                && Objects.equals(getTotalDiscount(), orderItem.getTotalDiscount())
                && Objects.equals(getTotalPayChannelDiscount(), orderItem.getTotalPayChannelDiscount())
                && Objects.equals(getDiscountSource(), orderItem.getDiscountSource())
                && Objects.equals(getUsedQuota(), orderItem.getUsedQuota())
                ;
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getOrder(), getProduct(), getCommodity(), getPrice(), getDiscountFee(), getCurrency(),
                getNumber(), getIsGift(), getState(), getReviewState(), getBelongToCommodityId(), getDeliveryState(),
                getSendEmailRule(), getTotalTax(), getTotalDiscount(), getTotalPayChannelDiscount(), getDiscountSource(), getUsedQuota());
    }

    @Override
    public String toString() {
        return "OrderItem{" +
                "id=" + id +
                ", order=" + order +
                ", product=" + product +
                ", commodity=" + commodity +
                ", price=" + price +
                ", originAmount=" + originAmount +
                ", discountFee=" + discountFee +
                ", currency='" + currency + '\'' +
                ", number=" + number +
                ", isGift=" + isGift +
                ", state=" + state +
                ", reviewState=" + reviewState +
                ", belongToCommodityId=" + belongToCommodityId +
                ", deliveryState=" + deliveryState +
                ", sendEmailRule=" + sendEmailRule +
                ", totalTax=" + totalTax +
                ", totalDiscount=" + totalDiscount +
                ", discountSource='" + discountSource + '\'' +
                ", usedQuota=" + usedQuota +
                ", totalPayChannelDiscount=" + totalPayChannelDiscount +
                ", itemCommodity=" + itemCommodity +
                '}';
    }
}
