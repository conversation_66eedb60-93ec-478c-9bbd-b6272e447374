package com.insta360.store.business.meta.enums;

import com.insta360.compass.core.util.StringUtil;

/**
 * <AUTHOR>
 * @Description 推单拦截类型枚举
 * @Date 2024/8/21 上午11:31
 */
public enum OrderPushBlockReasonTypeEnum {

    /**
     * 不是已支付状态
     */
    NOT_PAYED_STATE("不是已支付状态"),

    /**
     * 存在待审核售后单
     */
    RMA_ORDER("存在待审核售后单"),

    /**
     * 商品全部售后
     */
    ALL_RMA_REFUNDED("商品全部售后"),

    /**
     * 以旧换新订单
     */
    TRADE_UP_ORDER("以旧换新订单"),

    /**
     * 包含VB预售产品
     */
    VB_PRODUCT("包含VB预售产品"),

    /**
     * 交易对账异常
     */
    TRANSACTION_RECONCILIATION("交易对账异常"),

    /**
     * 包含定制贴
     */
    CUSTOMIZED_STICKERS("包含定制贴"),

    /**
     * 德国EORI订单
     */
    EORI("德国EORI订单"),

    /**
     * 包含ROC关键词
     */
    ROC("包含ROC关键词"),

    /**
     * 卖家备注不为空
     */
    ADMIN_REMARK("卖家备注不为空"),

    /**
     * 订单折扣&赠品价值风控
     */
    LOW_DISCOUNT_OR_GIFT_RULE("订单折扣&赠品价值风控"),

    /**
     * 订单价格低于设定阈值
     */
    BELOW_MIN_PRICE("订单价格低于设定阈值"),

    /**
     * 实际支付金额小数点超过三位（含）
     */
    EXCEEDS_THREE_DIGITS("实际支付金额小数点超过三位（含）"),

    /**
     * 商品数量超过限制
     */
    PRODUCT_NUM_OVER_LIMIT("商品数量超过限制"),

    /**
     * 支付风险订单
     */
    PAY_NOT_SECURITY("支付风险订单"),

    /**
     * 匹配到行为不端用户
     */
    MIS_BEHAVIOR("匹配到行为不端用户"),

    /**
     * 关闭了自动推单
     */
    ORDER_PUSH_TURNED_OFF("关闭了自动推单"),

    /**
     * 不满足配置条件
     */
    NOT_SATISFIED_CONFIGURATION("不满足配置条件"),

    /**
     * 重复下单
     */
    REPEAT_ORDER("重复下单"),

    /**
     * NCC订单审核
     */
    NCC_ORDER("需台湾NCC审核订单"),

    /**
     * 美国不可发货州
     */
    US_NON_SHIPPABLE_STATE("因US关税不可发货拦截"),

    ;

    private final String desc;

    OrderPushBlockReasonTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 解析
     *
     * @param desc
     * @return
     */
    public static OrderPushBlockReasonTypeEnum parse(String desc) {
        if (StringUtil.isBlank(desc)) {
            return null;
        }
        for (OrderPushBlockReasonTypeEnum item : values()) {
            if (item.desc.equals(desc)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 包装成日志形式的表述
     *
     * @return
     */
    public String toLogBasedExpression() {
        return "自动推单校验失败，" + this.desc + "。";
    }
}
