package com.insta360.store.service.controller.order.controller;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.security.annotation.Authorization;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.order.dto.OrderDTO;
import com.insta360.store.business.order.dto.OrderTradeDTO;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.aop.annotation.LogOrderStateChange;
import com.insta360.store.business.order.service.impl.handler.OrderOverseasInvoiceHelper;
import com.insta360.store.business.prime.error.PrimeInstaErrorCode;
import com.insta360.store.business.trade.dto.condition.StockCheckResult;
import com.insta360.store.business.trade.exception.TradeErrorCode;
import com.insta360.store.business.trade.service.ExtraTradeFeeService;
import com.insta360.store.business.trade.service.StockService;
import com.insta360.store.business.trade.service.TradeService;
import com.insta360.store.business.trade.service.impl.creation.TradeCreationCache;
import com.insta360.store.business.trade.service.impl.creation.TradeCreator;
import com.insta360.store.business.trade.service.impl.helper.OrderCustomsTaxHelper;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.StoreGuestOrderTokenHelper;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.common.StoreApiHeader;
import com.insta360.store.service.controller.order.format.OrderPack;
import com.insta360.store.service.controller.order.format.OrderSimpleInfoPack;
import com.insta360.store.service.controller.order.vo.OrderTradeVO;
import com.insta360.store.service.controller.order.vo.OrderVO;
import com.insta360.store.service.controller.order.vo.info.OrderInfoVO;
import com.insta360.store.service.controller.trade.format.OrderSheetParaHelper;
import com.insta360.store.service.controller.trade.vo.CountryConfigVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * service/order
 * @Author: hyc
 * @Date: 2019/2/16
 * @Description:
 */
@RestController
public class OrderApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderApi.class);

    @Autowired
    OrderPack orderPack;

    @Autowired
    StockService stockService;

    @Autowired
    TradeService tradeService;

    @Autowired
    TradeCreator tradeCreator;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderSimpleInfoPack orderSimpleInfoPack;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    OrderSheetParaHelper orderSheetParaHelper;

    @Autowired
    ExtraTradeFeeService extraTradeFeeService;

    @Autowired
    StoreGuestOrderTokenHelper guestOrderTokenHelper;

    @Autowired
    OrderOverseasInvoiceHelper orderOverseasInvoiceHelper;

    @Autowired
    OrderCustomsTaxHelper orderCustomsTaxHelper;

    /**
     * 获取支持下单的国家
     *
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @GetMapping("/store/trade/order/getSupportedCountries")
    public Response<? extends Map> getSupportedCountries() {
        InstaCountry country = getApiCountry();
        CountryConfig countryConfig = countryConfigService.getByCountry(country);

        CountryConfigVO countryConfigVO = new CountryConfigVO(null);
        countryConfigVO.setCountryCode(countryConfig.getCountryCode());
        countryConfigVO.setName(countryConfig.getCountryText());
        countryConfigVO.setCountry(countryConfig.getCountryText());
        countryConfigVO.setPhoneCode(countryConfig.getPhoneCode());

        List<CountryConfigVO> countries = Arrays.asList(countryConfigVO);
        return Response.ok("supported_countries", countries);
    }

    /**
     * 获取下单相关信息
     *
     * @param orderTradeParam
     * @return
     */
//    @AvoidRepeatableCommit(timeOut = 200)
    @PostMapping(path = "/store/trade/order/getTradeData", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<OrderTradeVO> getTradeData(@RequestBody OrderTradeDTO orderTradeParam) {
        OrderSheet orderSheet = orderSheetParaHelper.getBasicOrderSheet(this, orderTradeParam);

        // 封装相关信息
        OrderTradeVO orderTradeVO = new OrderTradeVO();

        // 检测库存
        StockCheckResult stockCheckResult = stockService.checkTradeDataStock(orderSheet);
        orderTradeVO.setStockOk(stockCheckResult.getStockOk());

        if (!stockCheckResult.getStockOk()) {
            orderTradeVO.setStockDetail(stockCheckResult.getStockDetail());
        }

        OrderCreation orderCreation = tradeCreator.createFakeOrder(orderSheet);

        // 运费、税费、包邮门槛差额
        orderTradeVO.setTax(extraTradeFeeService.calculateOrderTax(orderSheet, orderCreation));
        orderTradeVO.setShippingFee(extraTradeFeeService.calculateOrderShippingFee(orderCreation));
        orderTradeVO.setFreeDifference(extraTradeFeeService.calculateOrderShippingFreeDifference(orderCreation));
        return Response.ok(orderTradeVO);
    }

    /**
     * 创建订单
     *
     * @param orderTradeParam
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @LogOrderStateChange
    @PostMapping(path = "/store/trade/order/create", consumes = MediaType.APPLICATION_JSON_VALUE)
    @TraceLog(logPrefix = "[订单创建]", logPrefixSub = "订单创建入口")
    public Response<? extends Map> create(@RequestBody OrderTradeDTO orderTradeParam) {
        OrderSheet orderSheet = null;
        try {
            orderSheet = orderSheetParaHelper.getOrderSheet(this, orderTradeParam);
        } catch (Exception e) {
            FeiShuMessageUtil.storeGeneralMessage("订单参数有误：" + orderTradeParam, FeiShuGroupRobot.InternalWarning);
            FeiShuMessageUtil.storeGeneralMessage(e.getLocalizedMessage() + " " + apiHeaderParser.parseHeaderValue(request, StoreApiHeader.StoreToken.getKey()), FeiShuGroupRobot.InternalWarning);
            LOGGER.error("订单初步校验发生异常" + e.getMessage(), e);
            // 有异常往外抛
            throw e;
        }
        // 下单信息必须真实有效
        if (orderSheet == null) {
            throw new InstaException(TradeErrorCode.InvalidOrderSheetException);
        }

        try {
            // create order
            Order order = tradeService.createTrade(orderSheet);

            OrderInfoVO orderInfo = new OrderInfoVO();
            orderInfo.setId(order.getId());
            orderInfo.setOrderId(order.getId());
            orderInfo.setOrderNumber(order.getOrderNumber());
            orderInfo.setOrder_number(order.getOrderNumber());
            return Response.ok("order", orderInfo);
        } catch (Exception e) {
            LOGGER.error("订单场景异常... 请求参数:{}", JSON.toJSONString(orderTradeParam), e);
            if (e instanceof InstaException) {
                if (CloudSubscribeErrorCode.RepeatBuyCloudSubscriptionProductException.getCode().intValue() == ((InstaException) e).getErrorCode()) {
                    throw e;
                }
                if (TradeErrorCode.NotSupportedOrderAreaException.getCode().intValue() == ((InstaException) e).getErrorCode()) {
                    throw e;
                }
                PrimeInstaErrorCode[] primeInstaErrorCodes = PrimeInstaErrorCode.values();
                List<Integer> primeErrorCode = Arrays.stream(primeInstaErrorCodes).map(PrimeInstaErrorCode::getCode).collect(Collectors.toList());
                if (primeErrorCode.contains(((InstaException) e).getErrorCode())) {
                    throw e;
                }

            }
            throw new InstaException(TradeErrorCode.InvalidOrderSheetException);
        }
    }

    /**
     * 获取us子项总税费
     *
     * @param orderTradeParam
     * @return
     */
    @PostMapping(path = "/store/trade/order/getCustomsTax", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<OrderTradeVO> getCustomsTax(@RequestBody OrderTradeDTO orderTradeParam) {
        // 只处理us地区
        if (!InstaCountry.US.equals(getApiCountry())) {
            return Response.ok();
        }
        OrderSheet orderSheet = orderSheetParaHelper.getBasicOrderSheet(this, orderTradeParam);
        StoreAccount storeAccount = getAccessUser();
        if (storeAccount != null) {
            // 登录的话拿到登录的账户邮箱
            orderSheet.setContactEmail(storeAccount.getUsername());
        } else {
            // 未登录的话获取游客下单的联系邮箱
            orderSheet.setContactEmail(orderTradeParam.getContactEmail());
        }

        // 封装相关信息
        OrderTradeVO orderTradeVo = new OrderTradeVO();

        OrderCreation orderCreation = tradeCreator.createFakeOrder(orderSheet);

        TradeCreationCache tradeCreationCache = new TradeCreationCache(orderSheet);

        // 运费、税费、包邮门槛差额
        orderTradeVo.setCustomsTax(orderCustomsTaxHelper.calculateOrderCustomsTax(orderCreation, tradeCreationCache));
        return Response.ok(orderTradeVo);
    }

    /**
     * 获取订单信息，要求鉴权（详情版，目前用于/order/detail页面调用）
     *
     * @param orderNumber 订单号
     * @param checkNumber RSA加密订单号后的数据
     * @param guestToken  游客订单分配的token（保留是用于兼容已经发送过携带该token的邮件内容可以正常跳转）
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization
    @GetMapping("/store/trade/order/getOrderInfo")
    public Response<? extends Map> getOrderInfo(@RequestParam(required = false, value = "order_number") String orderNumber,
                                                @RequestParam(required = false, value = "check_number") String checkNumber,
                                                @RequestParam(required = false, value = "guest_token") String guestToken) {
        // 解密后的订单号需要和传入的一致
        if (checkNumber != null && !RSAUtil.decryptByPri(checkNumber).equals(orderNumber)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 游客订单Token需要和传入的订单号一致
        String verifyToken = guestOrderTokenHelper.verify(guestToken);
        if (verifyToken != null && !verifyToken.equals(orderNumber)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 工单订单不走该接口
        if (order.isRepairOrder()) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 自己只能获取自己的订单
        StoreAccount storeAccount = getAccessUser();
        if (!storeAccount.getInstaAccount().equals(order.getUserId())) {
            throw new InstaException(OrderErrorCode.OrderAcountNotMatchException);
        }

        return Response.ok("order", orderPack.doPack(order, getApiLanguage()));
    }

    /**
     * 获取订单信息，无须鉴权（精简版，目前用于/order/transfer、/order/pay两个页面调用）
     *
     * @param orderNumber
     * @param checkNumber
     * @return
     */
    @GetMapping("/store/trade/order/getSimpleOrderInfo")
    public Response<? extends Map> getSimpleOrderInfo(@RequestParam(value = "orderNumber") String orderNumber,
                                                      @RequestParam(value = "checkNumber") String checkNumber) {
        // 解密后的订单号需要和传入的一致
        if (!RSAUtil.decryptByPri(checkNumber).equals(orderNumber)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        return Response.ok("simpleOrderInfo", orderSimpleInfoPack.doPackSimpleOrderInfo(order, getApiLanguage()));
    }

    /**
     * 获取工单订单信息
     *
     * @param orderNumber
     * @param checkOrder
     * @return
     */
    @AvoidRepeatableCommit
    @GetMapping("/store/trade/order/getRepairOrderInfo")
    public Response<? extends Map> getRepairOrderInfo(@RequestParam(required = false, value = "order_number") String orderNumber,
                                                      @RequestParam(required = false, value = "checkOrder") String checkOrder) {
        if (StringUtils.isBlank(orderNumber) || StringUtils.isBlank(checkOrder)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        if (!RSAUtil.decryptByPri(checkOrder).equals(orderNumber)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 排除非工单订单
        if (!order.isRepairOrder()) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        return Response.ok("order", orderPack.doRepairPack(order, getApiLanguage()));
    }

    /**
     * 分页获取当前用户的所有订单
     *
     * @param pageSize
     * @param pageNumber
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization
    @GetMapping("/store/trade/order/getUserOrders")
    public Response<? extends Map> getUserOrders(@RequestParam(required = false, value = "page_size") Integer pageSize,
                                                 @RequestParam(required = false, value = "page_number") Integer pageNumber) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber, 1);
        pageQuery.setPageSize(pageSize, 10);
        PageResult<Order> orderPageResult = orderService.getUserOrders(getAccessUser(), pageQuery);
        List<Order> orders = orderPageResult.getList();
        List<OrderVO> orderVos = orderPack.packUserOrders(orders, getApiLanguage());

        PageResult<OrderVO> ordersPageResult = new PageResult<>();
        ordersPageResult.setList(orderVos);
        ordersPageResult.setTotalCount(orderPageResult.getTotalCount());
        ordersPageResult.setTotalPage(orderPageResult.getTotalPage());
        ordersPageResult.setPageSize(orderPageResult.getPageSize());
        ordersPageResult.setPageNumber(orderPageResult.getPageNumber());
        return Response.ok(ordersPageResult);
    }

    /**
     * 取消订单
     *
     * @param orderParam
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @LogOrderStateChange
    @Authorization
    @PostMapping(path = "/store/trade/order/cancel", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> cancel(@RequestBody OrderDTO orderParam) {
        Order order = orderService.getByOrderNumber(orderParam.getOrderNumber());
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 取消订单
        orderService.cancelOrder(order.getId(), getAccessUser(), orderParam.getReason());
        return Response.ok();
    }

    /**
     * 确认收货
     *
     * @param orderParam
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @LogOrderStateChange
    @Authorization
    @PostMapping(path = "/store/trade/order/confirmDelivery", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> confirmDelivery(@RequestBody OrderDTO orderParam) {
        Order order = orderService.getByOrderNumber(orderParam.getOrderNumber());
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 确认收货
        orderService.confirmDelivery(order.getId(), getAccessUser());
        return Response.ok();
    }

    /**
     * 获取（海外）订单发票pdf下载链接
     *
     * @param orderNumber
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization
    @GetMapping("/store/trade/order/getOrderInvoicePdfUrl")
    public Response<Object> getOrderInvoicePdfUrl(@RequestParam String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 自己只能获取自己的订单
        StoreAccount storeAccount = getAccessUser();
        if (!storeAccount.getInstaAccount().equals(order.getUserId())) {
            throw new InstaException(OrderErrorCode.OrderAcountNotMatchException);
        }

        String downloadUrl = orderOverseasInvoiceHelper.orderInfoInvoicePrint(order.getId());
        return Response.ok(downloadUrl);
    }
}
