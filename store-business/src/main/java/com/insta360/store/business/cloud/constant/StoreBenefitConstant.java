package com.insta360.store.business.cloud.constant;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.cloud.enums.SubscribeSubStatus;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/15
 */
public class StoreBenefitConstant {

    /**
     * 初始期数
     */
    public static final int INITIAL_PERIOD = 1;

    /**
     * 一周期
     */
    public static final int ONE_CYCLE = 12;

    /**
     * 启用
     */
    public static final Integer ENABLE = 1;

    /**
     * 禁用
     */
    public static final Integer DISABLE = 0;

    /**
     * 固定折扣率
     */
    public static final BigDecimal fIXED_DISCOUNT_RATE = new BigDecimal("0.2");

    /**
     * app渠道限制sku
     */
    public static final List<String> LIMIT_SKU_LIST = Lists.newArrayList("1024.365.1", "2048.365.1", "200.365.1", "1024.30.1", "2048.30.1", "200.30.1", "200.30.2", "200.30.3", "200-30-1", "200-30-2", "200-30-3", "1024-30-1", "2048-30-1", "1024-365-1", "200-365-1", "2048-365-1", "200_365_1", "1024_365_1", "2048_365_1", "200_30_1", "1024_30_1", "2048_30_1");

    /**
     * 默认可用额度
     */
    public static final int DEFAULT_AVAILABLE_QUOTA = 10;

    /**
     * 云存储渠道互斥错误码
     */
    public static final Integer CHANNEL_MUTUALLY_EXCLUSIVE_ERROR_CODE = 1003305;

    /**
     * 取消自动续费兜底原因
     */
    public static final String CANCEL_CLOUD_SUBSCRIBE_REASON = "User turns off auto-renewal without reason";

    /**
     * care或者延保权益初始额度
     */
    public static int CARE_OR_EXTEND_INIT_QUOTA = 1;

    /**
     * 配件折扣权益初始额度
     */
    public static int DISCOUNT_INIT_QUOTA = 10;

    /**
     * 小时转换成毫秒
     */
    public static final long HOURS_TO_MILLISECONDS = 60 * 60 * 1000L;

    /**
     * 天转换成毫秒
     */
    public static final long DAYS_TO_MILLISECONDS = 24 * 60 * 60 * 1000L;

    /**
     * 年包自动续费提醒时间
     */
    public static final int YEARLY_RENEW_REMIND_DAY = 16;

    /**
     * 月包自动续费提醒时间
     */
    public static final int MONTHLY_RENEW_REMIND_DAY = 4;

    /**
     * 订阅续费前置子状态集合
     */
    public static final List<Integer> SUBSCRIPTION_RENEWAL_PRE_SUB_STATES = Lists.newArrayList(SubscribeSubStatus.SUBSCRIBE_ING.getCode(), SubscribeSubStatus.EMAIL_HANDLE_ING.getCode());

    /**
     * 可升级窗口时间限制
     */
    public static final long UPGRADE_WINDOW_LIMIT_HOURS = 48 * 60 * 60 * 1000L;

    /**
     * 升级退款原因
     */
    public static final String UPGRADE_REFUND_REASON = "Upgrade Refund";

    /**
     * 配件八折权益限制国家
     */
    public static final List<String> DISCOUNT_BENEFIT_DISABLE_COUNTRY = Lists.newArrayList(InstaCountry.US.name(), InstaCountry.CN.name());
}
