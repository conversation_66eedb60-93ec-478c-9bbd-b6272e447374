package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.commodity;

import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import com.insta360.store.business.outgoing.rpc.store.job.*;
import feign.RetryableException;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: wkx
 * @Date: 2023/11/15
 * @Description:
 */
@Component
public class CommodityFunctionDescriptionCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommodityFunctionDescriptionCachePutHandler.class);

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Autowired
    HomeItemCachePutService homeItemCachePutService;

    @Autowired
    HomepageCachePutService homepageCachePutService;

    @Autowired
    CategoryPageCachePutService categoryPageCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}。", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(null, countDownLatch));
        cachePutThreadPool.execute(() -> this.task2(null, countDownLatch));
        cachePutThreadPool.execute(() -> this.task3(null, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    public CachePutKeyParameterBO cacheParamParse(JoinPoint joinPoint) {
        return null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.COMMODITY_FUNCTION_DESCRIPTION;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.HOME_PAGE_KEY, CacheableType.CATEGORY_PAGE);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            // 更新首页
            CacheConstant.COUNTIES.forEach(cacheCounties ->
                    homepageCachePutService.listHomepage(cacheCounties.getCountry(), cacheCounties.getLanguage()));
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
        try {
            // 更新全部类目
            CacheConstant.COUNTIES.forEach(cacheCounties ->
                    categoryPageCachePutService.listAllCategory(cacheCounties.getCountry(), cacheCounties.getLanguage()));
            countDownLatch.countDown();
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务2完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task2}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {
        try {
            // 更新类目页
            List<HomepageItemMain> homepageItemMains = homepageItemMainService.listEnableHomeItem();
            homepageItemMains.forEach(homepageItemMain ->
                    CacheConstant.COUNTIES.forEach(cacheCounties ->
                            homeItemCachePutService.listHomeItemInfoByHomeItemType(homepageItemMain.getId(),
                                    cacheCounties.getCountry(), cacheCounties.getLanguage())));
            countDownLatch.countDown();
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务3完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task3}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }
}
