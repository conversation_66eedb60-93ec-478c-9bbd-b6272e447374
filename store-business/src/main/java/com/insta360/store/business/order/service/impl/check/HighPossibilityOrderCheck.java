package com.insta360.store.business.order.service.impl.check;

import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.meta.enums.OrderPushBlockReasonTypeEnum;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemBindService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.impl.helper.OrderPushAutoHelper;
import com.insta360.store.business.outgoing.mq.guanyi.helper.GyMessageSendHelper;
import com.insta360.store.business.payment.enums.PaymentReconciliationStatus;
import com.insta360.store.business.payment.model.OrderPaymentTransactionReconciliation;
import com.insta360.store.business.payment.service.OrderPaymentTransactionReconciliationService;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import com.insta360.store.business.trade.model.EngravingImage;
import com.insta360.store.business.trade.service.EngravingImageService;
import com.insta360.store.business.tradeup.model.TradeupOrder;
import com.insta360.store.business.tradeup.service.TradeupOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 对订单检查的具体实现类
 *
 * <AUTHOR>
 */
@Component
public class HighPossibilityOrderCheck extends BaseOrderPushAutoCheckChain {

    @Autowired
    GyMessageSendHelper gyMessageSendHelper;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    TradeupOrderService tradeupOrderService;

    @Autowired
    OrderPaymentTransactionReconciliationService orderPaymentTransactionReconciliationService;

    @Autowired
    OrderItemBindService orderItemBindService;

    @Autowired
    EngravingImageService engravingImageService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderPushAutoHelper orderPushAutoHelper;

    @Override
    public boolean doCheck(Order order) {
        return haveRmaRefundOrder(order) && isTradeUpOrder(order) && ifVbProduct(order) && ifTransactionException(order) && ifIncludeCustomizedStickers(order);
    }

    /**
     * 订单最终是否被推送至OMS，还需要一些过滤条件
     * 以下条件return true 视为满足该子条件；全部条件满足，才会进行自动备货
     * a.
     * 1.有无售后单
     * 无  直接推
     * 有  2.售后状态有无待审核
     * 有  不推
     * 无  3.订单商品是否全部售后
     * 是  不推
     * 否  推
     *
     * @param order
     * @return true 视为满足该条件 ; false不满足条件 满足所有条件则自动推单
     */
    private boolean haveRmaRefundOrder(Order order) {
        List<RmaOrder> rmaOrders = rmaOrderService.getRmaOrdersByRmaType(RmaType.rma_refund.name(), order.getId());
        if (CollectionUtils.isEmpty(rmaOrders)) {
            return true;
        }
        // 售后单中有待审核的
        if (rmaOrders.stream().anyMatch(rmaOrder -> RmaState.init.equals(RmaState.parse(rmaOrder.getState())))) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.RMA_ORDER, null, order.getOrderNumber(), order.getId());
            return false;
        }
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        // 商品是否全部售后
        boolean allRefunded = orderItemList.stream().map(OrderItem::orderItemState).allMatch(state -> OrderItemState.refunded == state);
        if (allRefunded) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.ALL_RMA_REFUNDED, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * b.以旧换新订单
     *
     * @param order
     * @return
     */
    private boolean isTradeUpOrder(Order order) {
        TradeupOrder tradeupOrder = tradeupOrderService.getByOrder(order.getId());
        // 以旧换新订单，不推
        if (tradeupOrder != null) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.TRADE_UP_ORDER, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * c.是否包含VB预售产品
     *
     * @param order
     * @return
     */
    private boolean ifVbProduct(Order order) {
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        // 是否包含VB预售产品
        boolean existVbProduct = orderItemList.stream().anyMatch(item -> item.getCommodity() == Commodity.VB_PRESALE_ID);
        if (existVbProduct) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.VB_PRODUCT, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * d.交易对账是否异常
     *
     * @param order
     * @return
     */
    private boolean ifTransactionException(Order order) {
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        // 支付渠道是支付宝，无需判断是否交易对账
        if (PaymentChannel.alipay.equals(orderPayment.paymentChannel())) {
            return true;
        }
        OrderPaymentTransactionReconciliation reconciliation = orderPaymentTransactionReconciliationService.getByOrderNumber(order.getOrderNumber());
        if (reconciliation == null || reconciliation.parseStatus() != PaymentReconciliationStatus.RECONCILED) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.TRANSACTION_RECONCILIATION, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

    /**
     * o.订单包含定制贴
     *
     * @param order
     * @return
     */
    private boolean ifIncludeCustomizedStickers(Order order) {
        List<EngravingImage> engravingImages = engravingImageService.listByOrderNumber(order.getOrderNumber());
        if (CollectionUtils.isNotEmpty(engravingImages)) {
            orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.CUSTOMIZED_STICKERS, null, order.getOrderNumber(), order.getId());
            return false;
        }
        return true;
    }

}
