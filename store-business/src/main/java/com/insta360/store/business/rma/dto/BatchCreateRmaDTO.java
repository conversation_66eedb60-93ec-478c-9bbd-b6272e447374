package com.insta360.store.business.rma.dto;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 批量创建售后DTO
 * @Date 2022/6/7
 */
public class BatchCreateRmaDTO implements Serializable {

    /**
     * 售后类型
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    @NotBlank(message = "售后类型不能为空")
    private String rmaType;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @Min(value = 1)
    private Integer orderId;

    /**
     * 客服备注
     */
    private String adminRemark;

    /**
     * 主责归因
     */
    @NotNull(message = "主责归因不能为空")
    private Integer rmaMainDuty;

    /**
     * 售后原因
     */
    @NotBlank(message = "售后原因不能为空")
    private String adminReason;

    /**
     * 是否需要退回
     */
    @NotNull(message = "必须指定是否需退回")
    private Boolean needReturn;

    /**
     * 是否退回额度
     */
    private Boolean returnQuota;

    /**
     * 是否关闭自动订阅
     */
    private Boolean closeAutoSubscribe;

    /**
     * 售后商品
     */
    @NotEmpty(message = "售后商品不能为空")
    @Valid
    private List<ReturnItemDTO> refundItemList;

    /**
     * 平台来源
     */
    private Integer platformSource;

    /**
     * 是否终止权益（用于psp）
     */
    private Boolean terminationEquity;

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getAdminRemark() {
        return adminRemark;
    }

    public void setAdminRemark(String adminRemark) {
        this.adminRemark = adminRemark;
    }

    public String getAdminReason() {
        return adminReason;
    }

    public void setAdminReason(String adminReason) {
        this.adminReason = adminReason;
    }

    public List<ReturnItemDTO> getRefundItemList() {
        return refundItemList;
    }

    public void setRefundItemList(List<ReturnItemDTO> refundItemList) {
        this.refundItemList = refundItemList;
    }

    public Boolean getNeedReturn() {
        return needReturn;
    }

    public void setNeedReturn(Boolean needReturn) {
        this.needReturn = needReturn;
    }

    public Integer getPlatformSource() {
        return platformSource;
    }

    public void setPlatformSource(Integer platformSource) {
        this.platformSource = platformSource;
    }

    public Integer getRmaMainDuty() {
        return rmaMainDuty;
    }

    public void setRmaMainDuty(Integer rmaMainDuty) {
        this.rmaMainDuty = rmaMainDuty;
    }

    public Boolean getReturnQuota() {
        return returnQuota;
    }

    public void setReturnQuota(Boolean returnQuota) {
        this.returnQuota = returnQuota;
    }

    public Boolean getCloseAutoSubscribe() {
        return closeAutoSubscribe;
    }

    public void setCloseAutoSubscribe(Boolean closeAutoSubscribe) {
        this.closeAutoSubscribe = closeAutoSubscribe;
    }

    public Boolean getTerminationEquity() {
        return terminationEquity;
    }

    public void setTerminationEquity(Boolean terminationEquity) {
        this.terminationEquity = terminationEquity;
    }

    @Override
    public String toString() {
        return "BatchCreateRmaDTO{" +
                "rmaType='" + rmaType + '\'' +
                ", orderId=" + orderId +
                ", adminRemark='" + adminRemark + '\'' +
                ", rmaMainDuty=" + rmaMainDuty +
                ", adminReason='" + adminReason + '\'' +
                ", needReturn=" + needReturn +
                ", returnQuota=" + returnQuota +
                ", closeAutoSubscribe=" + closeAutoSubscribe +
                ", refundItemList=" + refundItemList +
                ", platformSource=" + platformSource +
                ", terminationEquity=" + terminationEquity +
                '}';
    }
}
