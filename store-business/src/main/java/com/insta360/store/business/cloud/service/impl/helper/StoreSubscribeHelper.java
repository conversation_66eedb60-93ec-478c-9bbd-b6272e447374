package com.insta360.store.business.cloud.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.cloud.bo.CloudSubscribeChangeNotifyBO;
import com.insta360.store.business.cloud.dto.CloudSubscribeDTO;
import com.insta360.store.business.cloud.enums.*;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.model.CloudSubscribeAuthResult;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.CloudSubscribeAuthResultService;
import com.insta360.store.business.cloud.service.SubscribeBillingAddressService;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.forter.bo.ForterAuthResultBO;
import com.insta360.store.business.forter.handler.AdaptiveAuthHandler;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.payment.bo.ForterAuthBO;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.bo.PaymentSubscribeBO;
import com.insta360.store.business.payment.bo.PaymentSubscribeResultBO;
import com.insta360.store.business.payment.constants.OceanConstant;
import com.insta360.store.business.payment.enums.CkoSubPaymentMethod;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.service.impl.helper.*;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.service.CreditCardPaymentInfoService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.UserPayInfoService;
import com.insta360.store.business.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2024/05/24
 * @Description:
 */
@Component
public class StoreSubscribeHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreSubscribeHelper.class);

    @Autowired
    CkoPaymentHelper ckoPaymentHelper;

    @Autowired
    OceanPaymentHelper oceanPaymentHelper;

    @Autowired
    AdaptiveAuthHandler adaptiveAuthHandler;

    @Autowired
    PayPalPaymentHelper payPalPaymentHelper;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    CreditCardPaymentInfoService creditCardPaymentInfoService;

    @Autowired
    CloudServiceSubscribeEngineHelper cloudServiceSubscribeEngineHelper;

    @Autowired
    KlarnaPaymentHelper klarnaPaymentHelper;

    @Autowired
    OrderService orderService;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    SubscribeBillingAddressService subscribeBillingAddressService;

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    CloudSubscribeAuthResultService cloudSubscribeAuthResultService;

    /**
     * 根据支付机构删除快捷支付id信息（storageSubscribe & userPayInfo 更新处于同一事务）
     *
     * @param storageSubscribe
     * @param userPayInfo
     */
    public void deletePayInfo(CloudStorageSubscribe storageSubscribe, UserPayInfo userPayInfo) {
        // 重置支付信息
        this.resetPayInfo(userPayInfo, storageSubscribe.getOrderNumber());
        // 删除支付信息
        cloudStorageSubscribeService.deleteUserPayInfo(storageSubscribe, userPayInfo);
    }

    /**
     * 根据支付机构删除快捷支付id信息 （只更新userPayInfo）
     *
     * @param orderNumber
     * @param userPayInfo
     */
    public void deletePayInfo(String orderNumber, UserPayInfo userPayInfo) {
        // 重置支付信息
        this.resetPayInfo(userPayInfo, orderNumber);
        // 删除支付信息
        userPayInfoService.updateUserPayInfo(userPayInfo);
    }

    /**
     * 关闭升级订阅
     *
     * @param upgradeSubscribe
     */
    public void closeUpgradeSubscribe(CloudStorageSubscribe upgradeSubscribe) {
        // 关闭当前‘预处理’中的订阅
        cloudStorageSubscribeService.closePreSubscribe(upgradeSubscribe);
        // 旧订阅
        CloudStorageSubscribe oldSubscribe = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(upgradeSubscribe.getInstaAccount());
        if (Objects.nonNull(oldSubscribe) && SubscribeStatus.CLOSE_RENEW.equals(oldSubscribe.parseSubscribeStatus())) {
            // 升级订阅订单号
            String orderNumber = upgradeSubscribe.getOrderNumber();
            // 用户的支付信息
            UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(upgradeSubscribe.getInstaAccount());
            try {
                this.deletePayInfo(orderNumber, userPayInfo);
            } catch (Exception e) {
                LOGGER.error(String.format("升级失败，升级订阅重置快捷支付id信息异常 订单号: [%s], 支付信息: %s", orderNumber, JSON.toJSONString(userPayInfo)), e);
                FeiShuMessageUtil.storeGeneralMessage(String.format("升级失败，升级订阅重置快捷支付id信息异常 订单号: [%s], 支付信息: %s", orderNumber, JSON.toJSONString(userPayInfo)), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        }
    }

    /**
     * 根据支付机构删除快捷支付id信息并推送云存储
     *
     * @param storageSubscribe
     * @param userPayInfo
     */
    public void deletePayInfoNotify(CloudStorageSubscribe storageSubscribe, UserPayInfo userPayInfo) {
        // 删除支付信息
        try {
            this.deletePayInfo(storageSubscribe, userPayInfo);
        } catch (Exception e) {
            LOGGER.error(String.format("[商城云服务]'游客注销、主动取消自动续费等场景'重置快捷支付id信息异常 订阅记录: %s, 支付信息: %s, 场景类型: %s", JSON.toJSONString(storageSubscribe), JSON.toJSONString(userPayInfo)), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务]'游客注销、主动取消自动续费(如PayPal、客服、用户)等场景'重置快捷支付id信息异常. 订阅记录: %s, 支付信息: %s", JSON.toJSONString(storageSubscribe), JSON.toJSONString(userPayInfo), ServiceScenesType.CANCELED), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw e;
        }
        // 通知云存储
        CloudSubscribeChangeNotifyBO cloudSubscribeChangeNotifyBo = new CloudSubscribeChangeNotifyBO();
        cloudSubscribeChangeNotifyBo.setScenesType(ServiceScenesType.CANCELED);
        cloudSubscribeChangeNotifyBo.setSubscribeId(storageSubscribe.getId());
        cloudSubscribeChangeNotifyBo.setNotifyReason(storageSubscribe.getReason());
        cloudServiceSubscribeEngineHelper.subscribeChangeNotify(cloudSubscribeChangeNotifyBo);
    }

    /**
     * 更新卡信息/重新订阅
     *
     * @param cloudSubscribeParam
     * @param storeAccount
     * @param storageSubscribe
     * @param userPayInfo
     * @param paymentExtra
     * @return
     */
    public String updateSubscribeInformation(CloudSubscribeDTO cloudSubscribeParam, StoreAccount storeAccount, CloudStorageSubscribe storageSubscribe, UserPayInfo userPayInfo, PaymentExtra paymentExtra) {
        StorePaymentMethodEnum storePaymentMethodEnum = userPayInfo.parsePaymentMethod();
        switch (storePaymentMethodEnum) {
            case OCEAN_PAYMENT:
                // 如果不在续费周期内，则正常创建Pay ID和更新卡信息
                String oceanPayUrl = oceanPaymentHelper.getOceanSubscribeInfo(storeAccount, storageSubscribe.getOrderNumber(), userPayInfo, cloudSubscribeParam, paymentExtra);
                if (StringUtils.isNotBlank(oceanPayUrl)) {
                    return oceanPayUrl;
                }
                break;
            case CKO_PAYMENT:
                PaymentSubscribeBO paymentSubscribe = new PaymentSubscribeBO();
                BeanUtil.copyProperties(cloudSubscribeParam, paymentSubscribe);
                CommonUtil.validationObject(paymentSubscribe);
                paymentSubscribe.setStoreAccount(storeAccount);
                paymentSubscribe.setUserPayInfo(userPayInfo);

                PaymentSubscribeResultBO paymentSubscribeResult = ckoPaymentHelper.updateSubscribeInformation(paymentSubscribe, storageSubscribe.getOrderNumber());
                // 需要进行3ds校验
                String payUrl = paymentSubscribeResult.getPayUrl();
                if (StringUtil.isNotBlank(payUrl)) {
                    return payUrl;
                }
                // 参数校验
                if (StringUtil.isBlank(paymentSubscribeResult.getPayId())) {
                    LOGGER.info(String.format("paymentSubscribeResult: {%s}", paymentSubscribeResult));
                    throw new InstaException(CommonErrorCode.InvalidParameterException);
                }
                userPayInfo.setPayId(paymentSubscribeResult.getPayId());
                userPayInfo.setPaymentTradeId(paymentSubscribeResult.getPaymentId());
                userPayInfo.setCardType(paymentSubscribeResult.getCardType());
                userPayInfo.setCardCountry(paymentSubscribeResult.getCardCountry());
                userPayInfo.setDeductCategory(paymentSubscribeResult.getCardNumber());
                userPayInfo.setPaymentSubMethod(CkoSubPaymentMethod.CREDIT_CARD.getMethodParaName());
                break;
            case PAYPAL_PAYMENT:
                return payPalPaymentHelper.getVaultApproveUrl(storeAccount, userPayInfo, storageSubscribe.getOrderNumber(), cloudSubscribeParam.getSubscribeActionType());
            case KLARNA_PAYMENT:
                return klarnaPaymentHelper.updateSubscribeInformation(storeAccount, userPayInfo, storageSubscribe, cloudSubscribeParam.getSubscribeActionType());
            default:
                throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        cloudStorageSubscribeService.updateSubscribeAndUserPayInfo(storageSubscribe, userPayInfo);
        // 如果在续费周期内24h，还没扣款成功，则会尝试发起扣款
        paymentHelper.tryRetryRenewOrderPayment(storageSubscribe);
        return null;
    }

    /**
     * 订阅 forter auth处理
     *
     * @param forterAuthBo
     * @param adaptiveAuthType
     * @return
     */
    public ForterAuthResultBO subscribeForterAdaptiveAuth(ForterAuthBO forterAuthBo, SubscribeAdaptiveAuthType adaptiveAuthType) {
        CreditCardPaymentInfo cardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(forterAuthBo.getOrderNumber());
        if (Objects.isNull(cardPaymentInfo) || cardPaymentInfo.getPayChannelId() == -1) {
            // 默认钱海支付渠道
            forterAuthBo.setPayChannelId(OceanConstant.DEFAULT_OCEAN_CHANNEL);
        }
        // 按照支付时的支付渠道id
        else {
            forterAuthBo.setPayChannelId(cardPaymentInfo.getPayChannelId());
        }
        // 区分主动续费forter调用
        switch (adaptiveAuthType) {
            case PAYMENT_AND_UPDATE:
                return adaptiveAuthHandler.preForterAdaptiveAuth(forterAuthBo);
            default:
                return adaptiveAuthHandler.doPreForterAdaptiveAuth(forterAuthBo);
        }
    }

    /**
     * 保存订阅 auth result
     *
     * @param storeAccount
     * @param parsePaymentMethod
     * @param subscribeAction
     * @param paymentMethod
     * @param authCode
     * @param authText
     */
    @Async
    public void saveSubscribeAuthResult(StoreAccount storeAccount, StorePaymentMethodEnum parsePaymentMethod, CloudSubscribeActionEnum subscribeAction,
                                        String paymentMethod, String authCode, String authText) {
        CloudSubscribeAuthResult subscribeAuthResult = new CloudSubscribeAuthResult();
        subscribeAuthResult.setInstaAccount(storeAccount.getInstaAccount());
        subscribeAuthResult.setEmail(storeAccount.getUsername());
        subscribeAuthResult.setPsp(parsePaymentMethod.getName());
        subscribeAuthResult.setActionType(subscribeAction.getActionType());
        subscribeAuthResult.setAuthType(subscribeAction.getAuthType());
        subscribeAuthResult.setPayMethod(paymentMethod);
        subscribeAuthResult.setAuthText(authText);
        subscribeAuthResult.setAuthCode(authCode);
        subscribeAuthResult.setCreateTime(LocalDateTime.now());
        subscribeAuthResult.setUpdateTime(LocalDateTime.now());
        cloudSubscribeAuthResultService.save(subscribeAuthResult);
    }

    /**
     * 校验订阅机构和发起支付机构一致性
     *
     * @param orderNumber
     * @param userId
     * @param paymentMethod
     */
    public void doCheckPayMethod(String orderNumber, Integer userId, StorePaymentMethodEnum paymentMethod) {
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(userId);
        if (Objects.isNull(userPayInfo)) {
            LOGGER.error(String.format("升级订单支付时user pay info不存在！userId: {%s}，paymentMethod{%s}", userId, paymentMethod));
            FeiShuMessageUtil.storeGeneralMessage(String.format("升级订单支付时user pay info不存在！userId:{%s}", userId), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(OrderErrorCode.OrderInfoMissingException);
        }
        StorePaymentMethodEnum payInfoMethod = userPayInfo.parsePaymentMethod();
        if (Objects.isNull(payInfoMethod)) {
            LOGGER.error(String.format("升级订单支付时pay info paymentMethod不存在！userId:{%s}，paymentMethod{%s}", userId, paymentMethod));
            FeiShuMessageUtil.storeGeneralMessage(String.format("升级订单支付时pay info paymentMethod不存在！userId:{%s}", userId), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(OrderErrorCode.OrderInfoMissingException);
        }
        if (!payInfoMethod.equals(paymentMethod)) {
            LOGGER.error(String.format("升级订单支付时pay info paymentMethod不一致！userId:{%s}，paymentMethod{%s}, 订单号{%s}", userId, paymentMethod, orderNumber));
            FeiShuMessageUtil.storeGeneralMessage(String.format("升级订单支付时pay info paymentMethod不存在！userId:{%s} 订单号{%s}", userId, orderNumber), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(PaymentErrorCode.InvalidPaymentChannelException);
        }
    }

    /**
     * 续费订单支付前置校验
     *
     * @param order
     * @param userId
     */
    public void checkRenewOrderPayment(Order order, Integer userId) {
        // 非续费订单或者非未支付状态则抛异常
        if (!ServiceScenesType.RENEW.name().equals(order.getSubscribeScenesType()) || !OrderState.init.equals(order.orderState())) {
            LOGGER.info(String.format("非续费订单或者非未支付状态 订单信息【%s】", order));
            throw new InstaException(CloudSubscribeErrorCode.RenewOrderPaymentException);
        }

        // 不存在订阅记录/续订扣款中，不允许发起支付
        CloudStorageSubscribe storageSubscribe = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(userId);
        if (Objects.isNull(storageSubscribe)
                || SubscribeSubStatus.RENEW_DEDUCTION_ING.equals(SubscribeSubStatus.parse(storageSubscribe.getSubscribeSubState()))) {
            LOGGER.info(String.format("不存在订阅记录/续订扣款中，不允许发起支付 订阅信息【%s】", storageSubscribe));
            throw new InstaException(CloudSubscribeErrorCode.RenewOrderPaymentException);
        }

        // 非订阅中不允许发起支付
        SubscribeStatus subscribeStatus = SubscribeStatus.parse(storageSubscribe.getSubscribeState());
        if (!SubscribeStatus.SUBSCRIBE_ING.equals(subscribeStatus)) {
            LOGGER.info(String.format("非订阅中不允许发起支付 订阅信息【%s】", storageSubscribe));
            throw new InstaException(CloudSubscribeErrorCode.RenewOrderPaymentException);
        }

        // 过期时间不允许发起支付
        long expireTime = storageSubscribe.getExpireTime() - System.currentTimeMillis();
        if (expireTime <= 0) {
            LOGGER.info(String.format("在续费周期不允许发起支付，订阅信息{%s}", storageSubscribe));
            throw new InstaException(CloudSubscribeErrorCode.RenewOrderPaymentException);
        }

        // 续费失败重试12h前后+5min内不允许发起
        if ((expireTime >= 12 * 3600 * 1000 - 5 * 60 * 1000) && expireTime <= (12 * 3600 * 1000 + 5 * 60 * 1000)) {
            LOGGER.info(String.format("在续费周期不允许发起支付，订阅信息{%s}", storageSubscribe));
            throw new InstaException(CloudSubscribeErrorCode.RenewOrderPaymentException);
        }

        // 支付信息校验
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(userId);
        if (Objects.isNull(userPayInfo)) {
            LOGGER.error(String.format("支付信息不存在不允许发起支付 订阅信息【%s】", storageSubscribe));
            FeiShuMessageUtil.storeGeneralMessage(String.format("订阅支付信息不存在，请检查。用户账号【%s】",
                    storageSubscribe.getInstaAccount()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(CloudSubscribeErrorCode.RenewOrderPaymentException);
        }

        // 支付渠道限制
        if (!StorePaymentMethodEnum.OCEAN_PAYMENT.equals(userPayInfo.parsePaymentMethod())) {
            LOGGER.error(String.format("非钱海不允许发起支付 订阅信息【%s】", storageSubscribe));
            throw new InstaException(CloudSubscribeErrorCode.RenewOrderPaymentException);
        }
    }

    /**
     * 重置支付机构快捷支付id信息
     *
     * @param userPayInfo
     * @param orderNumber
     */
    private void resetPayInfo(UserPayInfo userPayInfo, String orderNumber) {
        StorePaymentMethodEnum storePaymentMethodEnum = userPayInfo.parsePaymentMethod();
        switch (storePaymentMethodEnum) {
            case OCEAN_PAYMENT:
                userPayInfo.setCardType(StringUtils.EMPTY);
                userPayInfo.setCardCountry(StringUtils.EMPTY);
                break;
            case PAYPAL_PAYMENT:
                // 删除paypal vault id
                userPayInfo.setPaypalCustomerId(StringUtils.EMPTY);
                payPalPaymentHelper.deletePayPalVaultId(orderNumber, AESUtil.decode(AESUtil.PAY_TOKEN_KEY, userPayInfo.getPayId()));
                break;
            case CKO_PAYMENT:
                userPayInfo.setCardType(StringUtils.EMPTY);
                userPayInfo.setCardCountry(StringUtils.EMPTY);
                userPayInfo.setPaymentTradeId(StringUtils.EMPTY);
                break;
            case KLARNA_PAYMENT:
                klarnaPaymentHelper.cancelKlarnaPayToken(orderNumber, AESUtil.decode(AESUtil.PAY_TOKEN_KEY, userPayInfo.getPayId()));
                break;
            default:
                userPayInfo.setPayId(StringUtils.EMPTY);
        }
        userPayInfo.setPayId(StringUtils.EMPTY);
        userPayInfo.setDeductCategory(StringUtils.EMPTY);
        userPayInfo.setEnabled(false);
    }
}
