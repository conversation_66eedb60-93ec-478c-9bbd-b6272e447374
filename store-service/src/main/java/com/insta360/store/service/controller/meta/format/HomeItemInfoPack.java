package com.insta360.store.service.controller.meta.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.enums.CommodityTagType;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.discount.dto.bo.DiscountCheckResult;
import com.insta360.store.business.discount.model.Activity;
import com.insta360.store.business.discount.model.ActivityGift;
import com.insta360.store.business.discount.model.Coupon;
import com.insta360.store.business.discount.service.ActivityGiftService;
import com.insta360.store.business.discount.service.ActivityService;
import com.insta360.store.business.discount.service.CouponService;
import com.insta360.store.business.insurance.model.ClimbServiceCommodity;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.model.AdapterTypeInfo;
import com.insta360.store.business.meta.model.HomepageItemCommodityGroup;
import com.insta360.store.business.meta.model.HomepageItemCommodityInfo;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.HomepageItemCommodityGroupService;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import com.insta360.store.business.meta.service.impl.helper.MetaBatchHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductAdapterType;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.trade.dto.condition.TradeCodeUseResult;
import com.insta360.store.business.trade.service.TradeCodeService;
import com.insta360.store.service.controller.meta.vo.*;
import com.insta360.store.service.controller.product.vo.CommodityTagInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/09/13
 * @Description:
 */
@Component
public class HomeItemInfoPack {

    private final static Logger LOGGER = LoggerFactory.getLogger(HomeItemInfoPack.class);

    @Autowired
    ActivityService activityService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    TradeCodeService tradeCodeService;

    @Autowired
    ActivityGiftService activityGiftService;

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    HomepageItemCommodityGroupService homepageItemCommodityGroupService;

    @Autowired
    CouponService couponService;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    MetaBatchHelper metaBatchHelper;


    /**
     * home item 配置列表数据封装（类目页）
     *
     * @param homeItemId
     * @param language
     * @param country
     * @return
     */
    public HomeItemMainVO doPackHomeItemInfo(Integer homeItemId, InstaLanguage language, InstaCountry country) {
        HomepageItemMain homeItem = homepageItemMainService.getById(homeItemId);
        HomeItemMainVO homeItemMainVo = new HomeItemMainVO(homeItem);
        homeItemMainVo.setCommodityGroups(packHomepageItemCommodityGroup(homeItem, language, country, false));
        return homeItemMainVo;
    }

    /**
     * 封装套餐分组信息
     *
     * @param homepageItemMain
     * @param language
     * @param country
     * @param isHomepage
     * @return
     */
    private List<HomeItemCommodityGroupVO> packHomepageItemCommodityGroup(HomepageItemMain homepageItemMain, InstaLanguage language, InstaCountry country, Boolean isHomepage) {
        LOGGER.info("packHomepageItemCommodityGroup start ......");
        long startTime = System.currentTimeMillis();
        // 所有的首页套餐配置信息
        List<HomepageItemCommodityGroup> homepageItemCommodityGroups = homepageItemCommodityGroupService.listAllHomepageItemCommodityInfos(homepageItemMain.getId());
        List<Integer> commodityIds = homepageItemCommodityGroups.stream().map(HomepageItemCommodityGroup::getCommodityId).collect(Collectors.toList());
        // list to map
        Map<Integer, Commodity> commodityMap = commodityBatchHelper.commodityMapCommodityIds(commodityIds);
        Map<Integer, CommoditySaleState> commoditySaleStateMap = commodityBatchHelper.saleStateMapCommodityIds(commodityIds, country);
        Map<Integer, CommodityPrice> commodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIds, country);
        List<Integer> productIds = commodityMap.values().stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());
        Map<Integer, Product> productMap = productBatchHelper.productMapProductIds(productIds);

        homepageItemCommodityGroups = homepageItemCommodityGroups.stream()
                // 过滤缺货的套餐 和 没有价格的套餐
                .filter(commodityGroup -> checkCommodityEnabled(commodityGroup.getCommodityId(), commodityMap, productMap))
                .filter(commodityGroup -> checkCommoditySaleState(commodityGroup.getCommodityId(), commoditySaleStateMap, commodityPriceMap, country))
                .collect(Collectors.toList());
        // empty list
        if (CollectionUtils.isEmpty(homepageItemCommodityGroups)) {
            return new ArrayList<>(0);
        }

        // 主页只返回7个套餐
        homepageItemCommodityGroups = isHomepage ? homepageItemCommodityGroups.stream().limit(7).collect(Collectors.toList()) : homepageItemCommodityGroups;
        commodityIds = homepageItemCommodityGroups.stream().map(HomepageItemCommodityGroup::getCommodityId).collect(Collectors.toList());
        // list to map
        List<Integer> commodityGroupsIds = homepageItemCommodityGroups.stream().map(HomepageItemCommodityGroup::getId).collect(Collectors.toList());
        Map<Integer, HomepageItemCommodityInfo> itemCommodityInfoMap = metaBatchHelper.homepageItemInfoMapAdapterTypeIds(commodityGroupsIds, language);
        Map<Integer, CommodityInfo> commodityInfoMap = commodityBatchHelper.commodityInfoMapCommodityIds(commodityIds, language);
        Map<Integer, ProductInfo> productInfoMap = productBatchHelper.productInfoMapProductIds(productIds, language);
        Map<Integer, CommodityTradeRule> tradeRuleMap = commodityBatchHelper.tradeRuleMapCommodityIds(commodityIds);
        Map<Integer, Integer> stockCountMap = commodityBatchHelper.stockCountMapCommodityIds(commodityIds, country);
        Map<Integer, ClimbServiceCommodity> serviceTypeMap = commodityBatchHelper.serviceTypeMapCommodityIds(commodityIds);
        Map<Integer, CommodityFunctionDescription> functionDescriptionMap = commodityBatchHelper.functionDescriptionMapCommodityIds(commodityIds, language);
        Map<Integer, List<CommodityTagBind>> tagBindMap = commodityBatchHelper.commodityTagBindMapCommodityIds(commodityIds);
        Map<Integer, List<ProductAdapterType>> productAdapterMap = metaBatchHelper.productAdapterMapByProductIds(productIds);
        long dbEndTime = System.currentTimeMillis();
        LOGGER.info("packHomepageItemCommodityGroup db search end consume time ..." + (dbEndTime - startTime));

        // 数据封装
        List<HomeItemCommodityGroupVO> commodityGroupVOList = homepageItemCommodityGroups.stream()
                .map(commodityGroup -> {
                    HomeItemCommodityGroupVO commodityGroupVo = new HomeItemCommodityGroupVO(commodityGroup);
                    // 产品支持的适配类型
                    commodityGroupVo.setAdapterTypeNames(packProductAdapterType(commodityGroup.getProductId(), productAdapterMap, language));

                    // 套餐配置信息
                    Commodity commodity = commodityMap.get(commodityGroup.getCommodityId());
                    commodityGroupVo.setItemInfos(packHomeItemCommodityInfo(commodityGroup, productMap, productInfoMap, commodityInfoMap, itemCommodityInfoMap, serviceTypeMap, language));

                    // 销售状态
                    HomeItemCommoditySaleStateVO commoditySaleStateVo = packCommoditySaleState(commodity.getId(), commoditySaleStateMap);
                    commodityGroupVo.setCommoditySaleState(commoditySaleStateVo);

                    // 首页/类目页去除推荐标签
                    List<CommodityTagInfoVO> commodityTagInfoList = packCommodityTagInfo(commodity, tagBindMap, commoditySaleStateVo.getSaleState(), country, language);
                    List<CommodityTagInfoVO> tagInfoVOList = commodityTagInfoList.stream()
                            .filter(commodityTag -> !CommodityTagType.RECOMMEND_COMMODITY.equals(CommodityTagType.parse(commodityTag.getTagGroupType())))
                            .collect(Collectors.toList());
                    // 套餐关联的tag信息
                    commodityGroupVo.setTagInfos(tagInfoVOList);

                    // 套餐价格
                    commodityGroupVo.setHomeItemPrice(packCommodityPrice(commodity, commodityPriceMap, null, commoditySaleStateVo.getSaleState(), country));

                    // 交易规则
                    commodityGroupVo.setCommodityTradeRule(packCommodityTradeRule(commodity.getId(), tradeRuleMap, stockCountMap));

                    // 功能描述
                    commodityGroupVo.setFunctionDescription(packFunctionDescriptions(commodity.getId(), functionDescriptionMap));

                    return commodityGroupVo;
                }).collect(Collectors.toList());
        LOGGER.info("packHomepageItemCommodityGroup pack consume time ..." + (System.currentTimeMillis() - dbEndTime));
        return commodityGroupVOList;
    }

    /**
     * 封装套餐配置信息
     *
     * @param commodityGroup
     * @param productMap
     * @param productInfoMap
     * @param commodityInfoMap
     * @param itemCommodityInfoMap
     * @return
     */
    private HomeItemCommodityInfoVO packHomeItemCommodityInfo(HomepageItemCommodityGroup commodityGroup, Map<Integer, Product> productMap, Map<Integer, ProductInfo> productInfoMap,
                                                              Map<Integer, CommodityInfo> commodityInfoMap, Map<Integer, HomepageItemCommodityInfo> itemCommodityInfoMap,
                                                              Map<Integer, ClimbServiceCommodity> serviceTypeMap, InstaLanguage language) {
        HomepageItemCommodityInfo homepageItemCommodityInfo = itemCommodityInfoMap.get(commodityGroup.getId());

        // 数据封装
        HomeItemCommodityInfoVO homeItemCommodityInfo = new HomeItemCommodityInfoVO(homepageItemCommodityInfo);

        // 产品名
        ProductInfo productInfo = productInfoMap.get(commodityGroup.getProductId());
        if (productInfo == null) {
            String message = "主页/类目页产品配置信息缺失。产品Id:" + commodityGroup.getProductId() + ", 语言:" + language;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return null;
        }
        homeItemCommodityInfo.setProductName(productInfo.getName());
        homeItemCommodityInfo.setDescription(productInfo.getDescription());

        // 产品跳转链接
        Product product = productMap.get(commodityGroup.getProductId());
        homeItemCommodityInfo.setProductKey(product.getUrlKey());

        // 产品名称标注（为空字符串则取产品名）
        if (StringUtil.isBlank(homeItemCommodityInfo.getDisplayName())) {
            homeItemCommodityInfo.setDisplayName(productInfo.getName());
        }

        // 套餐主图
        homeItemCommodityInfo.setCommodityDisplayImage(packDisplayImage(commodityGroup.getCommodityId()));

        // 套餐名
        CommodityInfo commodityInfo = commodityInfoMap.get(commodityGroup.getCommodityId());
        if (commodityInfo == null) {
            String message = "主页/类目页套餐配置信息缺失。套餐Id:" + commodityGroup.getCommodityId() + ", 语言:" + language;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return null;
        }
        homeItemCommodityInfo.setCommodityName(commodityInfo.getName());

        // 增值服务类型
        ClimbServiceCommodity climbServiceCommodity = serviceTypeMap.get(commodityGroup.getCommodityId());
        if (Objects.nonNull(climbServiceCommodity)) {
            homeItemCommodityInfo.setServiceType(climbServiceCommodity.getServiceType());
        }

        return homeItemCommodityInfo;
    }

    /**
     * 封装套餐功能描述
     *
     * @param commodityId
     * @param functionDescriptionMap
     * @return
     */
    private CommodityFunctionDescriptionVO packFunctionDescriptions(Integer commodityId, Map<Integer, CommodityFunctionDescription> functionDescriptionMap) {
        CommodityFunctionDescription functionDescription = functionDescriptionMap.get(commodityId);
        if (Objects.isNull(functionDescription) || StringUtil.isBlank(functionDescription.getFunctionDescription())) {
            return null;
        }
        return new CommodityFunctionDescriptionVO(functionDescription);
    }

    /**
     * 封装产品支持的适配类型
     *
     * @param productId
     * @param productAdapterMap
     * @param language
     * @return
     */
    private List<String> packProductAdapterType(Integer productId, Map<Integer, List<ProductAdapterType>> productAdapterMap, InstaLanguage language) {
        // 产品支持的适配类型
        List<ProductAdapterType> productAdapterTypes = productAdapterMap.get(productId);
        if (CollectionUtils.isEmpty(productAdapterTypes)) {
            return null;
        }

        List<Integer> adapterTypeIds = productAdapterTypes.stream().map(ProductAdapterType::getAdapterTypeId).collect(Collectors.toList());
        Map<Integer, AdapterTypeInfo> adapterTypeInfoMap = metaBatchHelper.adapterTypeMapAdapterTypeIds(adapterTypeIds, language);

        // 适配类型列表名称
        return productAdapterTypes.stream().map(productAdapterType -> {
            // 适配类型文案集合
            AdapterTypeInfo adapterTypeInfo = adapterTypeInfoMap.get(productAdapterType.getAdapterTypeId());
            if (adapterTypeInfo == null) {
                return null;
            }
            return adapterTypeInfo.getInfoName();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 套餐关联的tag信息
     *
     * @param commodity
     * @param tagBindMap
     * @param commoditySaleState
     * @param country
     * @param language
     * @return
     */
    public List<CommodityTagInfoVO> packCommodityTagInfo(Commodity commodity, Map<Integer, List<CommodityTagBind>> tagBindMap, Integer commoditySaleState, InstaCountry country, InstaLanguage language) {
        // 没有绑定tag 和 缺货的套餐 则不进行展示
        List<CommodityTagBind> commodityTagBinds = tagBindMap.get(commodity.getId());
        if (CollectionUtils.isEmpty(commodityTagBinds) || SaleState.out_of_stock.equals(SaleState.parse(commoditySaleState))) {
            return new ArrayList<>();
        }

        List<Integer> tagBindIds = commodityTagBinds.stream().map(CommodityTagBind::getTagId).collect(Collectors.toList());
        // list to map
        Map<Integer, CommodityTagGroup> commodityTagGroupMap = commodityBatchHelper.commodityTagGroupMapIds(tagBindIds);
        Map<Integer, CommodityTagInfo> commodityTagInfoMap = commodityBatchHelper.commodityTagInfoMapIds(tagBindIds, language);

        // 封装tag信息
        return commodityTagBinds
                .stream()
                .map(commodityTagBind -> doPackCommodityTagInfo(commodityTagBind, commodityTagGroupMap, commodityTagInfoMap, commodity, country))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 封装套餐主图信息
     *
     * @param commodityId
     */
    private String packDisplayImage(Integer commodityId) {
        CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(commodityId);
        if (commodityDisplay == null) {
            FeiShuMessageUtil.storeGeneralMessage("主页/类目页套餐主图信息缺失。套餐Id:" + commodityId, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return null;
        }
        return commodityDisplay.getUrl();
    }

    /**
     * 封装套餐的价格信息
     *
     * @param commodity
     * @param priceMap
     * @param resellerCode
     * @param commoditySaleState
     * @param country
     * @return
     */
    private HomeItemPriceVO packCommodityPrice(Commodity commodity, Map<Integer, CommodityPrice> priceMap, String resellerCode, Integer commoditySaleState, InstaCountry country) {
        CommodityPrice commodityPrice = priceMap.get(commodity.getId());

        // 套餐价格数据
        HomeItemPriceVO homeItemPrice = new HomeItemPriceVO();

        // 现价
        homeItemPrice.setPrice(commodityPrice.price());

        // 原价（不相等才展示）
        if (!commodityPrice.getAmount().equals(commodityPrice.getOriginAmount())) {
            homeItemPrice.setOriginPrice(commodityPrice.originPrice());
        }

        // 节省金（缺货套餐不予展示节省金）
        if (!SaleState.out_of_stock.equals(SaleState.parse(commoditySaleState))) {
            Price saveAmount = getCommoditySaveAmount(commodity, commodityPrice, resellerCode, country);
            homeItemPrice.setSavePrice(saveAmount);
        }

        return homeItemPrice;
    }

    /**
     * 套餐销售状态
     *
     * @param commodityId
     * @param saleStateMap
     * @return
     */
    private HomeItemCommoditySaleStateVO packCommoditySaleState(Integer commodityId, Map<Integer, CommoditySaleState> saleStateMap) {
        return new HomeItemCommoditySaleStateVO(saleStateMap.get(commodityId));
    }

    /**
     * 套餐交易规则
     *
     * @param commodityId
     * @param tradeRuleMap
     * @return
     */
    private HomeItemCommodityTradeRuleVO packCommodityTradeRule(Integer commodityId, Map<Integer, CommodityTradeRule> tradeRuleMap, Map<Integer, Integer> stockCountMap) {
        Integer stockCount = stockCountMap.get(commodityId);
        CommodityTradeRule commodityTradeRule = tradeRuleMap.get(commodityId);
        HomeItemCommodityTradeRuleVO homeItemCommodityTradeRuleVO = Objects.nonNull(commodityTradeRule) ?
                new HomeItemCommodityTradeRuleVO(commodityTradeRule) : new HomeItemCommodityTradeRuleVO();
        homeItemCommodityTradeRuleVO.setStockCount(stockCount != null ? stockCount : 0);
        return homeItemCommodityTradeRuleVO;
    }

    /**
     * 套餐禁用 && 产品禁用 不予展示
     *
     * @param commodityId
     * @param commodityMap
     * @param productMap
     * @return
     */
    private Boolean checkCommodityEnabled(Integer commodityId, Map<Integer, Commodity> commodityMap, Map<Integer, Product> productMap) {
        Commodity commodity = commodityMap.get(commodityId);
        if (commodity == null) {
            return false;
        }

        Product product = productMap.get(commodity.getProduct());
        return product != null;
    }

    /**
     * 套餐下架 && 没有价格 不予展示
     *
     * @param commodityId
     * @param saleStateMap
     * @param priceMap
     * @param country
     * @return
     */
    private Boolean checkCommoditySaleState(Integer commodityId, Map<Integer, CommoditySaleState> saleStateMap, Map<Integer, CommodityPrice> priceMap, InstaCountry country) {
        CommoditySaleState saleState = saleStateMap.get(commodityId);
        if (saleState == null) {
            return false;
        }

        // 过滤掉下架的套餐和没有价格的套餐
        CommodityPrice commodityPrice = priceMap.get(commodityId);
        if (SaleState.remove.getCode() != saleState.getSaleState() && commodityPrice != null) {
            return true;
        }

        if (SaleState.remove.getCode() != saleState.getSaleState() && commodityPrice == null) {
            FeiShuMessageUtil.storeGeneralMessage("主页/类目页套餐价格未找到。套餐id：" + commodityId + ", 地区：" + country.name(), FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
        }
        return false;
    }

    /**
     * 获取当前地区某个套餐的活动赠品配置（一个地区可能存在多个活动，但一个套餐在该地区的活动中只能存在一条赠品配置）
     *
     * @param commodity
     * @param country
     * @return
     */
    private ActivityGift getActivityGifts(Commodity commodity, InstaCountry country) {
        // 当前地区正在进行的活动列表
        List<Activity> currentActivities = activityService.getCurrentActivities(country);
        if (CollectionUtils.isEmpty(currentActivities)) {
            return null;
        }

        // 遍历所有的活动拿到该套餐的赠品配置
        for (Activity currentActivity : currentActivities) {
            Optional<ActivityGift> giftConfig = activityGiftService.getGiftConfig(currentActivity.getId(), commodity.getProduct(), commodity.getId());
            if (giftConfig.isPresent()) {
                return giftConfig.get();
            }
        }
        return null;
    }

    /**
     * 获取某一个套餐的节省金（单个套餐的节省金 = 原价 - 现价 + 折扣码优惠）
     *
     * @param commodity
     * @param commodityPrice
     * @param resellerCode
     * @param country
     * @return
     */
    public Price getCommoditySaveAmount(Commodity commodity, CommodityPrice commodityPrice, String resellerCode, InstaCountry country) {
        // 总的赠品节省金
        float giftAllSaveAmount = commodityPrice.getOriginAmount() - commodityPrice.getAmount();

        // 节省金需要考虑分销减价
        Reseller reseller = resellerService.getByPromoCode(resellerCode);
        if (reseller != null) {
            Coupon coupon = null;
            if (StringUtils.isNotBlank(reseller.getRelatedCoupon())) {
                coupon = couponService.getByCode(reseller.getRelatedCoupon());
            }
            TradeCodeUseResult tradeCodeUseResult = tradeCodeService.commodityDiscount(commodity.getProduct(), commodity.getId(), coupon, country);
            DiscountCheckResult discountCheckResult = tradeCodeUseResult.getDiscountCheckResult();
            if (discountCheckResult != null) {
                giftAllSaveAmount += discountCheckResult.getPrice() == null ? 0 : tradeCodeUseResult.getDiscountCheckResult().getPrice().getAmount();
            }
        }

        // 节省金
        return new Price(commodityPrice.currency(), giftAllSaveAmount);
    }

    /**
     * 封装套餐的tag信息
     *
     * @param commodityTagBind
     * @param tagGroupMap
     * @param tagInfoMap
     * @param commodity
     * @param country
     * @return
     */
    private CommodityTagInfoVO doPackCommodityTagInfo(CommodityTagBind commodityTagBind, Map<Integer, CommodityTagGroup> tagGroupMap,
                                                      Map<Integer, CommodityTagInfo> tagInfoMap, Commodity commodity, InstaCountry country) {
        // 是否是赠品tag
        CommodityTagGroup commodityTagGroup = null;
        if (commodityTagBind.isActivityGiftTag()) {
            ActivityGift activityGift = getActivityGifts(commodity, country);
            if (activityGift != null && activityGift.getId().equals(commodityTagBind.getActivityGiftId())) {
                commodityTagGroup = tagGroupMap.get(commodityTagBind.getTagId());
            }
        } else {
            commodityTagGroup = tagGroupMap.get(commodityTagBind.getTagId());
        }

        // tag分组要真实有效
        if (commodityTagGroup == null || commodityTagGroup.getDisabled()) {
            return null;
        }

        // 当前语言下的配置需要真实有效
        CommodityTagInfo tagInfo = tagInfoMap.get(commodityTagBind.getTagId());
        if (tagInfo == null || tagInfo.getDisabled()) {
            return null;
        }

        // 数据转换
        CommodityTagInfoVO commodityTagInfoVo = new CommodityTagInfoVO(tagInfo);
        commodityTagInfoVo.setTagGroupType(commodityTagGroup.getGroupType());
        commodityTagInfoVo.setFontColor(commodityTagGroup.getFontColor());
        commodityTagInfoVo.setFontHoverColor(commodityTagGroup.getFontHoverColor());
        commodityTagInfoVo.setBackGroundColor(commodityTagGroup.getBackGroundColor());
        commodityTagInfoVo.setHoverBackGroundColor(commodityTagGroup.getHoverBackGroundColor());
        return commodityTagInfoVo;
    }
}