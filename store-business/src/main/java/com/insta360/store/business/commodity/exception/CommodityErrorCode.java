package com.insta360.store.business.commodity.exception;

import com.insta360.compass.core.exception.ErrorCode;

/**
 * @Author: wbt
 * @Date: 2020/10/20
 * @Description:
 */
public enum CommodityErrorCode implements ErrorCode {

    /**
     * 套餐价格未找到
     */
    CommodityPriceNotFoundException(21011, "套餐价格未找到"),

    /**
     * 套餐未找到
     */
    CommodityNotFoundException(21012, "套餐未找到"),

    /**
     * 套餐料号未找到
     */
    CommodityCodeNotFoundException(21013, "套餐料号未找到"),

    /**
     * 套餐Tag分组未找到
     */
    CommodityTagGroupNotFoundException(21014, "套餐Tag分组未找到"),

    /**
     * 套餐Tag详细配置未找到
     */
    CommodityTagInfoNotFoundException(21015, "套餐Tag详细配置未找到"),

    /**
     * 重复创建
     */
    CommodityTagInfoAlreadyCreateException(21015, "重复创建"),

    /**
     * 套餐Tag绑定关系未找到
     */
    CommodityTagBindNotFoundException(21016, "套餐Tag绑定关系未找到"),

    /**
     * 套餐Tag重复绑定
     */
    CommodityTagBindAlreadyCreateException(21017, "套餐Tag重复绑定"),

    /**
     * 配件适配类型未找到
     */
    AccessoryCompatibilityNotFoundException(21018, "配件适配类型未找到"),

    /**
     * 配件适配类目信息未找到
     */
    AccessoryCompatibilityCategoryNotFoundException(21019, "配件适配类目信息未找到"),

    /**
     * 配件适配分组未找到
     */
    AccessoryCompatibilityCommodityGroupNotFoundException(21020, "配件适配分组未找到"),

    /**
     * 该套餐已存在与该分组
     */
    AccessoryCompatibilityCommodityGroupAlreadyCreatedException(21021, "该套餐已存在与该分组"),

    /**
     * 配件适配分组的多语言文案未找到
     */
    AccessoryCompatibilityCommodityInfoNotFoundException(21022, "配件适配分组的多语言文案未找到"),

    /**
     * 适配套餐多语言文案重复创建
     */
    AccessoryCompatibilityCommodityInfoAlreadyCreatedException(21022, "适配套餐多语言文案重复创建"),

    /**
     * 配件适配绑定关系未找到
     */
    AccessoryCompatibilityBindNotFoundException(21023, "配件适配绑定关系未找到"),

    /**
     * 配件适配绑定关系已存在
     */
    AccessoryCompatibilityBindAlreadyCreatedException(21024, "配件适配绑定关系已存在"),

    /**
     * 料号已存在商城
     */
    CommodityPlatformCodeAlreadyCreatedException(21025, "料号已存在商城"),

    /**
     * 套餐料号绑定关系已存在
     */
    CommodityPlatformCodeAlreadyBindCreatedException(21026, "套餐料号绑定关系已存在"),

    /**
     * 套餐绑定关系不存在
     */
    CommodityPlatformCodeBindNotFoundException(21027, "套餐绑定关系不存在"),

    /**
     * 未匹配到正常销售的套餐
     */
    CommodityNotFoundNormalSaleException(21028, "未匹配到正常销售的套餐"),

    /**
     * 套餐推荐配件未找到
     */
    CommodityRecommendationNotFoundException(21029, "套餐推荐配件未找到"),

    /**
     * 产品推荐配件未找到
     */
    ProductRecommendationNotFoundException(21030, "产品推荐配件未找到"),

    /**
     * 料号存在前后空格
     */
    CommodityCodeErrException(21031, "料号存在前后空格"),

    /**
     * 套餐价格数据转换异常
     */
    CommodityPriceDataException(21032, "套餐价格数据转换异常"),

    /**
     * 地区货币信息有误
     */
    CommodityPriceAreaCurrencyException(21033, "地区货币信息有误"),

    /**
     * 套餐价格现价为0
     */
    CommodityPriceZeroException(21034, "套餐价格现价为0"),

    /**
     * 套餐现价高于原价
     */
    CommodityPriceHighException(21034, "套餐现价高于原价"),

    /**
     * 套餐价格数据缺失
     */
    CommodityPriceDataDeletionException(21035, "套餐价格数据缺失"),

    /**
     * 套餐价格重复
     */
    CommodityPriceRepeatException(21036, "套餐价格数据重复"),

    /**
     * 套餐报关信息缺失
     */
    CommodityMetaNotFountException(21037, "启用失败！，套餐配置报关信息后才能启用"),

    /**
     * 转码数据压缩中
     */
    DisplayCompressing(21038, "转码数据压缩中"),

    /**
     * 转码数据压缩失败
     */
    DisplayCompressFailed(21039, "转码数据压缩失败"),

    /**
     * 套餐未启用
     */
    CommodityNotEnabledException(21040, "套餐未启用"),

    /**
     * 日韩台价格不允许设为小数
     */
    JKTPriceNotDecimalException(21041, "日韩台价格不允许设为小数"),

    /**
     * 找不到仓库
     */
    StorageInfoNotFoundException(105021042, "找不到仓库"),

    /**
     * 可发货地区不能为空
     */
    DeliveryAreaNotEmptyException(105021043, "可发货地区不能为空"),

    /**
     * 主键id数量不一致
     */
    InconsistentPrimaryKeyIdsException(105021044, "主键id数量不一致"),

    /**
     * 总仓未配置
     */
    GeneralStorageNotConfigException(105021045, "总仓未配置"),

    /**
     * 套餐类型不合法
     */
    InvalidCommodityTypeException(105021046, "套餐类型不合法"),

    /**
     * 组合商品未配置子商品异常
     */
    MissingSubCommodityException(105021047, "组合商品未配置子商品异常"),

    /**
     * 子套餐数量不合法
     */
    InvalidSubCommodityQuantityException(105021048, "子套餐数量不合法"),

    /**
     * 单商品列表中存在非单商品类型
     */
    NonSingleCommodityTypeExistException(105021049, "单商品列表中存在非单商品类型"),

    /**
     * 此商品已配置为组合商品的子商品
     */
    AlreadySubCommodityInBundleException(105021050, "此商品已配置为组合商品的子商品"),



    ;

    /**
     * 异常码
     */
    private final Integer code;

    /**
     * 异常信息
     */
    private final String msg;

    CommodityErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
