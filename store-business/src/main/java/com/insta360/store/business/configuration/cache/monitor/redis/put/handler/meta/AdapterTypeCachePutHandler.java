package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import com.insta360.store.business.outgoing.rpc.store.job.CategoryPageCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.HomeItemCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.HomepageCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.ProductCachePutService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductAdapterType;
import feign.RetryableException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/11/15
 * @Description:
 */
@Component
public class AdapterTypeCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdapterTypeCachePutHandler.class);

    @Autowired
    HomeItemCachePutService homeItemCachePutService;

    @Autowired
    HomepageCachePutService homepageCachePutService;

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Autowired
    ProductCachePutService productCachePutService;

    @Autowired
    CategoryPageCachePutService categoryPageCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        Integer adapterTypeId = cachePutKeyParameter.getAdapterTypeId();
        if (Objects.isNull(adapterTypeId)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。适配机型ID为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        List<ProductAdapterType> productAdapterTypes = productAdapterTypeService.listByAdapterType(adapterTypeId);
        if (CollectionUtils.isEmpty(productAdapterTypes)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程.配置了该适配机型id的产品为空,不予处理.请求数据:{%s}", this.getCachePutType(), cachePutKeyParameter));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }
        List<Integer> productIds = productAdapterTypes.stream().map(ProductAdapterType::getProductId).distinct().collect(Collectors.toList());
        Collection<Product> products = productService.listByIds(productIds);
        if (CollectionUtils.isEmpty(products)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程.配置了该适配机型id的产品,均未启用,不予处理.请求数据:{%s},产品id:{%s}", this.getCachePutType(), cachePutKeyParameter, productIds));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}。", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(null, countDownLatch));
        cachePutThreadPool.execute(() -> this.task2(null, countDownLatch));
        cachePutThreadPool.execute(() -> this.task3(products, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新删除
        StoreCacheDataChangeEventBO storeCacheDataChangeEvent = new StoreCacheDataChangeEventBO();
        storeCacheDataChangeEvent.setProductEvents(this.parseProductEvent(products));
        return storeCacheDataChangeEvent;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.ADAPTER_TYPE_MAIN;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.HOME_PAGE_KEY, CacheableType.HOME_ITEM_KEY, CacheableType.CATEGORY_PAGE, CacheableType.CATEGORY_PAGE_FILTER, CacheableType.PRODUCT_INFO);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            CacheConstant.COUNTIES.forEach(cacheCounties -> {
                // 更新首页
                homepageCachePutService.listHomepage(cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新类目筛选器
                categoryPageCachePutService.listAllCategoryFilter(cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新全部类目
                categoryPageCachePutService.listAllCategory(cacheCounties.getCountry(), cacheCounties.getLanguage());
            });
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
        try {
            // 更新类目页
            List<HomepageItemMain> homepageItemMains = homepageItemMainService.listEnableHomeItem();
            homepageItemMains.forEach(homepageItemMain ->
                    CacheConstant.COUNTIES.forEach(cacheCounties -> {
                        // 更新类目页（旧）
                        homeItemCachePutService.listHomeItemInfoByHomeItemType(homepageItemMain.getId(), cacheCounties.getCountry(), cacheCounties.getLanguage());
                    }));

            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务2完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task2}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {
        try {
            List<Product> products = (List<Product>) param;
            List<Integer> productIds = products.stream().map(Product::getId).collect(Collectors.toList());

            for (Integer productId : productIds) {
                CacheConstant.COUNTIES.forEach(cacheCounties -> {
                    // 更新产品页
                    productCachePutService.getInfo(productId, cacheCounties.getCountry(), cacheCounties.getLanguage());
                });
            }

            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务3完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task3}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }

    @Override
    public Integer getTaskNumber() {
        return super.getTaskNumber() - 1;
    }
}
