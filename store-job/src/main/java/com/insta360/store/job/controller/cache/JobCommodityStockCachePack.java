package com.insta360.store.job.controller.cache;

import com.insta360.store.business.commodity.service.impl.helper.CommodityStockHelper;
import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * admin套餐库存高速缓存封装
 *
 * <AUTHOR>
 * @Date: 2022/8/30
 * @Description:
 * @date 2024/03/22
 */
@Component
public class JobCommodityStockCachePack {

    public static final Logger LOGGER = LoggerFactory.getLogger(JobCommodityStockCachePack.class);

    @Autowired
    CommodityStockHelper commodityStockHelper;

    /**
     * 处理库存
     *
     * @param cachePutKeyParameter 缓存放置键参数
     */
    @CachePutMonitor(cacheableType = CachePutType.COMMODITY_INFO)
    public void handleStock(CachePutKeyParameterBO cachePutKeyParameter) {
        LOGGER.info("HandleStock: {}", cachePutKeyParameter);
    }


}
