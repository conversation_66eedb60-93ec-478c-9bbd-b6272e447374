package com.insta360.store.business.admin.order.query.bo;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.dto.OrderQueryDTO;
import com.insta360.store.business.reseller.enums.ResellerUtmSourceType;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019-05-15
 * @Description:
 */
public class AdminOrderQueryCondition {

    private String orderNumber;
    private String userEmail;
    private String inscp;
    private String promoCode;
    private String couponCode;
    private LocalDateTime fromTime;
    private LocalDateTime endTime;
    private List<Integer> states;
    private List<Integer> productIds;
    private List<Integer> noProductIds;
    private List<Integer> commodityIds;
    private InstaCountry country;
    private List<String> countryList;
    private List<String> noCountryList;
    private PaymentChannel paymentChannel;
    private List<String> paymentChannelList;
    private List<String> noPaymentChannelList;
    private Integer endpoint;

    /**
     * 序列号
     */
    private String deviceSerial;

    /**
     * 物流号
     */
    private String logisticsNumber;

    /**
     * 包含的售后状态
     */
    private Integer rmaStateId;

    /**
     * 不包含的售后状态
     */
    private Integer noRmaStateId;

    /**
     * 以旧换新订单过滤标记
     */
    private Boolean tradeupOrderFilterMark;

    /**
     * 订单是否包含卖家备注
     */
    private Boolean orderSellerRemarkMark;

    /**
     * 售后状态
     *
     * @see com.insta360.store.business.rma.enums.RmaState
     */
    private List<Integer> rmaStateList;

    /**
     * utm来源
     */
    private List<String> utmSourceList;

    /**
     * 直接查询的utm来源
     */
    private List<String> queryUtmSourceList;

    /**
     * 是否包含utm来源
     */
    private Boolean utmSourceInclude;

    /**
     * 是否需要关联utm_source
     */
    private Boolean joinUtmSource;

    /**
     * utm_source 是否存在 utm_source link
     */
    private Boolean containsLink;

    public static AdminOrderQueryCondition parse(OrderQueryDTO orderQueryParam) {
        AdminOrderQueryCondition condition = new AdminOrderQueryCondition();

        condition.orderNumber = orderQueryParam.getOrderNumber();
        condition.userEmail = orderQueryParam.getUserEmail();
        condition.inscp = orderQueryParam.getInscp();
        condition.promoCode = orderQueryParam.getPromoCode();
        condition.couponCode = orderQueryParam.getCouponCode();
        condition.country = InstaCountry.parse(orderQueryParam.getCountryCode());
        condition.paymentChannel = PaymentChannel.parse(orderQueryParam.getPayChannel());
        condition.endpoint = orderQueryParam.getOrderEndpoint();
        condition.deviceSerial = orderQueryParam.getDeviceSerial();
        condition.logisticsNumber = orderQueryParam.getLogisticsNumber();
        condition.fromTime = orderQueryParam.getFrom();
        condition.endTime = orderQueryParam.getEnd();
        condition.utmSourceList = parseUtmSourceCondition(condition, orderQueryParam.getUtmSourceList());
        condition.queryUtmSourceList = orderQueryParam.getUtmSourceList();
        condition.containsLink = Optional.ofNullable(orderQueryParam.getUtmSourceList()).map(utmList -> utmList.contains(ResellerUtmSourceType.link.getCode())).orElse(false);



        JSONArray countryCodeList = orderQueryParam.getCountryCodeList();
        JSONArray noCountryCodeList = orderQueryParam.getNoCountryCodeList();
        JSONArray paymentChannelList = orderQueryParam.getPaymentChannelList();
        JSONArray noPaymentChannelList = orderQueryParam.getNoPaymentChannelList();

        if (paymentChannelList != null) {
            condition.paymentChannelList = paymentChannelList.stream()
                    .map(paymentChannel -> Objects.requireNonNull(PaymentChannel.parse((String) paymentChannel)).name())
                    .collect(Collectors.toList());
        }

        if (noPaymentChannelList != null) {
            condition.noPaymentChannelList = noPaymentChannelList.stream()
                    .map(paymentChannel -> Objects.requireNonNull(PaymentChannel.parse((String) paymentChannel)).name())
                    .collect(Collectors.toList());
        }

        if (countryCodeList != null) {
            condition.countryList = countryCodeList.stream().map(country -> InstaCountry.parse((String) country).name()).collect(Collectors.toList());
        }

        if (noCountryCodeList != null) {
            condition.noCountryList = noCountryCodeList.stream().map(country -> InstaCountry.parse((String) country).name()).collect(Collectors.toList());
        }

        JSONArray statesArray = orderQueryParam.getStates();
        if (statesArray != null) {
            condition.states = statesArray.stream().map(o -> (Integer) o).collect(Collectors.toList());
        }

        JSONArray productsArray = orderQueryParam.getProducts();
        if (productsArray != null && !productsArray.isEmpty()) {
            condition.productIds = productsArray.stream().map(o -> (Integer) o).collect(Collectors.toList());
        }

        JSONArray commoditiesArray = orderQueryParam.getCommodities();
        if (commoditiesArray != null) {
            condition.commodityIds = commoditiesArray.stream().map(o -> (Integer) o).collect(Collectors.toList());
        }

        JSONArray noProducts = orderQueryParam.getNoProducts();
        if (noProducts != null) {
            condition.noProductIds = noProducts.stream().map(p -> (Integer) p).collect(Collectors.toList());
        }

        Integer rmaState = orderQueryParam.getRmaState();
        if (rmaState != null) {
            condition.rmaStateId = rmaState;
        }

        Integer noRmaState = orderQueryParam.getNoRmaState();
        if (noRmaState != null) {
            condition.noRmaStateId = noRmaState;
        }

        Boolean tradeupOrderFilterMark = orderQueryParam.getTradeupOrderFilterMark();
        if (Objects.nonNull(tradeupOrderFilterMark)) {
            condition.tradeupOrderFilterMark = tradeupOrderFilterMark;
        }

        Boolean orderSellerRemarkMark = orderQueryParam.getOrderSellerRemarkMark();
        if (Objects.nonNull(orderSellerRemarkMark)) {
            condition.orderSellerRemarkMark = orderSellerRemarkMark;
        }

        List<Integer> rmaStateList = orderQueryParam.getRmaStateList();
        if (CollectionUtils.isNotEmpty(rmaStateList)) {
            condition.rmaStateList = rmaStateList;
        }

        return condition;
    }

    /**
     * 解析utm源条件
     *
     * @param condition     条件
     * @param utmSourceList utm来源集合
     * @return {@link List }<{@link String }>
     */
    private static List<String> parseUtmSourceCondition(AdminOrderQueryCondition condition, List<String> utmSourceList) {
        if (CollectionUtils.isEmpty(utmSourceList)) {
            condition.setJoinUtmSource(false);
            return null;
        }
        condition.setJoinUtmSource(true);

        List<ResellerUtmSourceType> utmSourceTypeList = utmSourceList.stream().map(ResellerUtmSourceType::parse).collect(Collectors.toList());
        if (utmSourceTypeList.size() == 1) {
            ResellerUtmSourceType resellerUtmSourceType = utmSourceTypeList.get(0);
            condition.setUtmSourceInclude(!ResellerUtmSourceType.link.equals(resellerUtmSourceType));
            switch (resellerUtmSourceType) {
                case link:
                    return Lists.newArrayList(ResellerUtmSourceType.affiliate_code.getCode(), ResellerUtmSourceType.qr_code.getCode());
                case affiliate_code:
                case qr_code:
                default:
                    return Lists.newArrayList(resellerUtmSourceType.getCode());
            }
        }

        if (utmSourceTypeList.size() == 2) {
            if (utmSourceTypeList.contains(ResellerUtmSourceType.affiliate_code) && utmSourceTypeList.contains(ResellerUtmSourceType.qr_code)) {
                condition.setUtmSourceInclude(true);
                return Lists.newArrayList(ResellerUtmSourceType.affiliate_code.getCode(), ResellerUtmSourceType.qr_code.getCode());
            }
            if (utmSourceTypeList.contains(ResellerUtmSourceType.affiliate_code) && utmSourceTypeList.contains(ResellerUtmSourceType.link)) {
                condition.setUtmSourceInclude(false);
                return Lists.newArrayList(ResellerUtmSourceType.qr_code.getCode());
            }
            if (utmSourceTypeList.contains(ResellerUtmSourceType.qr_code) && utmSourceTypeList.contains(ResellerUtmSourceType.link)) {
                condition.setUtmSourceInclude(false);
                return Lists.newArrayList(ResellerUtmSourceType.affiliate_code.getCode());
            }
        }
        if (utmSourceTypeList.size() == 3) {
            return utmSourceList;
        }
        return null;

    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public LocalDateTime getFromTime() {
        return fromTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public List<Integer> getStates() {
        return states;
    }

    public List<Integer> getProductIds() {
        return productIds;
    }

    public List<Integer> getCommodityIds() {
        return commodityIds;
    }

    public PaymentChannel getPaymentChannel() {
        return paymentChannel;
    }

    public String getInscp() {
        return inscp;
    }

    public String getPromoCode() {
        return promoCode;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public Integer getEndpoint() {
        return endpoint;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public List<Integer> getNoProductIds() {
        return noProductIds;
    }

    public Integer getRmaStateId() {
        return rmaStateId;
    }

    public Integer getNoRmaStateId() {
        return noRmaStateId;
    }

    public List<String> getCountryList() {
        return countryList;
    }

    public List<String> getNoCountryList() {
        return noCountryList;
    }

    public List<String> getPaymentChannelList() {
        return paymentChannelList;
    }

    public List<String> getNoPaymentChannelList() {
        return noPaymentChannelList;
    }

    public Boolean getTradeupOrderFilterMark() {
        return tradeupOrderFilterMark;
    }

    public Boolean getOrderSellerRemarkMark() {
        return orderSellerRemarkMark;
    }

    public List<Integer> getRmaStateList() {
        return rmaStateList;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public String getLogisticsNumber() {
        return logisticsNumber;
    }

    public void setRmaStateList(List<Integer> rmaStateList) {
        this.rmaStateList = rmaStateList;
    }

    public Boolean getUtmSourceInclude() {
        return utmSourceInclude;
    }

    public void setUtmSourceInclude(Boolean utmSourceInclude) {
        this.utmSourceInclude = utmSourceInclude;
    }

    public List<String> getUtmSourceList() {
        return utmSourceList;
    }

    public void setUtmSourceList(List<String> utmSourceList) {
        this.utmSourceList = utmSourceList;
    }

    public Boolean getJoinUtmSource() {
        return joinUtmSource;
    }

    public void setJoinUtmSource(Boolean joinUtmSource) {
        this.joinUtmSource = joinUtmSource;
    }

    public List<String> getQueryUtmSourceList() {
        return queryUtmSourceList;
    }

    public void setQueryUtmSourceList(List<String> queryUtmSourceList) {
        this.queryUtmSourceList = queryUtmSourceList;
    }

    public Boolean getContainsLink() {
        return containsLink;
    }

    public void setContainsLink(Boolean containsLink) {
        this.containsLink = containsLink;
    }
}
