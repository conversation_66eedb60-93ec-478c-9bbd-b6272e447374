package com.insta360.store.business.order.export;

import com.insta360.store.business.admin.order.enums.OrderExportType;
import com.insta360.store.business.admin.order.service.impl.handler.LogisticOrderExportHelper;
import com.insta360.store.business.configuration.utils.SpringContextLocator;
import com.insta360.store.business.order.export.impl.CustomOrderExportHandler;
import com.insta360.store.business.order.export.impl.FedexOrderExportHandler;
import com.insta360.store.business.order.export.interfaces.OrderExportInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24
 */
@Component
public class OrderExportFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogisticOrderExportHelper.class);

    /**
     * 获取导出处理程序
     *
     * @param orderExportType 订单导出类型
     * @return {@link OrderExportInterface}
     */
    public OrderExportInterface getExportHandler(OrderExportType orderExportType) {
        switch (orderExportType) {
            case FEDEX_EXPORT:
                return SpringContextLocator.getBean(FedexOrderExportHandler.class);
            case GO3S_CUSTOM_EXPORT:
            case GO3_CUSTOM_EXPORT:
            case CUSTOM_EXPORT:
            case FMG_CUSTOM_EXPORT:
                return SpringContextLocator.getBean(CustomOrderExportHandler.class);
            default: {
                LOGGER.error("Order export type is null，data:{}", orderExportType);
                return null;
            }
        }
    }
}
