package com.insta360.store.business.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.commodity.dao.CommodityCodeDao;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
@Service
public class CommodityCodeServiceImpl extends BaseServiceImpl<CommodityCodeDao, CommodityCode> implements CommodityCodeService {

    @Override
    public CommodityCode getCommodityCode(Integer commodityId, String area) {
        QueryWrapper<CommodityCode> qw = new QueryWrapper<>();
        qw.eq("commodity", commodityId);
        qw.eq("area", area);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<CommodityCode> listCommodityCode(List<Integer> commodityIds, String area) {
        if (CollectionUtils.isEmpty(commodityIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<CommodityCode> qw = new QueryWrapper<>();
        qw.in("commodity", commodityIds);
        qw.eq("area", area);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<CommodityCode> getCommodityCodes(Integer commodityId) {
        QueryWrapper<CommodityCode> qw = new QueryWrapper<>();
        qw.eq("commodity", commodityId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<CommodityCode> listBySku(String skuCode) {
        QueryWrapper<CommodityCode> qw = new QueryWrapper<>();
        qw.eq("`code`", skuCode);
        qw.groupBy("commodity");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<CommodityCode> listBySkuCodes(List<String> skuCodes) {
        QueryWrapper<CommodityCode> qw = new QueryWrapper<>();
        qw.in("`code`", skuCodes);
        return baseMapper.selectList(qw);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void batchUpsertCodes(Integer commodityId, String code, List<String> countryList) {
        Map<String, CommodityCode> commodityCodeMap = codeMapByCommodityId(commodityId, countryList);
        if (MapUtils.isEmpty(commodityCodeMap)) {
            // 批量新增
            baseMapper.batchSaveCodes(commodityId, code, countryList);
            return;
        }

        if (commodityCodeMap.size() == countryList.size()) {
            // 批量更新
            baseMapper.batchUpdateCodes(commodityId, code, countryList);
            return;
        }

        Set<String> countries = commodityCodeMap.keySet();
        countryList.removeAll(countries);
        // 部分新增
        baseMapper.batchSaveCodes(commodityId, code, countryList);
        // 部分更新
        baseMapper.batchUpdateCodes(commodityId, code, new ArrayList<>(countries));
    }

    @Override
    public List<CommodityCode> listAll(List<Integer> commodityIds) {
        QueryWrapper<CommodityCode> qw = new QueryWrapper<>();
        qw.eq("commodity", commodityIds);
        qw.isNotNull("code");
        qw.ne("code", "");
        return baseMapper.selectList(qw);
    }

    /**
     * 已配置地区映射
     *
     * @param commodityId
     * @param countryList
     * @return
     */
    private Map<String, CommodityCode> codeMapByCommodityId(Integer commodityId, List<String> countryList) {
        QueryWrapper<CommodityCode> qw = new QueryWrapper<>();
        qw.eq("commodity", commodityId);
        qw.in("area", countryList);
        return baseMapper.selectList(qw).stream().collect(Collectors.toMap(CommodityCode::getArea, c -> c));
    }
}
