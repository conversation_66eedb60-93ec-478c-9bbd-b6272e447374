package com.insta360.store.business.trade.service.impl.creation;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.AssertUtil;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.store.business.cloud.bo.UpgradeOrderPreResultBO;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.enums.SubscribeType;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.business.cloud.model.CloudStorageSku;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.service.CloudStorageSkuService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitService;
import com.insta360.store.business.cloud.service.impl.helper.StoreBenefitCheckHelper;
import com.insta360.store.business.cloud.service.impl.helper.StoreSubscribeOrderPreHelper;
import com.insta360.store.business.commodity.bo.GiftItem;
import com.insta360.store.business.commodity.bo.ServiceItem;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityDeliveryTimeConfig;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.configuration.activity.TShirtActivityConfiguration;
import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.discount.enums.CouponUsageType;
import com.insta360.store.business.discount.model.ActivityGift;
import com.insta360.store.business.discount.model.Coupon;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.service.ActivityGiftService;
import com.insta360.store.business.discount.service.CouponService;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.discount.service.impl.helper.TradeDiscountCommonHelper;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.bo.TradeCode;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.enums.TradeCodeType;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.order.bo.OrderExtraBO;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderAdminRemark;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderAdminRemarkService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;
import com.insta360.store.business.prime.service.helper.PrimeOrderHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.reseller.dto.condition.ResellerCode;
import com.insta360.store.business.trade.bo.CalculateTaxResultBO;
import com.insta360.store.business.trade.bo.CloudSubscribeOrderCheckResultBO;
import com.insta360.store.business.trade.bo.OrderCreateBO;
import com.insta360.store.business.trade.config.ActivityConfiguration;
import com.insta360.store.business.trade.dto.condition.TradePriceParam;
import com.insta360.store.business.trade.enums.BlackCloudActivityEnum;
import com.insta360.store.business.trade.service.TradePriceService;
import com.insta360.store.business.trade.service.TradeService;
import com.insta360.store.business.trade.service.impl.helper.TradeHelper;
import com.insta360.store.business.user.model.StoreAccount;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/25
 * @Description:
 */
@Component
public class TradeCreator {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradeCreator.class);

    @Autowired
    TradeHelper tradeHelper;

    @Autowired
    TradeChecker tradeChecker;

    @Autowired
    OrderService orderService;

    @Autowired
    TradeCreator tradeCreator;

    @Autowired
    TradeService tradeService;

    @Autowired
    CouponService couponService;

    @Autowired
    TradeCreatorTax tradeCreatorTax;

    @Autowired
    PrimeOrderHelper primeOrderHelper;

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    PaymentCalculator paymentCalculator;

    @Autowired
    TradePriceService tradePriceService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    OrderAdminRemarkService adminRemarkService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    TradeDiscountCommonHelper tradeDiscountCommonHelper;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    CloudStorageSkuService cloudStorageSkuService;

    @Autowired
    StoreBenefitCheckHelper storeBenefitCheckHelper;

    @Autowired
    StoreSubscribeOrderPreHelper storeSubscribeOrderPreHelper;

    @Autowired
    ActivityGiftService activityGiftService;

    @Autowired
    ActivityConfiguration activityConfiguration;

    @Autowired
    TShirtActivityConfiguration tShirtActivityConfiguration;

    public OrderCreation buildOrderCreationAndCheck(OrderSheet orderSheet){
        TradeCreationCache tradeCreationCache = new TradeCreationCache(orderSheet);
        tradeCreationCache.setFake(false);

        // 创建订单
        OrderCreation orderCreation = this.prepareOrderCreation(tradeCreationCache);

        // 在保存订单之前预先检测各种优惠码!!!
        this.checkForPayment(tradeCreationCache, orderCreation);

        // 是否云服务权益其内
        Boolean benefitPeriod = this.isBenefitPeriod(orderSheet.getStoreAccount());
        orderCreation.setBenefitPeriod(benefitPeriod);

        // 云服务订单校验
        this.validateAndPrepareCloudOrder(orderSheet, orderCreation);

        // PSP 服务校验处理
        this.pspServiceHandle(orderCreation);

        // 计算订单支付金额
        OrderPayment orderPayment = createPayment(orderCreation, tradeCreationCache);
        orderCreation.setOrderPayment(orderPayment);

        // prime
        orderCreation.setPrime(orderSheet.getPrime());
        return orderCreation;
    }

    @GrafanaDataStats(statisticsType = {GrafanaStatisticsType.GAUGE, GrafanaStatisticsType.COUNTER},
            keyType = GrafanaKeyType.ORDER_CREATE_NEW_PRODUCT,
            businessType = GrafanaBusinessType.ORDER)
    @Transactional(rollbackFor = RuntimeException.class)
    public OrderCreateBO create(OrderCreation orderCreation) {
        OrderSheet orderSheet = orderCreation.getOrderSheet();
        TradeCreationCache tradeCreationCache = new TradeCreationCache(orderSheet);
        tradeCreationCache.setFake(false);

        // 云服务订单校验
        this.validateAndPrepareCloudOrder(orderSheet, orderCreation);

        // PSP 服务校验处理
        this.pspServiceHandle(orderCreation);

        // 计算订单支付金额
        OrderPayment orderPayment = createPayment(orderCreation, tradeCreationCache);
        orderCreation.setOrderPayment(orderPayment);

        // 保存订单
        Order order = orderService.createOrder(orderCreation);

        // 绑定其他折扣信息
        tradeHelper.bindOtherDiscountOrder(orderCreation, order);

        // 免支付处理
        List<TradeCode> usedTradeCodes = tradeCreationCache.getUsedTradeCodes();

        // 分销商对应的交易券如果校验未通过，则不会返回该券。
        if (CollectionUtils.isNotEmpty(usedTradeCodes)) {
            tradeCreator.markDiscountInfo(usedTradeCodes, order);

            // 免支付判断
            if (orderPayment.isNotPay()) {
                this.freePayHandle(order, tradeCreationCache.getUsedTradeCodes().get(0));
            }
        }

        // 订单税费计算
        CalculateTaxResultBO calculateTaxResult = tradeCreatorTax.calculateTax(orderCreation, order);
        return new OrderCreateBO(order, calculateTaxResult);
    }

    /**
     * psp服务校验和打标
     *
     * @param orderCreation
     */
    private void pspServiceHandle(OrderCreation orderCreation) {
        List<OrderItem> orderItems = orderCreation.getOrderItems();

        for (OrderItem orderItem : orderItems) {
            if (!Product.PSP_CLOUD_ID.equals(orderItem.getProduct())) {
                break;
            }
            // 下单数量限制
            if (orderItems.size() != 1) {
                throw new InstaException(CommonErrorCode.InvalidParameter);
            }

            orderCreation.setPspOrderMark(Boolean.TRUE);
        }
    }

    /**
     * 标注折扣信息
     *
     * @param tradeCodes
     * @param order
     */
    @GrafanaDataStats(statisticsType = {GrafanaStatisticsType.GAUGE, GrafanaStatisticsType.COUNTER}, businessType = GrafanaBusinessType.DISCOUNT, keyType = GrafanaKeyType.DISCOUNT_USE)
    public void markDiscountInfo(List<TradeCode> tradeCodes, Order order) {
        if (CollectionUtils.isEmpty(tradeCodes)) {
            return;
        }
        for (TradeCode tradeCode : tradeCodes) {
            TradeCodeType tradeCodeType = tradeCode.getCodeType();
            switch (tradeCodeType) {
                case COUPON:
                    // 计算带次数优惠券的使用次数
                    couponService.updateCouponUsageCount(tradeCode.getCode(), CouponUsageType.reduce);
                    break;
                case GIFT_CARD:
                    giftCardService.markOrderUse(tradeCode.getCode(), order.getId());
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 支付处理 （支付、订单状态更新）
     *
     * @param order
     * @param tradeCode
     */
    public void freePayHandle(Order order, TradeCode tradeCode) {
        // 根据地区区分支付渠道
        PaymentChannel paymentChannel = InstaCountry.CN.equals(order.country()) ? PaymentChannel.insta360_gfsc_sclygn : PaymentChannel.insta360_gfsc_sclyhw;
        orderPaymentService.setPaymentSuccess(order.getOrderNumber(), paymentChannel, tradeCode.getCode(), PaymentTradeSecurityType.SECURITY);

        // 加上商家备注
        OrderAdminRemark remark = new OrderAdminRemark();
        remark.setOrder(order.getId());
        remark.setContent("市场领用【" + tradeCode.getRemark() + "】");
        adminRemarkService.save(remark);
    }

    /**
     * 创建假订单
     *
     * @param orderSheet
     * @return
     */
    public OrderCreation createFakeOrder(OrderSheet orderSheet) {
        TradeCreationCache tradeCreationCache = new TradeCreationCache(orderSheet);
        tradeCreationCache.setFake(true);
        return this.prepareOrderCreation(tradeCreationCache);
    }

    /**
     * 订单额外信息检查
     *
     * @param orderSheet
     */
    private void orderExtraCheck(OrderSheet orderSheet) {
        OrderExtraBO orderExtra = orderSheet.getOrderExtra();
        if (Objects.isNull(orderExtra) || StringUtils.isBlank(orderExtra.getStorageRegion()) || Objects.isNull(orderExtra.getAgreementVersion())) {
            throw new InstaException(OrderErrorCode.MissingRegionAndProtocolException);
        }
    }

    /**
     * 判断当前用户是否处于云服务权益期内。
     *
     * @param storeAccount 商店账户对象，不可为null。
     * @return 如果商店账户有关联的未过期的权益记录返回true，否则返回false。
     */
    private Boolean isBenefitPeriod(StoreAccount storeAccount) {
        // 检查传入的商店账户是否为null
        if (Objects.isNull(storeAccount)) {
            return Boolean.FALSE;
        }

        // 根据用户ID获取云存储商店收益，并判断是否过期
        CloudStorageStoreBenefit storeBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(storeAccount.getInstaAccount());
        return Objects.nonNull(storeBenefit) && !storeBenefit.isExpired();
    }

    /**
     * 判断订单是否包含云服务订阅商品
     *
     * @param orderCreation
     * @return
     */
    private CloudSubscribeOrderCheckResultBO isCloudSubscribeOrder(OrderCreation orderCreation) {
        List<OrderItem> orderItems = orderCreation.getOrderItems();
        List<Integer> commodityIds = orderItems.stream().map(OrderItem::getCommodity).distinct().collect(Collectors.toList());

        List<CloudStorageSku> cloudStorageSkuList = cloudStorageSkuService.getSkuByCommodityIds(commodityIds);

        // 存在云服务送赠
        Boolean cloud = orderCreation.getOrderSheet().getCloud();
        if (CollectionUtils.isEmpty(cloudStorageSkuList) && Objects.nonNull(cloud) && cloud) {
            return new CloudSubscribeOrderCheckResultBO(Boolean.TRUE, null);
        }

        if (CollectionUtils.isEmpty(cloudStorageSkuList)) {
            return new CloudSubscribeOrderCheckResultBO(Boolean.FALSE, null);
        }

        if (cloudStorageSkuList.size() != 1) {
            throw new InstaException(CloudSubscribeErrorCode.NotSupportedMultipleCommodityException);
        }

        return new CloudSubscribeOrderCheckResultBO(Boolean.TRUE, cloudStorageSkuList.get(0));
    }

    /**
     * 校验订单信息
     *
     * @param tradeCreationCache
     */
    private void checkForOrderCreation(TradeCreationCache tradeCreationCache) {
        OrderSheet orderSheet = tradeCreationCache.getOrderSheet();
        AssertUtil.notNull(orderSheet);

        // 检测购物车是否为空
        tradeChecker.checkOrderSheet(orderSheet);

        // 检查产品是否合法
        tradeChecker.checkProduct(orderSheet);

        // 检查增值服务数量是否一致
        tradeChecker.checkCustomService(orderSheet);


        // 假订单不检查
        if (!tradeCreationCache.getFake()) {
            // 收货地址
            tradeChecker.checkOrderDelivery(orderSheet);

            // 发票信息
            tradeChecker.checkInvoice(orderSheet);

            // 检查订单国家
            tradeChecker.checkCountry(orderSheet);

            // 检查库存
            tradeChecker.checkStock(orderSheet);

            // 检测是否超过购买限制
            tradeChecker.checkBuyLimit(orderSheet);
        }

        // 检查分销码
        ResellerCode resellerCode = tradeChecker.checkResellerCode(orderSheet);
        tradeCreationCache.setResellerCode(resellerCode);
    }

    /**
     * 解析交易券
     *
     * @param tradeCreationCache
     * @param orderCreation
     */
    private void checkForPayment(TradeCreationCache tradeCreationCache, OrderCreation orderCreation) {
        OrderSheet orderSheet = orderCreation.getOrderSheet();
        if (StringUtils.isNotBlank(orderSheet.getCouponCode())) {
            TradeCode coupon = tradeDiscountCommonHelper.findTradeCode(orderSheet.getCouponCode());
            tradeCreationCache.setCoupon(Objects.nonNull(coupon) ? (Coupon) coupon : null);
        }

        if (StringUtils.isNotBlank(orderSheet.getGiftCardCode())) {
            TradeCode giftCard = tradeDiscountCommonHelper.findTradeCode(orderSheet.getGiftCardCode());
            tradeCreationCache.setGiftCard(Objects.nonNull(giftCard) ? (GiftCard) giftCard : null);
        }
    }

    /**
     * 解析订单
     *
     * @param tradeCreationCache
     * @return
     */
    private OrderCreation prepareOrderCreation(TradeCreationCache tradeCreationCache) {
        this.checkForOrderCreation(tradeCreationCache);

        OrderSheet orderSheet = tradeCreationCache.getOrderSheet();
        List<OrderSheet.SheetItem> sheetItems = orderSheet.getSheetItems();

        // 订单子项的套餐id
        List<Integer> commodityIds = sheetItems.stream().map(OrderSheet.SheetItem::getCommodityId).distinct().collect(Collectors.toList());
        // 绑定服务的套餐id
        List<OrderSheet.SheetItem> bindServiceItems = sheetItems.stream().filter(sheetItem -> CollectionUtils.isNotEmpty(sheetItem.getBindServices())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bindServiceItems)) {
            List<Integer> bindServiceCommodityIds = sheetItems.stream().map(OrderSheet.SheetItem::getBindServices)
                    .flatMap(List::stream).map(ServiceItem::getId).distinct().collect(Collectors.toList());
            commodityIds.addAll(bindServiceCommodityIds);
        }

        // 赠品套餐id
        List<GiftItem> giftItems = tradeService.getOrderGifts(orderSheet);
        if (CollectionUtils.isNotEmpty(giftItems)) {
            List<Integer> giftCommodityIds = giftItems.stream().map(GiftItem::getCommodityId).distinct().collect(Collectors.toList());
            commodityIds.addAll(giftCommodityIds);
        }

        // 赠品数据套餐id
        List<OrderSheet.SheetItem> sheetGiftItems = orderSheet.getSheetGiftItems();
        if (CollectionUtils.isNotEmpty(sheetGiftItems)) {
            List<Integer> sheetGiftCommodityIds = sheetGiftItems.stream().map(OrderSheet.SheetItem::getCommodityId).distinct().collect(Collectors.toList());
            commodityIds.addAll(sheetGiftCommodityIds);
        }

        // 批量查询全部套餐信息
        Map<Integer, Commodity> commodityMap = commodityBatchHelper.commodityAllMapCommodityIds(commodityIds);
        Map<Integer, CommodityPrice> commodityPriceMap = commodityBatchHelper.getPriceByCommodityIds(commodityIds, orderSheet.getCountry());

        Currency currency = countryConfigService.getCountryCurrency(orderSheet.getCountry());

        // 订单项
        List<OrderItem> orderItems = prepareOrderItems(tradeCreationCache, commodityMap, commodityPriceMap);

        // 赠品
        List<OrderItem> giftOrderItems = prepareGiftOrderItems(currency, giftItems, commodityMap, commodityPriceMap);

        // 圣诞活动care赠品重置
        giftOrderItems = resetCareGiftItem(giftOrderItems, orderItems, orderSheet.getCountry());

        // 赠品主机数量一致性校验&赠品处理
        prepareOrderSheetGifts(orderItems, giftOrderItems, tradeCreationCache, currency, commodityMap, commodityPriceMap);

        // 计算订单发货时间
        CommodityDeliveryTimeConfig deliveryTimeConfig = tradeService.getEstimatedDeliveryTime(orderSheet.getCountry(), commodityMap, orderItems, giftOrderItems);

        // 大促云服务送增3197
        bindCloudGift(orderItems, giftOrderItems, orderSheet);

        // 创建订单
        OrderCreation orderCreation = new OrderCreation();
        orderCreation.setOrderSheet(orderSheet);
        orderCreation.setOrderItems(orderItems);
        orderCreation.setGiftItems(giftOrderItems);
        orderCreation.setEstimateDays(deliveryTimeConfig.getEstimateDays());
        orderCreation.setResellerCode(tradeCreationCache.getResellerCode());

        return orderCreation;
    }

    /**
     * 增值服务赠品拆分
     *
     * @param giftOrderItems
     * @param orderItems
     * @param country
     * @return
     */
    private List<OrderItem> resetCareGiftItem(List<OrderItem> giftOrderItems, List<OrderItem> orderItems, InstaCountry country) {
        if (!InstaCountry.US.equals(country)) {
            LOGGER.info(String.format("[圣诞送增care]非美国地区不予赠送,country:%s,giftOrderItems:%s,orderItems:%s", country, giftOrderItems, orderItems));
            return giftOrderItems;
        }

        // 不存在care赠品
        List<OrderItem> careItemList = giftOrderItems.stream().filter(orderItem -> Product.INSURANCE_SERVICE_PRODUCT.contains(orderItem.getProduct())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(careItemList)) {
            LOGGER.info(String.format("[圣诞送增care]不存在care赠品,country:%s,giftOrderItems:%s,careItemList:%s", country, giftOrderItems, careItemList));
            return giftOrderItems;
        }

        // 剔除合并赠品 进行拆分
        giftOrderItems = giftOrderItems.stream().filter(orderItem -> !Product.INSURANCE_SERVICE_PRODUCT.contains(orderItem.getProduct())).collect(Collectors.toList());
        Currency currency = countryConfigService.getCountryCurrency(country);
        for (OrderItem orderItem : orderItems) {
            Optional<ActivityGift> giftConfig = activityGiftService.getGiftConfig(activityConfiguration.getBlackActivityConfig(), orderItem.getProduct(), orderItem.getCommodity());
            if (giftConfig.isPresent()) {
                LOGGER.info(String.format("[圣诞送增care]不存在赠品,giftOrderItems:%s", giftOrderItems));
                List<GiftItem> giftItemList = activityGiftService.parseGiftItems(giftConfig.get());
                GiftItem gift = giftItemList.get(0);
                Integer commodityId = gift.getCommodityId();

                Commodity commodity = commodityService.getById(commodityId);
                CommodityPrice commodityPrice = commodityPriceService.getPrice(commodityId, country);

                OrderItem careGift = new OrderItem();
                careGift.setProduct(commodity.getProduct());
                careGift.setCommodity(commodityId);
                careGift.setNumber(orderItem.getNumber());
                careGift.setPrice(0f);
                careGift.setOriginAmount(commodityPrice.getOriginAmount());
                careGift.setCurrency(currency.name());
                careGift.setIsGift(true);
                giftOrderItems.add(careGift);
            }
        }
        LOGGER.info(String.format("[圣诞送增care]care拆分结束,giftOrderItems:%s", giftOrderItems));
        return giftOrderItems;
    }

    /**
     * 赠品主机数量一致性校验&赠品处理
     *
     * @param orderItems
     * @param giftOrderItems
     * @param tradeCreationCache
     * @param currency
     */
    private void prepareOrderSheetGifts(List<OrderItem> orderItems, List<OrderItem> giftOrderItems, TradeCreationCache tradeCreationCache,
                                        Currency currency, Map<Integer, Commodity> commodityMap, Map<Integer, CommodityPrice> priceMap) {
        OrderSheet orderSheet = tradeCreationCache.getOrderSheet();
        List<OrderSheet.SheetItem> sheetGiftItems = orderSheet.getSheetGiftItems();
        if (CollectionUtils.isEmpty(sheetGiftItems)) {
            return;
        }

        String uuid = UUIDUtils.generateUuid();
        LOGGER.info("[T恤送赠] uuid:{} 配置规则:{} orderItems:{} giftOrderItems:{} orderSheer:{} tradeCreationCache:{}",
                uuid,
                tShirtActivityConfiguration,
                orderItems,
                giftOrderItems,
                orderSheet,
                tradeCreationCache);

        Boolean enable = tShirtActivityConfiguration.getEnable();
        if (enable == null || !enable) {
            LOGGER.info(String.format("[T恤送赠]送赠活动未开启,uuid:%s country:%s", uuid, orderSheet.getCountry()));
            return;
        }

        InstaCountry country = orderSheet.getCountry();
        LocalDateTime now = LocalDateTime.now();
        boolean activityEligibility = tShirtActivityConfiguration.checkActivityEligibility(country.name(), now);
        if (!activityEligibility) {
            String message = String.format("[T恤送赠]活动不支持 uuid:%s country:%s time:%s rule:%s",
                    uuid,
                    orderSheet.getCountry(),
                    now,
                    tShirtActivityConfiguration.getGiftRules());
            LOGGER.info(message);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WKX);
            return;
        }

        // 赠品数据封装
        List<OrderItem> sheetGifts = sheetGiftItems.stream().map(giftItem -> {
            Commodity commodity = commodityMap.get(giftItem.getCommodityId());
            CommodityPrice commodityPrice = priceMap.get(giftItem.getCommodityId());
            if (Objects.isNull(commodity) || Objects.isNull(commodityPrice)) {
                throw new InstaException(OrderErrorCode.OrderInfoMissingException);
            }

            // todo 目前仅支持T恤产品
            if (!tShirtActivityConfiguration.getTiXuProductId().equals(commodity.getProduct())) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("不支持的赠品产品{%s}", orderSheet), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW, FeiShuAtUser.WKX);
                throw new InstaException(OrderErrorCode.OrderInfoMissingException);
            }

            OrderItem orderItem = new OrderItem();
            orderItem.setProduct(commodity.getProduct());
            orderItem.setCommodity(commodity.getId());
            orderItem.setNumber(giftItem.getNumber());
            orderItem.setPrice(0f);
            orderItem.setOriginAmount(commodityPrice.getOriginAmount());
            orderItem.setCurrency(currency.name());
            orderItem.setIsGift(true);
            return orderItem;
        }).collect(Collectors.toList());

        // 赠品&主机数量一致性校验
        checkSheetGift(sheetGifts, orderItems, orderSheet);
        giftOrderItems.addAll(sheetGifts);
    }

    /**
     * 大促云服务送增3197
     *
     * @param orderItems
     * @param giftOrderItems
     * @param orderSheet
     */
    private void bindCloudGift(List<OrderItem> orderItems, List<OrderItem> giftOrderItems, OrderSheet orderSheet) {
        if (Objects.isNull(orderSheet.getCloud()) || !orderSheet.getCloud()) {
            return;
        }

        if (InstaCountry.US.equals(orderSheet.getCountry()) || InstaCountry.CN.equals(orderSheet.getCountry())) {
            LOGGER.info(String.format("[云服务黑五送增]美国地区不参与活动,不支持赠品绑定.国家:%s,订单信息{%s}", orderSheet.getCountry().name(), orderSheet));
            return;
        }

        // 核对活动上下线时间
        BlackCloudActivityEnum blackCloudActivityEnum = BlackCloudActivityEnum.parseCountry(orderSheet.getCountry().name());

        // 缓存中存在
        String cloudCacheStartTime = (String) RedisTemplateUtil.getValue(blackCloudActivityEnum.getType() + "_START_TIME");
        String cloudCacheEndTime = (String) RedisTemplateUtil.getValue(blackCloudActivityEnum.getType() + "_END_TIME");

        // 送赠活动时间
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDateTime cloudStartTime = Objects.nonNull(cloudCacheStartTime) ? LocalDateTime.parse(cloudCacheStartTime) : LocalDateTime.parse(blackCloudActivityEnum.getStartTime());
        LocalDateTime cloudEndTime = Objects.nonNull(cloudCacheEndTime) ? LocalDateTime.parse(cloudCacheEndTime) : LocalDateTime.parse(blackCloudActivityEnum.getEndTime());

        if (!(nowTime.isAfter(cloudStartTime) && nowTime.isBefore(cloudEndTime))) {
            orderSheet.setCloud(false);
            LOGGER.info(String.format("[云服务黑五送增]当前国家不在活动时间内,不支持赠品绑定.国家:%s,订单信息{%s}", orderSheet.getCountry().name(), orderSheet));
            return;
        }

        // 相机包含x3 x4
        List<OrderItem> cameraXList = orderItems.stream().filter(orderItem -> Product.X3X4_LIST.contains(orderItem.getProduct())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cameraXList)) {
            orderSheet.setCloud(false);
            LOGGER.info(String.format("[云服务黑五送增]订单不存在x3x4,不支持赠品绑定.国家:%s,订单信息{%s}", orderSheet.getCountry().name(), orderSheet));
            return;
        }

        // 订单包含云服务
        List<OrderItem> cloudList = orderItems.stream().filter(orderItem -> Product.CLOUD_ID == orderItem.getProduct()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cloudList)) {
            orderSheet.setCloud(false);
            LOGGER.info(String.format("[云服务黑五送增]订单已经包含云服务商品,不支持赠品绑定.国家:%s,订单信息{%s}", orderSheet.getCountry().name(), orderSheet));
            return;
        }

        Currency currency = countryConfigService.getCountryCurrency(orderSheet.getCountry());

        // 增加3197为赠品
        CommodityPrice commodityPrice = commodityPriceService.getPrice(Commodity.CLOUD_GIFT_ID, orderSheet.getCountry());
        OrderItem orderItem = new OrderItem();
        orderItem.setProduct(Product.CLOUD_ID);
        orderItem.setCommodity(Commodity.CLOUD_GIFT_ID);
        orderItem.setNumber(1);
        orderItem.setPrice(0f);
        orderItem.setOriginAmount(commodityPrice.getOriginAmount());
        orderItem.setCurrency(currency.name());
        orderItem.setIsGift(true);
        giftOrderItems.add(orderItem);
        LOGGER.info(String.format("[云服务黑五送增]赠品绑定成功.国家:%s,订单信息{%s}", orderSheet.getCountry().name(), orderSheet));
    }

    /**
     * 主机&T恤数量校验(不允许赠品数量大于相机数量)
     *
     * @param sheetGiftItems
     * @param orderItems
     * @param orderSheet
     */
    private void checkSheetGift(List<OrderItem> sheetGiftItems, List<OrderItem> orderItems, OrderSheet orderSheet) {
        // 支持赠送T恤产品列表
        List<Integer> tShirtProducts = tShirtActivityConfiguration.getActivityProductIds();
        LOGGER.info("[T恤送赠] 支持赠送T恤产品列表:{}", tShirtProducts);

        int itemProductNumber = 0;
        int itemGiftNumber = 0;
        for (OrderItem orderItem : orderItems) {
            // 收集可赠送T恤产品数量
            if (tShirtProducts.contains(orderItem.getProduct())) {
                itemProductNumber += orderItem.getNumber();
            }
        }

        for (OrderItem sheetGiftItem : sheetGiftItems) {
            // 收集T恤产品数量
            if (tShirtActivityConfiguration.getTiXuProductId().equals(sheetGiftItem.getProduct())) {
                itemGiftNumber += sheetGiftItem.getNumber();
            }
        }

        // 主机&T恤数量校验(不允许赠品数量大于相机数量)
        if (itemGiftNumber > itemProductNumber) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("不允许赠品数量大于相机数量{%s}", orderSheet), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW, FeiShuAtUser.WXQ);
            throw new InstaException(OrderErrorCode.OrderGiftDisAccordException);
        }
    }

    /**
     * 支付信息
     *
     * @param orderCreation
     * @param tradeCreationCache
     * @return
     */
    private OrderPayment createPayment(OrderCreation orderCreation, TradeCreationCache tradeCreationCache) {
        PaymentCalculateInfo creationInfo = new PaymentCalculateInfo();
        creationInfo.setOrder(orderCreation);
        creationInfo.setCoupon(tradeCreationCache.getCoupon());
        creationInfo.setGiftCard(tradeCreationCache.getGiftCard());
        creationInfo.setResellerCode(tradeCreationCache.getResellerCode());
        creationInfo.setRepairOrder(orderCreation.getOrderSheet().getRepairOrder());
        PaymentCalculateResult paymentCalculateResult = paymentCalculator.calculate(creationInfo);

        // 记录交易券信息
        tradeCreationCache.setUsedTradeCodes(paymentCalculateResult.getUsedTradeCodes());
        // 记录折扣券折扣信息
        orderCreation.setDiscountCheckResult(paymentCalculateResult.getDiscountCheckResult());
        // 记录云服务配件折扣信息
        orderCreation.setBenefitDiscountResult(paymentCalculateResult.getBenefitDiscountResult());

        return paymentCalculateResult.getOrderPayment();
    }

    /**
     * 解析订单子项
     *
     * @param tradeCreationCache
     * @param commodityMap
     * @param commodityPriceMap
     * @return
     */
    private List<OrderItem> prepareOrderItems(TradeCreationCache tradeCreationCache, Map<Integer, Commodity> commodityMap, Map<Integer, CommodityPrice> commodityPriceMap) {
        OrderSheet orderSheet = tradeCreationCache.getOrderSheet();
        List<OrderSheet.SheetItem> sheetItems = orderSheet.getSheetItems();

        // 商品下单项
        List<OrderItem> productOrderItem = sheetItems.stream().map(sheetItem -> {
            Integer commodityId = sheetItem.getCommodityId();

            CommodityPrice commodityPrice = commodityPriceMap.get(commodityId);
            if (commodityPrice == null) {
                throw new InstaException(OrderErrorCode.OrderInfoMissingException);
            }

            TradePriceParam tradePriceParam = new TradePriceParam();
            tradePriceParam.setCommodityPrice(commodityPrice);
            tradePriceParam.setResellerCode(tradeCreationCache.getResellerCode());

            Price price = tradePriceService.defineTradePrice(tradePriceParam);
            Commodity commodity = commodityMap.get(commodityId);
            if (commodity == null) {
                throw new InstaException(OrderErrorCode.OrderInfoMissingException);
            }

            OrderItem orderItem = new OrderItem();
            orderItem.setProduct(commodity.getProduct());
            orderItem.setCommodity(commodityId);
            orderItem.setNumber(sheetItem.getNumber());
            orderItem.setPrice(price.getAmount());
            orderItem.setOriginAmount(commodityPrice.getOriginAmount());
            orderItem.setCurrency(price.getCurrency().name());
            orderItem.setState(OrderItemState.normal.getCode());
            orderItem.setIsGift(false);
            return orderItem;
        }).collect(Collectors.toList());

        // 把含在套餐里面的服务项分解出来，每个作为一个item
        List<OrderItem> serviceOrderItems = new ArrayList<>();
        sheetItems.forEach(si -> {
            List<ServiceItem> bindServices = si.getBindServices();
            if (bindServices != null) {
                bindServices.forEach(bs -> {
                    Integer commodityId = bs.getId();

                    // 获取价格
                    CommodityPrice commodityPrice = commodityPriceMap.get(commodityId);
                    if (commodityPrice == null) {
                        throw new InstaException(OrderErrorCode.OrderInfoMissingException);
                    }

                    Price price = commodityPrice.price();
                    Commodity commodity = commodityMap.get(commodityId);
                    if (commodity == null) {
                        throw new InstaException(OrderErrorCode.OrderInfoMissingException);
                    }

                    OrderItem orderItem = new OrderItem();
                    orderItem.setProduct(commodity.getProduct());
                    orderItem.setCommodity(commodityId);
                    orderItem.setNumber(bs.getNumber());
                    orderItem.setPrice(price.getAmount());
                    orderItem.setOriginAmount(commodityPrice.getOriginAmount());
                    orderItem.setCurrency(price.getCurrency().name());
                    orderItem.setState(OrderItemState.normal.getCode());
                    orderItem.setIsGift(false);
                    // 服务项带上所附属的套餐id
                    orderItem.setBelongToCommodityId(si.getCommodityId());
                    serviceOrderItems.add(orderItem);
                });
            }
        });

        productOrderItem.addAll(serviceOrderItems);
        return productOrderItem;
    }

    /**
     * 解析订单赠品子项
     *
     * @param currency
     * @param giftItems
     * @param commodityMap
     * @param commodityPriceMap
     * @return
     */
    private List<OrderItem> prepareGiftOrderItems(Currency currency, List<GiftItem> giftItems, Map<Integer, Commodity> commodityMap, Map<Integer, CommodityPrice> commodityPriceMap) {
        // 赠品项
        if (CollectionUtils.isEmpty(giftItems)) {
            return new ArrayList<>(0);
        }

        return giftItems.stream().map(giftItem -> {
            Integer commodityId = giftItem.getCommodityId();

            Commodity commodity = commodityMap.get(commodityId);
            CommodityPrice commodityPrice = commodityPriceMap.get(commodityId);

            if (commodity == null || commodityPrice == null) {
                throw new InstaException(OrderErrorCode.OrderInfoMissingException);
            }

            OrderItem orderItem = new OrderItem();
            orderItem.setProduct(commodity.getProduct());
            orderItem.setCommodity(commodityId);
            orderItem.setNumber(giftItem.getNumber());
            orderItem.setPrice(0f);
            orderItem.setOriginAmount(commodityPrice.getOriginAmount());
            orderItem.setCurrency(currency.name());
            orderItem.setIsGift(true);
            return orderItem;
        }).collect(Collectors.toList());
    }

    /**
     * 校验并准备云服务订单
     * 该方法用于执行云服务订单的特定校验和准备操作，根据订阅支付类型进行不同的处理
     *
     * @param orderSheet    订单表单对象，包含订单的详细信息
     * @param orderCreation 订单创建对象，用于设置订单的特定属性
     */
    private void validateAndPrepareCloudOrder(OrderSheet orderSheet, OrderCreation orderCreation) {
        // 是否云服务订单
        CloudSubscribeOrderCheckResultBO subscribeOrderCheckResult = this.isCloudSubscribeOrder(orderCreation);
        // 如果不是云服务订单，则直接返回，不进行后续处理
        if (!subscribeOrderCheckResult.getCloudSubscribeOrderMark()) {
            return;
        }

        // 获取订阅类型
        SubscribeType subscribeType = orderSheet.getCloudSubscribeType();
        // 如果订阅支付类型为空或未知，则直接返回，不进行后续处理
        if (Objects.isNull(subscribeType) || SubscribeType.UNKNOWN.equals(subscribeType)) {
            // todo 兼容旧版本，后续前端上线后下个迭代可删除
            subscribeType = SubscribeType.PURCHASE;
        }

        // 下单用户
        StoreAccount storeAccount = orderSheet.getStoreAccount();
        // 下单国家
        InstaCountry country = orderSheet.getCountry();

        // 订阅场景类型
        ServiceScenesType serviceScenesType;
        // 根据订阅支付类型执行相应的处理逻辑
        switch (subscribeType) {
            case PURCHASE:
                // 云服务购买前置校验
                Integer instaAccountId = Objects.nonNull(storeAccount) ? storeAccount.getInstaAccount() : null;
                String email = orderSheet.getContactEmail();
                storeBenefitCheckHelper.allowPurchasesCheck(instaAccountId, email);
                // 如果是云服务‘购买’订单，则需检查用户是否勾选存储地区与协议
                this.orderExtraCheck(orderSheet);
                // 设置云服务订阅场景
                serviceScenesType = ServiceScenesType.PURCHASE;
                break;
            case RENEW:
                // 对于续订操作，直接设置订阅场景类型
                serviceScenesType = ServiceScenesType.RENEW;
                break;
            case UPGRADE:
                // 云服务升级前置校验
                UpgradeOrderPreResultBO upgradeOrderPreResultBo = storeSubscribeOrderPreHelper.cloudSubscribeUpgradeOrderPreHandle(storeAccount, country, subscribeOrderCheckResult.getCloudStorageSku());
                orderSheet.setOrderDelivery(upgradeOrderPreResultBo.getOrderDelivery());
                orderSheet.setBillingAddress(upgradeOrderPreResultBo.getOrderBillingAddress());
                // 置空优惠券、代金券、分销码
                orderSheet.setResellerCode(null);
                orderSheet.setCouponCode(null);
                orderSheet.setGiftCardCode(null);
                serviceScenesType = ServiceScenesType.UPGRADE;
                break;
            default:
                // 如果订阅支付类型不受支持，则抛出异常
                throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 设置云服务订单标记和服务场景类型
        orderCreation.setCloudSubscribeOrderMark(Boolean.TRUE);
        orderCreation.setServiceScenesType(serviceScenesType);
    }
}
