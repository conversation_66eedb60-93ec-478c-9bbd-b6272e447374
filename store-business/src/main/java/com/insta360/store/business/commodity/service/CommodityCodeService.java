package com.insta360.store.business.commodity.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.commodity.model.CommodityCode;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/1/25
 * @Description:
 */
public interface CommodityCodeService extends BaseService<CommodityCode> {

    /**
     * 获取套餐在某个地区的料号
     *
     * @param commodityId
     * @param area
     * @return
     */
    CommodityCode getCommodityCode(Integer commodityId, String area);

    /**
     * 获取套餐在某个地区的料号
     *
     * @param commodityIds
     * @param area
     * @return
     */
    List<CommodityCode> listCommodityCode(List<Integer> commodityIds, String area);

    /**
     * 获取套餐在所有地区的料号
     *
     * @param commodityId
     * @return
     */
    List<CommodityCode> getCommodityCodes(Integer commodityId);

    /**
     * 根据料号获取对应的套餐
     *
     * @param skuCode
     * @return
     */
    List<CommodityCode> listBySku(String skuCode);

    /**
     * 根据料号获取对应的套餐
     *
     * @param skuCodes
     * @return
     */
    List<CommodityCode> listBySkuCodes(List<String> skuCodes);

    /**
     * 批量新增/保存
     *
     * @param commodityId
     * @param code
     * @param countryList
     */
    void batchUpsertCodes(Integer commodityId, String code, List<String> countryList);

    /**
     * 获取所有料号
     *
     * @return
     */
    List<CommodityCode> listAll(List<Integer> commodityIds);
}
