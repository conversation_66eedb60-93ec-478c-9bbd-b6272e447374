package com.insta360.store.service.controller.payment.controller.checkout;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.common.constants.CommonConstant;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.impl.aop.annotation.ApplePayApiTimeConsume;
import com.insta360.store.business.order.service.impl.aop.annotation.LogOrderStateChange;
import com.insta360.store.business.payment.bo.ApplePayBO;
import com.insta360.store.business.payment.bo.GooglePayBO;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.dto.CheckoutDTO;
import com.insta360.store.business.payment.enums.CkoTokenType;
import com.insta360.store.business.payment.enums.PaymentBusinessType;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentMethodEnum;
import com.insta360.store.business.payment.service.impl.helper.CkoPaymentHelper;
import com.insta360.store.business.utils.CommonUtil;
import com.insta360.store.service.controller.payment.controller.BasePayment;
import com.insta360.store.service.controller.payment.vo.ApplePaySessionVO;
import com.insta360.store.service.controller.test.dto.ApplePayDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2024/02/21
 * @Description:
 */
@RestController
public class CheckoutPaymentApi extends BasePayment {

    @Autowired
    CkoPaymentHelper ckoPaymentHelper;

    /**
     * Apple Pay session 获取
     *
     * @param applePayParam
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping(path = "/store/payment/checkout/validateApplePaySession", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> validateApplePaySession(@Validated @RequestBody ApplePayDTO applePayParam) {
        String responseBody = ckoPaymentHelper.validateApplePaySession(applePayParam.getAppleUrl(),
                applePayParam.getDisplayName(), applePayParam.getDomainName());
        ApplePaySessionVO paySessionVo = JSONObject.parseObject(responseBody, ApplePaySessionVO.class);
        return Response.ok(paySessionVo);
    }

    /**
     * Apple Pay（Checkout）
     *
     * @param paymentParam
     * @return
     * @LogOrderChange： Apple Pay支付结果在前置调用中进行处理
     */
    @ApplePayApiTimeConsume
    @LogOrderStateChange(isAuto = true)
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping(path = "/store/payment/checkout/ckoApplePay", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<? extends Map> ckoApplePay(@RequestBody CheckoutDTO paymentParam) {
        // 订单信息校验
        Order order = orderService.getByOrderNumber(paymentParam.getOrderNumber());
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        ApplePayBO applePayBo = JSONObject.toJavaObject(paymentParam.getInnerData(), ApplePayBO.class);
        CommonUtil.validationObject(applePayBo);

        // 支付业务类型校验
        PaymentBusinessType paymentBusinessType = PaymentBusinessType.parse(paymentParam.getPayMode());
        if (Objects.isNull(paymentBusinessType)) {
            paymentBusinessType = PaymentBusinessType.NORMAL_PAY;
        }

        PaymentExtra paymentExtra = getPaymentExtra();
        paymentExtra.setCkoPaymentMethod(CkoPaymentMethodEnum.TOKEN.getName());
        paymentExtra.setPaymentChannelId(paymentParam.getPaymentChannelId());
        paymentExtra.setTokenType(CkoTokenType.APPLE_PAY.getName());
        paymentExtra.setApplePayBo(applePayBo);
        paymentExtra.setPaymentMethod(StorePaymentMethodEnum.CKO_PAYMENT);
        PaymentChannel paymentChannel = PaymentBusinessType.NORMAL_PAY.equals(paymentBusinessType) ? ckoPaymentHelper.getCkoApChannel(order) : ckoPaymentHelper.getCkoApSubscribeChannel(order);

        // 1、返回订单号，页面手动Redirect到结算页面
        String result = paymentService.payOrder(order.getId(), paymentChannel, paymentExtra);
        return Response.ok("orderNumber", (Object) result);
    }

    /**
     * Google Pay（Checkout）
     *
     * @param paymentParam
     * @return
     * @LogOrderChange： Google Pay支付结果在前置调用中进行处理
     */
    @LogOrderStateChange(isAuto = true)
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping(path = "/store/payment/checkout/ckoGooglePay", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<? extends Map> ckoGooglePay(@RequestBody CheckoutDTO paymentParam) {
        // 订单信息校验
        Order order = orderService.getByOrderNumber(paymentParam.getOrderNumber());
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        GooglePayBO googlePayBo = JSONObject.toJavaObject(paymentParam.getGpInnerData(), GooglePayBO.class);
        CommonUtil.validationObject(googlePayBo);

        // 支付业务类型校验
        PaymentBusinessType paymentBusinessType = PaymentBusinessType.parse(paymentParam.getPayMode());
        if (Objects.isNull(paymentBusinessType)) {
            paymentBusinessType = PaymentBusinessType.NORMAL_PAY;
        }

        PaymentExtra paymentExtra = getPaymentExtra();
        paymentExtra.setCkoPaymentMethod(CkoPaymentMethodEnum.TOKEN.getName());
        paymentExtra.setPaymentChannelId(paymentParam.getPaymentChannelId());
        paymentExtra.setTokenType(CkoTokenType.GOOGLE_PAY.getName());
        paymentExtra.setUserAgent(getUserAgent());
        paymentExtra.setStoreAccount(getAccessUser());
        paymentExtra.setGooglePayBo(googlePayBo);
        paymentExtra.setPaymentMethod(StorePaymentMethodEnum.CKO_PAYMENT);
        PaymentChannel paymentChannel = PaymentBusinessType.NORMAL_PAY.equals(paymentBusinessType) ? ckoPaymentHelper.getCkoGpChannel(order) : ckoPaymentHelper.getCkoGpSubscribeChannel(order);

        // 1、返回订单号，页面手动Redirect到结算页面
        // 2、返回url，页面手动Redirect到3D验证页面
        String result = paymentService.payOrder(order.getId(), paymentChannel, paymentExtra);
        return result.startsWith(CommonConstant.ORDER_PREFIX) ? Response.ok("orderNumber", (Object) result) : Response.ok("payUrl", (Object) result);
    }

    /**
     * 信用卡支付（Checkout）
     *
     * @param paymentParam
     * @return
     * @LogOrderChange：checkout支付结果在前置调用中进行处理
     */
    @LogOrderStateChange(isAuto = true)
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping(path = "/store/payment/checkout/ckoCreditCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<? extends Map> ckoCreditCard(@RequestBody CheckoutDTO paymentParam) {
        // 订单信息校验
        Order order = orderService.getByOrderNumber(paymentParam.getOrderNumber());
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 支付业务类型校验
        PaymentBusinessType paymentBusinessType = PaymentBusinessType.parse(paymentParam.getPayMode());
        if (Objects.isNull(paymentBusinessType)) {
            paymentBusinessType = PaymentBusinessType.NORMAL_PAY;
        }

        PaymentExtra paymentExtra = getPaymentExtra();
        paymentExtra.setCkoPaymentMethod(CkoPaymentMethodEnum.CARD.getName());
        paymentExtra.setThreeDomainTrade(paymentParam.getThreeDomainTrade());
        paymentExtra.setCardMonth(paymentParam.getCardMonth());
        paymentExtra.setCardYear(paymentParam.getCardYear());
        paymentExtra.setName(paymentParam.getCardName());
        paymentExtra.setCardNumber(paymentParam.getCardNumber());
        paymentExtra.setCvv(paymentParam.getCardCvv());
        paymentExtra.setPaymentChannelId(paymentParam.getPaymentChannelId());
        paymentExtra.setExemption(paymentParam.getExemption());
        paymentExtra.setPaymentMethod(StorePaymentMethodEnum.CKO_PAYMENT);
        PaymentChannel paymentChannel = PaymentBusinessType.NORMAL_PAY.equals(paymentBusinessType) ? ckoPaymentHelper.getCkoNormalChannel(order) : ckoPaymentHelper.getCkoSubscribeChannel(order);

        // 1、返回订单号，页面手动Redirect到结算页面
        // 2、返回url，页面手动Redirect到3D验证页面
        String result = paymentService.payOrder(order.getId(), paymentChannel, paymentExtra);
        return result.startsWith(CommonConstant.ORDER_PREFIX) ? Response.ok("orderNumber", (Object) result) : Response.ok("payUrl", (Object) result);
    }

    /**
     * 异常通知
     */
    @ExceptionHandler(Exception.class)
    public void handlerPaymentException(Exception e, HttpServletResponse response) {
        this.doHandlerPaymentException(e, response);
    }

}
