package com.insta360.store.business.outgoing.rpc.user.service.fallback;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.outgoing.rpc.user.dto.UserAccount;
import com.insta360.store.business.outgoing.rpc.user.service.UserAccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2021/05/10
 * @Description: 服务熔断/降级处理
 */
@Component
public class UserAccountServiceFallBack implements UserAccountService {

    public static final Logger LOGGER = LoggerFactory.getLogger(UserAccountServiceFallBack.class);

    @Override
    public Response<UserAccount> getByUsername(String username) {
        LOGGER.error("user-service调用失败。路径：/rpc/user/service/account/getProfileByUsername。参数：【username" + username + "】");
        return Response.failed();
    }

    @Override
    public Response<UserAccount> getByUserToken(String token) {
        LOGGER.error("user-service调用失败。路径：/rpc/user/service/account/getProfileByToken。参数：【token" + token + "】");
        return Response.failed();
    }

    @Override
    public Response<UserAccount> getProfileByUserId(Integer userId) {
        LOGGER.error("user-service调用失败。路径：/rpc/user/service/account/getProfileByUserId。参数：【userId" + userId + "】");
        return Response.failed();
    }
}
