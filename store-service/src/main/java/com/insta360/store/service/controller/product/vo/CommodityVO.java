package com.insta360.store.service.controller.product.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.order.enums.OrderState;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/1/25
 * @Description:
 */
public class CommodityVO extends Commodity {

    private CommodityInfo info;

    private Integer stock;

    @JSONField(name = "sale_state")
    private Integer saleState;

    @JSONField(name = "order_buy_limit")
    private Integer orderBuyLimit;

    private Price price;

    @JSONField(name = "origin_price")
    private Price originPrice;

    private List<CommodityDisplay> displays;

    @JSONField(name = "product_name")
    private String productName;

    @JSONField(name = "product_url_key")
    private String productUrlKey;

    @JSONField(name = "product_description")
    private String productDescription;

    @JSONField(name = "bind_services")
    private List<CommodityVO> bindServices;

    @JSONField(name = "statistics_data")
    Map<OrderState, Integer> statisticsData;

    @JSONField(name = "delivery_time_text")
    private String deliveryTimeText;

    @JSONField(name = "delivery_time_config")
    private CommodityDeliveryTimeConfig deliveryTimeConfig;

    @JSONField(name = "differences")
    private List<CommodityDifference> differences;

    @JSONField(name = "product_id")
    private Integer productId;

    @JSONField(name = "commodity_tag_infos")
    private List<CommodityTagInfoVO> commodityTagInfos;

    @JSONField(name = "save_price")
    private Price savePrice;

    /**
     * 套餐属性
     *
     * @see com.insta360.store.business.admin.order.enums.CommodityAttributeType
     */
    private Integer commodityAttribute;

    /**
     * 产品内部名称
     */
    private String productInnerName;

    /**
     * 套餐的增值服务绑定信息
     */
    private List<CommodityBindClimbServiceVO> bindClimbServices;

    /**
     * 增值服务类型
     */
    private String serviceType;

    public CommodityVO() {
    }

    public CommodityVO(Commodity commodity) {
        if (commodity != null) {
            BeanUtil.copyProperties(commodity, this);
        }
    }

    /**
     * 套餐数据返回构建
     *
     * @param commodities
     * @return
     */
    public List<CommodityVO> builds(Collection<Commodity> commodities) {
        return commodities.stream().map(CommodityVO::new).collect(Collectors.toList());
    }

    public CommodityInfo getInfo() {
        return info;
    }

    public void setInfo(CommodityInfo info) {
        this.info = info;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getSaleState() {
        return saleState;
    }

    public void setSaleState(Integer saleState) {
        this.saleState = saleState;
    }

    public Price getPrice() {
        return price;
    }

    public void setPrice(Price price) {
        this.price = price;
    }

    public Price getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Price originPrice) {
        this.originPrice = originPrice;
    }

    public List<CommodityDisplay> getDisplays() {
        return displays;
    }

    public void setDisplays(List<CommodityDisplay> displays) {
        this.displays = displays;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductUrlKey() {
        return productUrlKey;
    }

    public void setProductUrlKey(String productUrlKey) {
        this.productUrlKey = productUrlKey;
    }

    public Integer getOrderBuyLimit() {
        return orderBuyLimit;
    }

    public void setOrderBuyLimit(Integer orderBuyLimit) {
        this.orderBuyLimit = orderBuyLimit;
    }

    public List<CommodityVO> getBindServices() {
        return bindServices;
    }

    public void setBindServices(List<CommodityVO> bindServices) {
        this.bindServices = bindServices;
    }

    public Map<OrderState, Integer> getStatisticsData() {
        return statisticsData;
    }

    public void setStatisticsData(Map<OrderState, Integer> statisticsData) {
        this.statisticsData = statisticsData;
    }

    public String getDeliveryTimeText() {
        return deliveryTimeText;
    }

    public void setDeliveryTimeText(String deliveryTimeText) {
        this.deliveryTimeText = deliveryTimeText;
    }

    public CommodityDeliveryTimeConfig getDeliveryTimeConfig() {
        return deliveryTimeConfig;
    }

    public void setDeliveryTimeConfig(CommodityDeliveryTimeConfig deliveryTimeConfig) {
        this.deliveryTimeConfig = deliveryTimeConfig;
    }

    public String getProductDescription() {
        return productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    public List<CommodityDifference> getDifferences() {
        return differences;
    }

    public void setDifferences(List<CommodityDifference> differences) {
        this.differences = differences;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<CommodityTagInfoVO> getCommodityTagInfos() {
        return commodityTagInfos;
    }

    public void setCommodityTagInfos(List<CommodityTagInfoVO> commodityTagInfos) {
        this.commodityTagInfos = commodityTagInfos;
    }

    public Price getSavePrice() {
        return savePrice;
    }

    public void setSavePrice(Price savePrice) {
        this.savePrice = savePrice;
    }

    public Integer getCommodityAttribute() {
        return commodityAttribute;
    }

    public void setCommodityAttribute(Integer commodityAttribute) {
        this.commodityAttribute = commodityAttribute;
    }

    public String getProductInnerName() {
        return productInnerName;
    }

    public void setProductInnerName(String productInnerName) {
        this.productInnerName = productInnerName;
    }

    public List<CommodityBindClimbServiceVO> getBindClimbServices() {
        return bindClimbServices;
    }

    public void setBindClimbServices(List<CommodityBindClimbServiceVO> bindClimbServices) {
        this.bindClimbServices = bindClimbServices;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    @Override
    public String toString() {
        return "CommodityVO{" +
                "info=" + info +
                ", stock=" + stock +
                ", saleState=" + saleState +
                ", orderBuyLimit=" + orderBuyLimit +
                ", price=" + price +
                ", originPrice=" + originPrice +
                ", displays=" + displays +
                ", productName='" + productName + '\'' +
                ", productUrlKey='" + productUrlKey + '\'' +
                ", productDescription='" + productDescription + '\'' +
                ", bindServices=" + bindServices +
                ", statisticsData=" + statisticsData +
                ", deliveryTimeText='" + deliveryTimeText + '\'' +
                ", deliveryTimeConfig=" + deliveryTimeConfig +
                ", differences=" + differences +
                ", productId=" + productId +
                ", commodityTagInfos=" + commodityTagInfos +
                ", savePrice=" + savePrice +
                ", commodityAttribute=" + commodityAttribute +
                ", productInnerName='" + productInnerName + '\'' +
                ", bindClimbServices=" + bindClimbServices +
                ", serviceType='" + serviceType + '\'' +
                '}';
    }
}
