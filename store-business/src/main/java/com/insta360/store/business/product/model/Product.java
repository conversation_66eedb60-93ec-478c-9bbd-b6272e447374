package com.insta360.store.business.product.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.product.enums.ProductCategoryFinalType;
import com.insta360.store.business.product.enums.ProductCategoryMainType;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
public class Product extends BaseModel<Product> {

    private static final long serialVersionUID = 1L;

    public static final int ONE_ID = 4;

    public static final int ONEX_ID = 7;

    public static final int PRO2_ID = 6;

    public static final int EVO_ID = 9;

    public static final int TITAN_ID = 10;

    /**
     * x4
     */
    public static final int X4_ID = 26;

    /**
     * x3
     */
    public static final int X3_ID = 19;

    /**
     * x3 x4
     */
    public static final List<Integer> X3X4_LIST = Arrays.asList(X4_ID, X3_ID);

    /**
     * 云服务产品ID
     */
    public static final Integer CLOUD_ID = 572;

    public static final int INSTA_COURSE_ID = 190;

    /**
     * 8K直播
     */
    public static final int EIGHT_LIVE = 258;

    /**
     * care
     */
    public static final int CARE_ID = 141;

    /**
     * 延保
     */
    public static final int EXTEND_INSURANCE_ID = 327;

    /**
     * care+ ace的相关id
     */
    public static final int ACE_CARE_PLUS_ID = 521;

    /**
     * care+ x4的相关id
     */
    public static final int X4_CARE_PLUS_ID = 523;

    /**
     * care+ go3S的相关id
     */
    public static final int GO3S_CARE_PLUS_ID = 549;

    /**
     * care+ link2/link2c的相关id
     */
    public static final int link2_CARE_PLUS_ID = 586;

    /**
     * care+ ace pro 2
     */
    public static final int IAC3_CARE_PLUS_ID = 590;

    /**
     * care+ flow2pro
     */
    public static final int FLOW2PRO_CARE_PLUS_ID = 636;

    /**
     * x5 care+
     */
    public static final Integer A3_CARE_PLUS_ID = 615;

    /**
     * 海外房产套餐单独页面
     */
    public static final int VIRTUAL_TOUR_KIT_ID = 21;

    /**
     * 中国大陆VR看房-ONE X2
     */
    public static final int REAL_ESTATE_KIT_ID = 225;

    /**
     * 中国大陆VR看房-ONE R
     */
    public static final int ONER_VIRTUAL_TOUR_KIT = 267;

    /**
     * 重复下单 排除此类产品的单
     */
    public static final List<Integer> REPEAT_ORDER_EXCLUDE_PRODUCT = Arrays.asList(VIRTUAL_TOUR_KIT_ID, REAL_ESTATE_KIT_ID, ONER_VIRTUAL_TOUR_KIT);

    /**
     * flexiCare的产品id
     */
    public static final List<Integer> CARE_PLUS_SERVICE_PRODUCT = Arrays.asList(A3_CARE_PLUS_ID, ACE_CARE_PLUS_ID, X4_CARE_PLUS_ID, GO3S_CARE_PLUS_ID, link2_CARE_PLUS_ID, IAC3_CARE_PLUS_ID, FLOW2PRO_CARE_PLUS_ID);

    /**
     * 保险服务
     */
    public static final List<Integer> INSURANCE_SERVICE_PRODUCT = Arrays.asList(CARE_ID, EXTEND_INSURANCE_ID, ACE_CARE_PLUS_ID, X4_CARE_PLUS_ID, GO3S_CARE_PLUS_ID, link2_CARE_PLUS_ID, IAC3_CARE_PLUS_ID, FLOW2PRO_CARE_PLUS_ID, A3_CARE_PLUS_ID);

    /**
     * T恤产品id
     */
    public static final int T_XU_ID = 592;

    public static List<Integer> ONE_SERIES = Arrays.asList(ONE_ID, ONEX_ID);

    public static List<Integer> EVO_SERIES = Arrays.asList(EVO_ID);

    /**
     * 免邮产品 571 Insta360 Connect，临时添加，其余需求请不要使用这个字段
     */
    public static Integer FREE_PRODUCT = 571;

    /**
     * 云服务产品ID
     */
    public static final Integer cloudProductId = 572;

    /**
     * PSP云服务产品ID
     */
    public static final Integer PSP_CLOUD_ID = 687;

    /**
     * x5
     */
    public static final Integer X5_ID = 33;

    /**
     * Flow2Pro
     */
    public static final Integer FLOW2PRO_ID = 32;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "`name`")
    private String name;

    @JSONField(name = "url_key")
    private String urlKey;

    @TableField(value = "`key`")
    private String key;

    private Boolean enabled;

    private String accessories;

    @JSONField(name = "accessories_commodity")
    private String accessoriesCommodity;

    /**
     * 判断是否相机
     */
    @JSONField(name = "is_camera")
    private Boolean isCamera;

    /**
     * type 用于区分产品类型
     */
    @JSONField(name = "is_repair_service")
    private Boolean isRepairService;

    /**
     * 产品类型(类目上线后不再使用)
     *
     * @see com.insta360.store.business.product.enums.ProductCategoryMainType
     * @see com.insta360.store.business.product.enums.ProductCategoryFinalType
     */
    private Integer type;

    /**
     * 产品类目key
     */
    private String categoryKey;

    /**
     * 税率>>固定值：0，0.01，0.015，0.03，0.04，0.05，0.06，0.09，0.10，0.11，0.13，0.16，0.17
     */
    private String taxRate;

    /**
     * 是否是新品（0：不是；1：是）
     */
    private Boolean newProduct;

    /**
     * 税号绑定id
     */
    private Integer taxCodeInfoId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 旧的urlKey (用于商城接口参数缓存同步用，不持久化)
     */
    @TableField(exist = false)
    private String oldUrlKey;

    /**
     * 佣金标记（不持久化）
     */
    @TableField(exist = false)
    private Boolean commissionTag;

    public String getOldUrlKey() {
        return oldUrlKey;
    }

    public void setOldUrlKey(String oldUrlKey) {
        this.oldUrlKey = oldUrlKey;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrlKey() {
        return urlKey;
    }

    public void setUrlKey(String urlKey) {
        this.urlKey = urlKey;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getAccessories() {
        return accessories;
    }

    public void setAccessories(String accessories) {
        this.accessories = accessories;
    }

    public String getAccessoriesCommodity() {
        return accessoriesCommodity;
    }

    public void setAccessoriesCommodity(String accessoriesCommodity) {
        this.accessoriesCommodity = accessoriesCommodity;
    }

    public Boolean getIsCamera() {
        return isCamera;
    }

    public void setIsCamera(Boolean isCamera) {
        this.isCamera = isCamera;
    }

    public Boolean getIsRepairService() {
        return isRepairService;
    }

    public void setIsRepairService(Boolean repairService) {
        isRepairService = repairService;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCategoryKey() {
        return categoryKey;
    }

    public void setCategoryKey(String categoryKey) {
        this.categoryKey = categoryKey;
    }

    public Boolean getNewProduct() {
        return newProduct;
    }

    public void setNewProduct(Boolean newProduct) {
        this.newProduct = newProduct;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getTaxCodeInfoId() {
        return taxCodeInfoId;
    }

    public void setTaxCodeInfoId(Integer taxCodeInfoId) {
        this.taxCodeInfoId = taxCodeInfoId;
    }

    public Boolean getCommissionTag() {
        return commissionTag;
    }

    public void setCommissionTag(Boolean commissionTag) {
        this.commissionTag = commissionTag;
    }

    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", urlKey='" + urlKey + '\'' +
                ", key='" + key + '\'' +
                ", enabled=" + enabled +
                ", accessories='" + accessories + '\'' +
                ", accessoriesCommodity='" + accessoriesCommodity + '\'' +
                ", isCamera=" + isCamera +
                ", isRepairService=" + isRepairService +
                ", type=" + type +
                ", categoryKey='" + categoryKey + '\'' +
                ", taxRate='" + taxRate + '\'' +
                ", newProduct=" + newProduct +
                ", taxCodeInfoId=" + taxCodeInfoId +
                ", createTime=" + createTime +
                ", oldUrlKey='" + oldUrlKey + '\'' +
                '}';
    }

    /**
     * 产品链接
     *
     * @return
     */
    public String productLink() {
        return "/product/" + urlKey;
    }

    /**
     * 是否相机
     *
     * @return
     */
    public boolean whetherCamera() {
        return this.isCamera;
    }

    /**
     * 是维修服务
     *
     * @return boolean
     */
    public boolean whetherRepairServiceProduct() {
        ProductCategoryMainType categoryMainType = ProductCategoryMainType.parse(this.getCategoryKey());
        return ProductCategoryMainType.CM_REPAIR_SERVICE.equals(categoryMainType);
    }

    /**
     * 是否云服务商品
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isCloudSubscribeItem() {
        return ProductCategoryFinalType.CF_CLOUD_SERVICE.name().equals(this.categoryKey);
    }

    /**
     * 是否psp商品
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isPspItem() {
        return ProductCategoryFinalType.CF_PSP_SERVICE.name().equals(this.categoryKey);
    }

    /**
     * 是否云服务产品
     *
     * @return
     */
    public boolean whetherCould() {
        return CLOUD_ID.equals(this.id);
    }
}
