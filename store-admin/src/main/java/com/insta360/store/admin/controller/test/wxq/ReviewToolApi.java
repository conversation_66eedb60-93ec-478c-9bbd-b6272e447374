package com.insta360.store.admin.controller.test.wxq;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.test.wxq.dto.ToolReviewDTO;
import com.insta360.store.business.review.model.Review;
import com.insta360.store.business.review.model.ReviewEmailSendRecord;
import com.insta360.store.business.review.model.ReviewResource;
import com.insta360.store.business.review.service.ReviewEmailSendRecordService;
import com.insta360.store.business.review.service.ReviewResourceService;
import com.insta360.store.business.review.service.ReviewService;
import com.insta360.store.business.review.service.ReviewStateRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * tool/评论资源
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6
 */
@RestController
public class ReviewToolApi extends BaseAdminApi {

    @Autowired
    ReviewService reviewService;

    @Autowired
    ReviewResourceService reviewResourceService;

    @Autowired
    ReviewStateRecordService reviewStateRecordService;

    @Autowired
    ReviewEmailSendRecordService reviewEmailSendRecordService;

    /**
     * 查询评论资源
     *
     * @param toolReviewDTO 工具评论DTO
     * @return {@link Response}<{@link Object}>
     */
    @PostMapping(value = "/admin/test/wxq/review/getReviewResources")
    public Response<Object> getReviewResources(@RequestBody @Validated ToolReviewDTO toolReviewDTO) {
        Review review = reviewService.getByOrderNumber(toolReviewDTO.getOrderNumber());
        if (review == null) {
            return Response.failed("订单评论不存在");
        }
        List<ReviewResource> reviewResources = reviewResourceService.listResourceByReviewId(review.getId());
        return Response.ok(reviewResources);
    }

    /**
     * 批量更新评论资源
     *
     * @param reviewResources 评论资源
     * @return {@link Response}<{@link Object}>
     */
    @PostMapping(value = "/admin/test/wxq/review/updateReviewResources")
    public Response<Object> updateReviewResources(@RequestBody @Validated List<ReviewResource> reviewResources) {
        reviewResourceService.updateBatchById(reviewResources);
        return Response.ok(reviewResources);
    }

    /**
     * 更新评论资源
     *
     * @param reviewResource 评论资源
     * @return {@link Response}<{@link Object}>
     */
    @PostMapping(value = "/admin/test/wxq/review/updateReviewResource")
    public Response<Object> updateReviewResource(@RequestBody ReviewResource reviewResource) {
        reviewResourceService.updateById(reviewResource);
        return Response.ok();
    }

    /**
     * 删除评论
     *
     * @param toolReviewDTO 删除评论DTO
     * @return {@link Response}<{@link Object}>
     */
    @PostMapping(value = "/admin/test/wxq/review/delReview")
    @Transactional
    public Response<Object> delReview(@RequestBody @Validated ToolReviewDTO toolReviewDTO) {
        if (StringUtils.isNotBlank(toolReviewDTO.getEmail())) {
            ReviewEmailSendRecord reviewEmailSendRecord = reviewEmailSendRecordService.getReviewEmailSendRecord(toolReviewDTO.getOrderNumber(), toolReviewDTO.getEmail());
            if (reviewEmailSendRecord != null) {
                reviewEmailSendRecordService.removeById(reviewEmailSendRecord.getId());
            }
        }
        Review review = reviewService.getByOrderNumber(toolReviewDTO.getOrderNumber());
        if (review == null) {
            return Response.failed("订单评论不存在");
        }
        reviewService.removeById(review.getId());

        List<ReviewResource> reviewResources = reviewResourceService.listResourceByReviewId(review.getId());
        if (CollectionUtils.isNotEmpty(reviewResources)) {
            List<Integer> reviewResourceIds = reviewResources.stream().map(ReviewResource::getId).collect(Collectors.toList());
            reviewResourceService.removeByIds(reviewResourceIds);
        }

        return Response.ok("数据已经删除");
    }

}
