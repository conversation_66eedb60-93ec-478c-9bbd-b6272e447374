package com.insta360.store.business.review.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.review.bo.ReviewQueryBO;
import com.insta360.store.business.review.bo.ReviewSubmitBO;
import com.insta360.store.business.review.model.Review;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-04
 * @Description:
 */
public interface ReviewService extends BaseService<Review> {

    /**
     * 根据订单编号获取评论
     * @param orderNumber
     * @return
     */
    Review getByOrderNumber(String orderNumber);

    /**
     * 分页获取某个产品
     *
     * @param productId
     * @param rate
     * @param sortKey
     * @param pageQuery
     * @return
     */
    PageResult<Review> listReviews(Integer productId, Integer rate, String sortKey, PageQuery pageQuery);

    /**
     * 获取某段时间的评论总数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<Review> listByBetweenCreateTime(String startTime, String endTime);

    /**
     * 多条件查询评论信息
     *
     * @param reviewQueryParam 评论查询参数
     * @param iPage            分页
     * @return {@link Page}<{@link Review}>
     */
    Page<Review> pageQueryReviews(ReviewQueryBO reviewQueryParam, IPage<Review> iPage);

    /**
     * 评论提交
     *
     * @param reviewSubmitBo 评论提交BO
     */
    void reviewSubmit(ReviewSubmitBO reviewSubmitBo);

    /**
     * 根据订单子项和邮箱获取评论记录
     *
     * @param orderItemId
     * @return
     */
    Review getByOrderItemId(Integer orderItemId);

    /**
     * 评论点赞
     *
     * @param reviewId
     */
    @Deprecated
    void like(Integer reviewId);

    /**
     * 评论取消点赞
     *
     * @param reviewId
     */
    @Deprecated
    void dislike(Integer reviewId);

    /**
     * 设置为待审核
     *
     * @param review
     */
    void setUnhandled(Review review);

    /**
     * 设置为已发布
     *
     * @param review
     * @param reviewResourceIds
     */
    void setReleased(Review review, List<Integer> reviewResourceIds);

    /**
     * 设置为已拒绝
     *
     * @param review
     */
    void setRejected(Review review);

    /**
     * 设置评论备注
     *
     * @param review
     * @param reviewRemark
     */
    void setReviewRemark(Review review, String reviewRemark);

    /**
     * 设置评论回复
     *
     * @param review
     * @param reviewReply
     */
    void setReviewReply(Review review, String reviewReply);

    /**
     * 设置评论置顶
     *
     * @param review
     */
    void topTag(Review review);

    /**
     * 设置评论取消置顶
     *
     * @param review
     */
    void topTagCancel(Review review);


    /**
     * 根据评论编号获取
     *
     * @param reviewNumber 评论编号
     * @return {@link Review}
     */
    Review getByReviewNumber(String reviewNumber);

    /**
     * 批次更新评论由id
     *
     * @param reviewList 评论集合
     */
    void batchUpdateReviewById(List<Review> reviewList);
}
