package com.insta360.store.business.insurance.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.admin.order.service.impl.helper.ExcelDataExportHelper;
import com.insta360.store.business.insurance.aop.CardRecordContext;
import com.insta360.store.business.insurance.aop.CareCardContext;
import com.insta360.store.business.insurance.bo.CareActivationCardBO;
import com.insta360.store.business.insurance.dto.CareCardDTO;
import com.insta360.store.business.insurance.enums.CareCardType;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.insurance.export.ActivationCardExportData;
import com.insta360.store.business.insurance.model.CareActivationCard;
import com.insta360.store.business.insurance.model.CareCardDeviceChangeRecord;
import com.insta360.store.business.insurance.model.CareCardDeviceType;
import com.insta360.store.business.insurance.model.CareInsuranceActivationCard;
import com.insta360.store.business.insurance.service.CareActivationCardService;
import com.insta360.store.business.insurance.service.CareCardDeviceChangeRecordService;
import com.insta360.store.business.insurance.service.CareCardDeviceTypeService;
import com.insta360.store.business.insurance.service.CareInsuranceActivationCardService;
import com.insta360.store.business.insurance.service.impl.helper.card.ActivationCardCodeBuilder;
import com.insta360.store.business.insurance.service.impl.helper.card.ActivationCardCodeIdentifierRegulation;
import com.insta360.store.business.integration.sap.configuration.SapCommonConfiguration;
import com.insta360.store.business.integration.sap.lib.request.SapAuthorizationRequest;
import com.insta360.store.business.integration.sap.lib.request.SapWordNumberRequest;
import com.insta360.store.business.integration.sap.lib.response.SapAuthorizationResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @description: 实体卡小助手
 * @author: py
 * @create: 2023-04-26 16:06
 */
@Component
public class CareCardHelper {

    protected static final Logger LOGGER = LoggerFactory.getLogger(CareCardHelper.class);

    /**
     * 字符串只允许英文（大小写）、数字、回车
     */
    private static final Pattern VALID_PATTERN = Pattern.compile("^[a-zA-Z0-9\\r]*$");

    /**
     * 实体卡批量查询限制卡号列表最大长度
     */
    private static final Integer MAX_CARD_NUMBERS_LIMIT = 150;

    @Autowired
    CareActivationCardService careActivationCardService;

    @Autowired
    CareCardDeviceChangeRecordService careCardDeviceChangeRecordService;

    @Autowired
    CareInsuranceActivationCardService careInsuranceActivationCardService;

    @Autowired
    CareCardDeviceTypeService careCardDeviceTypeService;

    @Autowired
    ExcelDataExportHelper excelDataExportHelper;

    @Autowired
    SapCommonConfiguration sapCommonConfiguration;

    /**
     * 实体卡批量绑定相机类型
     *
     * @param careCardParam
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchBindDeviceType(CareCardDTO careCardParam) {
        List<String> cardNumberList = careCardParam.getCardNumberList();
        List<CareActivationCard> careActivationCardList = careActivationCardService.listByCareNumber(cardNumberList);

        // 已绑定过的实体卡
        List<CareActivationCard> bindCards = careActivationCardList.stream().filter(CareActivationCard::getIsBind).collect(Collectors.toList());
        Integer override = careCardParam.getOverride();

        // 合法性校验
        legitimacyCheck(cardNumberList, careActivationCardList, bindCards, override);

        // 已绑过工单号的实体卡
        List<String> bindWorkNumberCardNumberList = careActivationCardList.stream().filter(careActivationCard -> !"".equals(careActivationCard.getWorkNumber())).map(CareActivationCard::getCardNumber).collect(Collectors.toList());

        String workNumber = careCardParam.getWorkNumber();
        Integer deviceTypeId = careCardParam.getDeviceTypeId();
        CareCardDeviceType careCardDeviceType = careCardDeviceTypeService.getById(deviceTypeId);
        String deviceType = careCardDeviceType.getDeviceType();

        List<CareCardDeviceChangeRecord> careCardDeviceChangeRecordList = new ArrayList<>(careActivationCardList.size());
        // 数据封装
        dataEncapsulation(careActivationCardList, deviceTypeId, workNumber, deviceType, careCardDeviceChangeRecordList, bindWorkNumberCardNumberList);

        // sava or update
        careActivationCardService.updateBatchById(careActivationCardList);
        careCardDeviceChangeRecordService.saveBatch(careCardDeviceChangeRecordList);

        // 记录必要信息
        CardRecordContext cardRecordContext = CardRecordContext.get();
        if (cardRecordContext != null) {
            List<Integer> recordIdList = careCardDeviceChangeRecordList.stream().map(CareCardDeviceChangeRecord::getId).collect(Collectors.toList());
            cardRecordContext.setRecordIdList(recordIdList);
        }
    }

    /**
     * 已绑定工单号数据
     *
     * @param careActivationCardList
     * @param deviceTypeId
     * @param workNumber
     * @param deviceType
     * @param careCardDeviceChangeRecordList
     * @param bindWorkNumberCardNumberList
     */
    private void dataEncapsulation(List<CareActivationCard> careActivationCardList, Integer deviceTypeId, String workNumber, String deviceType, List<CareCardDeviceChangeRecord> careCardDeviceChangeRecordList, List<String> bindWorkNumberCardNumberList) {
        if (CollectionUtils.isEmpty(careActivationCardList)) {
            return;
        }
        careActivationCardList.forEach(careCard -> {
            careCard.setWorkNumber(workNumber);
            careCard.setDeviceType(deviceType);
            careCard.setDeviceTypeId(deviceTypeId);
            careCard.setIsBind(true);
            careCard.setBindTime(LocalDateTime.now());
            careCard.setUpdateTime(LocalDateTime.now());

            // record
            CareCardDeviceChangeRecord careCardDeviceChangeRecord = new CareCardDeviceChangeRecord();
            careCardDeviceChangeRecord.setCardId(careCard.getId());
            careCardDeviceChangeRecord.setDeviceType(deviceType);
            careCardDeviceChangeRecord.setCreateTime(LocalDateTime.now());
            careCardDeviceChangeRecord.setUpdateTime(LocalDateTime.now());
            careCardDeviceChangeRecord.setDeviceTypeId(deviceTypeId);
            careCardDeviceChangeRecord.setWorkNumber(workNumber);
            careCardDeviceChangeRecordList.add(careCardDeviceChangeRecord);

            // 工单号置空
            if (bindWorkNumberCardNumberList.contains(careCard.getCardNumber()) && workNumber == null) {
                careCard.setWorkNumber("");
                careCardDeviceChangeRecord.setWorkNumber("置空");
            }
        });
    }

    /**
     * 合法性校验
     *
     * @param cardNumberList
     * @param careActivationCardList
     * @param bindCards
     * @param override
     */
    private void legitimacyCheck(List<String> cardNumberList, List<CareActivationCard> careActivationCardList, List<CareActivationCard> bindCards, Integer override) {
        // 编码规则校验
        List<String> illegalCardNumberList = cardNumberList.stream().map(cardNumber -> {
            Pattern patternFir = Pattern.compile(ActivationCardCodeIdentifierRegulation.REG_EXP_FIR);
            Pattern patternSec = Pattern.compile(ActivationCardCodeIdentifierRegulation.REG_EXP_SEC);
            // 两个都不匹配
            if (!patternFir.matcher(cardNumber).matches() && !patternSec.matcher(cardNumber).matches()) {
                return cardNumber;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(illegalCardNumberList)) {
            InstaException instaException = new InstaException(InsuranceErrorCode.CardNumberNotSatisfyRegulationException);
            instaException.putErrorData("cardNumberList", illegalCardNumberList);
            throw instaException;
        }

        // 卡号存在性校验
        List<String> dbCardNumberList = careActivationCardList.stream().map(CareActivationCard::getCardNumber).collect(Collectors.toList());
        cardNumberList.removeAll(dbCardNumberList);
        if (CollectionUtils.isNotEmpty(cardNumberList)) {
            InstaException instaException = new InstaException(InsuranceErrorCode.CardNumberNotExistException);
            instaException.putErrorData("cardNumberList", cardNumberList);
            throw instaException;
        }

        // 卡号激活校验
        Map<String, String> cardMap = careActivationCardList.stream().collect(Collectors.toMap(CareActivationCard::getActivationCode, CareActivationCard::getCardNumber));
        List<CareInsuranceActivationCard> dbActivationCareCards = careInsuranceActivationCardService.listByActivationCode(new ArrayList<>(cardMap.keySet()));
        if (CollectionUtils.isNotEmpty(dbActivationCareCards)) {
            List<String> activationCodes = dbActivationCareCards.stream().map(CareInsuranceActivationCard::getActivationCode).collect(Collectors.toList());
            List<String> resCardList = activationCodes.stream().map(cardMap::get).collect(Collectors.toList());
            InstaException instaException = new InstaException(InsuranceErrorCode.CardNumberIsActivationException);
            instaException.putErrorData("cardNumberList", resCardList);
            throw instaException;
        }

        // 卡号已绑定校验
        if (0 == override && CollectionUtils.isNotEmpty(bindCards)) {
            InstaException instaException = new InstaException(InsuranceErrorCode.CardNumberBindDeviceException);
            Map<String, String> resMap = bindCards.stream().collect(Collectors.toMap(CareActivationCard::getCardNumber, CareActivationCard::getDeviceType));
            instaException.putErrorData("cardNumberList", resMap);
            throw instaException;
        }
    }

    /**
     * 实体卡复杂查询
     *
     * @param careCardParam
     * @param pageQuery
     * @return
     */
    public PageResult<CareActivationCardBO> query(CareCardDTO careCardParam, PageQuery pageQuery) {
        PageResult<CareActivationCard> careActivationCardPageResult = careActivationCardService.query(careCardParam, pageQuery);
        List<CareActivationCard> cardPageResultList = careActivationCardPageResult.getList();

        List<Integer> cardIdList = cardPageResultList.stream().map(CareActivationCard::getId).collect(Collectors.toList());

        List<CareActivationCardBO> careActivationCardBOList = queryFromCardIdsAndCareActivationCards(cardIdList, cardPageResultList);

        return careActivationCardPageResult.replaceList(careActivationCardBOList);
    }

    /**
     * 实体卡批量查询
     *
     * @param cardNumbers
     * @return
     */
    public List<CareActivationCardBO> batchQuery(List<String> cardNumbers) {
        // 入参列表去重
        Set<String> cardNumberSet = new HashSet<>(cardNumbers);
        cardNumbers = new ArrayList<>(cardNumberSet);

        // 长度合法性校验
        if (cardNumbers.size() > MAX_CARD_NUMBERS_LIMIT) {
            LOGGER.error("the length of the cardNumbers exceeds the limit. cardNumber size is {}", cardNumbers.size());
            throw new InstaException(InsuranceErrorCode.CardNumbersExceedLimitException);
        }

        // 字符合法性
        if (!areAllStringsValid(cardNumbers)) {
            LOGGER.error("the cardNumbers contains invalid characters.");
            throw new InstaException(InsuranceErrorCode.CardNUmberHasInvalidCharactersException);
        }

        List<CareActivationCard> careActivationCards = careActivationCardService.batchQueryByCardNumbers(cardNumbers);

        // 收集查出来的cardNumbers
        List<String> cardNumbersFromDb = careActivationCards.stream().map(CareActivationCard::getCardNumber).collect(Collectors.toList());
        if (cardNumbersFromDb.size() != cardNumbers.size()) {
            // 卡号合法性校验
            ArrayList<String> intersection = new ArrayList<>(cardNumbersFromDb);
            intersection.retainAll(cardNumbers);
            List<String> difference = new ArrayList<>(cardNumbers);
            difference.removeAll(intersection);
            InstaException instaException = new InstaException(InsuranceErrorCode.CardNumberNotExistException);
            instaException.putErrorData("notExitCarNumbers", difference);
            LOGGER.error("card number does not exist");
            throw instaException;
        }

        List<Integer> cardIdList = careActivationCards.stream().map(CareActivationCard::getId).collect(Collectors.toList());

        return queryFromCardIdsAndCareActivationCards(cardIdList, careActivationCards);
    }

    /**
     * 正则匹配非法字符
     *
     * @param strings
     * @return
     */
    private boolean areAllStringsValid(List<String> strings) {
        for (String str : strings) {
            if (!VALID_PATTERN.matcher(str).matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据cardId列表查询绑定信息、物料信息和激活信息
     *
     * @param cardIdList
     * @param careActivationCards
     * @return
     */
    private List<CareActivationCardBO> queryFromCardIdsAndCareActivationCards(List<Integer> cardIdList, List<CareActivationCard> careActivationCards) {
        // 历史绑定记录
        List<CareCardDeviceChangeRecord> careCardDeviceChangeRecordList = careCardDeviceChangeRecordService.listByCardId(cardIdList);
        Map<Integer, List<CareCardDeviceChangeRecord>> recordMap = careCardDeviceChangeRecordList.stream().collect(Collectors.groupingBy(CareCardDeviceChangeRecord::getCardId));

        // 激活信息
        List<String> activationCodeList = careActivationCards.stream().map(CareActivationCard::getActivationCode).collect(Collectors.toList());
        List<CareInsuranceActivationCard> careInsuranceActivationCardList = careInsuranceActivationCardService.listByActivationCode(activationCodeList);
        Map<String, CareInsuranceActivationCard> cardMap = careInsuranceActivationCardList.stream().collect(Collectors.toMap(CareInsuranceActivationCard::getActivationCode, o -> o));

        // 物料信息
        Map<Integer, CareCardDeviceType> careCardDeviceTypeMap = careCardDeviceTypeService.listDevice().stream().collect(Collectors.toMap(CareCardDeviceType::getId, o -> o));

        List<CareActivationCardBO> careActivationCardBOList = careActivationCards.stream().map(careCard -> {
            CareActivationCardBO careActivationCardBo = new CareActivationCardBO(careCard);

            // 历史记录绑定
            bindRecords(careActivationCardBo, recordMap, careCard);

            // 物料名称
            bindBomName(careCard, careActivationCardBo, careCardDeviceTypeMap);

            // 激活信息
            bindActivationInfo(cardMap, careCard, careActivationCardBo);
            return careActivationCardBo;
        }).collect(Collectors.toList());
        return careActivationCardBOList;
    }

    /**
     * 绑定激活信息
     *
     * @param cardMap
     * @param careCard
     * @param careActivationCardBO
     */
    private void bindActivationInfo(Map<String, CareInsuranceActivationCard> cardMap, CareActivationCard careCard, CareActivationCardBO careActivationCardBO) {
        CareInsuranceActivationCard careInsuranceActivationCard = cardMap.get(careCard.getActivationCode());
        if (careInsuranceActivationCard == null) {
            return;
        }
        careActivationCardBO.setActivationTime(careInsuranceActivationCard.getBindTime());
        careActivationCardBO.setExpireTime(careInsuranceActivationCard.getExpireTime());
        careActivationCardBO.setDeviceSerial(careInsuranceActivationCard.getDeviceSerial());
    }

    /**
     * 绑定物料名称
     *
     * @param careCard
     * @param careActivationCardBO
     * @param careCardDeviceTypeMap
     */
    private void bindBomName(CareActivationCard careCard, CareActivationCardBO careActivationCardBO, Map<Integer, CareCardDeviceType> careCardDeviceTypeMap) {
        Integer deviceTypeId = careCard.getDeviceTypeId();
        if (deviceTypeId == null) {
            return;
        }
        CareCardDeviceType careCardDeviceType = careCardDeviceTypeMap.get(deviceTypeId);
        careActivationCardBO.setMpnCode(careCardDeviceType.getMpnCode());
        careActivationCardBO.setBomName(careCardDeviceType.getBomName());
    }

    /**
     * 历史机型和工单号记录的绑定
     *
     * @param careActivationCardBO
     * @param recordMap
     * @param careCard
     */
    private void bindRecords(CareActivationCardBO careActivationCardBO, Map<Integer, List<CareCardDeviceChangeRecord>> recordMap, CareActivationCard careCard) {
        List<CareCardDeviceChangeRecord> records = recordMap.get(careCard.getId());
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        // 历史绑定机型
        List<String> deviceLogList = records.stream().map(CareCardDeviceChangeRecord::getDeviceType).collect(Collectors.toList());
        careActivationCardBO.setDeviceLogList(deviceLogList);

        // 历史工单号
        List<String> workNumberLogList = records.stream().map(CareCardDeviceChangeRecord::getWorkNumber).filter(StringUtil::isNotBlank).collect(Collectors.toList());
        careActivationCardBO.setWorkNumberLogList(workNumberLogList);
    }

    /**
     * 批量生成实体卡
     *
     * @param companyCode
     * @param cardVersion
     * @param number
     * @return
     */
    private List<CareActivationCard> createActivationCard(String companyCode, String cardVersion, Integer number) {
        ActivationCardCodeBuilder builder = new ActivationCardCodeBuilder();
        builder.setCompanyCode(companyCode);
        builder.setCardVersion(cardVersion);
        builder.setNumber(number);
        List<CareActivationCard> activationCards = new ArrayList<>(number);
        for (int i = 1; i <= number; i++) {
            // 卡号
            String cardNumber;
            CareActivationCard careActivationCard;
            do {
                cardNumber = builder.buildCardNumber();
                careActivationCard = careActivationCardService.getByCardNumber(cardNumber);
            } while (careActivationCard != null);

            // 激活码
            CareActivationCard activationCard;
            String activationCode;
            do {
                activationCode = builder.buildCardCode();
                activationCard = careActivationCardService.getByActivationCodeDisabled(activationCode);
            } while (activationCard != null);

            activationCard = new CareActivationCard();
            activationCard.setCardNumber(cardNumber);
            activationCard.setActivationCode(activationCode);
            activationCard.setCompanyCode(companyCode);
            activationCard.setCardVersion(cardVersion);
            activationCard.setCreateTime(LocalDateTime.now());
            activationCards.add(activationCard);
        }
        return activationCards;
    }

    /**
     * 批量生成实体卡并返回oss链接
     *
     * @param careCardParam
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public String createActivationCard(CareCardDTO careCardParam) throws Exception {
        Integer number = careCardParam.getNumber();
        Boolean isTest = careCardParam.getIsTest();
        String companyCode = careCardParam.getCompanyCode();
        String cardVersion = careCardParam.getCardVersion();
        String cardType = careCardParam.getCardType();

        // 生成
        List<CareActivationCard> activationCards = new ArrayList<>(number);
        while (activationCards.size() < number) {
            int needNumber = number - activationCards.size();
            List<CareActivationCard> activationCardList = this.createActivationCard(companyCode, cardVersion, needNumber);
            activationCards.addAll(activationCardList);
        }

        CareCardType careCardType = CareCardType.parse(cardType);
        if (Objects.isNull(careCardType)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        switch (careCardType) {
            case physical:
                return getPhysicalCard(number, isTest, activationCards, careCardType);
            case virtual:
                return getVirtualCard(careCardParam, number, isTest, activationCards, careCardType);
            default:
                return null;
        }
    }

    /**
     * 获取虚拟卡
     *
     * @param careCardParam
     * @param number
     * @param isTest
     * @param activationCards
     * @param careCardType
     * @return
     * @throws Exception
     */
    private String getVirtualCard(CareCardDTO careCardParam, Integer number, Boolean isTest, List<CareActivationCard> activationCards, CareCardType careCardType) throws Exception {
        Integer deviceTypeId = careCardParam.getDeviceTypeId();
        if (Objects.isNull(deviceTypeId)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }
        CareCardDeviceType careCardDeviceType = careCardDeviceTypeService.getById(deviceTypeId);
        // 测试无需生成数据到数据库
        if (!isTest) {
            careActivationCardService.saveBatch(activationCards);
            if (Objects.isNull(careCardDeviceType)) {
                throw new InstaException(CommonErrorCode.InvalidParameter);
            }
            // 虚拟卡绑定机型
            String deviceType = careCardDeviceType.getDeviceType();
            ArrayList<CareCardDeviceChangeRecord> careCardDeviceChangeRecords = new ArrayList<>(activationCards.size());
            dataEncapsulation(activationCards, deviceTypeId, null, deviceType, careCardDeviceChangeRecords, new ArrayList<>());
            careCardDeviceChangeRecordService.saveBatch(careCardDeviceChangeRecords);
            careActivationCardService.updateBatchById(activationCards);
        }

        // 记录必要信息
        CareCardContext context = CareCardContext.get();
        if (context != null) {
            List<Integer> cardIdList = activationCards.stream().map(CareActivationCard::getId).collect(Collectors.toList());
            context.setCardIdList(cardIdList);
        }

        // 上传oss
        List<ActivationCardExportData> exportDataList = new ArrayList<>(number);
        activationCards.forEach(activationCard -> {
            ActivationCardExportData exportData = new ActivationCardExportData();
            exportData.setActivationCardNumber(activationCard.getCardNumber());
            exportData.setActivationCardCode(activationCard.getActivationCode());
            exportData.setDeviceType(careCardDeviceType.getInnerType());
            exportDataList.add(exportData);
        });
        String fileName = careCardType.name() + "_card_activation_data_";
        return excelDataExportHelper.createOSSFile(exportDataList, fileName, "实体卡激活数据", ActivationCardExportData.class);
    }

    /**
     * 获取实体卡
     *
     * @param number
     * @param isTest
     * @param activationCards
     * @param careCardType
     * @return
     * @throws Exception
     */
    private String getPhysicalCard(Integer number, Boolean isTest, List<CareActivationCard> activationCards, CareCardType careCardType) throws Exception {
        // 测试无需生成数据到数据库
        if (!isTest) {
            careActivationCardService.saveBatch(activationCards);
        }
        // 记录必要信息
        CareCardContext context = CareCardContext.get();
        if (context != null) {
            List<Integer> cardIdList = activationCards.stream().map(CareActivationCard::getId).collect(Collectors.toList());
            context.setCardIdList(cardIdList);
        }

        // 上传oss
        List<ActivationCardExportData> exportDataList = new ArrayList<>(number);
        activationCards.forEach(activationCard -> {
            ActivationCardExportData exportData = new ActivationCardExportData();
            exportData.setActivationCardNumber(activationCard.getCardNumber());
            exportData.setActivationCardCode(activationCard.getActivationCode());
            exportData.setDeviceType(activationCard.getDeviceType());
            exportDataList.add(exportData);
        });

        String fileName = careCardType.name() + "_card_activation_data_";
        return excelDataExportHelper.createOSSFile(exportDataList, fileName, "实体卡激活数据", ActivationCardExportData.class);
    }

    /**
     * 实体卡作废
     *
     * @param careCardParam
     */
    public void disabledCareCards(CareCardDTO careCardParam) {
        List<String> cardNumberList = careCardParam.getCardNumberList();
        // 数据库里的实体卡对象
        List<CareActivationCard> careActivationCardList = careActivationCardService.listByCareNumber(cardNumberList);
        List<String> dbCardList = careActivationCardList.stream()
                .map(CareActivationCard::getCardNumber)
                .collect(Collectors.toList());
        // 卡号是否存在
        cardNumberList.removeAll(dbCardList);
        if (CollectionUtils.isNotEmpty(cardNumberList)) {
            InstaException instaException = new InstaException(InsuranceErrorCode.CardNumberNotExistException);
            instaException.putErrorData("cardNumberList", cardNumberList);
            throw instaException;
        }

        // 卡号是否已经作废
        List<CareActivationCard> disabledCards = careActivationCardList.stream().filter(CareActivationCard::getDisabled).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(disabledCards)) {
            List<String> cardNumbers = disabledCards.stream().map(CareActivationCard::getCardNumber).collect(Collectors.toList());
            InstaException instaException = new InstaException(InsuranceErrorCode.CardNumberDisabledException);
            instaException.putErrorData("cardNumberList", cardNumbers);
            throw instaException;
        }

        // record 数据封装
        List<CareCardDeviceChangeRecord> careCardDeviceChangeRecordList = new ArrayList<>();
        LocalDateTime localDateTime = LocalDateTime.now();

        // 存在工单号的才记录
        List<CareActivationCard> recordCardsList = careActivationCardList.stream()
                .filter(careActivationCard -> !"".equals(careActivationCard.getWorkNumber()))
                .collect(Collectors.toList());

        for (CareActivationCard careActivationCard : recordCardsList) {
            CareCardDeviceChangeRecord careCardDeviceChangeRecord = new CareCardDeviceChangeRecord();
            careCardDeviceChangeRecord.setCardId(careActivationCard.getId());
            careCardDeviceChangeRecord.setWorkNumber("因作废置空");
            careCardDeviceChangeRecord.setDeviceType(careActivationCard.getDeviceType());
            careCardDeviceChangeRecord.setDeviceTypeId(careActivationCard.getDeviceTypeId());
            careCardDeviceChangeRecord.setCreateTime(localDateTime);
            careCardDeviceChangeRecord.setUpdateTime(localDateTime);
            careCardDeviceChangeRecordList.add(careCardDeviceChangeRecord);
        }

        // 作废
        careActivationCardService.batchDisabled(careCardParam.getDisableReason(), dbCardList, careCardDeviceChangeRecordList);

        // 记录必要信息
        CardRecordContext cardRecordContext = CardRecordContext.get();
        if (cardRecordContext != null) {
            List<Integer> recordIdList = careCardDeviceChangeRecordList.stream().map(CareCardDeviceChangeRecord::getId).collect(Collectors.toList());
            cardRecordContext.setRecordIdList(recordIdList);
        }
    }

    /**
     * 查询工单可以绑定的数量
     *
     * @param workNumber
     */
    public Object workQuery(String workNumber) {
        // 获取token
        SapAuthorizationRequest sapAuthorizationRequest = new SapAuthorizationRequest(sapCommonConfiguration);
        String executePost = sapAuthorizationRequest.executePostParam();
        SapAuthorizationResponse authorizationResponse = SapAuthorizationResponse.parse(executePost);
        String accessToken = authorizationResponse.getAccess_token();

        SapWordNumberRequest sapWordNumberRequest = new SapWordNumberRequest(sapCommonConfiguration);
        sapWordNumberRequest.setToken(accessToken);
        sapWordNumberRequest.setWorknumber(workNumber);
        String result = sapWordNumberRequest.executePost();
        JSONObject jsonObject = JSON.parseObject(result);
        return jsonObject.get("data");
    }
}
