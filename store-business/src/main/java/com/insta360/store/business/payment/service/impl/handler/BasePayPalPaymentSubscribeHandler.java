package com.insta360.store.business.payment.service.impl.handler;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.cloud.constant.CloudSubscribeTextConstant;
import com.insta360.store.business.cloud.email.BaseCloudEmail;
import com.insta360.store.business.cloud.email.CloudEmailFactory;
import com.insta360.store.business.cloud.email.CloudSubscribePayPalUpdateEmail;
import com.insta360.store.business.common.OkHttpUtils;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.bo.PaymentSubscribeBO;
import com.insta360.store.business.payment.constants.PayPalConstant;
import com.insta360.store.business.payment.constants.PaymentConstant;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.paypal.enums.state.PayPalOrderDetailState;
import com.insta360.store.business.payment.lib.paypal.enums.state.PaypalCaptureState;
import com.insta360.store.business.payment.lib.paypal.module.*;
import com.insta360.store.business.payment.lib.paypal.module.error.PayPalPaymentDetailError;
import com.insta360.store.business.payment.lib.paypal.request.api.CreatePayPalDeleteVaultIdRequest;
import com.insta360.store.business.payment.lib.paypal.request.api.CreatePayPalPaymentRequest;
import com.insta360.store.business.payment.lib.paypal.response.BasePayPalPayResultResponse;
import com.insta360.store.business.payment.lib.paypal.response.CreatePayPalCaptureResponse;
import com.insta360.store.business.payment.service.impl.helper.PaymentHelper;
import com.insta360.store.business.trade.enums.CreditCardPaymentActionEnum;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.UserPayInfoService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2024/06/01
 * @Description: paypal订阅支付抽象类
 */
public abstract class BasePayPalPaymentSubscribeHandler extends BasePayPalPaymentHandler {

    @Autowired
    CloudEmailFactory cloudEmailFactory;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    PaymentHelper paymentHelper;

    @Override
    protected <T> T _payOrder(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        PaymentSubscribeType subscribeType = this.order.paymentSubscribeType();
        switch (subscribeType) {
            // 续订
            case RENEW_SUBSCRIBE:
                return processRenewOrderPayment(paymentInfo, paymentExtra);
            case FIRST_SUBSCRIBE:
                return super._payOrder(paymentInfo, paymentExtra);
            default:
                throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
    }

    /**
     * 续费订单支付处理
     *
     * @param paymentInfo
     * @param paymentExtra
     * @param <T>
     * @return
     */
    protected <T> T processRenewOrderPayment(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        String authText = StringUtils.EMPTY;
        try {
            CreatePayPalPaymentRequest request = this.getPaymentRequest(paymentInfo, paymentExtra);
            LOGGER.info(String.format("paypal 续费订单前置支付请求参数：{%s} 订单号：{%s}", JSONObject.toJSONString(request), paymentInfo.orderNumber));
            OkHttpUtils.OkHttpResponse okHttpResponse = request.executePostWithRequestIdResponseCode(getPayPalConfigInfo());
            String resultResponse = okHttpResponse.getResponseBody();
            LOGGER.info(String.format("paypal 续费订单前置支付响应参数：{%s} 订单号：{%s}", resultResponse, paymentInfo.orderNumber));

            BasePayPalPayResultResponse payResultResponse = CreatePayPalCaptureResponse.parse(resultResponse);
            // 错误响应告警&重试
            if (payResultResponse.isErrorResponse()) {
                // paypal系统异常则延迟重试
                if (PayPalConstant.INTERNAL_SERVER_ERROR.equals(payResultResponse.getName())) {
                    paymentHelper.doRenewOrderPayRetryLogic(this.order);
                }

                // 发送续费订单扣款失败通知
                FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.RENEW_ORDER_PAYMENT_EXCEPTION_TEXT,
                                order.getOrderNumber(), this.getPaymentChannel().name(), payResultResponse.getName(), Arrays.toString(payResultResponse.getDetails())),
                        FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.LCX);

                // save auth result
                PayPalPaymentDetailError detailError = payResultResponse.getDetails()[0];
                authText = String.format(PayPalConstant.PAYPAL_PAYMENT_ERROR_TEXT, payResultResponse.getName(), detailError.getIssue(), detailError.getDescription());
                this.savePaymentAuthResultInfo(order.getOrderNumber(), String.valueOf(okHttpResponse.getCode()), authText, CreditCardPaymentActionEnum.CAPTURE_RESULT);

                LOGGER.info(String.format("[paypal数据记录]订单号:%s,失败authText:%s", order.getOrderNumber(), authText));
                return (T) Boolean.FALSE;
            }

            this.processCaptureResult(payResultResponse);
            // 回传状态
            String status = payResultResponse.getStatus();
            // Completed 才处理支付结果
            if (PayPalOrderDetailState.COMPLETED.name().equals(status)) {
                // 交易状态
                PayPalCapture payPalCapture = payResultResponse.getPurchase_units()[0].getPayments().getCaptures()[0];
                String captureStatus = payPalCapture.getStatus();
                // 续订前置支付结果只处理成功和pending的，pending待后续webhook通知处理
                if (PaypalCaptureState.COMPLETED.equals(PaypalCaptureState.parse(captureStatus)) ||
                        PaypalCaptureState.PENDING.equals(PaypalCaptureState.parse(captureStatus))) {
                    return (T) Boolean.TRUE;
                }
            }
        } catch (Exception e) {
            LOGGER.error(String.format("paypal 续费扣款发起报错 原因：{%s}", e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("paypal 续费扣款发起报错。原因：{%s}", e.getMessage()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(-1, "Fail to create Paypal Payment: " + e.getMessage());
        } finally {
            // 记录paypal支付记录
            paypalTransactionRecordHelper.savePaypalRecord(order, paymentInfo.amount, paymentInfo.currency, paymentInfo.orderNumber, paymentInfo.countryCode, LocalDateTime.now(), authText);
            LOGGER.info(String.format("[paypal数据记录]准备记录paypal支付信息,订单号:%s", order.getOrderNumber()));
        }
        return (T) Boolean.FALSE;
    }

    @Override
    protected CreatePayPalPaymentRequest getPaymentRequest(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        CreatePayPalPaymentRequest paymentRequest = super.getPaymentRequest(paymentInfo, paymentExtra);
        // 续费订阅不传支付定制信息
        if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(this.order.paymentSubscribeType())) {
            paymentRequest.setApplication_context(null);
        }
        paymentRequest.setPaymentSource(getPaymentSource(paymentExtra));
        packDescription(paymentRequest.getPurchase_units());
        // 订阅支付不需支付指令（否则接口会报参数异常）
        paymentRequest.setProcessing_instruction(null);
        return paymentRequest;
    }

    @Override
    public void doProcessCaptureResult(BasePayPalPayResultResponse resultResponse, Order order, String captureStatus, String
            channelPaymentId, PaymentTradeSecurityType paymentTradeSecurityType) {
        String orderNumber = order.getOrderNumber();
        PaypalCaptureState captureState = PaypalCaptureState.parse(captureStatus);
        switch (captureState) {
            case COMPLETED:
                // 设置为 支付成功
                orderPaymentService.setPaymentSuccess(orderNumber, getPaymentChannel(), channelPaymentId, paymentTradeSecurityType);
                // 如果vault状态为VAULTED，则记录pay token
                payPalPaymentHelper.savePayPalPayInfo(order, this.getPaymentChannel(), resultResponse.getPayment_source(), resultResponse.getId());
                break;
            case PENDING:
                // 设置为 支付异常
                orderPaymentService.setPaymentPending(orderNumber, getPaymentChannel(), channelPaymentId);
                payPalPaymentHelper.savePendingPayInfo(order);
                break;
            case DECLIEND:
            case FAILED:
                // 设置 为支付失败
                orderPaymentService.setPaymentFailure(orderNumber);
                // 续费失败邮件
                if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
                    paymentHelper.processSubscribeRenewOrderFailed(order);
                    paymentHelper.doRenewOrderPayRetryLogic(order);
                    throw new InstaException(PaymentErrorCode.TransactionFailedException);
                }
                if (PaypalCaptureState.FAILED.equals(captureState)) {
                    // failed状态据paypal说明是系统错误导致，需重新走结账流程，商家无需操作，关注即可
                    FeiShuMessageUtil.storeGeneralMessage("paypal capture 状态为failed，请关注！订单号{}", orderNumber, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public <T> T updateCancelSubscribeInfo(PaymentSubscribeBO paymentSubscribeParam) {
        String payId = paymentSubscribeParam.getPayId();
        LOGGER.info(String.format("paypal vault delete request {%s}", payId));
        CreatePayPalDeleteVaultIdRequest request = new CreatePayPalDeleteVaultIdRequest(payId);
        request.setAccessTokenCacheKey(this.getPayPalAccessTokenCacheKey());
        OkHttpUtils.OkHttpResponse deleteVaultResponse = request.executeDelete(this.getPayPalConfigInfo());
        LOGGER.info(String.format("paypal vault delete response {%s}", deleteVaultResponse));
        // 删除vault id请求没有body返回，状态为204
        if (!(deleteVaultResponse.getCode() >= 200 && deleteVaultResponse.getCode() < 300)) {
            LOGGER.info(String.format("paypal vault id删除失败！，payId【%s】", payId));
            FeiShuMessageUtil.storeGeneralMessage("paypal vault id删除失败！", FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        return null;
    }

    @Override
    protected void sendPaypalCardUpdateEmail(Order order) {
        if (order.getCloudSubscribeMark() && PaymentSubscribeType.FIRST_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            BaseCloudEmail subscribeOrderEmail = cloudEmailFactory.getSubscribeOrderEmail(order, CloudSubscribePayPalUpdateEmail.class);
            subscribeOrderEmail.doSend(order.getContactEmail());

            // 发送飞书通知
            FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.PAYPAL_PENDING_COMPLETED_TEXT, order.getOrderNumber()),
                    FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.LCX);
        }
    }

    /**
     * 支付来源对象
     *
     * @param paymentExtra
     * @return
     */
    protected PayPalPaymentSource getPaymentSource(PaymentExtra paymentExtra) {
        PayPalPaymentSource payPalPaymentSource = new PayPalPaymentSource();
        // 续订
        if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(this.order.paymentSubscribeType())) {
            payPalPaymentSource.setPaypal(getRenewOrderPaypal(paymentExtra));
        }
        // 首订
        else {
            payPalPaymentSource.setPaypal(getPaypal());
        }
        return payPalPaymentSource;
    }

    /**
     * 获取续费订单数据源
     *
     * @param paymentExtra
     * @return
     */
    protected PayPalInfo getRenewOrderPaypal(PaymentExtra paymentExtra) {
        StoreAccount storeAccount = paymentExtra.getStoreAccount();
        if (Objects.isNull(storeAccount)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(storeAccount.getInstaAccount());
        PayPalInfo payPal = new PayPalInfo();
        payPal.setVaultId(AESUtil.decode(AESUtil.PAY_TOKEN_KEY, userPayInfo.getPayId()));
        return payPal;
    }

    /**
     * 封装订阅描述
     *
     * @param purchaseUnits
     */
    protected void packDescription(PayPalPurchaseUnit[] purchaseUnits) {
        purchaseUnits[0].setDescription(PaymentConstant.PAYMENT_SUBSCRIBE_DESCRIPTION);
    }

    /**
     * paypal 钱包支付来源对象
     *
     * @return
     */
    protected PayPalInfo getPaypal() {
        PayPalInfo payPal = new PayPalInfo();
        payPal.setAttributes(getPayPalAttributes());
        return payPal;
    }

    /**
     * 钱包操作属性
     *
     * @return
     */
    protected PayPalAttributes getPayPalAttributes() {
        PayPalAttributes payPalAttributes = new PayPalAttributes();
        payPalAttributes.setVault(getPayPalVault());
        return payPalAttributes;
    }

    /**
     * 保存pay token 至保险库
     *
     * @return
     */
    protected PayPalVault getPayPalVault() {
        PayPalVault payPalVault = new PayPalVault();
        payPalVault.setStoreInVault(PayPalConstant.PAYPAL_VAULT);
        payPalVault.setUsageType(PayPalConstant.PAYPAL_USAGE_TYPE);
        payPalVault.setPermitMultiplePaymentTokens(true);
        return payPalVault;
    }

    @Override
    protected String getPayPalDescription() {
        return PayPalConstant.STORE_ORDER_DESCRIPTION;
    }

    @Override
    protected String getEmailAddress(PaymentInfo paymentInfo) {
        return paymentInfo.email;
    }
}
