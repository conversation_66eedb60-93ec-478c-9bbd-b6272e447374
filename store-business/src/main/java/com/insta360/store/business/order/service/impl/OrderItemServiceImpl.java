package com.insta360.store.business.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.order.bo.OrderItemStockBO;
import com.insta360.store.business.order.dao.OrderItemDao;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.helper.OrderProfessionEmailSendHelper;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.model.ResellerOrderItem;
import com.insta360.store.business.reseller.service.ResellerOrderItemService;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
@Service
public class OrderItemServiceImpl extends BaseServiceImpl<OrderItemDao, OrderItem> implements OrderItemService {

    @Autowired
    OrderService orderService;

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    ResellerOrderItemService resellerOrderItemService;

    @Autowired
    OrderProfessionEmailSendHelper orderProfessionEmailSendHelper;

    @Override
    public List<OrderItem> getByOrder(Integer orderId) {
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("`order`", orderId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<OrderItem> getByOrderNumber(String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            return Lists.newArrayList();
        }
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("`order`", order.getId());
        return baseMapper.selectList(qw);
    }

    @Override
    public List<OrderItem> getByProduct(Integer orderId, Integer productId) {
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("`order`", orderId);
        qw.eq("product", productId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<OrderItem> getByCommodity(Integer orderId, Integer commodityId) {
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("`order`", orderId);
        qw.eq("commodity", commodityId);
        return baseMapper.selectList(qw);
    }

    @Override
    public OrderItem getGift(Integer orderId, Integer commodityId) {
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("`order`", orderId);
        qw.eq("commodity", commodityId);
        qw.eq("is_gift", true);
        return baseMapper.selectOne(qw);
    }

    @Override
    public void close(Integer orderItemId) {
        OrderItem orderItem = getById(orderItemId);
        orderItem.setState(OrderItemState.closed.getCode());
        baseMapper.updateById(orderItem);
    }

    @Override
    public void setAlreadyReview(OrderItem orderItem) {
        orderItem.setReviewState(true);
        baseMapper.updateById(orderItem);
    }

    @Override
    public void setSuccess(Integer orderItemId) {
        OrderItem orderItem = getById(orderItemId);
        OrderItemState orderItemState = orderItem.orderItemState();

        switch (orderItemState) {
            case refunding:
                orderItem.setState(OrderItemState.refunded.getCode());
                break;
            case returning:
                orderItem.setState(OrderItemState.returned.getCode());
                break;
            case changing:
                orderItem.setState(OrderItemState.changed.getCode());
                break;
            default:
        }

        baseMapper.updateById(orderItem);
    }

    @Override
    public boolean checkAllowRefund(Integer orderItemId) {
        OrderItem orderItem = getById(orderItemId);
        OrderItemState orderItemState = orderItem.orderItemState();
        if (!OrderItemState.normal.equals(orderItemState)) {
            return false;
        }

        Order order = orderService.getById(orderItem.getOrder());
        List<OrderState> states = Arrays.asList(
                OrderState.payed,
                OrderState.prepared,
                OrderState.part_delivery
        );

        return states.contains(order.orderState());
    }

    @Override
    public boolean checkAllowReturnOrChange(Integer orderItemId) {
        OrderItem orderItem = getById(orderItemId);
        OrderItemState orderItemState = orderItem.orderItemState();
        if (!OrderItemState.normal.equals(orderItemState)) {
            return false;
        }

        Order order = orderService.getById(orderItem.getOrder());
        List<OrderState> states = Arrays.asList(
                OrderState.part_delivery,
                OrderState.on_delivery,
                OrderState.success
        );

        return states.contains(order.orderState());
    }

    @Override
    public void shipOrderItem(Integer orderItemId) {
        OrderItem orderItem = baseMapper.selectById(orderItemId);
        if (orderItem == null) {
            throw new InstaException(OrderErrorCode.OrderItemNotFoundException);
        }

        // 防止重复修改
        if (!OrderItemState.on_delivery.equals(orderItem.orderItemState())) {
            orderItem.setDeliveryState(OrderItemState.on_delivery.getCode());
            baseMapper.updateById(orderItem);
        }

        // 子项发货后检查是否需要发送额外的邮件
        orderProfessionEmailSendHelper.sendRealEstateSoftwareEmail(orderItem);
        orderProfessionEmailSendHelper.professionItemSendEmail(null, orderItem);
        // 临时活动MatterportSoftware
        orderProfessionEmailSendHelper.sendMatterportSoftwareEmail(orderItem);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void deleteGiftItem(OrderItem orderItem) {
        // 订单要真实有效
        Order order = orderService.getById(orderItem.getOrder());
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 只可对已支付和已配货的订单进行操作
        if (!OrderState.payedOrPrepared().contains(order.orderState())) {
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        // 删除对应子项
        this.removeById(orderItem.getId());

        // 关联的分销订单的赠品子项也需同步删除
        if (StringUtil.isBlank(order.getPromoCode())) {
            return;
        }

        // 分销订单
        ResellerOrder resellerOrder = resellerOrderService.getByOrderNumber(order.getOrderNumber());
        if (resellerOrder == null) {
            return;
        }

        // 分销订单item gift
        ResellerOrderItem item = resellerOrderItemService.getResellerOrderItemByItemId(orderItem.getId());
        if (Objects.nonNull(item)) {
            resellerOrderItemService.removeById(item.getId());
            return;
        }

        // 分销订单item gift (兼容旧单，之前reseller_order_item表未存储order_item_id，才需通过赠品标识进行数据定位)
        List<ResellerOrderItem> resellerOrderItemList = resellerOrderItemService.getResellerOrderItemGift(resellerOrder.getId(), orderItem.getCommodity(), true);
        if (CollectionUtils.isNotEmpty(resellerOrderItemList)) {
            List<Integer> resellerOrderItemIds = resellerOrderItemList.stream().map(ResellerOrderItem::getId).collect(Collectors.toList());
            resellerOrderItemService.removeByIds(resellerOrderItemIds);
        }
    }

    @Override
    public List<OrderItem> getOrderItemsByOrderId(Integer orderId) {
        if (orderId == null) {
            return Lists.newArrayList();
        }
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("`order`", orderId);
        qw.eq("is_gift", false);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<OrderItem> listByOrderIds(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.in("`order`", orderIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<OrderItem> getOrderItemByOrderIds(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.in("`order`", orderIds);
        qw.eq("is_gift", false);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Integer> getOrderIdByProductAndOrder(List<Integer> productIdList, List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(productIdList) || CollectionUtils.isEmpty(orderIdList)) {
            return Lists.newArrayList();
        }
        return baseMapper.getOrderIdByProductAndOrder(productIdList, orderIdList);
    }

    @Override
    public void updateItemById(Object item) {
        JSONObject itemJson = (JSONObject) item;
        OrderItem orderItem = JSONObject.parseObject(itemJson.toJSONString(), OrderItem.class);
        baseMapper.updateById(orderItem);
    }

    @Override
    public List<OrderItem> listByOrderIdsAndProductId(List<Integer> orderIdList, Integer productId) {
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.in("`order`", orderIdList);
        qw.eq("product", productId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<OrderItem> listOrderItemsByOrderId(Integer orderId) {
        if (orderId == null) {
            return Lists.newArrayList();
        }
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("`order`", orderId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<OrderItemStockBO> listOrderItemStockByCommodityIds(List<Integer> commodityIds, List<Integer> orderStates, LocalDateTime fromTime, LocalDateTime endTime, InstaCountry country) {
        if (CollectionUtils.isEmpty(commodityIds)
                || CollectionUtils.isEmpty(orderStates)
                || Objects.isNull(fromTime)
                || Objects.isNull(endTime)
                || Objects.isNull(country)
        ) {
            return new ArrayList<>(0);
        }

        return baseMapper.listItemLockStockByCommodityIds(commodityIds, orderStates, fromTime, endTime, country.name(), 0);
    }

    @Override
    public List<OrderItem> listByProduct(Integer x5Id) {
        QueryWrapper<OrderItem> qw = new QueryWrapper<>();
        qw.eq("product", x5Id);
        return baseMapper.selectList(qw);
    }
}
