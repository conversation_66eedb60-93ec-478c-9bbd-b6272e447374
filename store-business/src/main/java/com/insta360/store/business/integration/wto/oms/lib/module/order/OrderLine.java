package com.insta360.store.business.integration.wto.oms.lib.module.order;

/**
 * @Author: wkx
 * @Date: 2024/12/03
 * @Description:
 */
public class OrderLine {

    /**
     * 明细行项目号
     */
    private String orderLineNo;

    /**
     * 结算单价
     */
    private String actualPrice;

    /**
     * sku code
     */
    private String itemCode;

    /**
     * 子项数量
     */
    private String planQty;

    /**
     * 子项id
     */
    private String subSourceOrderCode;

    /**
     * 子项状态（兼容历史订单数据）
     */
    private String status;

    public String getOrderLineNo() {
        return orderLineNo;
    }

    public void setOrderLineNo(String orderLineNo) {
        this.orderLineNo = orderLineNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getPlanQty() {
        return planQty;
    }

    public void setPlanQty(String planQty) {
        this.planQty = planQty;
    }

    public String getSubSourceOrderCode() {
        return subSourceOrderCode;
    }

    public void setSubSourceOrderCode(String subSourceOrderCode) {
        this.subSourceOrderCode = subSourceOrderCode;
    }

    public String getActualPrice() {
        return actualPrice;
    }

    public void setActualPrice(String actualPrice) {
        this.actualPrice = actualPrice;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "OrderLine{" +
                "orderLineNo='" + orderLineNo + '\'' +
                ", actualPrice='" + actualPrice + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", planQty='" + planQty + '\'' +
                ", subSourceOrderCode='" + subSourceOrderCode + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
