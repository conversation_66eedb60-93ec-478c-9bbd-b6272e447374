package com.insta360.store.business.cloud.service.impl.handler.benefit;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.store.business.cloud.bo.StoreCloudBusinessTypeResultBO;
import com.insta360.store.business.cloud.constant.StoreBenefitConstant;
import com.insta360.store.business.cloud.enums.*;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.business.cloud.model.CloudStorageInsuranceBenefitBind;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitChange;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitDetail;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitContext;
import com.insta360.store.business.discount.enums.DiscountSource;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 年刊权益处理类
 * @Date 2024/5/14
 */
@Scope("prototype")
@Component
public class YearlyBenefitHandler extends BaseStoreBenefitCoreHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(YearlyBenefitHandler.class);

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @Override
    public void handle(StoreBenefitContext context) {
        super.handle(context);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void purchaseSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 首购场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        this.reinitializeBenefit(storeBenefitContext);
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 首购场景业务处理结束...", storeBenefit.getUserId());
    }

    /**
     * 续订场景
     * 重新下发所有特殊权益，则需将上一周期的权益记录明细设置为'过期'
     *
     * @param storeBenefitContext
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void renewSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 续订场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 下发新权益
        this.reinitializeBenefit(storeBenefitContext);
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 续订场景业务处理结束...", storeBenefit.getUserId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void upgradeSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 升级场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 本次变更场景业务类型映射结果
        StoreCloudBusinessTypeResultBO typeMappingResult = storeBenefitContext.getTypeMappingResult();
        // 升级后容量类型
        BenefitCapacityType afterUpgradeCapacityType = typeMappingResult.getCapacityType();
        // 当前用户权益记录的容量类型
        BenefitCapacityType currentCapacityType = BenefitCapacityType.matchType(benefitLastChange.getBeforeCapacity());
        // 当前用户权益记录的订阅类型
        SkuSubscribeType currentSubscribeType = SkuSubscribeType.matchType(benefitLastChange.getBeforeSubscribeType());

        // 月度 -> 年度
        if (SkuSubscribeType.MONTHLY.equals(currentSubscribeType)) {
            this.subscribeTypeUpgrade(afterUpgradeCapacityType);
        }
        // 年度 -> 年度 （容量空间升级）
        else if (SkuSubscribeType.YEARLY.equals(currentSubscribeType)) {
            this.capacityUpgrade(afterUpgradeCapacityType, currentCapacityType);
        }

        // 邮件发送
        this.sendBenefitEmail();
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 升级场景业务处理结束...", storeBenefit.getUserId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void downgradeSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 降级场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 年度订阅中，若不是1t/2t降级到200G，则不予处理
        BenefitCapacityType capacityType = storeBenefitContext.getTypeMappingResult().getCapacityType();
        if (!BenefitCapacityType.TWO_HUNDRED_G.equals(capacityType)) {
            LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 年度订阅套餐降级非'200G'场景不予处理...", storeBenefit.getUserId());
            return;
        }

        if (Objects.isNull(careBenefitDetail)) {
            LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 数据存在异常，年度订阅套餐降级未查询到care权益明细记录...", storeBenefit.getUserId());
            FeiShuMessageUtil.storeGeneralMessage(String.format("[云存储商城权益变更场景业务处理]userId:%s 数据存在异常，年度订阅套餐降级未查询到care权益明细记录...", storeBenefit.getUserId()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return;
        }
        // 年度降级，若容量空间降到200G，则终止care权益
        this.stopOldBenefitDetail(careBenefitDetail);
        // 禁用已绑定的代金券
        this.disableGiftCard(storeBenefit.getUserId());
        // 若care权益存在绑定相机序列号的记录则需要终止权益
        CloudStorageInsuranceBenefitBind insuranceBenefitBind = cloudStorageInsuranceBenefitBindService.getInsuranceBenefitBind(careBenefitDetail.getId(), BenefitType.CARE.getType());
        if (Objects.nonNull(insuranceBenefitBind)) {
            this.insuranceInvalid(insuranceBenefitBind);
        }

        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 降级场景业务处理结束...", storeBenefit.getUserId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void refundSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 退款场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 终止权益
        this.benefitEnd(true);
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 退款场景业务处理结束...", storeBenefit.getUserId());
    }

    @Override
    public void expiredSceneHandle(StoreBenefitContext storeBenefitContext) {
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} storeBenefitContext:{} 过期场景业务处理开始...", storeBenefit.getUserId(), JSON.toJSONString(storeBenefitContext));
        // 终止权益
        this.benefitEnd(false);
        LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 过期场景业务处理结束...", storeBenefit.getUserId());
    }

    /**
     * 重新初始化权益
     */
    private void reinitializeBenefit(StoreBenefitContext storeBenefitContext) {
        // 停用上一周期的所有权益
        this.stopAllBenefit();

        // 业务类型映射结果信息
        StoreCloudBusinessTypeResultBO typeMappingResult = storeBenefitContext.getTypeMappingResult();
        // 容量类型
        BenefitCapacityType capacityType = typeMappingResult.getCapacityType();
        // 场景类型
        ServiceScenesType serviceScenesType = typeMappingResult.getServiceScenesType();
        // 下单地区
        String region = storeBenefit.parseRegion();

        // 权益明细集合
        List<CloudStorageStoreBenefitDetail> benefitDetailList = new ArrayList<>(3);
        // 添加care权益
        if (!BenefitCapacityType.TWO_HUNDRED_G.equals(capacityType) && careOnlineRegionCheck(region)) {
            benefitDetailList.add(this.buildCareBenefitDetail());
        }

        // 添加延保权益
        if (extendOnlineRegionCheck(region)) {
            benefitDetailList.add(this.buildExtendBenefitDetail());
        }

        // 只有商城渠道的订单符合条件才正常下发‘配件八折’权益
        if (!StoreBenefitConstant.DISCOUNT_BENEFIT_DISABLE_COUNTRY.contains(region)
                && ServiceScenesType.PURCHASE.equals(serviceScenesType)
                && BenefitPlatform.STORE.getName().equals(storeBenefit.getPlatformSource())
        ) {
            Optional.ofNullable(this.subscribeDiscountPurchaseProcess(storeBenefitContext)).ifPresent(benefitDetailList::add);
        }

        // 批量保存下发的商城特殊权益
        Boolean success = cloudStorageStoreBenefitDetailService.batchSave(benefitDetailList);
        if (success) {
            // 邮件发送
            this.sendBenefitEmail();
        }
    }

    /**
     * 配件八折权益特殊处理（购买场景）
     */
    private CloudStorageStoreBenefitDetail subscribeDiscountPurchaseProcess(StoreBenefitContext storeBenefitContext) {
        // 商城云服务订阅记录
        CloudStorageSubscribe cloudStorageSubscribe = storeBenefitContext.getCloudStorageSubscribe();
        if (Objects.isNull(cloudStorageSubscribe)) {
            throw new InstaException(CloudSubscribeErrorCode.NotExistSubscribeRecordException);
        }
        // 查询云服务订单
        Order order = orderService.getByOrderNumber(cloudStorageSubscribe.getOrderNumber());
        if (Objects.isNull(order)) {
            throw new InstaException(CloudSubscribeErrorCode.NotExistSubscribeOrderException);
        }

        CloudStorageStoreBenefitDetail discountBenefitDetail = null;
        // 云服务订单创建时间在指定时间之前，则正常下发‘配件八折’权益
        LocalDateTime orderCreateTime = order.getCreateTime();
        // ‘配件八折’权益下线时间
        LocalDateTime discountBenefitOfflineTime = TimeUtil.parseLocalDateTime(cloudStorageConfig.getDiscountBenefitOfflineTime());
        if (orderCreateTime.isBefore(discountBenefitOfflineTime)) {
            // 构建‘配件八折’权益
            discountBenefitDetail = this.buildSubscribeDiscountBenefitDetail();
            // 查询订单商品
            List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
            // 已透支的额度
            int overdraftQuota = orderItemList.stream()
                    .filter(orderItem -> DiscountSource.CLOUD_SUBSCRIBE.name().equals(orderItem.getDiscountSource())).mapToInt(OrderItem::getUsedQuota).sum();

            // 已使用额度
            int usedQuota = Math.min(discountBenefitDetail.getFixedQuota(), overdraftQuota);
            // 剩余额度
            int remainderQuota = discountBenefitDetail.getRemainderQuota() - usedQuota;
            // 重置额度
            discountBenefitDetail.setUsedQuota(usedQuota);
            discountBenefitDetail.setRemainderQuota(remainderQuota);
        }

        return discountBenefitDetail;
    }

    /**
     * 订阅折扣权益重置额度
     */
    private void subscribeDiscountResetLimit(CloudStorageStoreBenefitChange yearlyChangeRecord) {
        // 下单国家限制
        if (StoreBenefitConstant.DISCOUNT_BENEFIT_DISABLE_COUNTRY.contains(storeBenefit.parseRegion())) {
            return;
        }
        if (Objects.isNull(subscribeDiscountBenefitDetail)) {
            LOGGER.info("[云存储商城权益变更场景业务处理]userId:{} 订阅折扣权益重置额度，未查询到订阅折扣权益明细记录...", storeBenefit.getUserId());
            cloudStorageStoreBenefitDetailService.save(this.buildSubscribeDiscountBenefitDetail());
            return;
        }

        // 最近一次年度权益记录开始时间
        LocalDateTime lastYearlyStartTime = yearlyChangeRecord.getCreateTime();
        Period between = Period.between(lastYearlyStartTime.toLocalDate(), LocalDateTime.now().toLocalDate());
        // 最后一个年度订阅权益变更时间超过一年则需重置额度，反之则继续沿用之前的额度
        if (between.getDays() >= SkuSubscribeType.YEARLY.getDuration()) {
            subscribeDiscountBenefitDetail.setRemainderQuota(10);
            subscribeDiscountBenefitDetail.setUsed(false);
            subscribeDiscountBenefitDetail.setUsedQuota(0);
        }
        subscribeDiscountBenefitDetail.setExpired(false);
        subscribeDiscountBenefitDetail.setCreateTime(LocalDateTime.now());
        subscribeDiscountBenefitDetail.setModifyTime(LocalDateTime.now());
        cloudStorageStoreBenefitDetailService.updateById(subscribeDiscountBenefitDetail);
    }

    /**
     * 用户商城权益终止
     *
     * @param recycleNow
     */
    private void benefitEnd(Boolean recycleNow) {
        // 终止配件折扣权益
        this.stopOldBenefitDetail(subscribeDiscountBenefitDetail);
        // 终止care权益
        if (Objects.nonNull(careBenefitDetail)) {
            careBenefitDetail.setExpired(true);
            cloudStorageStoreBenefitDetailService.updateById(careBenefitDetail);
            if (recycleNow) {
                Optional.ofNullable(cloudStorageInsuranceBenefitBindService.getInsuranceBenefitBind(careBenefitDetail.getId(), BenefitType.CARE.getType()))
                        .ifPresent(this::insuranceInvalid);

                // 禁用代金券
                this.disableGiftCard(storeBenefit.getUserId());
            }
        }
        // 终止延保权益
        if (Objects.nonNull(extendBenefitDetail)) {
            extendBenefitDetail.setExpired(true);
            cloudStorageStoreBenefitDetailService.updateById(extendBenefitDetail);
            if (recycleNow) {
                Optional.ofNullable(cloudStorageInsuranceBenefitBindService.getInsuranceBenefitBind(extendBenefitDetail.getId(), BenefitType.EXTEND.getType()))
                        .ifPresent(this::insuranceInvalid);
            }
        }
    }

    /**
     * 年度订阅-容量升级
     * 规则：若最近的这次权益变更记录是年度订阅且是'512G'版本，升级后的容量空间非'512G'版本，则需下发'care'权益
     *
     * @param afterUpgradeCapacityType 升级后的容量类型
     * @param currentCapacityType      当前的容量类型
     */
    private void capacityUpgrade(BenefitCapacityType afterUpgradeCapacityType, BenefitCapacityType currentCapacityType) {
        if (BenefitCapacityType.TWO_HUNDRED_G.equals(currentCapacityType) && !BenefitCapacityType.TWO_HUNDRED_G.equals(afterUpgradeCapacityType)) {
            // 将旧的权益设置为'过期'
            this.stopOldBenefitDetail(careBenefitDetail);
            // 重新下发新的care权益
            if (careOnlineRegionCheck(storeBenefit.parseRegion())) {
                cloudStorageStoreBenefitDetailService.save(this.buildCareBenefitDetail());
            }
        }
    }


    /**
     * 订阅类型升级处理
     * 对于年度订阅权益变更，进行处理，包括更新或下发新的权益细节。
     * 月度 -> 年度
     * '订阅配件折扣'规则处理：
     * 1、如果该用户权益变更记录中存在历史年度订阅，则需判断上次年度订阅时的开始时间与当前这次年度订阅升级开始时间间隔是否超过一年
     * 如果超过一年，则需重置对应'订阅配件折扣'权益额度，反之则无需重置继续沿用之前的额度
     * 2、如果该用户权益变更记录中不存在历史年度订阅，则直接下发'订阅配件折扣'权益
     * <p>
     * '延保'权益
     * 1、将对应权益设置未'未过期'，因为月度订阅本身就会下发'延保'权益，无需额外处理
     * <p>
     * 'care'权益
     * 1、如果当前升的年度订阅非'512'G版本，则需下发'care'权益
     * 参数：
     * afterUpgradeCapacityType - 升级后的容量类型
     */
    private void subscribeTypeUpgrade(BenefitCapacityType afterUpgradeCapacityType) {
//        // 查询最后一次年度订阅权益变更记录
//        CloudStorageStoreBenefitChange yearlyChangeRecord = cloudStorageStoreBenefitChangeService.getRecentBenefitChangeRecord(storeBenefit.getUserId(), storeBenefit.getId(), SkuSubscribeType.YEARLY.getType());
//        if (Objects.isNull(yearlyChangeRecord)) {
//            // 如果不存在年度订阅权益变更记录，则更新之前的订阅优惠权益为已过期，并下发新的权益
//            this.stopOldBenefitDetail(subscribeDiscountBenefitDetail);
//            // 下发新的折扣权益
//            if(!StoreBenefitConstant.DISCOUNT_BENEFIT_DISABLE_COUNTRY.contains(storeBenefit.parseRegion())) {
//                cloudStorageStoreBenefitDetailService.save(this.buildSubscribeDiscountBenefitDetail());
//            }
//        } else {
//            // 如果存在年度订阅权益变更记录，则进行订阅优惠重置限制处理
//            this.subscribeDiscountResetLimit(yearlyChangeRecord);
//        }
        // 停止当期订阅的‘配件八折’权益 （月度 -> 年度）
        this.stopOldBenefitDetail(subscribeDiscountBenefitDetail);

        // 对于延保权益，无论月度还是年度订阅，直接设置为未过期
        if (Objects.nonNull(extendBenefitDetail)) {
            extendBenefitDetail.setExpired(false);
            cloudStorageStoreBenefitDetailService.updateById(extendBenefitDetail);
        }

        // 如果升级后的容量类型不是200G，将之前的权益设置为'已过期'，重新下发新权益
        if (!BenefitCapacityType.TWO_HUNDRED_G.equals(afterUpgradeCapacityType)) {
            // 停止旧的care权益
            this.stopOldBenefitDetail(careBenefitDetail);
            // 下发新的care权益
            if (this.careOnlineRegionCheck(storeBenefit.parseRegion())) {
                cloudStorageStoreBenefitDetailService.save(this.buildCareBenefitDetail());
            }
        }
    }
}
