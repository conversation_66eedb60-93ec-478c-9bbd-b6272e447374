package com.insta360.store.business.user.email;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.model.EmailSubscribeActivityPage;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2020/07/07
 * @Description:
 */
@Component
public class UserEmailFactory {

    /**
     * 邮件到货通知
     *
     * @param commodity
     * @param language
     * @param country
     * @param userClsEmail
     * @return
     */
    public BaseUserEmail getEmail(Commodity commodity, InstaLanguage language, InstaCountry country, Class<? extends BaseUserEmail> userClsEmail) {
        BaseUserEmail email = ApplicationContextHolder.getApplicationContext().getBean(userClsEmail);
        email.setCommodity(commodity);
        email.setLanguage(language);
        email.setCountry(country);
        return email;
    }

    /**
     * test email send使用
     *
     * @param templateKey
     * @param activityCode
     * @param country
     * @param userClsEmail
     * @return
     */
    public BaseUserEmail getEmail(String templateKey, String activityCode, InstaCountry country, Class<? extends BaseUserEmail> userClsEmail) {
        BaseUserEmail email = ApplicationContextHolder.getApplicationContext().getBean(userClsEmail);
        email.setCountry(country);
        email.setActivityGiftCode(activityCode);
        email.setTemplateKey(templateKey);
        return email;
    }

    /**
     * 邮件订阅
     *
     * @param emailSubscribe
     * @param userClsEmail
     * @return
     */
    public BaseUserEmail getEmail(EmailSubscribe emailSubscribe, Class<? extends BaseUserEmail> userClsEmail, EmailSubscribeActivityPage emailSubscribeActivityPage) {
        BaseUserEmail email = ApplicationContextHolder.getApplicationContext().getBean(userClsEmail);
        email.setEmailSubscribe(emailSubscribe);
        email.setCountry(InstaCountry.parse(emailSubscribe.getCountry()));
        email.setTemplateKey(emailSubscribeActivityPage.getEmailKey());
        return email;
    }
}