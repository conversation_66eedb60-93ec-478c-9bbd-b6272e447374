package com.insta360.store.service.controller.order.vo.info;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2022/10/18
 * @Description:
 */
public class OrderItemInfoVO implements Serializable {

    /**
     * id
     */
    private Integer id;

    /**
     * 是否是赠品
     */
    private Boolean isGift;

    /**
     * 数量
     */
    private Integer number;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 图片链接
     */
    private String imageUrl;

    /**
     * 单价
     */
    private Float amount;

    /**
     * 折扣价
     */
    private Float discountFee;

    /**
     * 货币
     */
    private String currency;

    /**
     * 货币符号
     */
    private String signal;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getIsGift() {
        return isGift;
    }

    public void setIsGift(Boolean gift) {
        isGift = gift;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public Float getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(Float discountFee) {
        this.discountFee = discountFee;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getSignal() {
        return signal;
    }

    public void setSignal(String signal) {
        this.signal = signal;
    }

    @Override
    public String toString() {
        return "OrderItemInfoVO{" +
                "id=" + id +
                ", isGift=" + isGift +
                ", number=" + number +
                ", productId=" + productId +
                ", commodityId=" + commodityId +
                ", productName='" + productName + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", amount=" + amount +
                ", discountFee=" + discountFee +
                ", currency='" + currency + '\'' +
                ", signal='" + signal + '\'' +
                '}';
    }
}
