package com.insta360.store.admin.controller.commodity.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.commodity.format.AdCommodityTypePack;
import com.insta360.store.admin.controller.commodity.vo.type.AdCommodityTypeVO;
import com.insta360.store.admin.controller.commodity.vo.type.AdSingleCommodityProductVO;
import com.insta360.store.business.commodity.constants.CommodityTypeConstants;
import com.insta360.store.business.commodity.dto.type.BundleCommodityDTO;
import com.insta360.store.business.commodity.dto.type.SingleCommodityDTO;
import com.insta360.store.business.commodity.enums.CommodityTypeEnum;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.BundleCommodityDetail;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.ProductCommodityType;
import com.insta360.store.business.commodity.service.BundleCommodityDetailService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.ProductCommodityTypeService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityTypeHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/6 上午11:19
 */
@RestController
@PermissionResource(code = "commodityType", desc = "套餐类型")
public class AdCommodityTypeApi extends BaseAdminApi {

    @Autowired
    ProductCommodityTypeService productCommodityTypeService;

    @Autowired
    AdCommodityTypePack adCommodityTypePack;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommodityTypeHelper commodityTypeHelper;

    @Autowired
    BundleCommodityDetailService bundleCommodityDetailService;

    /**
     * 获取全部独立商品信息
     *
     * @return
     */
    @LogAttr(desc = "获取全部独立商品信息", logType = LogType.query)
    @Permission(code = "store.commodity.commodityType.listSingleCommodityInfos", desc = "获取全部独立商品信息")
    @GetMapping("/admin/commodity/listSingleCommodityInfos")
    public Response<List<AdSingleCommodityProductVO>> listSingleCommodityInfos() {
        List<ProductCommodityType> singleCommodities = productCommodityTypeService.listByType(CommodityTypeEnum.SINGLE.getType());
        return Response.ok(adCommodityTypePack.packSingleCommodities(singleCommodities));
    }

    /**
     * 查询指定组合商品信息
     *
     * @param commodityId
     * @return
     */
    @LogAttr(desc = "单个组合商品查询接口", logType = LogType.query)
    @Permission(code = "store.commodity.commodityType.getBundleCommodityInfo", desc = "获取指定组合商品信息")
    @GetMapping("/admin/commodity/getBundleCommodityInfo")
    public Response<AdCommodityTypeVO> getBundleCommodityInfo(@RequestParam(value = "commodityId") Integer commodityId) {
        // 校验套餐是否合法
        Commodity commodity = commodityService.getById(commodityId);
        if (commodity == null) {
            throw new InstaException(CommodityErrorCode.CommodityNotFoundException);
        }
        // 未找到商品类型配置，直接返回
        ProductCommodityType productCommodityType = productCommodityTypeService.getByCommodityId(commodityId);
        if (productCommodityType == null) {
            return Response.ok();
        }
        CommodityTypeEnum commodityType = CommodityTypeEnum.parse(productCommodityType.getType());
        if (commodityType == null) {
            throw new InstaException(CommodityErrorCode.InvalidCommodityTypeException);
        }
        // 独立套餐提前返回
        if (CommodityTypeEnum.SINGLE.equals(commodityType)) {
            AdCommodityTypeVO commodityTypeVo = new AdCommodityTypeVO();
            commodityTypeVo.setProductId(commodity.getProduct());
            commodityTypeVo.setCommodityId(commodityId);
            commodityTypeVo.setType(CommodityTypeEnum.SINGLE.name());
            return Response.ok(commodityTypeVo);
        }
        // 组合套餐子套餐信息封装
        return Response.ok(adCommodityTypePack.packBundleCommodityInfo(commodity));
    }

    /**
     * 编辑组合商品
     *
     * @param bundleCommodityParam
     * @return
     */
    @LogAttr(desc = "编辑组合商品信息")
    @Permission(code = "store.commodity.commodityType.updateBundleCommodityInfo", desc = "编辑组合商品信息")
    @PostMapping("/admin/commodity/updateBundleCommodityInfo")
    public Response<Object> updateBundleCommodityInfo(@RequestBody @Validated BundleCommodityDTO bundleCommodityParam) {
        // 校验套餐是否合法
        Commodity commodity = commodityService.getById(bundleCommodityParam.getCommodityId());
        if (commodity == null) {
            throw new InstaException(CommodityErrorCode.CommodityNotFoundException);
        }
        // 校验套餐类型是否合法
        CommodityTypeEnum commodityType = CommodityTypeEnum.parse(bundleCommodityParam.getType());
        if (commodityType == null) {
            throw new InstaException(CommodityErrorCode.InvalidCommodityTypeException);
        }
        // 组合商品未配置子商品异常
        List<SingleCommodityDTO> subCommodities = bundleCommodityParam.getSubCommodities();
        if (CommodityTypeEnum.BUNDLE.equals(commodityType) && CollectionUtils.isEmpty(subCommodities)) {
            throw new InstaException(CommodityErrorCode.MissingSubCommodityException);
        }

        // 子套餐类型校验
        checkSubCommodityType(subCommodities);

        // 子套餐数量校验
        checkSubCommodityQuantity(subCommodities);

        // 考虑编辑为组合商品时，此商品已经是某些组合商品的子商品的情况
        List<BundleCommodityDetail> bundleCommodityDetails = bundleCommodityDetailService.listBySubCommodityId(bundleCommodityParam.getCommodityId());
        if (CommodityTypeEnum.BUNDLE.equals(commodityType) && CollectionUtils.isNotEmpty(bundleCommodityDetails)) {
            throw new InstaException(CommodityErrorCode.AlreadySubCommodityInBundleException.getCode(),
                    String.format(
                            CommodityErrorCode.AlreadySubCommodityInBundleException.getMsg() + ",组合商品id：%s",
                            bundleCommodityDetails.stream().map(BundleCommodityDetail::getCommodityId).collect(Collectors.toList())
                    ));
        }

        commodityTypeHelper.doUpdateCommodityType(bundleCommodityParam);
        return Response.ok();
    }

    /**
     * 校验子套餐类型是否合法
     *
     * @return
     */
    private void checkSubCommodityType(List<SingleCommodityDTO> subCommodities) {
        if (CollectionUtils.isEmpty(subCommodities)) {
            return;
        }
        List<Integer> subCommodityIds = subCommodities.stream().map(SingleCommodityDTO::getSubCommodityId).collect(Collectors.toList());
        // 批量查询类型
        List<ProductCommodityType> productCommodityTypes = productCommodityTypeService.listByCommodityIds(subCommodityIds);
        productCommodityTypes.stream()
                .filter(productCommodityType -> {
                    CommodityTypeEnum commodityType = CommodityTypeEnum.parse(productCommodityType.getType());
                    return !CommodityTypeEnum.SINGLE.equals(commodityType);
                })
                .findFirst()
                .ifPresent(
                        productCommodityType -> {
                            String errorMsg = String.format(CommodityErrorCode.NonSingleCommodityTypeExistException.getMsg() + ": 套餐ID=%d", productCommodityType.getCommodityId());
                            throw new InstaException(CommodityErrorCode.NonSingleCommodityTypeExistException.getCode(), errorMsg);
                        }
                );
    }

    /**
     * 校验子套餐数量是否合法
     *
     * @return
     */
    private void checkSubCommodityQuantity(List<SingleCommodityDTO> subCommodities) {
        if (CollectionUtils.isEmpty(subCommodities)) {
            return;
        }
        subCommodities.stream()
                .filter(singleCommodity -> {
                    Integer number = singleCommodity.getNumber();
                    return number == null ||
                            number < CommodityTypeConstants.SUB_COMMODITY_MIN_NUMBER ||
                            number > CommodityTypeConstants.SUB_COMMODITY_MAX_NUMBER;
                })
                .findFirst()
                .ifPresent(
                        singleCommodity -> {
                            String errorMsg = String.format(CommodityErrorCode.InvalidSubCommodityQuantityException.getMsg() + ": 套餐ID=%d, 数量=%d (合法范围: %d ~ %d)",
                                    singleCommodity.getSubCommodityId(), singleCommodity.getNumber(), CommodityTypeConstants.SUB_COMMODITY_MIN_NUMBER, CommodityTypeConstants.SUB_COMMODITY_MAX_NUMBER);
                            throw new InstaException(CommodityErrorCode.InvalidSubCommodityQuantityException.getCode(), errorMsg);
                        }
                );
    }
}
