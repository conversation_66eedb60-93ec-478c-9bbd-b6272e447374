package com.insta360.store.business.configuration.cache.contancts;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CacheCountiesBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: wbt
 * @Date: 2023/09/13
 * @Description:
 */
public class CacheConstant {

    /**
     * 商城国家语言映射（项目启动时初始化）
     *
     * @Description: initialCapacity = 62（目前商城支持62个国家地区）
     */
    public static final List<CacheCountiesBO> COUNTIES = new ArrayList<>(62);

    /**
     * 以任务数进行分割
     */
    public static final List<List<CacheCountiesBO>> PARTITION_COUNTIES = new ArrayList<>(BaseCachePutHandler.TASK_NUMBER);

    /**
     * 地区对应语言
     */
    public static final Set<InstaLanguage> LANGUAGES = new HashSet<>(9);

    /**
     * 以任务数进行分割
     */
    public static final List<List<InstaLanguage>> PARTITION_LANGUAGES = new ArrayList<>(BaseCachePutHandler.TASK_NUMBER);
}
