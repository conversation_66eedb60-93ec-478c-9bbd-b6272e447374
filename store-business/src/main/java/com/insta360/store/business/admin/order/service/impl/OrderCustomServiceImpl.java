package com.insta360.store.business.admin.order.service.impl;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.admin.order.export.OrderCustomData;
import com.insta360.store.business.admin.order.service.OrderCustomService;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import com.insta360.store.business.trade.model.EngravingImage;
import com.insta360.store.business.trade.service.EngravingImageService;
import com.insta360.store.business.trade.service.impl.helper.bind_service.enums.CustomImageType;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2021/02/25
 * @Description:
 */
@Service
public class OrderCustomServiceImpl implements OrderCustomService {

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    EngravingImageService engravingImageService;

    @Override
    public List<OrderCustomData> listCustomedShellData(List<Order> orders, CustomImageType customImageType) {
        List<OrderCustomData> customDataList = new ArrayList<>(orders.size());
        for (Order order : orders) {
            List<EngravingImage> engravingImageList = engravingImageService.listByOrderNumberAndCustomType(order.getOrderNumber(), customImageType);
            for (EngravingImage engravingImage : engravingImageList) {
                OrderCustomData orderCustomData = new OrderCustomData();
                // 序号
                String serialId = engravingImage.getSerialId();
                serialId = StringUtil.isNotBlank(serialId) ? serialId : engravingImage.getId().toString();
                orderCustomData.setId(serialId);
                orderCustomData.setIconType(engravingImage.getIconColor());
                // 订单号
                orderCustomData.setOrderNumber(order.getOrderNumber());
                orderCustomData.setOrderState(OrderState.parse(order.getState()).getNameZh());
                // 订单项
                OrderItem orderItem = orderItemService.getById(engravingImage.getOrderItemId());
                ProductInfo productInfo = productInfoService.getInfo(orderItem.getProduct(), InstaLanguage.zh_CN);
                CommodityInfo commodityInfo = commodityInfoService.getInfo(orderItem.getCommodity(), InstaLanguage.zh_CN);
                orderCustomData.setOrderItemName(productInfo.getName() + " 【" + commodityInfo.getName() + "】");

                // rma 状态
                RmaOrder rmaOrder = rmaOrderService.getByOrderItem(orderItem.getId());
                if (Objects.nonNull(rmaOrder)){
                    orderCustomData.setRmaType(rmaOrder.rmaType().getNameZh());
                    orderCustomData.setRmaState(rmaOrder.rmaState().getNameZh());
                }

                // 服务定制数量
                Integer number = engravingImage.getCustomNumber();
                number = number == -1 ? orderItem.getNumber() : number;
                orderCustomData.setOrderItemNumber(number.toString());

                // 定制图
                orderCustomData.setProduceImageUrl(engravingImage.getProduceImageUrl());
                orderCustomData.setCustomImageUrl(engravingImage.getCustomerImageUrl());

                // 来源
                orderCustomData.setImageOrigin(engravingImage.parseByOrigin().getDetail());
                orderCustomData.setIconType(engravingImage.parseByIconType());

                customDataList.add(orderCustomData);
            }
        }
        return customDataList;
    }
}
