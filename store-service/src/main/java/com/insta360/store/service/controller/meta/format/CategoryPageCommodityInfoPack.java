package com.insta360.store.service.controller.meta.format;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.insurance.model.ClimbServiceCommodity;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.CategorySceneryCardType;
import com.insta360.store.business.meta.model.*;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.*;
import com.insta360.store.business.meta.service.impl.helper.MetaBatchHelper;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductAdapterType;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import com.insta360.store.business.review.model.ReviewRateCount;
import com.insta360.store.business.review.service.ReviewRateCountService;
import com.insta360.store.service.controller.meta.vo.*;
import com.insta360.store.service.controller.product.vo.CommodityTagInfoVO;
import com.insta360.store.service.controller.review.format.ReviewPack;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: wkx
 * @Date: 2023/11/8
 * @Description:
 */
@Component
public class CategoryPageCommodityInfoPack {

    public static final Logger LOGGER = LoggerFactory.getLogger(CategoryPageCommodityInfoPack.class);

    /**
     * 特殊处理的套餐id：展示sku价格
     */
    private static final List<Integer> COMMODITY_ID_LIST = Lists.newArrayList(1792, 1737, 2125, 1258, 2110,
            2109, 2652, 2653, 2792, 2983, 2982, 2993, 2992, 3392, 3399, 3405, 2972, 3164, 3277, 3628, 3629, 2844, 2882, 3785, 3787, 3945, 3946);

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    MetaBatchHelper metaBatchHelper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ReviewPack reviewPack;

    @Autowired
    ProductCategoryImageTextSelectorService productCategoryImageTextSelectorService;

    @Autowired
    ProductCategoryImageTextInfoService productCategoryImageTextInfoService;

    @Autowired
    ProductCategoryImageTextCommodityService productCategoryImageTextCommodityService;

    @Autowired
    ProductService productService;

    @Autowired
    ProductCategoryTopSortCommodityService productCategoryTopSortCommodityService;

    @Autowired
    ReviewRateCountService reviewRateCountService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Autowired
    ProductCategoryScenerySectionService productCategoryScenerySectionService;

    @Autowired
    ProductCategorySceneryCardMainService productCategorySceneryCardMainService;

    @Autowired
    ProductCategorySceneryCardCommodityInfoService productCategorySceneryCardCommodityInfoService;

    @Autowired
    ProductCategorySceneryCardResourceService productCategorySceneryCardResourceService;

    @Autowired
    SceneryTagInfoService sceneryTagInfoService;

    /**
     * 类目缓存数据封装
     *
     * @param categoryKey
     * @param country
     * @param language
     */
    public CategoryPageVO packCategoryPage(String categoryKey, InstaCountry country, InstaLanguage language) {
        CategoryPageVO categoryPageVo = new CategoryPageVO();
        categoryPageVo.setTopSortCommodityIds(packTopSortCommodityIds(categoryKey));
        categoryPageVo.setSelectorMap(packSelectorInfo(language, country, categoryKey));
        categoryPageVo.setScenerySectionList(packScenerySectionList(language));
        categoryPageVo.setCommodityInfoMap(packCategoryPageCommodityGroup(categoryPageVo.getTopSortCommodityIds(), language, country, categoryKey));
        return categoryPageVo;
    }

    /**
     * 封装场景专区数据
     *
     * @param language
     * @return
     */
    private List<ScenerySectionVO> packScenerySectionList(InstaLanguage language) {
        List<ProductCategoryScenerySection> productCategoryScenerySectionList = productCategoryScenerySectionService.listEnableSection();
        if (CollectionUtils.isEmpty(productCategoryScenerySectionList)) {
            return new ArrayList<>();
        }
        List<Integer> sectionIds = productCategoryScenerySectionList.stream().map(ProductCategoryScenerySection::getId).collect(Collectors.toList());

        List<ProductCategorySceneryCardMain> productCategorySceneryCardMains = productCategorySceneryCardMainService.listBySectionIds(sectionIds);
        if (CollectionUtils.isEmpty(productCategorySceneryCardMains)) {
            return new ArrayList<>();
        }

        Map<Integer, List<ProductCategorySceneryCardMain>> sceneryCardMainMap = productCategorySceneryCardMains.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardMain::getScenerySectionId));
        List<Integer> sceneryCardMainIds = productCategorySceneryCardMains.stream().map(ProductCategorySceneryCardMain::getId).collect(Collectors.toList());

        List<ProductCategorySceneryCardCommodityInfo> productCategorySceneryCardCommodityInfos = productCategorySceneryCardCommodityInfoService.listByCardIdsLanguage(sceneryCardMainIds, language);
        Map<Integer, String> commodityInfoMap = productCategorySceneryCardCommodityInfos.stream().collect(Collectors.toMap(ProductCategorySceneryCardCommodityInfo::getSceneryCardId, ProductCategorySceneryCardCommodityInfo::getCommodityName));

        List<ProductCategorySceneryCardResource> productCategorySceneryCardResources = productCategorySceneryCardResourceService.listByCardIds(sceneryCardMainIds);
        Map<Integer, List<ProductCategorySceneryCardResource>> cardResourceMap = productCategorySceneryCardResources.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardResource::getSceneryCardId));

        List<Integer> sceneryTagIds = productCategoryScenerySectionList.stream().map(ProductCategoryScenerySection::getSceneryTagId).collect(Collectors.toList());
        List<SceneryTagInfo> sceneryTagInfos = sceneryTagInfoService.listByTagIds(sceneryTagIds, language);
        Map<Integer, SceneryTagInfo> sceneryTagInfoMap = sceneryTagInfos.stream().collect(Collectors.toMap(SceneryTagInfo::getSceneryTagId, o -> o));

        return productCategoryScenerySectionList.stream()
                .sorted(Comparator.comparingInt(ProductCategoryScenerySection::getOrderIndex).reversed())
                .map(scenerySection -> {
                    ScenerySectionVO scenerySectionVo = new ScenerySectionVO(scenerySection);
                    Integer sectionId = scenerySection.getId();
                    SceneryTagInfo sceneryTagInfo = sceneryTagInfoMap.get(scenerySection.getSceneryTagId());
                    if (Objects.isNull(sceneryTagInfo)) {
                        LOGGER.info(String.format("[场景专区]场景专区ID:%s,语言:%s,场景标签多语言文案未配置,跳过当前场景专区", sectionId, language.name()));
                        return null;
                    }
                    List<ProductCategorySceneryCardMain> sceneryCardMains = sceneryCardMainMap.get(sectionId);
                    if (CollectionUtils.isEmpty(sceneryCardMains)) {
                        LOGGER.info(String.format("[场景专区]场景专区ID:%s,语言:%s,一个卡片都不存在,跳过当前场景专区", sectionId, language.name()));
                        return null;
                    }
                    Map<String, List<ProductCategorySceneryCardMain>> cardMap = sceneryCardMains.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardMain::getCardType));

                    // 大小卡片
                    List<SceneryCardVO> largeCardList = getCardList(cardMap.get(CategorySceneryCardType.large.name()), commodityInfoMap, cardResourceMap);
                    List<SceneryCardVO> smallCardList = getCardList(cardMap.get(CategorySceneryCardType.small.name()), commodityInfoMap, cardResourceMap);

                    scenerySectionVo.setLargeCardList(largeCardList);
                    scenerySectionVo.setSmallCardList(smallCardList);
                    scenerySectionVo.setSceneryTagName(sceneryTagInfo.getName());

                    return scenerySectionVo;
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取卡片列表
     *
     * @param cardMains
     * @param commodityInfoMap
     * @param cardResourceMap
     * @return
     */
    private List<SceneryCardVO> getCardList(List<ProductCategorySceneryCardMain> cardMains,
                                            Map<Integer, String> commodityInfoMap,
                                            Map<Integer, List<ProductCategorySceneryCardResource>> cardResourceMap) {
        if (CollectionUtils.isEmpty(cardMains)) {
            LOGGER.info("[场景专区]卡片为空");
            return null;
        }

        return cardMains.stream()
                .sorted(Comparator.comparingInt(ProductCategorySceneryCardMain::getOrderIndex).reversed())
                .map(card -> {
                    SceneryCardVO sceneryCardVo = new SceneryCardVO(card);
                    Integer cardId = card.getId();
                    String commodityName = commodityInfoMap.get(cardId);
                    sceneryCardVo.setCommodityName(commodityName);
                    if (Objects.isNull(commodityName)) {
                        LOGGER.info(String.format("[场景专区]套餐名称为空,card:[%s]", card));
                        return null;
                    }

                    // 大卡片增加一层判断
                    List<ProductCategorySceneryCardResource> cardResources = cardResourceMap.get(cardId);
                    if (CategorySceneryCardType.large.name().equals(card.getCardType()) && CollectionUtils.isEmpty(cardResources)) {
                        LOGGER.info(String.format("[场景专区]大卡片素材为空,card:[%s]", card));
                        return null;
                    }
                    if (CollectionUtils.isNotEmpty(cardResources)) {
                        List<SceneryCardResourceVO> cardResourceVos = cardResources.stream().map(SceneryCardResourceVO::new).collect(Collectors.toList());
                        sceneryCardVo.setSceneryCardResourceList(cardResourceVos);
                    }
                    LOGGER.info(String.format("[场景专区]场景专区配置,sceneryCardVo:[%s]", sceneryCardVo));
                    return sceneryCardVo;
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 封装置顶排序套餐
     *
     * @param categoryKey
     * @return
     */
    private List<Integer> packTopSortCommodityIds(String categoryKey) {
        List<ProductCategoryTopSortCommodity> topSortCommodityList = productCategoryTopSortCommodityService.listByCategoryKey(categoryKey);
        return topSortCommodityList.stream()
                .map(ProductCategoryTopSortCommodity::getCommodityId).collect(Collectors.toList());
    }

    /**
     * 封装图文筛选器信息
     *
     * @param language
     * @param country
     * @param categoryKey
     * @return
     */
    private Map<String, List<Integer>> packSelectorInfo(InstaLanguage language, InstaCountry country, String categoryKey) {
        List<ProductCategoryImageTextSelector> imageTextSelectorList = productCategoryImageTextSelectorService.listEnabledByCategoryKey(categoryKey);
        if (CollectionUtils.isEmpty(imageTextSelectorList) && ProductCategoryMainType.CM_ACCESSORY.name().equals(categoryKey)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("国家:[%s]，类目页:[%s]没有配置图文筛选器，请马上处理！", country.name(), categoryKey), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return new HashMap<>(1);
        }

        if (CollectionUtils.isEmpty(imageTextSelectorList) && ProductCategoryMainType.CM_VIRTUAL_SERVICE.name().equals(categoryKey)) {
            LOGGER.info(String.format("国家:[%s],类目页:[%s]没有配置图文筛选器", country.name(), categoryKey));
            return new HashMap<>(1);
        }

        List<Integer> selectorIds = imageTextSelectorList.stream().map(ProductCategoryImageTextSelector::getId).collect(Collectors.toList());
        List<ProductCategoryImageTextInfo> imageTextInfos = productCategoryImageTextInfoService.listBySelectorIdsAndCountry(country, language, selectorIds);
        if (CollectionUtils.isEmpty(imageTextInfos) && ProductCategoryMainType.CM_ACCESSORY.name().equals(categoryKey)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("国家:[%s]，类目页:[%s]图文筛选器【全部没有】配置多语言数据，请马上处理！", country.name(), categoryKey), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return new HashMap<>(1);
        }

        if (CollectionUtils.isEmpty(imageTextInfos) && ProductCategoryMainType.CM_VIRTUAL_SERVICE.name().equals(categoryKey)) {
            LOGGER.info(String.format("国家:[%s],类目页:[%s]图文筛选器[全部没有]配置多语言数据", country.name(), categoryKey));
            return new HashMap<>(1);
        }

        List<Integer> selectorEnableIds = imageTextSelectorList.stream().filter(imageTextSelector -> !imageTextSelector.getDefaultDisplay())
                .map(ProductCategoryImageTextSelector::getId).collect(Collectors.toList());

        Map<Integer, List<ProductCategoryImageTextCommodity>> imageTextCommodityMap = productCategoryImageTextCommodityService.listBySelectorIds(selectorEnableIds)
                .stream().collect(Collectors.groupingBy(ProductCategoryImageTextCommodity::getSelectorId));

        // 类目和套餐映射
        List<String> allCategoryKeys = new ArrayList<>(imageTextSelectorList.size() * 3);
        imageTextSelectorList.forEach(imageTextSelector -> {
            List<ProductCategoryImageTextCommodity> imageTextCommodityList = imageTextCommodityMap.get(imageTextSelector.getId());
            if (CollectionUtils.isEmpty(imageTextCommodityList)) {
                return;
            }
            allCategoryKeys.addAll(imageTextCommodityList.stream().map(ProductCategoryImageTextCommodity::getCategoryKey)
                    .filter(StringUtil::isNotBlank).collect(Collectors.toList()));
        });
        Map<String, List<Product>> productMap = null;
        Map<Integer, Commodity> commodityMap = null;
        if (CollectionUtils.isNotEmpty(allCategoryKeys)) {
            productMap = productService.listProductsByCategoryKeys(allCategoryKeys).stream().collect(Collectors.groupingBy(Product::getCategoryKey));
            if (MapUtils.isNotEmpty(productMap)) {
                List<Integer> productIds = new ArrayList<>(productMap.size() * 8);
                productMap.values().forEach(products -> products.forEach(product -> productIds.add(product.getId())));
                commodityMap = commodityBatchHelper.listByGroupProductIdsEnable(productIds, country)
                        .stream().collect(Collectors.toMap(Commodity::getProduct, c -> c));
            }
        }
        return doPackSelectorInfo(imageTextSelectorList, imageTextCommodityMap, productMap, commodityMap);
    }

    /**
     * 封装类目套餐信息
     *
     * @param topSortCommodityIds
     * @param language
     * @param country
     * @param categoryKey
     * @return
     */
    private Map<String, CategoryPageCommodityVO> packCategoryPageCommodityGroup(List<Integer> topSortCommodityIds
            , InstaLanguage language, InstaCountry country, String categoryKey) {
        // 所有的类目套餐配置信息
        Map<Integer, Product> categoryProductMap = productCategoryHelper.listCategoryProducts(categoryKey, country);

        List<Integer> productIds = new ArrayList<>(categoryProductMap.keySet());

        // view all（对用户展示）：各SPU下第一个启用的SKU+特殊处理过的SKU+置顶套餐+启用的图文筛选器单独配置套餐（整体去重）
        Map<Integer, Commodity> commodityMap = commodityBatchHelper.listByGroupProductIdsEnable(productIds, country)
                .stream().collect(Collectors.toMap(Commodity::getId, c -> c));

        // 图文筛选器
        Set<Integer> imageTextCommodityIds = listImageTextCommodityIds(language, country);
        imageTextCommodityIds.addAll(topSortCommodityIds);
        // 场景专区套餐
        Set<Integer> scenerySectionCommodity = listScenerySectionCommodityIds(language);
        imageTextCommodityIds.addAll(scenerySectionCommodity);

        if (CollectionUtils.isNotEmpty(imageTextCommodityIds)) {
            Map<Integer, Commodity> specialCommodityMap = commodityBatchHelper.commodityMapCommodityIds(new ArrayList<>(imageTextCommodityIds));
            commodityMap.putAll(specialCommodityMap);
        }

        List<Integer> commodityIds = new ArrayList<>(commodityMap.keySet());
        // empty list
        if (CollectionUtils.isEmpty(commodityIds)) {
            return new HashMap<>(1);
        }

        // list to map --配置的套餐相关的信息
        Map<Integer, CommoditySaleState> commoditySaleStateMap = commodityBatchHelper.saleStateMapCommodityIds(commodityIds, country);
        Map<Integer, ClimbServiceCommodity> commodityServiceTypeMap = commodityBatchHelper.serviceTypeMapCommodityIds(commodityIds);
        Map<Integer, CommodityPrice> commodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIds, country);
        Map<Integer, CommodityInfo> commodityInfoMap = commodityBatchHelper.commodityInfoMapCommodityIds(commodityIds, language);
        Map<Integer, ProductInfo> productInfoMap = productBatchHelper.productInfoMapProductIds(productIds, language);

        List<Commodity> commodityList = commodityMap.values().stream()
                // 过滤下架的套餐 和 没有价格的套餐 和没有配置多语言文案
                .filter(commodity -> checkCommodityEnabled(commodity.getId(), commodityMap, categoryProductMap, language))
                .filter(commodity -> checkCommoditySaleState(commodity.getId(), commoditySaleStateMap, commodityPriceMap, country))
                .filter(commodity -> checkCommodityInfo(commodity.getId(), commodityMap, commodityInfoMap, productInfoMap, language))
                .collect(Collectors.toList());

        // empty list
        if (CollectionUtils.isEmpty(commodityList)) {
            return new HashMap<>(1);
        }

        commodityIds = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        // list to map
        Map<Integer, List<CommodityTagBind>> tagBindMap = commodityBatchHelper.commodityTagBindMapCommodityIds(commodityIds);

        // 适配机型
        Map<Integer, List<ProductAdapterType>> productAdapterMap = metaBatchHelper.productAdapterMapByProductIds(productIds);
        List<Integer> adapterTypeIds = new ArrayList<>(productAdapterMap.size() * 4);
        productAdapterMap.values().forEach(adapterTypes -> adapterTypes.forEach(adapterType -> adapterTypeIds.add(adapterType.getAdapterTypeId())));
        Map<Integer, AdapterTypeInfo> adapterTypeInfoMap = metaBatchHelper.adapterTypeMapAdapterTypeIds(adapterTypeIds, language);

        // 场景标签
        Map<Integer, List<SceneryTagProduct>> sceneryTagProductMap = metaBatchHelper.productSceneryTagMapByProductIds(productIds);
        List<Integer> sceneryTagIds = sceneryTagProductMap.values().stream()
                .map(sceneryTagProducts -> sceneryTagProducts.stream().map(SceneryTagProduct::getSceneryTagId).collect(Collectors.toList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        Map<Integer, SceneryTagInfo> sceneryTagInfoMap = metaBatchHelper.sceneryTagInfoMapSceneryTagIds(sceneryTagIds, language);

        // 全部的套餐
        List<Integer> productIdList = commodityList.stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());
        List<Commodity> commodities = commodityService.listByProductIdsEnable(productIdList);
        List<Integer> commodityIdList = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        // list to map
        Map<Integer, CommodityTradeRule> tradeRuleMap = commodityBatchHelper.tradeRuleMapCommodityIds(commodityIdList);
        Map<Integer, Integer> commodityStockMap = commodityBatchHelper.stockCountMapCommodityIds(commodityIdList, country);
        Map<Integer, CommoditySaleState> commoditySaleStateMapAll = commodityBatchHelper.saleStateMapCommodityIds(commodityIdList, country);
        Map<Integer, CommodityPrice> commodityPriceMapAll = commodityBatchHelper.priceMapCommodityIds(commodityIdList, country);
        Map<Integer, CommodityDisplay> commodityDisplayMap = commodityBatchHelper.listCommodityDisplayMap(commodityIds);
        Map<Integer, CommodityFunctionDescription> functionDescriptionMap = commodityBatchHelper.functionDescriptionMapCommodityIds(commodityIds, language);

        // 过滤没有销售状态和价格的套餐
        List<Commodity> commodityListFinal = commodities.stream()
                .filter(commodity -> checkCommoditySaleState(commodity.getId(), commoditySaleStateMapAll, commodityPriceMapAll, country))
                .collect(Collectors.toList());
        Map<Integer, List<Commodity>> productCommodityMap = commodityListFinal.stream().collect(Collectors.groupingBy(Commodity::getProduct));

        // 选择新品标签
        Map<Integer, CommodityTagGroup> newCommodityTagGroupMap = commodityBatchHelper.newTagGroupMapTagIds();
        List<Integer> newTagIds = new ArrayList<>(newCommodityTagGroupMap.keySet());
        Map<Integer, CommodityTagInfo> commodityTagInfoMap = commodityBatchHelper.commodityTagInfoMapIds(newTagIds, language);
        // 评论星级
        Map<Integer, ReviewRateCount> reviewRateCountMap = reviewRateCountService.listByProductIds(productIds)
                .stream().collect(Collectors.toMap(ReviewRateCount::getProductId, r -> r));

        // 数据封装
        Map<String, CategoryPageCommodityVO> categoryPageCommodityMap = new HashMap<>(commodityList.size());
        commodityList.forEach(commodity -> {
            CategoryPageCommodityVO commodityGroupVo = new CategoryPageCommodityVO(commodity);
            // 产品支持的适配类型
            commodityGroupVo.setAdapterTypeNames(packProductAdapterType(commodity.getProduct(), productAdapterMap, adapterTypeInfoMap));

            // 产品支持的适配场景
            commodityGroupVo.setSceneryTagNames(packProductSceneryTag(commodity.getProduct(), sceneryTagProductMap, sceneryTagInfoMap));

            // 套餐-产品内部名称
            Integer productId = commodity.getProduct();
            Product product = categoryProductMap.get(productId);
            commodityGroupVo.setProductInternalName(product.getName());

            // 套餐配置信息
            commodityGroupVo.setItemInfos(packHomeItemCommodityInfo(commodity, categoryProductMap, productInfoMap,
                    commodityInfoMap, commodityDisplayMap, commoditySaleStateMap, functionDescriptionMap, commodityServiceTypeMap));

            // 套餐关联的tag信息-首页只展示新品标签
            CommoditySaleState commoditySaleState = commoditySaleStateMap.get(commodity.getId());
            commodityGroupVo.setTagInfos(packCommodityTagInfo(commodity,
                    tagBindMap, commoditySaleState.getSaleState(), newCommodityTagGroupMap, newTagIds, commodityTagInfoMap));

            // 评论星级
            commodityGroupVo.setReviewRateInfo(reviewPack.doPackReviewRateInfo(productId, reviewRateCountMap));

            // 交易规则
            commodityGroupVo.setCommodityTradeRule(packCommodityTradeRule(commodity.getId(), tradeRuleMap, commodityStockMap));

            // 套餐价格(剔除了没有配置套餐价格和销售状态的套餐)
            List<Commodity> allCommodities = productCommodityMap.get(productId);
            if (CollectionUtils.isEmpty(allCommodities)) {
                return;
            }
            commodityGroupVo.setHomeItemPrice(packCommodityPrice(allCommodities, commodity,
                    commodityStockMap, commoditySaleStateMapAll, commodityPriceMapAll, categoryKey));
            commodityGroupVo.setSkuPrice(packSkuPrice(commodity,
                    commodityStockMap, commoditySaleStateMapAll, commodityPriceMapAll, categoryKey));

            categoryPageCommodityMap.put(String.valueOf(commodity.getId()), commodityGroupVo);
        });
        return categoryPageCommodityMap;
    }

    /**
     * 获取sku的价格
     *
     * @param commodity
     * @param commodityStockMap
     * @param commoditySaleStateMap
     * @param commodityPriceMap
     * @param categoryKey
     * @return
     */
    private HomeItemPriceVO packSkuPrice(Commodity commodity,
                                         Map<Integer, Integer> commodityStockMap, Map<Integer, CommoditySaleState> commoditySaleStateMap,
                                         Map<Integer, CommodityPrice> commodityPriceMap, String categoryKey) {
        // 单个sku的价格
        CommodityPrice commodityPrice = commodityPriceMap.get(commodity.getId());

        // 套餐价格VO
        HomeItemPriceVO homeItemPriceVo = new HomeItemPriceVO();
        homeItemPriceVo.setShowSpuPrice(false);

        // 现价
        homeItemPriceVo.setPrice(commodityPrice.price());
        // 原价（不相等才展示）
        if (!commodityPrice.getAmount().equals(commodityPrice.getOriginAmount())) {
            homeItemPriceVo.setOriginPrice(commodityPrice.originPrice());
        }

        // 库存为0、缺货-原价->现价
        Integer commodityId = commodityPrice.getCommodityId();
        if (Objects.isNull(commodityStockMap.get(commodityId)) || commodityStockMap.get(commodityId) == 0) {
            homeItemPriceVo.setPrice(commodityPrice.originPrice());
            homeItemPriceVo.setOriginPrice(null);
            homeItemPriceVo.setShowOutOfStock(true);
        }

        // 最大折扣力度
        Price saveAmount = getCommoditySaveAmount(Collections.singletonList(commodityPrice));
        if (Objects.isNull(saveAmount)) {
            return homeItemPriceVo;
        }
        homeItemPriceVo.setMaxDiscount(saveAmount);
        return homeItemPriceVo;
    }

    /**
     * 场景专区套餐列表
     *
     * @param language
     * @return
     */
    private Set<Integer> listScenerySectionCommodityIds(InstaLanguage language) {
        List<ProductCategorySceneryCardCommodityInfo> productCategorySceneryCardCommodityInfos = productCategorySceneryCardCommodityInfoService.listByLanguage(language);
        if (CollectionUtils.isEmpty(productCategorySceneryCardCommodityInfos)) {
            return new HashSet<>(0);
        }
        List<Integer> cardIds = productCategorySceneryCardCommodityInfos.stream().map(ProductCategorySceneryCardCommodityInfo::getSceneryCardId).collect(Collectors.toList());

        Collection<ProductCategorySceneryCardMain> productCategorySceneryCardMains = productCategorySceneryCardMainService.listByIds(cardIds);
        if (CollectionUtils.isEmpty(productCategorySceneryCardMains)) {
            return new HashSet<>(0);
        }

        return productCategorySceneryCardMains.stream().map(ProductCategorySceneryCardMain::getCommodityId).collect(Collectors.toSet());
    }

    /**
     * 封装产品场景标签
     *
     * @param productId
     * @param sceneryTagProductMap
     * @param sceneryTagInfoMap
     * @return
     */
    private List<SceneryTagInfoVO> packProductSceneryTag(Integer productId, Map<Integer, List<SceneryTagProduct>> sceneryTagProductMap, Map<Integer, SceneryTagInfo> sceneryTagInfoMap) {
        // 产品支持的场景标签
        List<SceneryTagProduct> sceneryTagProducts = sceneryTagProductMap.get(productId);
        if (CollectionUtils.isEmpty(sceneryTagProducts)) {
            return new ArrayList<>(0);
        }

        // 场景标签列表名称
        return sceneryTagProducts.stream().map(sceneryTagProduct -> {
            // 场景标签文案集合
            SceneryTagInfo sceneryTagInfo = sceneryTagInfoMap.get(sceneryTagProduct.getSceneryTagId());
            if (sceneryTagInfo == null) {
                return null;
            }
            SceneryTagInfoVO sceneryTagInfoVo = new SceneryTagInfoVO();
            sceneryTagInfoVo.setSceneryTagId(sceneryTagProduct.getSceneryTagId());
            sceneryTagInfoVo.setInfoName(sceneryTagInfo.getName());
            return sceneryTagInfoVo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 过滤套餐&产品多语言文案 不予展示
     *
     * @param commodityId
     * @param commodityInfoMap
     * @param productInfoMap
     * @param language
     * @return
     */
    private boolean checkCommodityInfo(Integer commodityId, Map<Integer, Commodity> commodityMap,
                                       Map<Integer, CommodityInfo> commodityInfoMap, Map<Integer, ProductInfo> productInfoMap,
                                       InstaLanguage language) {
        // 获取套餐
        Commodity commodity = commodityMap.get(commodityId);
        // 判断多语言
        if (Objects.isNull(commodityInfoMap.get(commodityId))) {
            String message = "类目套餐配置信息缺失。套餐Id:" + commodity.getId() + ", 语言:" + language;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return false;
        }
        return Objects.nonNull(productInfoMap.get(commodity.getProduct()));
    }


    /**
     * 封装图文筛选器信息
     *
     * @param imageTextSelectorList
     * @param imageTextCommodityMap
     * @param productMap
     * @param commodityMap
     * @return
     */
    private Map<String, List<Integer>> doPackSelectorInfo(List<ProductCategoryImageTextSelector> imageTextSelectorList,
                                                          Map<Integer, List<ProductCategoryImageTextCommodity>> imageTextCommodityMap,
                                                          Map<String, List<Product>> productMap, Map<Integer, Commodity> commodityMap) {
        Map<String, List<Integer>> selectorMap = new HashMap<>(imageTextSelectorList.size());
        imageTextSelectorList.forEach(imageTextSelector -> {
            List<ProductCategoryImageTextCommodity> imageTextCommodityList = imageTextCommodityMap.get(imageTextSelector.getId());
            if (CollectionUtils.isEmpty(imageTextCommodityList)) {
                return;
            }
            Set<Integer> commodityIdsSet = new HashSet<>();
            List<String> categoryKeys = new ArrayList<>();
            imageTextCommodityList.forEach(imageTextCommodity -> {
                if (StringUtil.isNotBlank(imageTextCommodity.getCategoryKey())) {
                    categoryKeys.add(imageTextCommodity.getCategoryKey());
                }

                if (-1 != imageTextCommodity.getCommodityId()) {
                    commodityIdsSet.add(imageTextCommodity.getCommodityId());
                }
            });
            if (MapUtils.isEmpty(productMap) && MapUtils.isEmpty(commodityMap)) {
                selectorMap.put(String.valueOf(imageTextSelector.getId()), new ArrayList<>(commodityIdsSet));
                return;
            }

            // 封装类目对应产品sku
            categoryKeys.forEach(categoryKey -> {
                List<Product> productList = productMap.get(categoryKey);
                if (CollectionUtils.isNotEmpty(productList)) {
                    productList.forEach(product -> {
                        Commodity commodity = commodityMap.get(product.getId());
                        if (Objects.nonNull(commodity)) {
                            commodityIdsSet.add(commodity.getId());
                        }
                    });
                }
            });
            selectorMap.put(String.valueOf(imageTextSelector.getId()), new ArrayList<>(commodityIdsSet));
        });
        return selectorMap;
    }


    /**
     * 启用图文筛选器单独配置套餐列表
     *
     * @param language
     * @param country
     * @return
     */
    private Set<Integer> listImageTextCommodityIds(InstaLanguage language, InstaCountry country) {
        List<ProductCategoryImageTextInfo> imageTextInfos = productCategoryImageTextInfoService.listImageTextInfo(country, language);
        if (CollectionUtils.isEmpty(imageTextInfos)) {
            return new HashSet<>(1);
        }
        List<Integer> selectorIds = imageTextInfos.stream().map(ProductCategoryImageTextInfo::getSelectorId).collect(Collectors.toList());
        List<ProductCategoryImageTextSelector> imageTextSelectorList = productCategoryImageTextSelectorService.listEnabledByIds(selectorIds);

        if (CollectionUtils.isEmpty(imageTextSelectorList)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("%s图文筛选器没有配置数据，请马上处理！", country.name()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return new HashSet<>(1);
        }

        List<Integer> selectorEnableIds = imageTextSelectorList.stream().filter(imageTextSelector -> !imageTextSelector.getDefaultDisplay())
                .map(ProductCategoryImageTextSelector::getId).collect(Collectors.toList());

        Map<Integer, List<ProductCategoryImageTextCommodity>> imageTextCommodityMap = productCategoryImageTextCommodityService.listBySelectorIds(selectorEnableIds)
                .stream().collect(Collectors.groupingBy(ProductCategoryImageTextCommodity::getSelectorId));

        Set<Integer> commodityIdsSet = new HashSet<>();
        imageTextSelectorList.forEach(imageTextSelector -> {
            List<ProductCategoryImageTextCommodity> imageTextCommodityList = imageTextCommodityMap.get(imageTextSelector.getId());
            if (CollectionUtils.isEmpty(imageTextCommodityList)) {
                return;
            }
            imageTextCommodityList.forEach(imageTextCommodity -> {
                if (-1 != imageTextCommodity.getCommodityId()) {
                    commodityIdsSet.add(imageTextCommodity.getCommodityId());
                }
            });
        });
        return commodityIdsSet;
    }

    /**
     * 封装套餐配置信息
     *
     * @param commodity
     * @param productMap
     * @param productInfoMap
     * @param commodityInfoMap
     * @param commodityDisplayMap
     * @param commoditySaleStateMap
     * @param functionDescriptionMap
     * @param commodityServiceTypeMap
     * @return
     */
    private CategoryPageCommodityInfoVO packHomeItemCommodityInfo(Commodity commodity, Map<Integer, Product> productMap,
                                                                  Map<Integer, ProductInfo> productInfoMap, Map<Integer, CommodityInfo> commodityInfoMap,
                                                                  Map<Integer, CommodityDisplay> commodityDisplayMap,
                                                                  Map<Integer, CommoditySaleState> commoditySaleStateMap,
                                                                  Map<Integer, CommodityFunctionDescription> functionDescriptionMap,
                                                                  Map<Integer, ClimbServiceCommodity> commodityServiceTypeMap) {
        // 数据封装
        CategoryPageCommodityInfoVO commodityInfoVo = new CategoryPageCommodityInfoVO();

        // 产品名
        ProductInfo productInfo = productInfoMap.get(commodity.getProduct());
        commodityInfoVo.setProductName(productInfo.getName());

        // 功能描述
        CommodityFunctionDescription functionDescription = functionDescriptionMap.get(commodity.getId());
        if (Objects.nonNull(functionDescription)) {
            commodityInfoVo.setFunctionDescription(functionDescription.getFunctionDescription());
        }

        // 产品跳转链接
        Product product = productMap.get(commodity.getProduct());
        commodityInfoVo.setProductKey(product.getUrlKey());

        // 套餐主图
        commodityInfoVo.setCommodityDisplayImage(packDisplayImage(commodity.getId(), commodityDisplayMap));

        // 套餐名
        CommodityInfo commodityInfo = commodityInfoMap.get(commodity.getId());
        commodityInfoVo.setCommodityName(commodityInfo.getName());
        // 销售状态
        commodityInfoVo.setCommoditySaleState(commoditySaleStateMap.get(commodity.getId()).getSaleState());

        // 增值服务类型
        ClimbServiceCommodity climbServiceCommodity = commodityServiceTypeMap.get(commodity.getId());
        if (climbServiceCommodity != null) {
            commodityInfoVo.setServiceType(climbServiceCommodity.getServiceType());
        }

        return commodityInfoVo;
    }

    /**
     * 封装产品支持的适配类型
     *
     * @param productId
     * @param productAdapterMap
     * @param adapterTypeInfoMap
     * @return
     */
    private List<AdapterTypeInfoVO> packProductAdapterType(Integer productId, Map<Integer,
            List<ProductAdapterType>> productAdapterMap, Map<Integer, AdapterTypeInfo> adapterTypeInfoMap) {
        // 产品支持的适配类型
        List<ProductAdapterType> productAdapterTypes = productAdapterMap.get(productId);
        if (CollectionUtils.isEmpty(productAdapterTypes)) {
            return new ArrayList<>(0);
        }

        // 适配类型列表名称
        return productAdapterTypes.stream().map(productAdapterType -> {
            // 适配类型文案集合
            AdapterTypeInfo adapterTypeInfo = adapterTypeInfoMap.get(productAdapterType.getAdapterTypeId());
            if (adapterTypeInfo == null) {
                return null;
            }
            return new AdapterTypeInfoVO(adapterTypeInfo);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 套餐关联的tag信息
     *
     * @param commodity
     * @param tagBindMap
     * @param commoditySaleState
     * @param newCommodityTagGroupMap 所有启用的新品标签
     * @param commodityTagInfoMap
     * @param newTagIds
     * @return
     */
    private List<CommodityTagInfoVO> packCommodityTagInfo(Commodity commodity, Map<Integer, List<CommodityTagBind>> tagBindMap,
                                                          Integer commoditySaleState, Map<Integer, CommodityTagGroup> newCommodityTagGroupMap,
                                                          List<Integer> newTagIds, Map<Integer, CommodityTagInfo> commodityTagInfoMap) {
        // 没有绑定tag 和 缺货的套餐 则不进行展示
        List<CommodityTagBind> commodityTagBinds = tagBindMap.get(commodity.getId());
        if (CollectionUtils.isEmpty(commodityTagBinds) || SaleState.out_of_stock.equals(SaleState.parse(commoditySaleState))) {
            return new ArrayList<>(0);
        }

        // 只选择新品标签
        List<CommodityTagBind> newCommodityTagBinds = commodityTagBinds.stream()
                .filter(commodityTagBind -> newTagIds.contains(commodityTagBind.getTagId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(newCommodityTagBinds)) {
            return new ArrayList<>(0);
        }

        // 封装tag信息
        return newCommodityTagBinds
                .stream()
                .map(commodityTagBind -> doPackCommodityTagInfo(commodityTagBind, newCommodityTagGroupMap, commodityTagInfoMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 封装套餐主图信息
     *
     * @param commodityId
     * @param commodityDisplayMap
     */
    private String packDisplayImage(Integer commodityId, Map<Integer, CommodityDisplay> commodityDisplayMap) {
        CommodityDisplay commodityDisplay = commodityDisplayMap.get(commodityId);
        if (commodityDisplay == null) {
            FeiShuMessageUtil.storeGeneralMessage("类目套餐主图信息缺失。套餐Id:" + commodityId, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.PY, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return null;
        }
        return commodityDisplay.getUrl();
    }

    /**
     * 封装套餐的价格信息
     *
     * @param allCommodities
     * @param commodity
     * @param commodityStockMap
     * @param commoditySaleStateMap
     * @param commodityPriceMap
     * @return
     */
    private HomeItemPriceVO packCommodityPrice(List<Commodity> allCommodities, Commodity commodity, Map<Integer, Integer> commodityStockMap,
                                               Map<Integer, CommoditySaleState> commoditySaleStateMap,
                                               Map<Integer, CommodityPrice> commodityPriceMap, String categoryKey) {
        List<Integer> commodityIdList = allCommodities.stream().map(Commodity::getId).collect(Collectors.toList());
        boolean showSpuPrice = commodityIdList.size() != 1;

        // todo: 特殊处理套餐列表
        Integer skuCommodityId = commodity.getId();
        //  增值服务类目+特殊处理的id
        if (COMMODITY_ID_LIST.contains(skuCommodityId) || ProductCategoryMainType.CM_VIRTUAL_SERVICE.name().equals(categoryKey)) {
            showSpuPrice = false;
            commodityIdList = Collections.singletonList(skuCommodityId);
        }

        // 销售状态为缺货
        List<Integer> outOfStockStateCommodityIds = commodityIdList
                .stream()
                .filter(commodityId -> SaleState.outOfStockStates().contains(SaleState.parse(commoditySaleStateMap.get(commodityId).getSaleState())))
                .collect(Collectors.toList());

        // 库存为0
        List<Integer> noStockCommodityIds = commodityIdList
                .stream()
                .filter(commodityId -> Objects.isNull(commodityStockMap.get(commodityId)) || commodityStockMap.get(commodityId) == 0)
                .collect(Collectors.toList());

        // 缺货总list
        List<Integer> finalStockCommodityIds = Stream.concat(outOfStockStateCommodityIds.stream(), noStockCommodityIds.stream())
                .distinct()
                .collect(Collectors.toList());

        // 非缺货的总list
        List<Integer> normalCommodityIds = commodityIdList
                .stream()
                .filter(commodityId -> !finalStockCommodityIds.contains(commodityId))
                .collect(Collectors.toList());

        // 单个sku的价格
        CommodityPrice commodityPrice = commodityPriceMap.get(commodity.getId());

        // 是否展示spu的价格
        if (showSpuPrice) {
            // 缺货、即将上架、下架sku
            List<CommodityPrice> outOfStockStateCommodityPrices = finalStockCommodityIds.stream().map(commodityPriceMap::get)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            CommodityPrice priceStock = outOfStockStateCommodityPrices.stream().min(Comparator.comparing(CommodityPrice::getOriginAmount)).orElse(null);

            // 日常sku
            List<CommodityPrice> normalCommodityPrices = normalCommodityIds.stream().map(commodityPriceMap::get)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            CommodityPrice priceNormal = normalCommodityPrices.stream().min(Comparator.comparing(CommodityPrice::getAmount)).orElse(null);

            if (Objects.nonNull(priceStock) && Objects.nonNull(priceNormal)) {
                commodityPrice = priceStock.getOriginAmount() >= priceNormal.getAmount() ? priceNormal : priceStock;
            } else {
                commodityPrice = Objects.isNull(priceNormal) ? priceStock : priceNormal;
            }
        }

        // 套餐价格VO
        HomeItemPriceVO homeItemPriceVo = new HomeItemPriceVO();

        // 控制卡片价格展示
        homeItemPriceVo.setShowSpuPrice(showSpuPrice);

        // 现价
        homeItemPriceVo.setPrice(commodityPrice.price());
        // 原价（不相等才展示）
        if (!commodityPrice.getAmount().equals(commodityPrice.getOriginAmount())) {
            homeItemPriceVo.setOriginPrice(commodityPrice.originPrice());
        }

        // 库存为0、缺货-原价->现价
        if (finalStockCommodityIds.contains(commodityPrice.getCommodityId())) {
            homeItemPriceVo.setPrice(commodityPrice.originPrice());
            homeItemPriceVo.setOriginPrice(null);
        }

        // 全部的sku都缺货、下架、即将上架、库存为0
        if (finalStockCommodityIds.size() == commodityIdList.size()) {
            homeItemPriceVo.setShowOutOfStock(true);
            //（缺货套餐不予展示折扣信息）
            return homeItemPriceVo;
        }
        commodityIdList.removeAll(finalStockCommodityIds);
        List<CommodityPrice> commodityPrices = commodityIdList.stream().map(commodityPriceMap::get).filter(Objects::nonNull).collect(Collectors.toList());

        // 最大折扣力度
        Price saveAmount = getCommoditySaveAmount(commodityPrices);
        if (Objects.isNull(saveAmount)) {
            return homeItemPriceVo;
        }
        homeItemPriceVo.setMaxDiscount(saveAmount);
        return homeItemPriceVo;
    }

    /**
     * 套餐禁用 && 产品禁用  不予展示
     *
     * @param commodityId
     * @param commodityMap
     * @param productMap
     * @return
     */
    private Boolean checkCommodityEnabled(Integer commodityId, Map<Integer, Commodity> commodityMap, Map<Integer
            , Product> productMap, InstaLanguage language) {
        Commodity commodity = commodityMap.get(commodityId);
        if (commodity == null) {
            LOGGER.info("类目套餐配置信息缺失。套餐Id:" + commodityId + ", 语言:" + language);
            return false;
        }

        Product product = productMap.get(commodity.getProduct());
        if (product == null) {
            LOGGER.info("类目产品配置信息缺失。产品Id:" + commodity.getProduct() + ", 语言:" + language);
            return false;
        }
        return true;
    }

    /**
     * 套餐下架 && 没有价格 不予展示
     * 销售状态不存在
     *
     * @param commodityId
     * @param saleStateMap
     * @param priceMap
     * @param country
     * @return
     */
    private Boolean checkCommoditySaleState(Integer commodityId, Map<Integer, CommoditySaleState> saleStateMap, Map<Integer, CommodityPrice> priceMap, InstaCountry country) {
        CommoditySaleState saleState = saleStateMap.get(commodityId);
        if (saleState == null) {
            LOGGER.info("类目销售状态配置信息缺失。套餐Id:" + commodityId);
            return false;
        }

        // 过滤掉下架的套餐和没有价格的套餐
        CommodityPrice commodityPrice = priceMap.get(commodityId);
        if (SaleState.remove.getCode() != saleState.getSaleState() && commodityPrice != null) {
            return true;
        }

        if (SaleState.remove.getCode() != saleState.getSaleState() && commodityPrice == null) {
            String message = "类目套餐价格未找到。套餐id：" + commodityId + ", 地区：" + country.name();
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
        }
        return false;
    }

    /**
     * 获取套餐最大折扣价格（单个套餐的节省金 = 原价 - 现价）
     *
     * @param commodityPrices
     * @return
     */
    public Price getCommoditySaveAmount(List<CommodityPrice> commodityPrices) {
        List<Price> priceList = commodityPrices.stream().map(commodityPrice -> {
            // 总的赠品折扣
            BigDecimal originAmount = new BigDecimal(String.valueOf(commodityPrice.getOriginAmount()));
            BigDecimal amount = new BigDecimal(String.valueOf(commodityPrice.getAmount()));
            BigDecimal giftAllSaveAmount = originAmount.subtract(amount);
            return new Price(commodityPrice.currency(), formatFloat(giftAllSaveAmount.floatValue()));
        }).collect(Collectors.toList());

        Price price = priceList.stream().max(Comparator.comparing(Price::getAmount)).orElse(null);
        if (price != null && price.getAmount() != 0) {
            return price;
        }
        return null;
    }

    /**
     * 格式化
     *
     * @param amount
     * @return
     */
    private static Float formatFloat(Float amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return Float.valueOf(decimalFormat.format(amount));
    }

    /**
     * 套餐交易规则
     *
     * @param commodityId
     * @param tradeRuleMap
     * @return
     */
    private HomeItemCommodityTradeRuleVO packCommodityTradeRule(Integer commodityId, Map<Integer, CommodityTradeRule> tradeRuleMap, Map<Integer, Integer> commodityStockMap) {
        Integer stockCount = commodityStockMap.get(commodityId);
        CommodityTradeRule commodityTradeRule = tradeRuleMap.get(commodityId);
        HomeItemCommodityTradeRuleVO homeItemCommodityTradeRuleVO = Objects.nonNull(commodityTradeRule) ?
                new HomeItemCommodityTradeRuleVO(commodityTradeRule) : new HomeItemCommodityTradeRuleVO();
        homeItemCommodityTradeRuleVO.setStockCount(stockCount != null ? stockCount : 0);
        return homeItemCommodityTradeRuleVO;
    }

    /**
     * 封装套餐的tag信息
     *
     * @param commodityTagBind
     * @param tagGroupMap
     * @param tagInfoMap
     * @return
     */
    private CommodityTagInfoVO doPackCommodityTagInfo(CommodityTagBind commodityTagBind, Map<Integer, CommodityTagGroup> tagGroupMap,
                                                      Map<Integer, CommodityTagInfo> tagInfoMap) {
        CommodityTagGroup commodityTagGroup = tagGroupMap.get(commodityTagBind.getTagId());
        CommodityTagInfo tagInfo = tagInfoMap.get(commodityTagBind.getTagId());

        // tag分组、info信息要真实有效
        if (Objects.isNull(commodityTagGroup) || Objects.isNull(tagInfo)) {
            return null;
        }

        // 数据转换
        CommodityTagInfoVO commodityTagInfoVo = new CommodityTagInfoVO(tagInfo);
        commodityTagInfoVo.setTagGroupType(commodityTagGroup.getGroupType());
        commodityTagInfoVo.setFontColor(commodityTagGroup.getFontColor());
        commodityTagInfoVo.setFontHoverColor(commodityTagGroup.getFontHoverColor());
        commodityTagInfoVo.setBackGroundColor(commodityTagGroup.getBackGroundColor());
        commodityTagInfoVo.setHoverBackGroundColor(commodityTagGroup.getHoverBackGroundColor());
        return commodityTagInfoVo;
    }
}
