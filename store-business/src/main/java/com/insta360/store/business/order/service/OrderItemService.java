package com.insta360.store.business.order.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.order.bo.OrderItemStockBO;
import com.insta360.store.business.order.model.OrderItem;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
public interface OrderItemService extends BaseService<OrderItem> {

    /**
     * 获取订单的所有子项
     *
     * @param orderId
     * @return
     */
    List<OrderItem> getByOrder(Integer orderId);

    /**
     * 获取订单的所有子项
     *
     * @param orderNumber
     * @return
     */
    List<OrderItem> getByOrderNumber(String orderNumber);

    /**
     * 获取订单某个产品的子项
     *
     * @param orderId
     * @param productId
     * @return
     */
    List<OrderItem> getByProduct(Integer orderId, Integer productId);

    /**
     * 获取订单某个套餐的子项
     *
     * @param orderId
     * @param commodityId
     * @return
     */
    List<OrderItem> getByCommodity(Integer orderId, Integer commodityId);

    /**
     * 获取订单的某项礼品
     *
     * @param orderId
     * @param commodityId
     * @return
     */
    OrderItem getGift(Integer orderId, Integer commodityId);

    /**
     * 设置为已关闭
     *
     * @param orderItemId
     */
    void close(Integer orderItemId);

    /**
     * 设置为成功
     *
     * @param orderItemId
     */
    void setSuccess(Integer orderItemId);

    /**
     * 设置为已评论
     *
     * @param orderItem
     */
    void setAlreadyReview(OrderItem orderItem);

    /**
     * 退货许可
     *
     * @param orderItemId
     * @return
     */
    boolean checkAllowRefund(Integer orderItemId);

    /**
     * 退换货许可
     *
     * @param orderItemId
     * @return
     */
    boolean checkAllowReturnOrChange(Integer orderItemId);

    /**
     * 子项已发货
     *
     * @param orderItemId
     */
    void shipOrderItem(Integer orderItemId);

    /**
     * 删除订单赠品
     *
     * @param orderItem
     */
    void deleteGiftItem(OrderItem orderItem);

    /**
     * 通过订单ID查询对应订单商品信息(非赠品)
     *
     * @param orderId
     * @return
     */
    List<OrderItem> getOrderItemsByOrderId(Integer orderId);

    /**
     * 根据订单ids批量查询
     *
     * @param orderIds
     * @return
     */
    List<OrderItem> listByOrderIds(List<Integer> orderIds);

    /**
     * 通过订单ID批量查询订单商品
     *
     * @param orderIds
     * @return
     */
    List<OrderItem> getOrderItemByOrderIds(List<Integer> orderIds);

    /**
     * 指定产品、订单查询对应订单
     *
     * @param productIdList
     * @param orderIdList
     * @return
     */
    List<Integer> getOrderIdByProductAndOrder(List<Integer> productIdList, List<Integer> orderIdList);

    /**
     * 通过id 更新item
     *
     * @param item
     */
    void updateItemById(Object item);

    /**
     * 通过订单id 和产品id查询
     *
     * @param orderIdList
     * @param productId
     * @return
     */
    List<OrderItem> listByOrderIdsAndProductId(List<Integer> orderIdList, Integer productId);

    /**
     * 通过订单ID查询对应订单商品信息（包括赠品）
     *
     * @param orderId 订单id
     * @return {@link List}<{@link OrderItem}>
     */
    List<OrderItem> listOrderItemsByOrderId(Integer orderId);

    /**
     * 根据套餐ID查询预占用库存数
     *
     * @param commodityIds
     * @param orderStates
     * @param fromTime
     * @param endTime
     * @param country
     * @return
     */
    List<OrderItemStockBO> listOrderItemStockByCommodityIds(List<Integer> commodityIds, List<Integer> orderStates, LocalDateTime fromTime, LocalDateTime endTime, InstaCountry country);

    /**
     * 通过产品ID查询
     *
     * @param x5Id
     * @return
     */
    List<OrderItem> listByProduct(Integer x5Id);
}
