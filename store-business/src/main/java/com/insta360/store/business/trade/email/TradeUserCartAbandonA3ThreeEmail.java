package com.insta360.store.business.trade.email;

import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 用户架构忘记下单提醒邮件
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Scope("prototype")
@Component
public class TradeUserCartAbandonA3ThreeEmail extends BaseTradeEmail {

    /**
     * 邮件模版名称
     */
    protected String templateName;

    @Override
    public String getTemplateName() {
        return getUserCartEmailEnum().getTemplateKey();
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {


    }


}
