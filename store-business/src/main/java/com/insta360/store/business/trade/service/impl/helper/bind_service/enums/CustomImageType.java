package com.insta360.store.business.trade.service.impl.helper.bind_service.enums;

import com.insta360.store.business.common.constants.RedisKeyConstant;

/**
 * 定制贴类型
 *
 * <AUTHOR>
 * @Date 2022/12/29 17:15
 * @Version 1.0
 */
public enum CustomImageType {

    /**
     * go2
     */
    customed_shell(
            "GO2",
            "G",
            "go2",
            11812,
            2598,
            424,
            145,
            330,
            1,
            6,
            2,
            6*2,
            1230,
            800,
            1,
            1,
            "png",
            1,
            1,
            ""),

    /**
     * FMG 序号居中坐标为：x 坐标为：116,y 坐标为：369
     */
    fmg_customed_shell(
            "FMG",
            "F",
            "fmg",
            4725,
            3544,
            241,
            712,
            282,
            517,
            8,
            2,
            8 * 2,
            304,
            890,
            50,
            36,
            "png",
            52,
            65,
            "classpath:static/image/FMG_datum_img.png"),

    /**
     * go3 定制贴边距不统一，所以不在枚举中定义，设为 0
     */
    go3_customed_shell(
            "GO3",
            "T",
            "go3",
            2363,
            4725,
            0,
            255,
            287,
            255,
            4,
            5,
            4 * 5,
            280,
            620,
            50,
            36,
            "png",
            30,
            80,
            "classpath:static/image/GO3_datum_img.png"),

    /**
     * go3S 坐标序号 和GO3一致
     */
    go3s_customed_shell(
            "GO3S",
            "TC2-",
            "go3s",
                2363,
                4725,
                0,
                255,
                287,
                255,
                4,
                5,
                4 * 5,
                280,
                620,
                50,
                36,
                "png",
                30,
                80,
                "classpath:static/image/GO3S_datum_img.png"),
    ;

    CustomImageType(String name,
                    String serialPrefix,
                    String redisKeyPrefix,
                    int mergeImageWidth,
                    int mergeImageHeight,
                    int lateralImageDistance,
                    int endianImageDistance,
                    int leftSizeDistance,
                    int topSizeDistance,
                    int lineImageSize,
                    int rowImageSize,
                    int pageSize,
                    int imageWidth,
                    int imageHeight,
                    int serialNumberPaddingHorizontal,
                    int serialNumberSize,
                    String imageSuffix,
                    int serialNumberOfCoordinatesX,
                    int serialNumberOfCoordinatesY,
                    String datumImagePath) {
        this.name = name;
        this.serialPrefix = serialPrefix;
        this.redisKeyPrefix = redisKeyPrefix;
        this.mergeImageWidth = mergeImageWidth;
        this.mergeImageHeight = mergeImageHeight;
        this.lateralImageDistance = lateralImageDistance;
        this.endianImageDistance = endianImageDistance;
        this.leftSizeDistance = leftSizeDistance;
        this.topSizeDistance = topSizeDistance;
        this.lineImageSize = lineImageSize;
        this.rowImageSize = rowImageSize;
        this.pageSize = pageSize;
        this.imageWidth = imageWidth;
        this.imageHeight = imageHeight;
        this.serialNumberPaddingHorizontal = serialNumberPaddingHorizontal;
        this.serialNumberSize = serialNumberSize;
        this.imageSuffix = imageSuffix;
        this.serialNumberOfCoordinatesX = serialNumberOfCoordinatesX;
        this.serialNumberOfCoordinatesY = serialNumberOfCoordinatesY;
        this.datumImagePath = datumImagePath;
    }

    /**
     * 自定义的产品类型名
     */
    private final String name;

    /**
     * 自定义的产品类型名
     */
    private final String serialPrefix;

    /**
     * 自定义的产品类型名
     */
    private final String redisKeyPrefix;

    /**
     * 合成图片宽度
     */
    private final int mergeImageWidth;

    /**
     * 合成图片高度
     */
    private final int mergeImageHeight;

    /**
     * 图片之间的横向距离
     */
    private final int lateralImageDistance;

    /**
     * 图片之间的纵向距离
     */
    private final int endianImageDistance;

    /**
     * 最左边边距
     */
    private final int leftSizeDistance;

    /**
     * 顶部边距
     */
    private final int topSizeDistance;

    /**
     * 一行几张图片
     */
    private final int lineImageSize;

    /**
     * 一列几张图片
     */
    private final int rowImageSize;

    /**
     * 每张合成图片包含的图片
     */
    private final int pageSize;

    /**
     * 单张图片的宽度
     */
    private final int imageWidth;

    /**
     * 单张图片的高度
     */
    private final int imageHeight;

    /**
     * 序号的左右内边距
     */
    private final int serialNumberPaddingHorizontal;

    /**
     * 序号字体大小
     */
    private final int serialNumberSize;

    /**
     * 图片后缀格式
     */
    private final String imageSuffix;

    /**
     * 序号坐标x
     */
    private final int serialNumberOfCoordinatesX;

    /**
     * 序号坐标y
     */
    private final int serialNumberOfCoordinatesY;

    /**
     * 基准图像路径
     */
    private final String datumImagePath;

    /**
     * 解析
     *
     * @param customImageTypeName 定制 服务类型
     * @return {@link CustomImageType }
     */
    public static CustomImageType parse(String customImageTypeName) {
        try {
            return valueOf(customImageTypeName);
        } catch (Exception e) {
            return null;
        }
    }

    public String getSerialPrefix() {
        return serialPrefix;
    }

    public int getSerialNumberOfCoordinatesX() {
        return serialNumberOfCoordinatesX;
    }

    public int getSerialNumberOfCoordinatesY() {
        return serialNumberOfCoordinatesY;
    }

    public String getName() {
        return name;
    }

    public int getMergeImageWidth() {
        return mergeImageWidth;
    }

    public int getMergeImageHeight() {
        return mergeImageHeight;
    }

    public int getLateralImageDistance() {
        return lateralImageDistance;
    }

    public int getEndianImageDistance() {
        return endianImageDistance;
    }

    public int getLeftSizeDistance() {
        return leftSizeDistance;
    }

    public int getTopSizeDistance() {
        return topSizeDistance;
    }

    public int getLineImageSize() {
        return lineImageSize;
    }

    public int getRowImageSize() {
        return rowImageSize;
    }

    public int getPageSize() {
        return pageSize;
    }

    public int getImageWidth() {
        return imageWidth;
    }

    public int getImageHeight() {
        return imageHeight;
    }

    public int getSerialNumberSize() {
        return serialNumberSize;
    }

    public int getSerialNumberPaddingHorizontal() {
        return serialNumberPaddingHorizontal;
    }

    public String getImageSuffix() {
        return imageSuffix;
    }

    public String getDatumImagePath() {
        return datumImagePath;
    }

    public String getRedisKeyPrefix() {
        return redisKeyPrefix;
    }

    @Override
    public String toString() {
        return "CustomImageType{" +
                "serialPrefix='" + serialPrefix + '\'' +
                ", endianImageDistance=" + endianImageDistance +
                ", name='" + name + '\'' +
                ", mergeImageWidth=" + mergeImageWidth +
                ", mergeImageHeight=" + mergeImageHeight +
                ", lateralImageDistance=" + lateralImageDistance +
                ", leftSizeDistance=" + leftSizeDistance +
                ", topSizeDistance=" + topSizeDistance +
                ", lineImageSize=" + lineImageSize +
                ", rowImageSize=" + rowImageSize +
                ", pageSize=" + pageSize +
                ", imageWidth=" + imageWidth +
                ", imageHeight=" + imageHeight +
                ", serialNumberPaddingHorizontal=" + serialNumberPaddingHorizontal +
                ", serialNumberSize=" + serialNumberSize +
                ", imageSuffix='" + imageSuffix + '\'' +
                ", serialNumberOfCoordinatesX=" + serialNumberOfCoordinatesX +
                ", serialNumberOfCoordinatesY=" + serialNumberOfCoordinatesY +
                ", datumImagePath='" + datumImagePath + '\'' +
                '}';
    }

    /**
     * 获取定制贴的redis key
     *
     * @return {@link String}
     */
    public String getSerialIdRedisKey() {
        return RedisKeyConstant.CUSTOM_IMAGE_KEY + "serial_id:" + this.redisKeyPrefix;
    }
}
