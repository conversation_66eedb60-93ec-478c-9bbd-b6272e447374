package com.insta360.store.business.configuration.cache.monitor.redis.put.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.CachePutService;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.commodity.*;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.faq.FaqCategoryCachePutHandler;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.faq.FaqOtherCachePutHandler;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta.*;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.product.*;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.review.ReviewInfoCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.exception.CommonErrorCode;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2023/09/13
 * @Description:
 */
@Component
public class CachePutHandlerFactory {

    /**
     * 获取对应的缓存类型
     *
     * @param cachePutType
     * @return
     */
    public CachePutService getCachePutService(String cachePutType) {
        switch (cachePutType) {
            case CachePutType.TOP_BAR:
                return ApplicationContextHolder.getApplicationContext().getBean(TopBarCachePutHandler.class);
            case CachePutType.PRODUCT_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductInfoCachePutHandler.class);
            case CachePutType.TRADE_POINT:
                return ApplicationContextHolder.getApplicationContext().getBean(TradePointCachePutHandler.class);
            case CachePutType.HOME_PAGE_KEY:
                return ApplicationContextHolder.getApplicationContext().getBean(HomepageCachePutHandler.class);
            case CachePutType.NAVIGATION_BAR_CATEGORY_KEY:
                return ApplicationContextHolder.getApplicationContext().getBean(NavigationBarCachePutHandler.class);
            case CachePutType.OVERVIEW_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductOverviewCachePutHandler.class);
            case CachePutType.PRODUCT_ACCESSORY_COMPATIBILITY:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductAccessoryCompatibilityCachePutHandler.class);
            case CachePutType.PRODUCT_ADAPTER_TYPE:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductAdapterTypeCachePutHandler.class);
            case CachePutType.PRODUCT_FAQ:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductFaqCachePutHandler.class);
            case CachePutType.PRODUCT_PACK_LIST:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductPackListCachePutHandler.class);
            case CachePutType.LIVE_BROADCAST_PAGE:
                return ApplicationContextHolder.getApplicationContext().getBean(LiveBroadcastCachePutHandler.class);
            case CachePutType.SEO_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(SeoCachePutHandler.class);
            case CachePutType.COMMODITY_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityInfoCachePutHandler.class);
            case CachePutType.COMMODITY_PRICE:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityPriceCachePutHandler.class);
            case CachePutType.COMMODITY_PRICE_BATCH:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityPriceBatchCachePutHandler.class);
            case CachePutType.COMMODITY_SALE_STATE:
                return ApplicationContextHolder.getApplicationContext().getBean(CommoditySaleStateCachePutHandler.class);
            case CachePutType.COMMODITY_DELIVERY_TIME:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityDeliveryTimeCachePutHandler.class);
            case CachePutType.COMMODITY_DISPLAY:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityDisplayCachePutHandler.class);
            case CachePutType.COMMODITY_FUNCTION_DESCRIPTION:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityFunctionDescriptionCachePutHandler.class);
            case CachePutType.COMMODITY_RECOMMENDATION:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityRecommendationCachePutHandler.class);
            case CachePutType.COMMODITY_TAG:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityTagCachePutHandler.class);
            case CachePutType.FAQ_QUESTION:
                return ApplicationContextHolder.getApplicationContext().getBean(FaqCategoryCachePutHandler.class);
            case CachePutType.FAQ_OTHER_QUESTION:
                return ApplicationContextHolder.getApplicationContext().getBean(FaqOtherCachePutHandler.class);
            case CachePutType.ADAPTER_TYPE_MAIN:
                return ApplicationContextHolder.getApplicationContext().getBean(AdapterTypeCachePutHandler.class);
            case CachePutType.ADAPTER_TYPE_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(AdapterTypeInfoCachePutHandler.class);
            case CachePutType.AVALARA_TAX_PROVINCE:
                return ApplicationContextHolder.getApplicationContext().getBean(AvalaraTaxCachePutHandler.class);
            case CachePutType.GRAPHIC_NAVIGATION:
                return ApplicationContextHolder.getApplicationContext().getBean(GraphicNavigationCachePutHandler.class);
            case CachePutType.HOME_ITEM_KEY:
                return ApplicationContextHolder.getApplicationContext().getBean(MetaHomeItemCachePutHandler.class);
            case CachePutType.HOME_ITEM_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(MetaHomeItemInfoCachePutHandler.class);
            case CachePutType.CATEGORY_PAGE_FILTER:
                return ApplicationContextHolder.getApplicationContext().getBean(CategoryPageFilterCachePutHandler.class);
            case CachePutType.CATEGORY_PAGE:
                return ApplicationContextHolder.getApplicationContext().getBean(CategoryPageCachePutHandler.class);
            case CachePutType.REVIEW_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(ReviewInfoCachePutHandler.class);
            case CachePutType.BANNER_INFO:
                return ApplicationContextHolder.getApplicationContext().getBean(BannerCachePutHandler.class);
            case CachePutType.PRODUCT_VIDEO:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductVideoCachePutHandler.class);
            case CachePutType.META_SHIPPING_COST:
                return ApplicationContextHolder.getApplicationContext().getBean(MetaShippingCostPutHandler.class);
            case CachePutType.PRODUCT_MAIN_VIDEO:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductMainVideoCachePutHandler.class);
            case CachePutType.SCENERY_TAG_MAIN:
                return ApplicationContextHolder.getApplicationContext().getBean(SceneryTagMainCachePutHandler.class);
            case CachePutType.CLIMB_SERVICE_COMMODITY:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityBindServiceCachePutHandler.class);
            case CachePutType.SCENERY_SECTION:
                return ApplicationContextHolder.getApplicationContext().getBean(ScenerySectionCachePutHandler.class);
            default:
                throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
    }
}
