package com.insta360.store.business.reseller.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.reseller.dao.ResellerGiftConfigNewDao;
import com.insta360.store.business.reseller.enums.ResellerGiftModuleType;
import com.insta360.store.business.reseller.model.ResellerGiftConfigNew;
import com.insta360.store.business.reseller.service.ResellerGiftConfigNewService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-25
 * @Description:
 */
@Service
public class ResellerGiftConfigNewServiceImpl extends BaseServiceImpl<ResellerGiftConfigNewDao, ResellerGiftConfigNew> implements ResellerGiftConfigNewService {

    @Override
    public List<ResellerGiftConfigNew> listByPromoCode(String resellerCode) {
        if (StringUtils.isBlank(resellerCode)){
            return new ArrayList<>(0);
        }
        QueryWrapper<ResellerGiftConfigNew> qw = new QueryWrapper<>();
        qw.eq("promo_code", resellerCode);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ResellerGiftConfigNew> listByResellerGiftConfig(Integer productId, List<Integer> commodityIdList, List<Integer> resellerIds) {
        if (Objects.isNull(productId)
                && CollectionUtils.isEmpty(commodityIdList)
                && CollectionUtils.isEmpty(resellerIds)) {
            return null;
        }
        QueryWrapper<ResellerGiftConfigNew> qw = new QueryWrapper<>();
        qw.eq(Objects.nonNull(productId), "product_id", productId);
        qw.in(CollectionUtils.isNotEmpty(commodityIdList), "commodity_id", commodityIdList);
        qw.in(CollectionUtils.isNotEmpty(resellerIds), "reseller_auto_id", resellerIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ResellerGiftConfigNew> getConfigByReseller(Integer productId, List<Integer> resellerIds, Integer defaultGiftMark) {
        if (Objects.isNull(productId) || CollectionUtils.isEmpty(resellerIds) || Objects.isNull(defaultGiftMark)) {
            return null;
        }
        QueryWrapper<ResellerGiftConfigNew> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        qw.in("reseller_auto_id", resellerIds);
        qw.eq("default_gift_mark", defaultGiftMark);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ResellerGiftConfigNew> getDefaultConfig(Integer productId, List<Integer> commodityIdList, Integer defaultGiftMark) {
        if (Objects.isNull(productId) || CollectionUtils.isEmpty(commodityIdList) || Objects.isNull(defaultGiftMark)) {
            return null;
        }
        QueryWrapper<ResellerGiftConfigNew> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        qw.eq("reseller_auto_id", -1);
        qw.in("commodity_id", commodityIdList);
        qw.eq("default_gift_mark", defaultGiftMark);
        return baseMapper.selectList(qw);
    }

    @Override
    public boolean batchSaveResellerGiftConfig(List<ResellerGiftConfigNew> resellerGiftConfigList, ResellerGiftModuleType moduleType) {
        if (CollectionUtils.isEmpty(resellerGiftConfigList) || Objects.isNull(moduleType)) {
            return false;
        }
        switch (moduleType) {
            case DEFAULT:
                baseMapper.batchSaveDefault(resellerGiftConfigList);
                break;
            case CUSTOM_MADE:
                baseMapper.batchSaveSpecial(resellerGiftConfigList);
                break;
            default:
                return false;
        }
        return true;
    }
}
