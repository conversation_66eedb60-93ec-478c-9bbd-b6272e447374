package com.insta360.store.business.rma.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/11/19
 * @Description:
 */
public class RmaOrderQueryDTO implements Serializable {

    /**
     * 当前页
     */
    private Integer pageNumber;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 售后单号
     */
    private String rmaNumber;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 售后类型
     *
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    private String rmaType;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 国家/地区二字码
     *
     * @see com.insta360.compass.core.enums.InstaCountry
     */
    private String countryCode;

    /**
     * 售后单申请时间-开始
     */
    private Long from;

    /**
     * 售后单申请时间-截止
     */
    private Long end;

    /**
     * 售后单完成时间-开始
     */
    private Long finishFrom;

    /**
     * 售后单完成时间-截止
     */
    private Long finishEnd;

    /**
     * 售后单状态集合
     *
     * @see com.insta360.store.business.rma.enums.RmaState
     */
    private List<Integer> states;

    /**
     * 是否需退回
     */
    private Boolean needReturn;

    /**
     * 仅退款-选项文案类型
     */
    private String negotiationOptionsKey;

    /**
     * 用户售后原因
     */
    private String customerRefundReason;

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Long getFrom() {
        return from;
    }

    public void setFrom(Long from) {
        this.from = from;
    }

    public Long getEnd() {
        return end;
    }

    public void setEnd(Long end) {
        this.end = end;
    }

    public Long getFinishFrom() {
        return finishFrom;
    }

    public void setFinishFrom(Long finishFrom) {
        this.finishFrom = finishFrom;
    }

    public Long getFinishEnd() {
        return finishEnd;
    }

    public void setFinishEnd(Long finishEnd) {
        this.finishEnd = finishEnd;
    }

    public List<Integer> getStates() {
        return states;
    }

    public void setStates(List<Integer> states) {
        this.states = states;
    }

    public Boolean getNeedReturn() {
        return needReturn;
    }

    public void setNeedReturn(Boolean needReturn) {
        this.needReturn = needReturn;
    }

    public String getNegotiationOptionsKey() {
        return negotiationOptionsKey;
    }

    public void setNegotiationOptionsKey(String negotiationOptionsKey) {
        this.negotiationOptionsKey = negotiationOptionsKey;
    }

    public String getCustomerRefundReason() {
        return customerRefundReason;
    }

    public void setCustomerRefundReason(String customerRefundReason) {
        this.customerRefundReason = customerRefundReason;
    }

    @Override
    public String toString() {
        return "RmaOrderQueryDTO{" +
                "pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", rmaNumber='" + rmaNumber + '\'' +
                ", userEmail='" + userEmail + '\'' +
                ", rmaType='" + rmaType + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", from=" + from +
                ", end=" + end +
                ", finishFrom=" + finishFrom +
                ", finishEnd=" + finishEnd +
                ", states=" + states +
                ", needReturn=" + needReturn +
                ", negotiationOptionsKey='" + negotiationOptionsKey + '\'' +
                ", customerRefundReason='" + customerRefundReason + '\'' +
                '}';
    }
}
