package com.insta360.store.admin.controller.test.wxq.helper;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.compass.libs.aliyun.oss.enums.EndpointEnum;
import com.insta360.compass.libs.aliyun.oss.enums.ModuleEnum;
import com.insta360.store.admin.controller.test.wxq.dto.CommissionRecalculationDTO;
import com.insta360.store.admin.controller.test.wxq.dto.CustomResellerCodeDTO;
import com.insta360.store.admin.controller.test.wxq.dto.NewResellerProductDTO;
import com.insta360.store.admin.controller.test.wxq.enums.ResellerHandlerType;
import com.insta360.store.business.discount.utils.MathUtil;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.model.MetaCurrency;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.MetaCurrencyService;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.reseller.helper.ResellerSendMessageHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.reseller.bo.CommissionDetailBO;
import com.insta360.store.business.reseller.enums.ResellerOrderState;
import com.insta360.store.business.reseller.enums.ResellerType;
import com.insta360.store.business.reseller.model.*;
import com.insta360.store.business.reseller.service.*;
import com.insta360.store.business.reseller.service.impl.helper.ResellerOrderHelper;
import com.insta360.store.business.reseller.service.impl.helper.commission.ResellerCommissionCalculateContext;
import com.insta360.store.business.reseller.service.impl.helper.commission.ResellerOrderCommissionCounterHelper;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6
 */
@Component
public class ResellerTestHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResellerTestHelper.class);

    @Autowired
    OSSService ossService;

    @Autowired
    OrderService orderService;

    @Autowired
    ProductService productService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    ResellerOrderHelper resellerOrderHelper;

    @Autowired
    MetaCurrencyService metaCurrencyService;

    @Autowired
    ResellerInfoService resellerInfoService;

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    ResellerApplyService resellerApplyService;

    @Autowired
    ResellerProductService resellerProductService;

    @Autowired
    ResellerWithdrawService resellerWithdrawService;

    @Autowired
    ResellerUserInfoService resellerUserInfoService;

    @Autowired
    ResellerUtmSourceService resellerUtmSourceService;

    @Autowired
    ResellerPromoLinkService resellerPromoLinkService;

    @Autowired
    ResellerOrderItemService resellerOrderItemService;

    @Autowired
    ResellerSendMessageHelper resellerSendMessageHelper;

    @Autowired
    ResellerGiftConfigNewService resellerGiftConfigNewService;

    @Autowired
    ResellerCommissionRateService resellerCommissionRateService;

    @Autowired
    ResellerWithdrawAccountService resellerWithdrawAccountService;

    @Autowired
    ResellerOrderCommissionCounterHelper resellerOrderCommissionCounterHelper;

    /**
     * 路径
     */
    @Value("${path.commodityPriceCalculate.excel:/data/calculate/excel}")
    private String path;

    public static <T> void ifPresent(T value, Consumer<T> consumer) {
        if (value != null) {
            consumer.accept(value);
        }
    }

    public static void isNotEmpty(List<Integer> value, Consumer<List<Integer>> consumer, String name, String uuid) {
        LOGGER.info("uuid:{} name:{} value:{} ", uuid, name, value);
        if (CollectionUtils.isNotEmpty(value)) {
            consumer.accept(value);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createNewProductReseller(NewResellerProductDTO newResellerProductDto) {
        ResellerProduct resellerProduct = new ResellerProduct();
        resellerProduct.setProductId(newResellerProductDto.getProductId());
        resellerProduct.setState(newResellerProductDto.getState() ? 1 : 0);
        resellerProduct.setCreateTime(LocalDateTime.now());
        resellerProductService.save(resellerProduct);

        ResellerPromoLink afterLink = resellerPromoLinkService.getResellerPromoLink(newResellerProductDto.getProductAfterId(), newResellerProductDto.getPromotionLinkType());

        Product product = productService.getById(newResellerProductDto.getProductId());
        ResellerPromoLink resellerPromoLink = new ResellerPromoLink();
        resellerPromoLink.setType(newResellerProductDto.getPromotionLinkType().name());
        int orderIndex = afterLink.getOrderIndex() + 1;

        // 先更改原有的链接顺序
        List<ResellerPromoLink> greaterLinkList = resellerPromoLinkService.listGreaterByOrderIndex(orderIndex);
        if (CollectionUtils.isNotEmpty(greaterLinkList)) {
            List<ResellerPromoLink> updateOrderIndexList = greaterLinkList.stream().peek(greaterLink -> greaterLink.setOrderIndex(greaterLink.getOrderIndex() + 1)).collect(Collectors.toList());
            resellerPromoLinkService.updateBatchById(updateOrderIndexList);
        }

        // 保存链接
        resellerPromoLink.setOrderIndex(orderIndex);
        resellerPromoLink.setWxappUrl("/pages/product/product?id=" + product.getUrlKey());
        resellerPromoLink.setUrl("https://www.insta360.com/sal/" + product.getUrlKey());
        resellerPromoLink.setRelatedProduct(newResellerProductDto.getProductId());
        resellerPromoLinkService.save(resellerPromoLink);

        ResellerCommissionRate resellerCommissionRate = new ResellerCommissionRate();
        resellerCommissionRate.setResellerAutoId(0);
        resellerCommissionRate.setProductId(newResellerProductDto.getProductId());
        resellerCommissionRate.setCommissionRate(newResellerProductDto.getCommissionRate().doubleValue());
        resellerCommissionRate.setFixed(newResellerProductDto.getFixed());
        resellerCommissionRateService.save(resellerCommissionRate);
    }

    @Transactional(rollbackFor = Exception.class)
    public void createResellerOrder(String orderNumber, String resellerCode) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            return;
        }
        order.setPromoCode(resellerCode);
        orderService.updateById(order);

        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (!OrderPaymentState.PAYED.equals(orderPayment.paymentState())) {
            return;
        }
        resellerSendMessageHelper.sendCreateResellerOrderMsg(order, orderPayment.getPayTime());

    }

    /**
     * 重置分销订单提现
     *
     * @param orderNumberList 订单编号集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetResellerOrderWithdraw(List<String> orderNumberList) {
        List<ResellerOrder> resellerOrders = resellerOrderService.listByOrderNumbers(orderNumberList);
        List<ResellerOrder> resetResellerOrders = resellerOrders.stream()
                .map(resellerOrder -> {
                    ResellerOrder newResellerOrder = new ResellerOrder();
                    BeanUtils.copyProperties(resellerOrder, newResellerOrder);
                    newResellerOrder.setState(ResellerOrderState.withdraw.getCode());
                    return newResellerOrder;
                }).collect(Collectors.toList());
        resellerOrderService.updateBatchById(resetResellerOrders);

        List<Integer> resellerOrderIds = resetResellerOrders.stream().map(ResellerOrder::getId).collect(Collectors.toList());
        resellerWithdrawService.removeByResellerOrderIds(resellerOrderIds);

    }

    /**
     * 佣金重新计算 只允许改为USD
     *
     * @param resellerCommissionUpdateParam 分销佣金更新param
     */
    @Transactional(rollbackFor = Exception.class)
    public void commissionRecalculation(CommissionRecalculationDTO resellerCommissionUpdateParam) {
        String uuid = UUIDUtils.generateUuid();

        LOGGER.info("{} commissionRecalculation start data {}", uuid, resellerCommissionUpdateParam);
        // 前置参数校验
        Reseller reseller = resellerService.getByPromoCode(resellerCommissionUpdateParam.getPromoCode());
        if (Objects.isNull(reseller)) {
            throw new InstaException(-1, "分销商不存在");
        }

        // 根据分销码获取分销商的所有订单
        List<ResellerOrder> resellerOrderList = resellerOrderService.getByPromoCode(resellerCommissionUpdateParam.getPromoCode());

        // 1. 分销订单 进行佣金重计算前置操作
        prepositionResellerOrderCommissionRecalculation(resellerCommissionUpdateParam, uuid, resellerOrderList, reseller);

        // 2. 对历史分销订单 进行佣金重计算
        List<ResellerOrder> newResellerOrderList = oldResellerOrderCommissionRecalculation(resellerCommissionUpdateParam, uuid, resellerOrderList, reseller);

        // 3. 对新分销订单 走原有分销订单佣金重计算流程
        newResellerOrderCommissionRecalculation(uuid, newResellerOrderList, reseller);
        LOGGER.info("{} commissionRecalculation update end", uuid);

        FeiShuMessageUtil.storeGeneralMessage(String.format("uuid:%s 分销佣金重计算-计算完成，共%s单.", uuid, resellerOrderList.size()), FeiShuGroupRobot.ResellerSpecific);
    }

    /**
     * 新分销订单计算
     *
     * @param uuid
     * @param resellerOrderList 分销订单集合
     * @param reseller          分销
     */
    @Transactional(rollbackFor = Exception.class)
    public void newResellerOrderCommissionRecalculation(String uuid, List<ResellerOrder> resellerOrderList, Reseller reseller) {
        LOGGER.info("{} 新分销订单计算 {}", uuid, JSON.toJSONString(resellerOrderList));
        if (CollectionUtils.isEmpty(resellerOrderList)) {
            return;
        }
        List<ResellerOrder> updateResellerOrderList = Lists.newArrayListWithCapacity(resellerOrderList.size());
        List<ResellerOrderItem> updateResellerOrderItemList = Lists.newArrayList();

        for (ResellerOrder resellerOrder : resellerOrderList) {
            // 获取订单商品列表
            List<ResellerOrderItem> resellerOrderItemList = resellerOrderItemService.getByOrder(resellerOrder.getId());
            LOGGER.info("{} 获取订单商品列表 {}", uuid, JSON.toJSONString(resellerOrderItemList));
            if (CollectionUtils.isEmpty(resellerOrderItemList)) {
                continue;
            }

            // 订单提现币种
            Currency withdrawCurrency = resellerOrder.useCurrency();

            resellerOrderItemList.stream()
                    .forEach(resellerOrderItem -> {
                        // 售后单
                        RmaOrder rmaOrder = rmaOrderService.getByOrderItem(resellerOrderItem.getOrderItemId());
                        // 订单商品
                        OrderItem orderItem = orderItemService.getById(resellerOrderItem.getOrderItemId());

                        // 订单商品佣金重计算
                        ResellerCommissionCalculateContext context = ResellerCommissionCalculateContext.buildContext(reseller, orderItem, rmaOrder, withdrawCurrency, resellerOrderItem.getInitCommissionRate(), resellerOrder.getPayCurrencyUsdRate(), resellerOrder.getWithdrawCurrencyUsdRate(), false);
                        CommissionDetailBO commissionDetail = resellerOrderCommissionCounterHelper.orderItemCommissionInitCalculate(context);
                        resellerOrderItem.setEstimatedIncome(commissionDetail.getEstimatedIncome());
                    });

            // 订单佣金总收益
            BigDecimal totalEstimatedIncome = resellerOrderItemList.stream()
                    .map(resellerOrderItem -> new BigDecimal(String.valueOf(resellerOrderItem.getEstimatedIncome())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            LOGGER.info("{} 订单号 {} 订单佣金总收益 {}", uuid, resellerOrder.getOrderNumber(), totalEstimatedIncome);
            resellerOrder.setCommission(totalEstimatedIncome.doubleValue());

            updateResellerOrderList.add(resellerOrder);
            updateResellerOrderItemList.addAll(resellerOrderItemList);
        }

        LOGGER.info("{} [新分销订单佣金重计算] 获取订单订单列表 {} 订单子项 {}", uuid, JSON.toJSONString(updateResellerOrderList), JSON.toJSONString(updateResellerOrderItemList));
        if (CollectionUtils.isNotEmpty(updateResellerOrderList) && CollectionUtils.isNotEmpty(updateResellerOrderItemList)) {
            // 更新DB
            resellerOrderHelper.batchUpdateResellerOrder(updateResellerOrderList, updateResellerOrderItemList);
        }

    }

    /**
     * 云服务 分销订单计算
     *
     * @param uuid
     * @param resellerOrderList 分销订单集合
     * @param reseller          分销
     */
    @Transactional(rollbackFor = Exception.class)
    public void cloudResellerOrderCommissionRecalculation(String uuid, List<ResellerOrder> resellerOrderList, Reseller reseller) {
        LOGGER.info("{} 新分销订单计算 {}", uuid, JSON.toJSONString(resellerOrderList));
        if (CollectionUtils.isEmpty(resellerOrderList)) {
            return;
        }
        List<ResellerOrder> updateResellerOrderList = Lists.newArrayListWithCapacity(resellerOrderList.size());
        List<ResellerOrderItem> updateResellerOrderItemList = Lists.newArrayList();

        for (ResellerOrder resellerOrder : resellerOrderList) {
            // 获取订单商品列表
            List<ResellerOrderItem> resellerOrderItemList = resellerOrderItemService.getByOrder(resellerOrder.getId());
            LOGGER.info("{} 获取订单商品列表 {}", uuid, JSON.toJSONString(resellerOrderItemList));
            if (CollectionUtils.isEmpty(resellerOrderItemList)) {
                continue;
            }

            // 订单提现币种
            Currency withdrawCurrency = resellerOrder.useCurrency();

            resellerOrderItemList.stream()
                    .forEach(resellerOrderItem -> {
                        // 售后单
                        RmaOrder rmaOrder = rmaOrderService.getByOrderItem(resellerOrderItem.getOrderItemId());
                        // 订单商品
                        OrderItem orderItem = orderItemService.getById(resellerOrderItem.getOrderItemId());

                        // 订单商品佣金重计算
                        ResellerCommissionCalculateContext context = ResellerCommissionCalculateContext.buildContext(reseller, orderItem, rmaOrder, withdrawCurrency, resellerOrderItem.getInitCommissionRate(), resellerOrder.getPayCurrencyUsdRate(), resellerOrder.getWithdrawCurrencyUsdRate(), true);
                        CommissionDetailBO commissionDetail = resellerOrderCommissionCounterHelper.orderItemCommissionInitCalculate(context);

                        // 如果是云服务的则重新设置 佣金率 和 佣金
                        if (Product.CLOUD_ID.equals(resellerOrderItem.getProductId())) {
                            resellerOrderItem.setEstimatedIncome(commissionDetail.getEstimatedIncome());
                            resellerOrderItem.setInitCommissionRate(commissionDetail.getCommissionRate());
                        }
                    });

            // 订单佣金总收益
            BigDecimal totalEstimatedIncome = resellerOrderItemList.stream()
                    .map(resellerOrderItem -> new BigDecimal(String.valueOf(resellerOrderItem.getEstimatedIncome())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            LOGGER.info("{} 订单号 {} 订单佣金总收益 {}", uuid, resellerOrder.getOrderNumber(), totalEstimatedIncome);
            // 如果是不可提现,且原佣金为0才进入订单状态修改判断
            if (ResellerOrderState.close.getCode().equals(resellerOrder.getState()) && resellerOrder.getCommission() == 0) {
                // 佣金大于0 重置为未可提现, 小于等于0 设置为不可提现(原有状态)
                Integer state = totalEstimatedIncome.compareTo(BigDecimal.ZERO) > 0 ? ResellerOrderState.payed.getCode() : ResellerOrderState.close.getCode();
                resellerOrder.setState(state);
            }
            resellerOrder.setCommission(totalEstimatedIncome.doubleValue());
            updateResellerOrderList.add(resellerOrder);
            updateResellerOrderItemList.addAll(resellerOrderItemList);
        }

        LOGGER.info("{} [新分销订单佣金重计算] 获取订单订单列表 {} 订单子项 {}", uuid, JSON.toJSONString(updateResellerOrderList), JSON.toJSONString(updateResellerOrderItemList));
        if (CollectionUtils.isNotEmpty(updateResellerOrderList) && CollectionUtils.isNotEmpty(updateResellerOrderItemList)) {
            // 更新DB
            resellerOrderHelper.batchUpdateResellerOrder(updateResellerOrderList, updateResellerOrderItemList);
        }

    }

    /**
     * 分销订单重计算信息
     *
     * @param uuid
     * @param reseller
     * @param resellerOrderList
     * @return
     */
    public String resellerOrderCalculationInfo(String uuid, Reseller reseller, List<ResellerOrder> resellerOrderList) {
        String title = String.format("uuid:%s 分销码:%s 分销商邮箱:%s  订单总数%s\n", uuid, reseller.getPromoCode(), reseller.getEmail(), resellerOrderList.size());
        StringBuilder resellerOrderCalculationInfo = new StringBuilder(title);
        Map<String, ResellerOrder> reselelrOrdermap = resellerOrderList.stream().collect(Collectors.toMap(ResellerOrder::getOrderNumber, Function.identity()));
        List<ResellerOrder> reCaculateOrderList = resellerOrderService.listByOrderNumbers(new ArrayList<>(reselelrOrdermap.keySet()));
        Map<String, ResellerOrder> reResellerOrderMap = reCaculateOrderList.stream().collect(Collectors.toMap(ResellerOrder::getOrderNumber, Function.identity()));
        for (Map.Entry<String, ResellerOrder> resellerOrderEntry : reselelrOrdermap.entrySet()) {
            String orderNumber = resellerOrderEntry.getKey();
            ResellerOrder resellerOrder = resellerOrderEntry.getValue();
            ResellerOrder reResellerOrder = reResellerOrderMap.get(orderNumber);
            String format = String.format("订单号:[%s] 原佣金:[%s] 新佣金:[%s] 原状态:[%s] 新状态:[%s] 币种:[%s]",
                    orderNumber,
                    resellerOrder.getCommission(),
                    reResellerOrder.getCommission(),
                    ResellerOrderState.parse(resellerOrder.getState()).getValue(),
                    ResellerOrderState.parse(reResellerOrder.getState()).getValue(),
                    resellerOrder.getCurrency());
            resellerOrderCalculationInfo.append(format).append("\n");
        }

        return resellerOrderCalculationInfo.toString();

    }

    /**
     * 定制分销code
     *
     * @param customResellerCodeParam 定制分销法典参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void customResellerCode(CustomResellerCodeDTO customResellerCodeParam) {
        String updateInfo = "定制分销码";

        // 修改 用户分销码
        Reseller reseller = resellerService.getByPromoCode(customResellerCodeParam.getOriginCode());
        reseller.setPromoCode(customResellerCodeParam.getTargetCode());
        reseller.setUpdateTime(LocalDateTime.now());
        resellerService.updateResellerById(reseller);

        // 修改 分销订单
        List<ResellerOrder> resellerOrders = resellerOrderService.getByPromoCode(customResellerCodeParam.getOriginCode());
        if (CollectionUtils.isNotEmpty(resellerOrders)) {
            LOGGER.info("修改分销订单:{}", resellerOrders);
            updateInfo += "修改分销订单：" + resellerOrders.size() + "\n";
            List<ResellerOrder> newResellerOrderList = resellerOrders.stream().map(item -> {
                ResellerOrder resellerOrder = new ResellerOrder();
                BeanUtils.copyProperties(item, resellerOrder);
                resellerOrder.setPromoCode(customResellerCodeParam.getTargetCode());
                return resellerOrder;
            }).collect(Collectors.toList());
            resellerOrderService.updateBatchById(newResellerOrderList);
        }

        // 修改 赠品配置
        List<ResellerGiftConfigNew> resellerGiftConfigNewList = resellerGiftConfigNewService.listByPromoCode(customResellerCodeParam.getOriginCode());
        if (CollectionUtils.isNotEmpty(resellerGiftConfigNewList)) {
            LOGGER.info("修改赠品配置:{}", resellerGiftConfigNewList);
            updateInfo += "修改赠品配置：" + resellerGiftConfigNewList.size() + "\n";
            List<ResellerGiftConfigNew> resellerGiftConfigNews = resellerGiftConfigNewList.stream().map(item -> {
                ResellerGiftConfigNew resellerGiftConfigNew = new ResellerGiftConfigNew();
                BeanUtils.copyProperties(item, resellerGiftConfigNew);
                resellerGiftConfigNew.setPromoCode(customResellerCodeParam.getTargetCode());
                return resellerGiftConfigNew;
            }).collect(Collectors.toList());
            resellerGiftConfigNewService.updateBatchById(resellerGiftConfigNews);
        }

        List<Order> orders = orderService.getByPromoCode(customResellerCodeParam.getOriginCode());
        if (CollectionUtils.isNotEmpty(orders)) {
            LOGGER.info("修改订单:{}", orders);
            updateInfo += "修改订单：" + resellerGiftConfigNewList.size() + "\n";
            orders.forEach(item -> item.setPromoCode(customResellerCodeParam.getTargetCode()));
            orderService.updateBatchById(orders);
        }

        List<ResellerUtmSource> resellerUtmSourceList = resellerUtmSourceService.listByPromoCode(customResellerCodeParam.getOriginCode());
        if (CollectionUtils.isNotEmpty(resellerUtmSourceList)) {
            LOGGER.info("修改utm_source:{}", resellerUtmSourceList);
            updateInfo += "修改utm_source：" + resellerUtmSourceList.size() + "\n";
            resellerUtmSourceList.forEach(item -> item.setPromoCode(customResellerCodeParam.getTargetCode()));
            resellerUtmSourceService.updateBatchById(resellerUtmSourceList);
        }

        FeiShuMessageUtil.storeGeneralMessage(String.format("修改分销码：原分销码：%s -> 新分销码：%s\n %s", customResellerCodeParam.getOriginCode(), customResellerCodeParam.getTargetCode(), updateInfo), FeiShuGroupRobot.ResellerSpecific);

    }

    /**
     * 违规用户清理
     *
     * @param promoCode
     * @param resellerHandlerType
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String cancelReseller(String promoCode, ResellerHandlerType resellerHandlerType) {
        if (!resellerHandlerType.getCode().startsWith("Cancel")) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "操作类型错误" + resellerHandlerType);
        }
        String uuid = UUIDUtils.generateUuid();
        Reseller reseller = resellerService.getByPromoCode(promoCode);
        if (reseller == null) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "分销商不存在");
        }
        List<ResellerOrder> resellerOrderList = resellerOrderService.getByPromoCode(promoCode);
        List<Integer> orderIds = resellerOrderList.stream().map(ResellerOrder::getId).collect(Collectors.toList());

        List<ResellerGiftConfigNew> resellerGiftConfigNewList = resellerGiftConfigNewService.listByPromoCode(promoCode);
        List<ResellerWithdrawRecord> resellerWithdrawRecordList = resellerWithdrawService.listByOrderIds(orderIds);

        List<ResellerWithdrawAccount> resellerWithdrawAccountList = resellerWithdrawAccountService.getByResellerAutoId(reseller.getId());

        ResellerInfo resellerInfo = resellerInfoService.getByResellerAutoId(reseller.getId());

        ResellerUserInfo resellerUserInfo = resellerUserInfoService.getUserInfoByResellerAutoId(reseller.getId());

        ResellerApply resellerApply = resellerApplyService.getByUserId(reseller.getUserId(), ResellerType.individual);
        List<ResellerApply> resellerApplyList = Objects.nonNull(resellerApply) ? Lists.newArrayList(resellerApply) : null;

        List<ResellerOrderItem> resellerOrderItemList = resellerOrderItemService.listByOrderIds(orderIds);

        // 将查出来的所有数据写入Excel中
        File targetFile = createExcelFile();

        try (FileOutputStream fos = new FileOutputStream(targetFile);
             ExcelWriter writer = EasyExcel.write(fos).build()) {

            // 写入订单
            writer.write(resellerOrderList, EasyExcel.writerSheet("ResellerOrder").head(ResellerOrder.class).build());

            // 写入赠品配置
            writer.write(resellerGiftConfigNewList, EasyExcel.writerSheet("ResellerGiftConfigNew").head(ResellerGiftConfigNew.class).build());

            // 写入提现记录
            writer.write(resellerWithdrawRecordList, EasyExcel.writerSheet("ResellerWithdrawRecord").head(ResellerWithdrawRecord.class).build());

            // 写入提现账户
            writer.write(resellerWithdrawAccountList, EasyExcel.writerSheet("ResellerWithdrawAccount").head(ResellerWithdrawAccount.class).build());

            // 写入订单项
            writer.write(resellerOrderItemList, EasyExcel.writerSheet("ResellerOrderItem").head(ResellerOrderItem.class).build());

            // 写入分销商信息
            writer.write(Collections.singletonList(resellerInfo), EasyExcel.writerSheet("ResellerInfo").head(ResellerInfo.class).build());

            // 写入分销商用户信息
            writer.write(Collections.singletonList(resellerUserInfo), EasyExcel.writerSheet("ResellerUserInfo").head(ResellerUserInfo.class).build());

            // 写入申请信息
            writer.write(resellerApplyList, EasyExcel.writerSheet("ResellerApply").head(ResellerApply.class).build());

        } catch (FileNotFoundException e) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("uuid:%s 操作失败[%s]", uuid, resellerHandlerType.getDesc()), FeiShuGroupRobot.ResellerSpecific);
            throw new InstaException(CommonErrorCode.InvalidParameter, String.format("操作失败[%s]", resellerHandlerType.getDesc()));
        } catch (IOException e) {
            // 处理涉及 IO 操作的可能异常
            FeiShuMessageUtil.storeGeneralMessage(String.format("uuid:%s 操作失败[%s] 文件写入过程发生异常", uuid, resellerHandlerType.getDesc()), FeiShuGroupRobot.ResellerSpecific);
            throw new InstaException(CommonErrorCode.InvalidParameter, "文件写入过程发生异常");
        }

        // 删除分销商信息
        ifPresent(reseller.getId(), resellerService::deleteResellerById);

        // 删除分销订单
        List<Integer> resellerOrderIds = resellerOrderList.stream().map(ResellerOrder::getId).collect(Collectors.toList());
        isNotEmpty(resellerOrderIds, resellerOrderService::removeByIds, "删除分销订单", uuid);

        // 删除赠品信息
        List<Integer> resellerGiftConfigIds = resellerGiftConfigNewList.stream().map(ResellerGiftConfigNew::getId).collect(Collectors.toList());
        isNotEmpty(resellerGiftConfigIds, resellerGiftConfigNewService::removeByIds, "删除赠品信息", uuid);

        // 删除分销商提现记录
        List<Integer> resellerWithdrawRecordIds = resellerWithdrawRecordList.stream().map(ResellerWithdrawRecord::getId).collect(Collectors.toList());
        isNotEmpty(resellerWithdrawRecordIds, resellerWithdrawService::removeByIds, "删除分销商提现记录", uuid);

        // 删除分销商提现账户
        List<Integer> resellerWithdrawAccountIds = resellerWithdrawAccountList.stream().map(ResellerWithdrawAccount::getId).collect(Collectors.toList());
        isNotEmpty(resellerWithdrawAccountIds, resellerWithdrawAccountService::removeByIds, "删除分销商提现账户", uuid);

        // 删除分销商订单项
        List<Integer> resellerOrderItemIds = resellerOrderItemList.stream().map(ResellerOrderItem::getId).collect(Collectors.toList());
        isNotEmpty(resellerOrderItemIds, resellerOrderItemService::removeByIds, "删除分销商订单项", uuid);

        // 删除分销商信息
        ifPresent(resellerInfo, info -> resellerInfoService.deleteByResellerId(info.getResellerAutoId()));

        // 删除分销商用户信息
        ifPresent(resellerUserInfo, info -> resellerUserInfoService.removeById(info.getId()));

        if (ResellerHandlerType.CancelNormalReseller.equals(resellerHandlerType)) {
            // 删除分销商申请信息
            List<Integer> resellerApplyIds = resellerApplyList.stream().map(ResellerApply::getId).collect(Collectors.toList());
            isNotEmpty(resellerApplyIds, resellerApplyService::removeByIds, "删除分销商申请信息", uuid);
        }

        String ossFileUrl = ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, targetFile);
        FeiShuMessageUtil.storeGeneralMessage(String.format("uuid:%s 操作[%s] url:%s", uuid, resellerHandlerType.getDesc(), ossFileUrl), FeiShuGroupRobot.ResellerSpecific);
        FileUtil.del(targetFile);
        return ossFileUrl;

    }

    /**
     * 创建excel文件
     *
     * @return {@link File}
     */
    public File createExcelFile() {
        if (!FileUtil.exist(path)) {
            FileUtil.mkdir(path);
        }

        // 任务类型+任务ID-任务名称-导出人-创建时间（年月日）
        String fileName = String.format("提现账户信息-%s-%s.xlsx", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")), UUIDUtils.generateUuid());
        return new File(path + fileName);
    }

    /**
     * 分销订单 进行佣金重计算前置操作
     *
     * @param resellerCommissionUpdateParam
     * @param uuid
     * @param resellerOrderList
     * @param reseller
     */
    private void prepositionResellerOrderCommissionRecalculation(CommissionRecalculationDTO resellerCommissionUpdateParam, String uuid, List<ResellerOrder> resellerOrderList, Reseller reseller) {
        LOGGER.info("{} commissionRecalculation resellerOrderList  {}", uuid, JSON.toJSONString(resellerOrderList));
        if (CollectionUtils.isEmpty(resellerOrderList)) {
            throw new InstaException(-1, "订单不存在 或 部分订单不存在");
        }

        // 校验有没有已经打款的订单
        List<ResellerOrder> successResellerOrderList = resellerOrderService.getByPromoCodeAndState(resellerCommissionUpdateParam.getPromoCode(), ResellerOrderState.success);
        if (CollectionUtils.isNotEmpty(successResellerOrderList) && resellerCommissionUpdateParam.getCheck()) {
            throw new InstaException(-1, "存在已经打款的订单");
        }

        if (CollectionUtils.isEmpty(successResellerOrderList) && !resellerCommissionUpdateParam.getCheck()) {
            // 将查出来的所有数据写入Excel中
            File targetFile = createExcelFile();
            try (FileOutputStream fos = new FileOutputStream(targetFile);
                 ExcelWriter writer = EasyExcel.write(fos).build()) {

                // 写入订单
                writer.write(resellerOrderList, EasyExcel.writerSheet("ResellerOrder").head(ResellerOrder.class).build());
                List<Integer> resellerOrderIds = resellerOrderList.stream().map(ResellerOrder::getId).collect(Collectors.toList());

                // 写入订单子项
                List<ResellerOrderItem> resellerOrderItemList = resellerOrderItemService.listByOrderIds(resellerOrderIds);
                writer.write(resellerOrderItemList, EasyExcel.writerSheet("ResellerOrderItem").head(ResellerOrderItem.class).build());

                List<ResellerWithdrawRecord> resellerWithdrawRecords = resellerWithdrawService.listByOrderIds(resellerOrderIds);
                writer.write(resellerWithdrawRecords, EasyExcel.writerSheet("ResellerWithdrawRecord").head(ResellerWithdrawRecord.class).build());

                String ossFileUrl = ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, targetFile);
                FeiShuMessageUtil.storeGeneralMessage(String.format("uuid:%s 操作[%s] url:%s", uuid, "佣金重计算", ossFileUrl), FeiShuGroupRobot.ResellerSpecific);
                FileUtil.del(targetFile);
            }catch (Exception e) {
                // 处理涉及 IO 操作的可能异常
                FeiShuMessageUtil.storeGeneralMessage(String.format("uuid:%s 操作失败[%s] 文件写入过程发生异常", uuid, "佣金重计算"), FeiShuGroupRobot.ResellerSpecific);
                throw new InstaException(CommonErrorCode.InvalidParameter, "文件写入过程发生异常");
            }

        }

        LOGGER.info("{} commissionRecalculation update start {}", uuid, JSON.toJSONString(resellerCommissionUpdateParam));

        // 修改分销商 提现币种
        reseller.setUseCurrency(resellerCommissionUpdateParam.getCurrency().name());
        resellerService.updateResellerById(reseller);
        LOGGER.info("{} update reseller {}", uuid, JSON.toJSONString(reseller));

        // 重置订单订单提现状态
        List<ResellerOrder> resetStateResellerOrderList = resellerOrderList.stream()
                // 待审核的才需要修改状态（打回可提现状态）
                .filter(resellerOrder -> ResellerOrderState.examine.equals(ResellerOrderState.parse(resellerOrder.getState())))
                // 修改订单提现状态
                .map(resellerOrder -> {
                    ResellerOrder newResellerOrder = new ResellerOrder();
                    newResellerOrder.setId(resellerOrder.getId());
                    newResellerOrder.setState(ResellerOrderState.withdraw.getCode());
                    return newResellerOrder;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(resetStateResellerOrderList)) {
            LOGGER.info("{} 重置订单订单提现状态 {}", uuid, JSON.toJSONString(resetStateResellerOrderList));
            resellerOrderService.updateBatchById(resetStateResellerOrderList);
        }

        // 删除已经提现申请 的提现记录
        List<Integer> resellerWithdrawIdList = resetStateResellerOrderList.stream().map(ResellerOrder::getId).collect(Collectors.toList());
        LOGGER.info("{} 删除已经提现申请的提现记录 {}", uuid, JSON.toJSONString(resetStateResellerOrderList));
        resellerWithdrawService.removeByResellerOrderIds(resellerWithdrawIdList);
    }

    /**
     * 历史分销订单 佣金重计算
     *
     * @param resellerCommissionUpdateParam
     * @param uuid
     * @param resellerOrderList
     * @param reseller
     * @return
     */
    private List<ResellerOrder> oldResellerOrderCommissionRecalculation(CommissionRecalculationDTO resellerCommissionUpdateParam, String uuid, List<ResellerOrder> resellerOrderList, Reseller reseller) {

        LOGGER.info("{} 更新提现币种和提现美元汇率 {}", uuid, JSON.toJSONString(resellerCommissionUpdateParam));
        // 更新提现币种 和提现美元汇率 ps:以promo_code维度
        resellerOrderService.updateCommissionCurrencyAndUsdRate(resellerCommissionUpdateParam.getPromoCode(), resellerCommissionUpdateParam.getCurrency(), BigDecimal.ONE);

        // 获取当前分销订单 当前提现货币转美元的汇率
        Currency currency = resellerOrderList.get(0).commissionCurrency();
        MetaCurrency metaCurrency = metaCurrencyService.getByCurrency(currency);

        // 历史分销订单 原提现币种要修改为的币种转美元的汇率
        BigDecimal oldCommissionCurrencyUsdRate = MathUtil.getBigDecimal(metaCurrency.getUsdRate());

        // 整理历史分销订单数据佣金
        List<ResellerOrder> updateOldResellerOrderList = resellerOrderList.stream()
                // 过滤历史订单
                .filter(ResellerOrder::checkIsOldNumber)
                // 修改佣金订单数据
                .map(resellerOrder -> {
                    ResellerOrder newResellerOrder = new ResellerOrder();
                    newResellerOrder.setId(resellerOrder.getId());

                    // 历史分销订单 原提现币种 订单佣金
                    BigDecimal commission = MathUtil.getBigDecimal(resellerOrder.getCommission());

                    // 历史分销订单的 新计算出的佣金 原币种转美元的汇率 * 原币种的订单佣金 四舍五入取2位小数
                    BigDecimal newCommission = commission.multiply(oldCommissionCurrencyUsdRate).setScale(2, RoundingMode.HALF_UP);
                    newResellerOrder.setCommission(newCommission.doubleValue());
                    return newResellerOrder;
                }).
                collect(Collectors.toList());

        // 更新历史分销订单数据
        if (CollectionUtils.isNotEmpty(updateOldResellerOrderList)) {
            LOGGER.info("{} 更新历史分销订单数据佣金 {}", uuid, JSON.toJSONString(updateOldResellerOrderList));
            resellerOrderService.updateBatchById(updateOldResellerOrderList);
        }

        // 获取新的 重构后数据充足的分销订单（之所以要重新查一遍是为了继承已经修改的币种）
        List<String> newResellerOrderNumbers = resellerOrderList.stream().filter(resellerOrder -> !resellerOrder.checkIsOldNumber()).map(ResellerOrder::getOrderNumber).collect(Collectors.toList());
        LOGGER.info("{} 获取新的重构后数据充足的分销订单号 {}", uuid, JSON.toJSONString(newResellerOrderNumbers));

        List<ResellerOrder> newResellerOrderList = resellerOrderService.listByOrderNumbers(newResellerOrderNumbers);
        LOGGER.info("{} 重新从数据库查询的分销订单数据 {}", uuid, JSON.toJSONString(newResellerOrderList));
        return newResellerOrderList;
    }

}
