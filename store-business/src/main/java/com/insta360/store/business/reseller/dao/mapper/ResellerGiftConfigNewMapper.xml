<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.reseller.dao.ResellerGiftConfigNewDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.reseller.dao.ResellerGiftConfigNewDao"/>

    <insert id="batchSaveSpecial" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO reseller_gift_config_new (product_id,commodity_id,reseller_auto_id,promo_code,default_gift_mark) values
        <foreach collection="list" item="config" separator=",">
            (#{config.productId},#{config.commodityId},#{config.resellerAutoId},#{config.promoCode},#{config.defaultGiftMark})
        </foreach>
    </insert>

    <insert id="batchSaveDefault" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO reseller_gift_config_new (product_id,commodity_id,default_gift_mark) values
        <foreach collection="list" item="config" separator=",">
            (#{config.productId},#{config.commodityId},#{config.defaultGiftMark})
        </foreach>
    </insert>
</mapper>
