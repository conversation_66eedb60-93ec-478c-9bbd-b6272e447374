package com.insta360.store.business.admin.order.service.impl.handler;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.admin.order.export.OrderExportData;
import com.insta360.store.business.admin.reseller.service.impl.constant.ResellerExportConstant;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.configuration.utils.ProfileUtil;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.model.GiftCardTemplate;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.discount.service.GiftCardTemplateService;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.service.PriceService;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.order.service.OrderAdminRemarkService;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.reseller.enums.ResellerUtmSourceType;
import com.insta360.store.business.reseller.enums.ResellerWithdrawAccountType;
import com.insta360.store.business.reseller.model.*;
import com.insta360.store.business.reseller.service.*;
import com.insta360.store.business.reseller.service.impl.helper.ResellerExportHelper;
import com.insta360.store.business.reseller.service.impl.helper.ResellerOrderHelper;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import com.insta360.store.business.user.model.StoreAccount;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: hyc
 * @Date: 2019-12-05
 * @Description:
 */
@Component
public class OrderDataExportHandler {

    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Autowired
    PriceService priceService;

    @Autowired
    ProductService productService;

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    ResellerOrderItemService resellerOrderItemService;

    @Autowired
    OrderPaymentService paymentService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderAdminRemarkService adminRemarkService;

    @Autowired
    GiftCardTemplateService giftCardTemplateService;

    @Autowired
    ResellerCommissionRateService commissionRateService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    ResellerUtmSourceService resellerUtmSourceService;

    @Autowired
    ResellerOrderHelper resellerOrderHelper;

    @Autowired
    ResellerWithdrawService resellerWithdrawService;

    @Autowired
    ResellerWithdrawAccountService withdrawAccountService;

    @Autowired
    ResellerExportHelper resellerExportHelper;

    /**
     * 数据封装处理
     *
     * @param order
     * @return
     */
    public List<OrderExportData> handle(Order order) {
        List<OrderExportData> datas = new ArrayList<>();

        String orderNumber = order.getOrderNumber();
        String country = order.getArea();
        String state = order.orderState().getNameZh();
        String contactEmail = order.getContactEmail();
        String isGuest = StoreAccount.GUEST_USER_ID.equals(order.getUserId()) ? "是" : "否";
        String promoCode = order.getPromoCode();
        String giftCardCode = order.getGiftCardCode();
        String couponCode = order.getCouponCode();
        String buyerRemark = order.getRemark();


        // 代金券模版信息
        String giftCardTemplateCode = null;
        if (StringUtil.isNotBlank(giftCardCode)) {
            GiftCard giftCard = giftCardService.getByCode(giftCardCode);
            if (giftCard != null && giftCard.getTemplateId() != null) {
                GiftCardTemplate giftCardTemplate = giftCardTemplateService.getById(giftCard.getTemplateId());
                if (giftCardTemplate != null) {
                    giftCardTemplateCode = giftCardTemplate.getTemplateCode();
                }
            }
        }

        // 支付信息
        OrderPayment payment = paymentService.getByOrder(order.getId());
        PaymentChannel paymentChannel = payment.paymentChannel();
        String channel = null;
        if (paymentChannel != null) {
            channel = paymentChannel.getNameZh();
        }

        Price totalPayPrice = payment.getTotalPayPrice();
        LocalDateTime payTime = payment.getPayTime();
        String payTimeStr = null;
        if (payTime != null) {
            payTimeStr = formatter.format(payTime.plusHours(8));
        }

        LocalDateTime createTime = order.getCreateTime();
        String createTimeStr = null;
        if (createTime != null) {
            createTimeStr = formatter.format(createTime.plusHours(8));
        }

        OrderDelivery delivery = orderDeliveryService.getOrderDelivery(order.getId());
        String fullName = ProfileUtil.getFullName(delivery.getFirstName(), delivery.getLastName());
        String phoneCode = delivery.getPhoneCode();
        String phone = delivery.getPhone();
        String expressCompany = delivery.getExpressCompany();
        String expressCode = delivery.getExpressCode();
        String fullAddress = ProfileUtil.getFullAddress(delivery.getProvince(), delivery.getAddress(), delivery.getSubAddress());

        OrderAdminRemark adminRemark = adminRemarkService.getByOrder(order.getId());

        // 分销信息
        ResellerOrder resellerOrder = resellerOrderService.getByOrderNumber(orderNumber);
        // 分销商相关信息
        Reseller reseller = Optional.ofNullable(resellerOrder)
                .map(r -> resellerService.getByPromoCode(r.getPromoCode()))
                .orElse(null);
        ResellerWithdrawAccount resellerWithdrawAccount = Optional.ofNullable(resellerOrder)
                .map(r -> resellerWithdrawService.getByResellerOrder(r.getId()))
                .map(record -> withdrawAccountService.getById(record.getWithdrawAccount()))
                .orElse(null);

        Boolean isHistoryOrder = resellerOrderHelper.isResellerHistoryOrder(resellerOrder);

        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        for (OrderItem item : orderItems) {
            OrderExportData data = new OrderExportData();
            // 分销订单信息
            this.buildResellerOrderData(isHistoryOrder, data, item, resellerOrder, orderNumber, reseller, resellerWithdrawAccount);

            // 订单信息
            data.setCountry(country);
            data.setOrderState(state);
            data.setEmail(contactEmail);
            data.setIsGuest(isGuest);
            data.setGiftCardCode(giftCardCode);
            data.setCouponCode(couponCode);
            data.setResellerCode(promoCode);
            data.setGiftCardTemplateCode(giftCardTemplateCode);
            data.setBuyerRemark(buyerRemark);
            data.setCreateTime(createTimeStr);

            // 支付信息
            data.setPayChannel(channel);
            data.setOrderNumber(orderNumber);

            Price totalItemPayPrice = item.getTotalItemPayPrice();
            data.setComboTotalPrice(formatFloat(totalItemPayPrice.getAmount()));

            data.setTotalPrice(formatFloat(totalPayPrice.getAmount()));
            data.setTotalPriceCny(transferCnyPrice(totalPayPrice));
            data.setTax(formatFloat(payment.getTax()));
            data.setShippingCost(formatFloat(payment.getShippingCost()));
            data.setCurrency(payment.getCurrency());
            data.setTotalDiscountPrice(formatFloat(payment.getTotalDiscountFee()));
            data.setPayTime(payTimeStr);

            // 收货信息
            data.setCustomerName(fullName);
            data.setPhoneCode(phoneCode);
            data.setPhone(phone);
            data.setExpressCompany(expressCompany);
            data.setExpressNumber(expressCode);
            data.setAddress(fullAddress);

            LocalDate estimatedDeliveryDate = delivery.getEstimatedDeliveryDate();
            if (estimatedDeliveryDate != null) {
                data.setEstimateDeliveryDate(dateFormatter.format(estimatedDeliveryDate));
            }

            // 订单项
            Integer commodityId = item.getCommodity();
            Integer productId = item.getProduct();

            CommodityInfo commodityInfo = commodityInfoService.getInfoDefaultEnglish(commodityId, InstaLanguage.zh_CN);
            ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(productId, InstaLanguage.zh_CN);
            data.setItemName(productInfo.getName() + " 【" + commodityInfo.getName() + "】");
            Product product = productService.getById(productId);
            if (product != null) {
                data.setIsCamera(product.whetherCamera() ? "是" : "否");
            }

            data.setItemQuantity(item.getNumber());
            data.setPrice(formatFloat(item.getPrice()));
            // 兼容以前商品没有原价
            data.setOriginAccount(item.getOriginAmount() != null ? formatFloat(item.getOriginAmount()) : null);
            data.setDiscount(formatFloat(item.getDiscountFee()));

            // 商家备注
            data.setAdminRemark(adminRemark != null ? adminRemark.getContent() : null);


            // 退货退款信息
            RmaOrder rmaOrder = rmaOrderService.getByOrderItem(item.getId());
            if (rmaOrder != null) {
                LocalDateTime rmaOrderCreateTime = rmaOrder.getCreateTime();
                LocalDateTime rmaOrderFinishTime = rmaOrder.getFinishTime();
                String rmaCreateTimeStr = rmaOrderCreateTime == null ? null : formatter.format(rmaOrderCreateTime.plusHours(8));
                String rmaFinishTimeStr = rmaOrderFinishTime == null ? null : formatter.format(rmaOrderFinishTime.plusHours(8));
                data.setRmaState(rmaOrder.rmaState().getNameZh());
                data.setRmaType(rmaOrder.rmaType().getNameZh());
                data.setRmaReason(rmaOrder.getReason());
                data.setRmaExtraReason(rmaOrder.getExtraReason());
                data.setRmaAdminReason(rmaOrder.getAdminReason());
                data.setRmaAdminRemark(rmaOrder.getAdminRemark());
                data.setRmaCreatTime(rmaCreateTimeStr);
                data.setRmaFinishTime(rmaFinishTimeStr);
                data.setRmaRefundTotalAmount(Double.valueOf(String.valueOf(rmaOrder.getRefundAmount())));
            }

            datas.add(data);
        }

        return datas;
    }

    /**
     * 构建分销信息
     *
     * @param isHistoryOrder
     * @param data
     * @param item
     * @param resellerOrder
     * @param orderNumber
     */
    private void buildResellerOrderData(Boolean isHistoryOrder, OrderExportData data, OrderItem item, ResellerOrder resellerOrder, String orderNumber, Reseller reseller, ResellerWithdrawAccount resellerWithdrawAccount) {
        if (Objects.isNull(resellerOrder)) {
            return;
        }

        // 设置分销码来源
        ResellerUtmSource resellerUtmSource = resellerUtmSourceService.getByOrderNumber(orderNumber);
        String utmSource = Optional.ofNullable(resellerUtmSource)
                .map(ResellerUtmSource::getUtmSource)
                .map(ResellerUtmSourceType::parse)
                .map(ResellerUtmSourceType::getCode)
                .orElse(ResellerUtmSourceType.link.getDesc());

        data.setUtmSource(utmSource);
        // 支付货币美元汇率
        Double payCurrencyUsdRate = resellerOrder.getPayCurrencyUsdRate();
        // 提现货币美元汇率
        Double withdrawCurrencyUsdRate = resellerOrder.getWithdrawCurrencyUsdRate();
        // 历史分销单标识
        data.setHistoryTag(isHistoryOrder ? "是" : "否");
        // 支付货币兑换汇率
        data.setPayCurrencyUsdRate(payCurrencyUsdRate);
        // 提现货币兑换汇率
        data.setWithdrawCurrencyUsdRate(withdrawCurrencyUsdRate);

        // 获取到分销订单商品
        ResellerOrderItem resellerOrderItem = resellerOrderItemService.getResellerOrderItemByItemId(item.getId());
        if (Objects.isNull(resellerOrderItem)) {
            List<ResellerOrderItem> resellerOrderItemList = resellerOrderItemService.getResellerOrderItemGift(resellerOrder.getId(), item.getCommodity(), item.getIsGift());
            if (CollectionUtils.isNotEmpty(resellerOrderItemList)) {
                resellerOrderItem = resellerOrderItemList.get(0);
            }
        }
        if (Objects.nonNull(resellerOrderItem)) {
            // 套餐初始佣金率
            Double initCommissionRate = resellerOrderItem.getInitCommissionRate();
            // 套餐佣金
            Double estimatedIncome = resellerOrderItem.getEstimatedIncome();
            data.setCommissionRate(initCommissionRate);
            data.setEstimatedIncome(estimatedIncome);
        }

        // 设置分销商邮箱
        Optional.ofNullable(reseller).map(Reseller::getEmail).ifPresent(data::setResellerEmail);

        // 设置当前分销订单提现渠道
        Optional.ofNullable(resellerWithdrawAccount).ifPresent(r -> {
            // 设置提现渠道
            String withdrawChannel = Optional.ofNullable(r.withdrawAccountType()).map(ResellerWithdrawAccountType::getDesc).orElse(StringUtils.EMPTY);
            data.setWithdrawChannel(withdrawChannel);

            // 设置提现账户用户名
            Optional.ofNullable(r.getData()).map(JSONObject::parseObject)
                    .map(json -> StringUtils.isNotBlank(json.getString("username")) ? json.getString("username") : ResellerExportConstant.SLASH)
                    .ifPresent(data::setResellerName);
        });
    }

    /**
     * 价格格式转换
     *
     * @param amount
     * @return
     */
    private static Double formatFloat(Float amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return Double.valueOf(decimalFormat.format(amount));
    }

    /**
     * 转换为人民币货币
     *
     * @param totalPayPrice
     * @return
     */
    private Double transferCnyPrice(Price totalPayPrice) {
        // 人民币无需转换
        if (Currency.CNY.equals(totalPayPrice.getCurrency())) {
            return formatFloat(totalPayPrice.getAmount());
        }

        // 转换为人民币货币
        Price cnyPrice = priceService.changeCurrency(totalPayPrice, Currency.CNY);
        return formatFloat(cnyPrice.getAmount());
    }
}
