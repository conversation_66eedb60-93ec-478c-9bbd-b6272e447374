package com.insta360.store.business.prime.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.prime.bo.PrimeCommodityDomainBO;
import com.insta360.store.business.prime.dto.PrimeCommodityDTO;
import com.insta360.store.business.prime.enums.PrimeCommodityType;
import com.insta360.store.business.prime.model.PrimeCommodity;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-06-04
 * @Description:
 */
public interface PrimeCommodityService extends BaseService<PrimeCommodity> {

    List<PrimeCommodity> listByCommodityIdsType(List<Long> commodityIds, PrimeCommodityType primeCommodityType);

    List<PrimeCommodity> listByCommodityIds(List<Long> commodityIds);

    /**
     * 根据套餐类型获取
     *
     * @param primeCommodityType 套餐类型
     * @return 对应类型的套餐列表
     */
    List<PrimeCommodity> listByType(PrimeCommodityType primeCommodityType);

    /**
     * 根据商城套餐获取commodity ID
     *
     * @param commodityId
     * @return
     */
    PrimeCommodity getByCommodityId(Integer commodityId);

    /**
     * 根据prime 产品ID获取
     *
     * @param productId
     * @return
     */
    PrimeCommodity getByPrimeProductId(String productId);

    /**
     * 删除产品套餐
     *
     * @param commodityParam
     */
    void deletePrimeCommodity(PrimeCommodityDTO commodityParam);

    /**
     * 更新prime商品
     *
     * @param commodityParam
     */
    void updatePrimeCommodity(PrimeCommodityDTO commodityParam);

    /**
     * 创建prime商品
     *
     * @param commodityParam
     */
    void createPrimeCommodity(PrimeCommodityDTO commodityParam);

    /**
     * 获取prime套餐信息
     *
     * @param commodityId
     * @return
     */
    PrimeCommodityDomainBO getPrimeDetail(Long commodityId);
}
