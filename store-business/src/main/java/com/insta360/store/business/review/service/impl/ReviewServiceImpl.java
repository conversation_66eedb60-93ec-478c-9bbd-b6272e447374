package com.insta360.store.business.review.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.outgoing.mq.check.helper.DoubleCheckSendHelper;
import com.insta360.store.business.outgoing.mq.review.helper.ReviewMessageHelper;
import com.insta360.store.business.review.bo.ReviewQueryBO;
import com.insta360.store.business.review.bo.ReviewSubmitBO;
import com.insta360.store.business.review.dao.ReviewDao;
import com.insta360.store.business.review.enums.ReviewLikeEnum;
import com.insta360.store.business.review.enums.ReviewRateLevelEnum;
import com.insta360.store.business.review.enums.ReviewResourceStateEnum;
import com.insta360.store.business.review.enums.ReviewStateEnum;
import com.insta360.store.business.review.exception.ReviewErrorCode;
import com.insta360.store.business.review.model.Review;
import com.insta360.store.business.review.service.ReviewRateCountService;
import com.insta360.store.business.review.service.ReviewRateLevelCountService;
import com.insta360.store.business.review.service.ReviewResourceService;
import com.insta360.store.business.review.service.ReviewService;
import com.insta360.store.business.review.service.impl.hepler.ReviewNumberGenerator;
import com.insta360.store.business.review.service.impl.hepler.ReviewQueryHelper;
import com.insta360.store.business.review.service.impl.hepler.ReviewResourceHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-04
 * @Description:
 */
@Service
public class ReviewServiceImpl extends BaseServiceImpl<ReviewDao, Review> implements ReviewService {

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ReviewQueryHelper reviewQueryHelper;

    @Autowired
    ReviewMessageHelper reviewMessageHelper;

    @Autowired
    ReviewResourceHelper reviewResourceHelper;

    @Autowired
    ReviewNumberGenerator reviewNumberGenerator;

    @Autowired
    ReviewResourceService reviewResourceService;

    @Autowired
    DoubleCheckSendHelper doubleCheckSendHelper;

    @Autowired
    ReviewRateCountService reviewRateCountService;

    @Autowired
    ReviewRateLevelCountService reviewRateLevelCountService;

    @Override
    public Review getByOrderNumber(String orderNumber) {
        QueryWrapper<Review> qw = new QueryWrapper<>();
        qw.eq("order_number", orderNumber);
        return baseMapper.selectOne(qw);
    }

    @Override
    public PageResult<Review> listReviews(Integer productId, Integer rate, String sortKey, PageQuery pageQuery) {
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery),
                reviewQueryHelper.getQueryWrapperBySort(productId, rate, sortKey)));
    }

    @Override
    public List<Review> listByBetweenCreateTime(String startTime, String endTime) {
        QueryWrapper<Review> qw = new QueryWrapper<>();
        qw.between("create_time", startTime, endTime);
        return baseMapper.selectList(qw);
    }

    @Override
    public Page<Review> pageQueryReviews(ReviewQueryBO reviewQueryParam, IPage<Review> iPage) {
        return (Page<Review>) iPage.setRecords(baseMapper.selectAdReviewsWithConditions(iPage, reviewQueryParam));
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void reviewSubmit(ReviewSubmitBO reviewSubmitBo) {
        // 评论子项
        OrderItem orderItem = reviewSubmitBo.getOrderItem();

        // 重复评论（放在订单信息后置校验，避免接口参数枚举查看订单子项评论状态）
        Review review = this.getByOrderItemId(orderItem.getId());
        if (review != null || orderItem.getReviewState()) {
            throw new InstaException(ReviewErrorCode.OrderItemAlreadyReviewException);
        }

        // 评论数据保存
        review = new Review();
        review.setOrderItemId(orderItem.getId());
        review.setOrderNumber(reviewSubmitBo.getOrderNumber());
        review.setReviewNumber(reviewNumberGenerator.generate());
        review.setProductId(orderItem.getProduct());
        review.setCommodityId(orderItem.getCommodity());
        review.setEmail(reviewSubmitBo.getEmail());
        review.setOrderArea(reviewSubmitBo.getOrderArea());
        review.logAndSetState(ReviewStateEnum.unhandled.getCode());
        review.setNickName(reviewSubmitBo.getNickName());
        review.setReviewTitle(reviewSubmitBo.getReviewTitle());
        review.setReviewContent(reviewSubmitBo.getReviewContent());
        review.setReviewRate(reviewSubmitBo.getReviewRate());
        review.setCreateTime(LocalDateTime.now());
        review.setUpdateTime(LocalDateTime.now());
        baseMapper.insert(review);

        doubleCheckSendHelper.sendDoubleCheckMessage(review.getId(), DoubleCheckEnum.ReviewSubmitCheck);
        reviewResourceHelper.saveBatchReviewResources(review.getId(), reviewSubmitBo.getCn(), reviewSubmitBo.getReviewResourceParamList());

        // 同步修改子项评论状态
        orderItemService.setAlreadyReview(orderItem);
    }

    @Override
    public Review getByOrderItemId(Integer orderItemId) {
        QueryWrapper<Review> qw = new QueryWrapper<>();
        qw.eq("order_item_id", orderItemId);
        return baseMapper.selectOne(qw);
    }

    @Override
    public void like(Integer reviewId) {
        // 评论需要真实有效
        Review review = this.getById(reviewId);
        if (review == null) {
            throw new InstaException(ReviewErrorCode.ReviewNotFoundException);
        }

        // 已发布才允许点赞
        if (!ReviewStateEnum.released.equals(review.reviewState())) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }

        // 评论点赞事件通知
        reviewMessageHelper.sendReviewLikeMessage(review, ReviewLikeEnum.LIKE);
    }

    @Override
    public void dislike(Integer reviewId) {
        Review review = this.getById(reviewId);
        if (review == null) {
            throw new InstaException(ReviewErrorCode.ReviewNotFoundException);
        }

        // 已发布才允许取消点赞
        if (!ReviewStateEnum.released.equals(review.reviewState())) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }

        // 评论取消点赞事件通知
        reviewMessageHelper.sendReviewLikeMessage(review, ReviewLikeEnum.DISLIKE);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void setUnhandled(Review review) {
        // 设置【待审核】前置状态要是【已拒绝】或者【已发布】
        ReviewStateEnum reviewState = review.reviewState();
        if (!(ReviewStateEnum.rejected.equals(reviewState) || ReviewStateEnum.released.equals(reviewState))) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }

        // 同步去除置顶状态
        review.setTopTag(false);

        // 设置为 待审核
        review.logAndSetState(ReviewStateEnum.unhandled.getCode());
        baseMapper.updateById(review);

        // 如果是由【已发布】改为【待审核】
        if (ReviewStateEnum.released.equals(reviewState)) {
            // 资源状态同步设置为 待审核
            reviewResourceService.updateResourceState(review.getId(), ReviewResourceStateEnum.unhandled);

            // 则同步扣除更新该产品的各个评级数量（优先扣除星级数量，下面会使用星级数量进行计算）
            reviewRateLevelCountService.updateReviewRateLevelCount(review.getProductId(), review.getReviewRate(), -1);

            // 则同步扣除更新该产品的评分信息（平均分，总分等）
            reviewRateCountService.updateReviewRateCount(review.getProductId(), review.getReviewRate(), -1);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void setReleased(Review review, List<Integer> reviewResourceIds) {
        // 设置【已发布】前置状态要是【待审核】
        if (!ReviewStateEnum.unhandled.equals(review.reviewState())) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }

        // 检查是否转码完成
        reviewResourceHelper.checkStateOfCompression(review.getId());

        // 设置为 已发布
        review.logAndSetState(ReviewStateEnum.released.getCode());
        baseMapper.updateById(review);

        // 更新资源状态
        reviewResourceService.updateResourceStateReleased(review.getId(), reviewResourceIds);

        // 同步记录产品的评级数量（优先新增星级数量，下面会使用星级数量进行计算）
        reviewRateLevelCountService.updateReviewRateLevelCount(review.getProductId(), review.getReviewRate(), 1);

        // 评论发布后，同步新增更新该产品的评分信息（平均分，总分等）
        reviewRateCountService.updateReviewRateCount(review.getProductId(), review.getReviewRate(), 1);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void setRejected(Review review) {
        // 设置【已拒绝】前置状态要是【待审核】或者【已发布】
        ReviewStateEnum reviewState = review.reviewState();
        if (!(ReviewStateEnum.unhandled.equals(reviewState) || ReviewStateEnum.released.equals(reviewState))) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }

        // 同步去除置顶状态
        review.setTopTag(false);

        // 设置为 已拒绝
        review.logAndSetState(ReviewStateEnum.rejected.getCode());
        baseMapper.updateById(review);

        // 如果是由【已发布】改为【已拒绝】
        if (ReviewStateEnum.released.equals(reviewState)) {
            // 资源状态同步设置为 已拒绝
            reviewResourceService.updateResourceState(review.getId(), ReviewResourceStateEnum.rejected);

            // 则同步扣除更新该产品的各个评级数量（优先扣除星级数量，下面会使用星级数量进行计算）
            reviewRateLevelCountService.updateReviewRateLevelCount(review.getProductId(), review.getReviewRate(), -1);

            // 则同步扣除更新该产品的评分信息（平均分，总分等）
            reviewRateCountService.updateReviewRateCount(review.getProductId(), review.getReviewRate(), -1);
        }
    }

    @Override
    public void setReviewRemark(Review review, String reviewRemark) {
        // 设置备注
        review.setReviewRemark(reviewRemark);
        baseMapper.updateById(review);
    }

    @Override
    public void setReviewReply(Review review, String reviewReply) {
        // 设置回复
        review.setReviewReply(reviewReply);
        baseMapper.updateById(review);
    }

    @Override
    public void topTag(Review review) {
        // 已发布的评论才允许置顶
        if (!ReviewStateEnum.released.equals(review.reviewState())) {
            throw new InstaException(ReviewErrorCode.ReviewStateInvalidException);
        }

        // 5星评论才允许置顶
        if (!ReviewRateLevelEnum.LV5.equals(review.rateLevel())) {
            throw new InstaException(ReviewErrorCode.ReviewRateLevelException);
        }

        // 不能重复置顶
        if (review.getTopTag()) {
            throw new InstaException(ReviewErrorCode.ReviewTopTagStateExceptiton);
        }

        // 设置置顶标记
        review.setTopTag(true);
        baseMapper.updateById(review);
    }

    @Override
    public void topTagCancel(Review review) {
        // 未置顶的不能取消置顶
        if (!review.getTopTag()) {
            throw new InstaException(ReviewErrorCode.ReviewTopTagStateExceptiton);
        }

        // 5星评论才允许取消置顶
        if (!ReviewRateLevelEnum.LV5.equals(review.rateLevel())) {
            throw new InstaException(ReviewErrorCode.ReviewRateLevelException);
        }

        // 设置置顶取消标记
        review.setTopTag(false);
        baseMapper.updateById(review);
    }

    @Override
    public Review getByReviewNumber(String reviewNumber) {
        QueryWrapper<Review> qw = new QueryWrapper<>();
        qw.eq("review_number", reviewNumber);
        return baseMapper.selectOne(qw);
    }

    @Override
    public void batchUpdateReviewById(List<Review> reviewList) {
        if (CollectionUtils.isEmpty(reviewList)){
            return;
        }
        baseMapper.batchUpdateReviewById(reviewList);
    }

}
