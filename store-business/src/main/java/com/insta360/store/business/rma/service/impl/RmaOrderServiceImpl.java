package com.insta360.store.business.rma.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.utils.PageResultUtil;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.rma.helper.RmaOrderMessageSendHelper;
import com.insta360.store.business.outgoing.mq.wto.helper.StoreDataSyncOmsMessageSendHelper;
import com.insta360.store.business.rma.dao.RmaOrderDao;
import com.insta360.store.business.rma.dto.condition.RmaQueryCondition;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.model.RmaDelivery;
import com.insta360.store.business.rma.model.RmaItemStock;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.model.RmaOrderDetail;
import com.insta360.store.business.rma.service.RmaDeliveryService;
import com.insta360.store.business.rma.service.RmaItemStockService;
import com.insta360.store.business.rma.service.RmaOrderDetailService;
import com.insta360.store.business.rma.service.RmaOrderService;
import com.insta360.store.business.rma.service.impl.helper.OrderItemUpdater;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-09-06
 * @Description:
 */
@Service
public class RmaOrderServiceImpl extends BaseServiceImpl<RmaOrderDao, RmaOrder> implements RmaOrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RmaOrderServiceImpl.class);

    @Autowired
    RmaDeliveryService rmaDeliveryService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderItemUpdater orderItemUpdater;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    RmaOrderMessageSendHelper rmaOrderMessageSendHelper;

    @Autowired
    StoreDataSyncOmsMessageSendHelper storeDataSyncOmsMessageSendHelper;

    @Autowired
    RmaItemStockService rmaItemStockService;

    @Autowired
    RmaOrderDetailService rmaOrderDetailService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public RmaOrder create(RmaOrder rmaOrderInfo, RmaOrderDetail rmaOrderDetail, OrderItem orderItem) {
        // 创建售后单
        baseMapper.insert(rmaOrderInfo);
        // 创建售后单明细
        rmaOrderDetail.setRmaOrderId(rmaOrderInfo.getId());
        rmaOrderDetailService.save(rmaOrderDetail);
        // 更新订单子项状态
        orderItemUpdater.update(orderItem, rmaOrderInfo.rmaType());
        // 扣减售后可退库存
        rmaItemStockService.doDeductionStock(rmaOrderInfo.getOrderId(), rmaOrderInfo.getOrderItemId(), rmaOrderDetail.getReturnNum());
        return rmaOrderInfo;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void update(RmaOrder rmaOrder, RmaOrderDetail rmaOrderDetail, RmaItemStock rmaItemStock) {
        if (Objects.isNull(rmaOrderDetail.getRmaOrderDetailId())) {
            rmaOrderDetailService.save(rmaOrderDetail);
        } else {
            rmaOrderDetailService.updateById(rmaOrderDetail);
        }
        rmaItemStockService.updateById(rmaItemStock);
        baseMapper.updateById(rmaOrder);
    }

    @Override
    public List<RmaOrder> listByOrderId(Integer orderId) {
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.eq("order_id", orderId);
        return baseMapper.selectList(qw);
    }

    @Override
    public RmaOrder getByOrderItem(Integer orderItemId) {
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.eq("order_item_id", orderItemId);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<RmaOrder> listByOrderItemId(Integer orderItemId) {
        if (Objects.isNull(orderItemId)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.eq("order_item_id", orderItemId);
        return baseMapper.selectList(qw);
    }

    @Override
    public RmaOrder getByRmaNumber(String rmaNumber) {
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.eq("rma_number", rmaNumber);
        return baseMapper.selectOne(qw);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void customerSendOff(Integer rmaId, String expressCompany, String expressNumber) {
        RmaDelivery delivery = rmaDeliveryService.getByRmaOrder(rmaId);

        if (delivery == null) {
            delivery = new RmaDelivery();
            delivery.setRmaId(rmaId);
            delivery.setExpressFromCompany(expressCompany);
            delivery.setExpressFromNumber(expressNumber);
            delivery.setExpressFromTime(LocalDateTime.now());
            rmaDeliveryService.save(delivery);
        } else {
            delivery.setExpressFromCompany(expressCompany);
            delivery.setExpressFromNumber(expressNumber);
            delivery.setExpressFromTime(LocalDateTime.now());
            rmaDeliveryService.updateById(delivery);
        }

        RmaOrder rmaOrder = getById(rmaId);
        rmaOrder.setState(RmaState.pending_for_receive.getCode());
        rmaOrder.setModifyTime(LocalDateTime.now());
        baseMapper.updateById(rmaOrder);

        // 发送同步oms消息
        storeDataSyncOmsMessageSendHelper.sendRmaOrderChangeSyncOmsMessage(rmaOrder, Boolean.FALSE);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public RmaOrder reject(Integer rmaId) {
        RmaOrder rmaOrder = getById(rmaId);
        if (RmaState.cancelled.equals(rmaOrder.rmaState())) {
            throw new InstaException(-1, "错误的售后状态流转");
        }
        rmaOrder.setCloseState(rmaOrder.getState());
        rmaOrder.setState(RmaState.rejected.getCode());
        rmaOrder.setFinishTime(LocalDateTime.now());
        rmaOrder.setModifyTime(LocalDateTime.now());
        baseMapper.updateById(rmaOrder);
        // 更新订单商品状态
        orderItemService.close(rmaOrder.getOrderItemId());
        // 回退售后库存
        rmaItemStockService.doGoBackStock(rmaOrder.getOrderId(), rmaOrder.getOrderItemId(), rmaOrder.getQuantity());
        return rmaOrder;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public RmaOrder setSuccess(Integer rmaId) {
        RmaOrder rmaOrder = getById(rmaId);
        // 1、如售后单状态为'已拒绝、已关闭'，再次设置成'已成功'时需扣减库存
        if (rmaOrder.isClose()) {
            rmaItemStockService.doDeductionStock(rmaOrder.getOrderId(), rmaOrder.getOrderItemId(), rmaOrder.getQuantity());
        }
        rmaOrder.setState(RmaState.success.getCode());
        rmaOrder.setFinishTime(LocalDateTime.now());
        rmaOrder.setModifyTime(LocalDateTime.now());
        baseMapper.updateById(rmaOrder);
        // 2、更新订单商品状态
        orderItemService.setSuccess(rmaOrder.getOrderItemId());
        // 3、作废已绑定的保险服务
        rmaOrderMessageSendHelper.sendAutoCancelInsuranceMessage(rmaOrder);

        return rmaOrder;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public RmaOrder cancel(Integer rmaId) {
        RmaOrder rmaOrder = baseMapper.selectById(rmaId);
        if (RmaState.rejected.equals(rmaOrder.rmaState())) {
            throw new InstaException(-1, "错误的售后状态流转");
        }
        rmaOrder.setCloseState(rmaOrder.getState());
        rmaOrder.setState(RmaState.cancelled.getCode());
        rmaOrder.setFinishTime(LocalDateTime.now());
        rmaOrder.setModifyTime(LocalDateTime.now());
        baseMapper.updateById(rmaOrder);
        // 更新订单商品状态
        orderItemService.close(rmaOrder.getOrderItemId());
        // 回退售后库存
        rmaItemStockService.doGoBackStock(rmaOrder.getOrderId(), rmaOrder.getOrderItemId(), rmaOrder.getQuantity());
        return rmaOrder;
    }

    @Override
    public PageResult<RmaOrder> query(RmaQueryCondition condition, PageQuery pageQuery) {
        if (Objects.isNull(condition) || Objects.isNull(pageQuery)) {
            return PageResultUtil.emptyResult();
        }

        IPage page = PageUtil.toIPage(pageQuery);
        page.setRecords(baseMapper.selectRmaOrderByPage(page, condition));

        return PageUtil.toPageResult(page);
    }

    @Override
    public List<RmaOrder> getByOrderAndState(Integer orderId, List<Integer> orderStates) {
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.eq("order_id", orderId);
        qw.in("state", orderStates);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<RmaOrder> getRmaOrderByOrder(String rmaType, List<Integer> orderIdList) {
        if (StringUtils.isBlank(rmaType) || CollectionUtils.isEmpty(orderIdList)) {
            return Lists.newArrayList();
        }
        return baseMapper.getRmaOrderByOrder(rmaType, orderIdList);
    }

    @Override
    public List<RmaOrder> getRmaOrdersByRmaType(String rmaType, Integer orderId) {
        if (StringUtils.isBlank(rmaType) || orderId == null) {
            return null;
        }
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.eq("rma_type", rmaType);
        qw.eq("order_id", orderId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<RmaOrder> listByOrderIds(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.in("order_id", orderIdList);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<RmaOrder> listByOrderItemIds(List<Integer> orderItemIds) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return null;
        }
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.in("order_item_id", orderItemIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<RmaOrder> listByRmaIds(List<Integer> rmaOrderIds) {
        if (CollectionUtils.isEmpty(rmaOrderIds)) {
            return null;
        }
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.in("id", rmaOrderIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public void batchUpdateRmaOrderById(List<RmaOrder> rmaOrderList) {
        if (CollectionUtils.isEmpty(rmaOrderList)) {
            return;
        }
        baseMapper.batchUpdateRmaOrderById(rmaOrderList);
    }

    @Override
    public List<RmaOrder> listHistoryRmaOrder(List<Integer> orderStateList, String startTime, String endTime) {
        if (CollectionUtils.isEmpty(orderStateList) || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return new ArrayList<>(0);
        }
        return baseMapper.selectRmaOrderHistory(Lists.newArrayList(RmaType.rma_refund.name(), RmaType.rma_return.name()), orderStateList, 0, startTime, endTime);
    }

    @Override
    public List<RmaOrder> listByRmaOrderNumber(List<String> rmaOrderNumberList) {
        if (CollectionUtils.isEmpty(rmaOrderNumberList)) {
            return null;
        }
        QueryWrapper<RmaOrder> qw = new QueryWrapper<>();
        qw.in("rma_number", rmaOrderNumberList);
        return baseMapper.selectList(qw);
    }
}
