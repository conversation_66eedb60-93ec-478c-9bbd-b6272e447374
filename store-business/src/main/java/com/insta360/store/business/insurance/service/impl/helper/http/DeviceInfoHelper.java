package com.insta360.store.business.insurance.service.impl.helper.http;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.api.ResponseCode;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceActivationInfo;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceInfo;
import com.insta360.store.business.outgoing.rpc.app.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2021/04/09
 * @Description:
 */
@Component
public class DeviceInfoHelper {

    @Autowired
    DeviceService deviceService;

    /**
     * 根据序列号获取设备信息
     *
     * @param deviceSerial
     * @return
     */
    public String getDeviceType(String deviceSerial) {
        DeviceInfo deviceInfo = getDeviceInfo(deviceSerial);
        return deviceInfo == null ? null : deviceInfo.getDeviceType();
    }

    /**
     * 根据序列号获取设备信息
     *
     * @param deviceSerial
     * @return
     */
    public DeviceInfo getDeviceInfo(String deviceSerial) {
        return getDeviceInfo(deviceSerial, null);
    }

    /**
     * 根据序列号和设备类型获取设备信息
     *
     * @param deviceSerial
     * @param deviceType
     * @return
     */
    public DeviceInfo getDeviceInfo(String deviceSerial, String deviceType) {
        Response<DeviceInfo> deviceInfo = deviceService.getDeviceInfo(deviceSerial, deviceType);
        if (!ResponseCode.SUCCESS.equals(deviceInfo.getCode()) || deviceInfo.getData() == null) {
            // 允许程序继续执行
            return null;
        }
        return deviceInfo.getData();
    }

    /**
     * 获取设备激活信息
     *
     * @param deviceSerial
     * @return
     */
    public DeviceActivationInfo getDeviceActivationInfo(String deviceSerial) {
        Response<DeviceActivationInfo> deviceActivationInfo = deviceService.getDeviceActivationInfo(deviceSerial);
        if (!ResponseCode.SUCCESS.equals(deviceActivationInfo.getCode())) {
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }
        return deviceActivationInfo.getData();
    }
}
