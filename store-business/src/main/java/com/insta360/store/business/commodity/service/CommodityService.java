package com.insta360.store.business.commodity.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.commodity.bo.CommodityLowInventoryBO;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.integration.google.dto.GoogleCommodityInsertQueryDTO;

import java.util.Collection;
import java.util.List;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
public interface CommodityService extends BaseService<Commodity> {

    List<Commodity> listCommodityIds(List<Long> commodityIds);

    /**
     * 保存导入的套餐信息
     * @param commodity
     */
    void saveImport(Commodity commodity);

    /**
     * 获取一个产品所有套餐
     *
     * @param productId
     * @return
     */
    List<Commodity> getCommodities(Integer productId);

    /**
     * 获取一个产品所有已启用的套餐
     *
     * @param productId
     * @param enabled
     * @return
     */
    List<Commodity> getCommodities(Integer productId, Boolean enabled);

    /**
     * 获取产品id对应的套餐id
     *
     * @param productIds
     * @return
     */
    List<Integer> getCommodityIds(Collection<Integer> productIds);

    /**
     * 获取可以当赠品的套餐
     *
     * @return
     */
    List<Commodity> getCanBeGiftCommodity();

    /**
     * 根据商城料号或三方料号获取当前套餐
     *
     * @param skuCode
     * @return
     */
    Commodity getCommodityByPlatformCode(String skuCode);

    /**
     * 根据sku获取当前套餐
     *
     * @param skuCode
     * @return
     */
    Commodity getCommodityBySku(String skuCode);

    /**
     * 批量获取对应的套餐信息
     *
     * @param commodities
     * @return
     */
    List<Commodity> listCommodities(List<Integer> commodities);

    /**
     * 根据套餐区分是否是相机
     *
     * @param commodityIds
     * @return
     */
    List<Integer> listCameraCommodities(List<Integer> commodityIds);

    /**
     * 批量获取对应的套餐信息
     *
     * @param commodityIds
     * @return
     */
    List<Commodity> listByCommodities(List<Integer> commodityIds);

    /**
     * 批量获取产品对应套餐信息
     *
     * @param productIds
     * @return
     */
    List<Commodity> getCommodities(List<Integer> productIds);

    /**
     * 根据创建时间范围查询套餐(只查询产品类型-相机、配件的套餐)
     *
     * @param googleCommodityInsertQueryDTO
     * @return
     */
    List<Commodity> listCommodityByTime(GoogleCommodityInsertQueryDTO googleCommodityInsertQueryDTO);

    /**
     * 根据产品id查询套餐
     *
     * @param productIds
     * @return
     */
    List<Commodity> listByProductIds(List<Integer> productIds);

    /**
     * 查询所有启用的套餐信息
     *
     * @return
     */
    List<Commodity> listCommodityByEnabled();

    /**
     * 获取所有已启动的新品套餐数据
     *
     * @param enabled
     * @return
     */
    List<Commodity> listNewCommodities(Boolean enabled);

    /**
     * 更新或保存套餐信息
     *
     * @param commodityData
     */
    void upsertCommodity(Commodity commodityData);

    /**
     * 获取产品对应的已启用套餐
     *
     * @param productId
     */
    List<Commodity> listEnabledCommodities(Integer productId);

    /**
     * 获取全部套餐（不区分启用、禁用状态）
     *
     * @return
     */
    List<Commodity> getCommodityAll();

    /**
     * 根据sku获取当前套餐
     * 排除产品类型为内部链接的
     *
     * @param skuCode
     * @return
     */
    Commodity getCommodityBySkuCode(String skuCode);

    /**
     * 通过套餐id list 查到启用且正常销售的套餐
     *
     * @param commodityIds
     * @return
     */
    List<Commodity> listByCommoditiesNormalSale(List<Integer> commodityIds);

    /**
     * 通过产品id list 查询全部启用的套餐
     *
     * @param productIdList
     * @return
     */
    List<Commodity> listByProductIdsEnable(List<Integer> productIdList);

    /**
     * 新增套餐
     *
     * @param commodityData
     */
    void doInsertCommodity(Commodity commodityData);

    /**
     * 查询产品下面指定顺序区间的套餐
     *
     * @param orderIndex
     * @param product
     * @return
     */
    List<Commodity> listByRange(Integer orderIndex, Integer product);

    /**
     * 批量更新套餐
     *
     * @param commodities
     */
    void updateOrderIndexByIds(List<Commodity> commodities);

    /**
     * 查询产品下面指定顺序区间的套餐
     *
     * @param product
     * @param firstIndex
     * @param lastIndex
     * @return
     */
    List<Commodity> listByAllRange(Integer product, Integer firstIndex, Integer lastIndex);

    /**
     * 更新套餐
     *
     * @param commodity
     */
    void doUpdateCommodity(Commodity commodity);

    /**
     * 批量保存
     *
     * @param commodityList
     */
    void batchSave(List<Commodity> commodityList);

    /**
     * 根据套餐名称、产品ID查询对应套餐信息
     *
     * @param commodityNames
     * @param productId
     * @return
     */
    List<Commodity> listByNames(List<String> commodityNames, Integer productId);

    /**
     * 查询低库存的套餐
     *
     * @param stock
     * @param saleState
     * @return
     */
    List<CommodityLowInventoryBO> listByCommodityLowInventory(Integer stock, SaleState saleState);

    /**
     * 由产品ID集合获取套餐
     *
     * @param productIdList 产品id集合
     * @return {@link List}<{@link Commodity}>
     */
    List<Commodity> getCommodityByProductList(List<Integer> productIdList);

    /**
     * 获取套餐集合
     *
     * @param commodityIdList 套餐id集合
     * @return {@link List }<{@link Commodity }>
     */
    List<Commodity> listByCommodityIdIgnoreEnable(List<Integer> commodityIdList);

    /**
     * 批量新增套餐并返回套餐Id
     *
     * @param commodityList
     * @return
     */
    List<Integer> batchInsertCommodities(List<Commodity> commodityList);
}
