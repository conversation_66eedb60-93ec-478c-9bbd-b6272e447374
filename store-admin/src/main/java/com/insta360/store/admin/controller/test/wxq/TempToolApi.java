package com.insta360.store.admin.controller.test.wxq;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.annotations.AvoidRepeatableCommit;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.business.admin.commodity.price.CommodityPriceCalculateHelper;
import com.insta360.store.business.admin.commodity.price.CommodityPriceCalculateTaskInterface;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.meta.service.ActivityComponentService;
import com.insta360.store.business.meta.service.ActivityDynamicParamService;
import com.insta360.store.business.meta.service.ActivityLocalesConfigService;
import com.insta360.store.business.meta.service.ActivityPageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * tool/临时
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6
 */
@RestController
public class TempToolApi extends BaseAdminApi {

    @Autowired
    ActivityPageService activityPageService;

    @Autowired
    ActivityComponentService activityComponentService;

    @Autowired
    ActivityDynamicParamService activityDynamicParamService;

    @Autowired
    ActivityLocalesConfigService activityLocalesConfigService;

    @Autowired
    CommodityPriceCalculateHelper commodityPriceCalculateHelper;

    @Autowired
    CommodityPriceCalculateTaskInterface commodityPriceCalculateTaskExecute;

    /**
     * 套餐价格计算触发
     *
     * @param taskId
     */
    @GetMapping("/admin/tool/wxq/temp/commodityPriceCalculate")
    public void commodityPriceCalculate(@RequestParam(value = "taskId") Integer taskId) {
        commodityPriceCalculateTaskExecute.executeTask(taskId, UUIDUtils.generateUuid());
    }

    /**
     * 复制 价格任务计算
     *
     * @param taskId
     */
    @GetMapping("/admin/tool/wxq/temp/copy")
    public void copy(@RequestParam(value = "taskId") Integer taskId) {
        commodityPriceCalculateHelper.copy(taskId, UUIDUtils.generateUuid());
    }

    /**
     * rsa解密测试接口
     *
     * @param str
     * @return
     */
    @PostMapping("/admin/tool/wxq/temp/rsa")
    public String rsa(@RequestParam(required = false) String str, @RequestBody(required = false) JSONObject strBody) {
        String apt = strBody.getString("apt");
        String resStr = StringUtils.defaultIfBlank(apt, str);
        return RSAUtil.decryptByMyPrivateKey(resStr);
    }

    /**
     * 删除 活动落地页配置
     *
     * @param activityId 活动 ID
     * @return {@link Response }<{@link Object }>
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @Transactional
    @GetMapping(value = "/admin/meta/activity/removeByActivityId", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> removeByActivityId(@RequestParam Integer activityId) {
        activityPageService.removeById(activityId);
        activityComponentService.removeByActivityId(activityId);
        activityLocalesConfigService.removeByActivityId(activityId);
        activityDynamicParamService.removeByActivityId(activityId);
        return Response.ok();
    }

}
