package com.insta360.store.admin.controller.commodity.controller;

import com.google.common.collect.Lists;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.commodity.format.AdCommodityPack;
import com.insta360.store.admin.controller.commodity.vo.AdCommodityVO;
import com.insta360.store.business.commodity.dto.CommodityCodeDTO;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.integration.wto.oms.service.helper.OmsPushPlatformRouterHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: hyc
 * @Date: 2019-08-05
 * @Description: 套餐料号
 */
@RestController
@PermissionResource(code = "commodityCode", desc = "套餐料号配置")
public class AdCommodityCodeApi extends BaseAdminApi {

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    AdCommodityPack adCommodityPack;

    @Autowired
    CommodityService commodityService;

    @Autowired
    OmsPushPlatformRouterHelper omsPushPlatformRouterHelper;

    /**
     * 获取套餐料号
     *
     * @param commodityId
     * @return
     */
    @LogAttr(desc = "获取套餐料号", logType = LogType.query)
    @Permission(code = "store.commodity.commodityCode.getCode", desc = "获取套餐料号")
    @GetMapping("/admin/commodity/getCodes")
    public Response<? extends Map> getCode(@RequestParam(required = false, value = "commodity_id") Integer commodityId) {
        Commodity commodity = commodityService.getById(commodityId);
        AdCommodityPack.PackSetting packSetting = new AdCommodityPack.PackSetting(this);
        packSetting.setWithCode(true);

        AdCommodityVO commodityVO = adCommodityPack.doPack(commodity, packSetting);

        return Response.ok("codes", commodityVO.getCodes());

    }

    /**
     * 批量新增/更新
     *
     * @param commodityCodeParam
     * @return
     */
    @LogAttr(desc = "批量新增/更新料号")
    @Permission(code = "store.commodity.commodityCode.batchUpsertCodes", desc = "批量新增/更新料号")
    @PostMapping(path = "/admin/commodity/batchUpsertCodes", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> batchUpsertCodes(@Validated @RequestBody CommodityCodeDTO commodityCodeParam) {
        String code = commodityCodeParam.getCode();
        // 料号校验
        if (code.length() != code.trim().length()) {
            throw new InstaException(CommodityErrorCode.CommodityCodeErrException);
        }

        List<String> countryList = commodityCodeParam.getCountryList();
        if (CollectionUtils.isEmpty(countryList)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        commodityCodeService.batchUpsertCodes(commodityCodeParam.getCommodityId(), code, countryList);
        // 推送料号到oms
        omsPushPlatformRouterHelper.skuCodeSync(Lists.newArrayList(code));
        return Response.ok();
    }
}
