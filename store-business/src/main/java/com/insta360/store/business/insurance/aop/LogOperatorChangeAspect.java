package com.insta360.store.business.insurance.aop;

import com.insta360.store.business.insurance.service.CareActivationCardService;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/09/17
 * @Description:
 */
@Aspect
@Component
public class LogOperatorChangeAspect {

    @Autowired
    CareActivationCardService activationCardService;

    @After("@annotation(loc)")
    public void after(LogOperatorChange loc) {

        CareCardContext context = CareCardContext.get();
        if (context != null) {
            List<Integer> cardIdList = context.getCardIdList();

            // 操作人信息
            String operator = loc.isAuto() ? "admin_auto" : context.getOperator();

            // 批量更新
            activationCardService.updateOperator(cardId<PERSON>ist, operator);
        }
    }
}
