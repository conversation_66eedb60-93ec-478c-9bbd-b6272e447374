package com.insta360.store.business.rma.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 售后单编辑DTO
 * @Date 2023/6/28
 */
public class RmaOrderModifyDTO implements Serializable {

    /**
     * 售后单ID
     */
    @NotNull(message = "售后单ID必填")
    @Min(value = 1)
    private Integer rmaId;

    /**
     * 售后类型
     *
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    @NotBlank(message = "售后类型必填")
    private String rmaType;

    /**
     * 客服备注
     */
    private String adminRemark;

    /**
     * 主责归因
     */
    @NotNull(message = "主责归因必填")
    private Integer rmaMainDuty;

    /**
     * 客服确认原因
     */
    @NotBlank(message = "客服确认原因必填")
    private String adminReason;

    /**
     * 是否需要退回
     */
    @NotNull(message = "是否需要退回必填")
    private Boolean needReturn;

    /**
     * 售后数量
     */
    @NotNull(message = "售后数量必填")
    @Min(value = 1)
    private Integer returnNum;

    /**
     * 售后单实退金额
     */
    @NotNull(message = "售后单实退金额必填")
    private BigDecimal refundAmount;

    /**
     * 退税费
     */
    @NotNull(message = "退税费必填")
    private BigDecimal refundedTax;

    /**
     * 退关税
     */
    @NotNull(message = "退关税必填")
    private BigDecimal refundedCustomsTax;

    /**
     * 是否退回额度
     */
    private Boolean returnQuota;

    /**
     * 是否关闭自动订阅
     */
    private Boolean closeAutoSubscribe;

    /**
     * 是否终止权益（用于psp）
     */
    private Boolean terminationEquity;

    public Integer getRmaId() {
        return rmaId;
    }

    public void setRmaId(Integer rmaId) {
        this.rmaId = rmaId;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public String getAdminRemark() {
        return adminRemark;
    }

    public void setAdminRemark(String adminRemark) {
        this.adminRemark = adminRemark;
    }

    public String getAdminReason() {
        return adminReason;
    }

    public void setAdminReason(String adminReason) {
        this.adminReason = adminReason;
    }

    public Boolean getNeedReturn() {
        return needReturn;
    }

    public void setNeedReturn(Boolean needReturn) {
        this.needReturn = needReturn;
    }

    public Integer getReturnNum() {
        return returnNum;
    }

    public void setReturnNum(Integer returnNum) {
        this.returnNum = returnNum;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getRefundedTax() {
        return refundedTax;
    }

    public void setRefundedTax(BigDecimal refundedTax) {
        this.refundedTax = refundedTax;
    }

    public Integer getRmaMainDuty() {
        return rmaMainDuty;
    }

    public void setRmaMainDuty(Integer rmaMainDuty) {
        this.rmaMainDuty = rmaMainDuty;
    }

    public Boolean getReturnQuota() {
        return returnQuota;
    }

    public void setReturnQuota(Boolean returnQuota) {
        this.returnQuota = returnQuota;
    }

    public Boolean getCloseAutoSubscribe() {
        return closeAutoSubscribe;
    }

    public void setCloseAutoSubscribe(Boolean closeAutoSubscribe) {
        this.closeAutoSubscribe = closeAutoSubscribe;
    }

    public @NotNull(message = "退关税必填") BigDecimal getRefundedCustomsTax() {
        return refundedCustomsTax;
    }

    public void setRefundedCustomsTax(@NotNull(message = "退关税必填") BigDecimal refundedCustomsTax) {
        this.refundedCustomsTax = refundedCustomsTax;
    }

    public Boolean getTerminationEquity() {
        return terminationEquity;
    }

    public void setTerminationEquity(Boolean terminationEquity) {
        this.terminationEquity = terminationEquity;
    }

    @Override
    public String toString() {
        return "RmaOrderModifyDTO{" +
                "rmaId=" + rmaId +
                ", rmaType='" + rmaType + '\'' +
                ", adminRemark='" + adminRemark + '\'' +
                ", rmaMainDuty=" + rmaMainDuty +
                ", adminReason='" + adminReason + '\'' +
                ", needReturn=" + needReturn +
                ", returnNum=" + returnNum +
                ", refundAmount=" + refundAmount +
                ", refundedTax=" + refundedTax +
                ", refundedCustomsTax=" + refundedCustomsTax +
                ", returnQuota=" + returnQuota +
                ", closeAutoSubscribe=" + closeAutoSubscribe +
                ", terminationEquity=" + terminationEquity +
                '}';
    }
}
