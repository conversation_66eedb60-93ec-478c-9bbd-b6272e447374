<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.commodity.dao.CommodityCodeDao">
    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.commodity.dao.CommodityCodeDao"/>

    <update id="batchUpdateCodes">
        update product_commodity_code
        <set>
            code = #{code},
        </set>
        <where>
            commodity = #{commodityId}
            and area in
            <foreach collection="countryList" item="country" open="(" close=")" separator=",">
                #{country}
            </foreach>
        </where>
    </update>

    <insert id="batchSaveCodes">
        insert into product_commodity_code(commodity,area,code)
        VALUES
        <foreach collection="countryList" item="country" separator=",">
            (
            #{commodityId},#{country},#{code}
            )
        </foreach>
    </insert>

<!--    <select id="selectEnableCommodityCodes">-->
<!--        select pcc.*-->
<!--        from product p-->
<!--        join product_commodity pc on p.id = pc.product-->
<!--        join product_commodity_code pcc on pc.id = pcc.commodity-->
<!--        <where>-->
<!--            <if test="enabled != null">-->
<!--                p.enabled = #{enabled}-->
<!--            </if>-->
<!--            <if test="enabled != null">-->
<!--                pc.enabled = #{enabled}-->
<!--            </if>-->
<!--            <if test="isRepairService != null">-->
<!--                p.is_repair_service = #{isRepairService}-->
<!--            </if>-->
<!--            <if test="area != null">-->
<!--                pcc.area = #{area}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->
</mapper>
