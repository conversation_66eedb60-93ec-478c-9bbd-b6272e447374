package com.insta360.store.admin.controller.test.wxq;

import com.alibaba.fastjson.JSONArray;
import com.insta360.compass.core.annotations.AvoidRepeatableCommit;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.test.wxq.bo.CopyCommodityBO;
import com.insta360.store.admin.controller.test.wxq.dto.CopyCommodityDTO;
import com.insta360.store.admin.controller.test.wxq.enums.AlarmFilterHandlerType;
import com.insta360.store.admin.controller.test.wxq.helper.CommodityToolHelper;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.commodity.service.impl.helper.CommodityHelper;
import com.insta360.store.business.configuration.environment.EnvConstant;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.model.StoreConfig;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * tool/commodity
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6
 */
@RestController
public class CommodityToolApi extends BaseAdminApi {

    /**
     * 套餐格式 正则表达式
     */
    private static final String REGEX = "^[0-9,]+$";

    /**
     * 消息格式
     */
    private static final String MESSAGE_FORMAT = "套餐ID:%s 产品:%s 套餐:%s\n";

    private static final Logger LOGGER = LoggerFactory.getLogger(CommodityToolApi.class);

    @Autowired
    CommodityHelper commodityHelper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    CommodityToolHelper commodityToolHelper;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    /**
     * 获取复制套餐Bo
     *
     * @param copyCommodityDto 复制套餐DTO
     * @return {@link CopyCommodityBO }
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping(value = "/admin/test/wxq/commodity/getCopyCommodityBO")
    public CopyCommodityBO getCopyCommodityBO(@RequestBody CopyCommodityDTO copyCommodityDto) {

        return commodityToolHelper.getCopyCommodityBO(copyCommodityDto);
    }

    /**
     * 导入产品套餐数据
     *
     * @param commodityBo 套餐Bo
     * @return {@link CopyCommodityBO }
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping(value = "/admin/test/wxq/commodity/importCommodityInfo")
    public Response<Object> importCommodityInfo(@RequestBody CopyCommodityBO commodityBo) {
        if (EnvConstant.isProd()) {
            throw new InstaException(-1, "生产环境不允许执行此操作");
        }
        if (commodityBo.getProduct() == null || CollectionUtils.isEmpty(commodityBo.getCommodities())) {
            throw new InstaException(-1, "没有商品数据");
        }
        commodityToolHelper.importCommodityInfo(commodityBo);
        return Response.ok("导入完成");
    }

    /**
     * 调整套餐价格监控过滤
     *
     * @param alarmFilterHandlerType 报警处理类型
     * @param commodityIds           套餐身份证
     * @param email                  邮件
     * @return {@link Response }<{@link Object }>
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping(value = "/admin/test/wxq/commodity/alarmFilter")
    public Response<Object> alarmFilter(
            @RequestParam @NotNull AlarmFilterHandlerType alarmFilterHandlerType,
            @RequestParam @NotBlank(message = "套餐ID不能为空") String commodityIds,
            @RequestParam(defaultValue = "<EMAIL>") String email) {

        LOGGER.info("报警过滤器处理程序类型:{}", alarmFilterHandlerType);
        LOGGER.info("套餐ID:{}", commodityIds);

        if (!commodityIds.matches(REGEX)) {
            FeiShuMessageUtil.storeGeneralMessage("套餐ID格式错误", FeiShuGroupRobot.CommodityMonitoringAdjustment, FeiShuAtUser.matchEmail(email));
            return Response.failed("套餐ID格式错误");
        }
        // 获取商品监控忽略配置的值
        String configValue = storeConfigService.getConfigValue(StoreConfigKey.monitor_ignore_commodity);
        LOGGER.info("获取商品监控忽略配置的值:{}", configValue);

        // 如果配置值为空，则返回错误信息
        if (StringUtils.isBlank(configValue)) {
            FeiShuMessageUtil.storeGeneralMessage("现有套餐配置为空", FeiShuGroupRobot.CommodityMonitoringAdjustment, FeiShuAtUser.WXQ);
            return Response.failed("现有套餐配置为空");
        }
        // 分割商品ID字符串为数组
        String[] split = commodityIds.split(",");

        // 映射并收集商品ID为整型的集合
        List<Integer> handlerCommodityIds = Arrays.stream(split)
                .map(String::trim)
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(handlerCommodityIds)) {
            FeiShuMessageUtil.storeGeneralMessage("套餐ID错误,请检查", FeiShuGroupRobot.CommodityMonitoringAdjustment, FeiShuAtUser.matchEmail(email));
            return Response.failed("套餐ID错误,请检查");
        }
        // 如果集合大小不等于数组长度，说明有重复的商品ID，返回错误信息
        if (new HashSet<>(handlerCommodityIds).size() != split.length) {
            FeiShuMessageUtil.storeGeneralMessage("套餐ID不能重复", FeiShuGroupRobot.CommodityMonitoringAdjustment, FeiShuAtUser.matchEmail(email));
            return Response.failed("套餐ID不能重复");
        }

        List<Commodity> checkExistCommodityList = commodityService.listByCommodityIdIgnoreEnable(handlerCommodityIds);
        List<Integer> checkExistCommodityIds = checkExistCommodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        List<Integer> subtractCommodityIds = ListUtils.subtract(handlerCommodityIds, checkExistCommodityIds);
        if (CollectionUtils.isNotEmpty(subtractCommodityIds)) {
            LOGGER.info("套餐ID不存在:{}", subtractCommodityIds);
            FeiShuMessageUtil.storeGeneralMessage(String.format("套餐ID不存在: %s", subtractCommodityIds), FeiShuGroupRobot.CommodityMonitoringAdjustment, FeiShuAtUser.matchEmail(email));
            return Response.failed("套餐ID不存在");
        }

        // 解析配置值为整型列表
        List<Integer> ignoreCommodityIds = JSONArray.parseArray(configValue, Integer.class);

        // 根据处理类型添加或删除商品ID
        switch (alarmFilterHandlerType) {
            case add:
                ignoreCommodityIds.addAll(handlerCommodityIds);
                break;
            case delete:
                ignoreCommodityIds.removeAll(handlerCommodityIds);
                break;
        }

        // 对ID进行排序后去重
        List<Integer> lastIgnoreCommodityIds = ignoreCommodityIds.stream().sorted().distinct().collect(Collectors.toList());
        LOGGER.info("最终更新商品监控忽略配置的值:{}", JSONArray.toJSONString(lastIgnoreCommodityIds));

        // 更新配置值
        StoreConfig storeConfig = new StoreConfig();
        storeConfig.setKey(StoreConfigKey.monitor_ignore_commodity.name());
        storeConfig.setValue(JSONArray.toJSONString(lastIgnoreCommodityIds));
        int i = storeConfigService.updateByKey(storeConfig);
        if (i == 0) {
            FeiShuMessageUtil.storeGeneralMessage("更新失败", FeiShuGroupRobot.CommodityMonitoringAdjustment, FeiShuAtUser.WXQ);
            return Response.failed("更新失败");
        }

        // 查询并获取商品列表
        List<Commodity> commodities = commodityService.listByCommodityIdIgnoreEnable(ignoreCommodityIds);
        // 收集商品关联的产品ID
        List<Integer> productIds = commodities.stream().map(Commodity::getProduct).collect(Collectors.toList());
        // 根据产品ID获取产品映射
        Map<Integer, Product> productMap = productBatchHelper.productUnLimitEnableMapProductIds(productIds);

        // 构建通知信息
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("【活动价格套餐监控】\n 操作:[%s] ", alarmFilterHandlerType)).append(String.format("套餐ID:[%s] 调整完毕\n", commodityIds)).append("现有过滤套餐如下:\n");
        commodities.forEach(commodity -> {
            String productName = Optional.ofNullable(productMap.get(commodity.getProduct())).map(Product::getName).orElse("");
            String commodityName = commodity.getName();
            String format = String.format(MESSAGE_FORMAT, commodity.getId(), productName, commodityName);
            stringBuilder.append(format);
        });

        // 发送通知信息到飞书
        FeiShuMessageUtil.storeGeneralMessage(stringBuilder.toString(), FeiShuGroupRobot.CommodityMonitoringAdjustment, FeiShuAtUser.matchEmail(email));
        // 返回成功响应
        return Response.ok();
    }

}
