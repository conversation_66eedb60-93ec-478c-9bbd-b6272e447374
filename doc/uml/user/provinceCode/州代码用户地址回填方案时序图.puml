@startuml
'https://plantuml.com/sequence-diagram
title
州代码用户地址回填方案时序图
end title

'客户端（接口）
participant client as c
'服务端（服务器）
participant server as s
'飞书
participant feishu as feishu
'数据库
database db as db

autonumber 1
c -> s: 触发回填请求

opt 基础数据准备
s -> db: 读取用户地址总数
db --> s:返回用户地址总数(下面称作 addrCount)
s -> db: 读取州代码数据
db --> s:返回州代码数据
s -> s: 州代码数据转 HashMap （下面称作 provinceMap）
note left
key: province 州名字
value: provinceCode 州代码
end note

end

s -> db: 查询1000条需要更新的记录（下面称作addrList）
note left:第一期仅处理 US/CA

db --> s: 需要更新的记录

opt 更新addrList步骤
s -> s: 遍历 addrList（item=addr）
s -> s: 通过addr的province作为provinceMap的key获取provinceCode
note left: 也就是获取州代码provinceCode

s -> s: 将provinceCode更新到addr
end

opt 将addrList更新到db
s -> s: 获取addrList的所有ID （addrIds）
s -> s: log.info【 addrIds,addrList长度 】
s -> s: 将addrList转成Map key:province(省/州) value:addrIds(该省/州的地址id集合)
s -> db: 将更新后的addrList 批量更新（）
note left: 具体请查看批量更新方案

group 更新过程有异常
s->feishu: 发送飞书消息提醒
s->s:log.error【 addrIds,addrList长度,error】
else 无异常
db --> s: 更新结果
group 更新结果为true
s->s: 更新成功总数
else 更新结果为false
s->feishu:发送飞书消息提醒
s->s:log.error【 addrIds,addrList长度】
end

end

s->s:重复步骤 7，直到 addrList 为空
s->s:判断更新结果总数和addrCount是否相等
group 相等
s->feishu:发送消息提醒：更新完毕，结果相等
else 不相等
s->feishu:发送消息提醒：更新完毕，结果不相等，请检查数据库
end

autonumber stop







@enduml