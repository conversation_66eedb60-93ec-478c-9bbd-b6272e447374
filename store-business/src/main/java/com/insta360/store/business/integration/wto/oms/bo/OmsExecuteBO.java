package com.insta360.store.business.integration.wto.oms.bo;

import com.insta360.store.business.order.enums.OrderStockSourceType;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.model.RmaOrderDetail;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2024/11/29
 * @Description:
 */
public class OmsExecuteBO implements Serializable {

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 售后单号
     */
    private String rmaNumber;

    /**
     * 是否为历史数据
     */
    private Boolean historyMark;

    /**
     * 推单来源
     */
    private OrderStockSourceType orderStockSourceType;

    /**
     * 组合套餐料号
     */
    private String bundleCommodityCode;

    /**
     * 售后单对象（主要解决主从数据问题）
     */
    private RmaOrder rmaOrder;

    /**
     * 售后单详情对象（主要解决主从数据问题）
     */
    private RmaOrderDetail rmaOrderDetail;

    /**
     * 订单地址
     */
    private OmsOrderDeliveryBO orderDelivery;

    /**
     * 赠品信息bo
     */
    private OmsGiftBO omsGiftBo;

    /**
     * sku编码bo
     */
    private OmsSkuCodeBO omsSkuCodeBo;

    /**
     * 订单发货详情
     */
    private OmsOrderDeliveryDetailBO orderDeliveryDetailBo;

    /**
     * 订单退款oms货物拦截结果
     */
    private OmsOrderRefundInterceptResultBO omsOrderRefundInterceptResultBo;

    /**
     * 订单退款售后商品入库结果
     */
    private OmsReturnItemStockResultBO omsReturnItemStockResultBo;

    /**
     * oms库存查询
     */
    private OmsSkuStockQueryBO omsSkuStockQuery;

    /**
     * sku库存同步信息
     */
    private OmsSkuStockSyncNotifyBO omsSkuStockSyncNotifyBo;

    private OmsLogisticsTrajectorySyncNotifyBO omsLogisticsTrajectorySyncNotifyBo;

    public OmsExecuteBO() {
    }

    public OmsExecuteBO(OmsOrderDeliveryBO orderDelivery) {
        this.orderDelivery = orderDelivery;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public OmsOrderDeliveryBO getOrderDelivery() {
        return orderDelivery;
    }

    public void setOrderDelivery(OmsOrderDeliveryBO orderDelivery) {
        this.orderDelivery = orderDelivery;
    }

    public OmsGiftBO getOmsGiftBo() {
        return omsGiftBo;
    }

    public void setOmsGiftBo(OmsGiftBO omsGiftBo) {
        this.omsGiftBo = omsGiftBo;
    }

    public OmsOrderDeliveryDetailBO getOrderDeliveryDetailBo() {
        return orderDeliveryDetailBo;
    }

    public void setOrderDeliveryDetailBo(OmsOrderDeliveryDetailBO orderDeliveryDetailBo) {
        this.orderDeliveryDetailBo = orderDeliveryDetailBo;
    }

    public OmsOrderRefundInterceptResultBO getOmsOrderRefundInterceptResultBo() {
        return omsOrderRefundInterceptResultBo;
    }

    public void setOmsOrderRefundInterceptResultBo(OmsOrderRefundInterceptResultBO omsOrderRefundInterceptResultBo) {
        this.omsOrderRefundInterceptResultBo = omsOrderRefundInterceptResultBo;
    }

    public OmsReturnItemStockResultBO getOmsReturnItemStockResultBo() {
        return omsReturnItemStockResultBo;
    }

    public void setOmsReturnItemStockResultBo(OmsReturnItemStockResultBO omsReturnItemStockResultBo) {
        this.omsReturnItemStockResultBo = omsReturnItemStockResultBo;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getBundleCommodityCode() {
        return bundleCommodityCode;
    }

    public void setBundleCommodityCode(String bundleCommodityCode) {
        this.bundleCommodityCode = bundleCommodityCode;
    }

    public OrderStockSourceType getOrderStockSourceType() {
        return orderStockSourceType;
    }

    public void setOrderStockSourceType(OrderStockSourceType orderStockSourceType) {
        this.orderStockSourceType = orderStockSourceType;
    }

    public RmaOrder getRmaOrder() {
        return rmaOrder;
    }

    public void setRmaOrder(RmaOrder rmaOrder) {
        this.rmaOrder = rmaOrder;
    }

    public Boolean getHistoryMark() {
        return historyMark;
    }

    public void setHistoryMark(Boolean historyMark) {
        this.historyMark = historyMark;
    }

    public RmaOrderDetail getRmaOrderDetail() {
        return rmaOrderDetail;
    }

    public void setRmaOrderDetail(RmaOrderDetail rmaOrderDetail) {
        this.rmaOrderDetail = rmaOrderDetail;
    }

    public OmsSkuStockQueryBO getOmsSkuStockQuery() {
        return omsSkuStockQuery;
    }

    public void setOmsSkuStockQuery(OmsSkuStockQueryBO omsSkuStockQuery) {
        this.omsSkuStockQuery = omsSkuStockQuery;
    }

    public OmsSkuStockSyncNotifyBO getOmsSkuStockSyncNotifyBo() {
        return omsSkuStockSyncNotifyBo;
    }

    public void setOmsSkuStockSyncNotifyBo(OmsSkuStockSyncNotifyBO omsSkuStockSyncNotifyBo) {
        this.omsSkuStockSyncNotifyBo = omsSkuStockSyncNotifyBo;
    }

    public OmsLogisticsTrajectorySyncNotifyBO getOmsLogisticsTrajectorySyncNotifyBo() {
        return omsLogisticsTrajectorySyncNotifyBo;
    }

    public void setOmsLogisticsTrajectorySyncNotifyBo(OmsLogisticsTrajectorySyncNotifyBO omsLogisticsTrajectorySyncNotifyBo) {
        this.omsLogisticsTrajectorySyncNotifyBo = omsLogisticsTrajectorySyncNotifyBo;
    }

    public OmsSkuCodeBO getOmsSkuCodeBo() {
        return omsSkuCodeBo;
    }

    public void setOmsSkuCodeBo(OmsSkuCodeBO omsSkuCodeBo) {
        this.omsSkuCodeBo = omsSkuCodeBo;
    }

    @Override
    public String toString() {
        return "OmsExecuteBO{" +
                "orderId=" + orderId +
                ", orderNumber='" + orderNumber + '\'' +
                ", rmaNumber='" + rmaNumber + '\'' +
                ", historyMark=" + historyMark +
                ", orderStockSourceType=" + orderStockSourceType +
                ", bundleCommodityCode='" + bundleCommodityCode + '\'' +
                ", rmaOrder=" + rmaOrder +
                ", rmaOrderDetail=" + rmaOrderDetail +
                ", orderDelivery=" + orderDelivery +
                ", omsGiftBo=" + omsGiftBo +
                ", omsSkuCodeBo=" + omsSkuCodeBo +
                ", orderDeliveryDetailBo=" + orderDeliveryDetailBo +
                ", omsOrderRefundInterceptResultBo=" + omsOrderRefundInterceptResultBo +
                ", omsReturnItemStockResultBo=" + omsReturnItemStockResultBo +
                ", omsSkuStockQuery=" + omsSkuStockQuery +
                ", omsSkuStockSyncNotifyBo=" + omsSkuStockSyncNotifyBo +
                ", omsLogisticsTrajectorySyncNotifyBo=" + omsLogisticsTrajectorySyncNotifyBo +
                '}';
    }
}
