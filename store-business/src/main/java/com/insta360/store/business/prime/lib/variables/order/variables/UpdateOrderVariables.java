package com.insta360.store.business.prime.lib.variables.order.variables;

import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.prime.lib.variables.PrimeVariables;
import com.insta360.store.business.prime.lib.variables.order.OrderIdentifierDTO;
import com.insta360.store.business.prime.lib.variables.order.TaxTotalAmountDTO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15
 */
public class UpdateOrderVariables implements PrimeVariables {

    private OrderIdentifierDTO orderIdentifier;

    private InputDTO input;

    public OrderIdentifierDTO getOrderIdentifier() {
        return orderIdentifier;
    }

    public void setOrderIdentifier(OrderIdentifierDTO orderIdentifier) {
        this.orderIdentifier = orderIdentifier;
    }

    public InputDTO getInput() {
        return input;
    }

    public void setInput(InputDTO input) {
        this.input = input;
    }


    public static class InputDTO {

        private String desiredExecutionState;

        private TaxTotalAmountDTO taxes;

        public InputDTO(BigDecimal totalTax) {
            this.taxes = new TaxTotalAmountDTO(totalTax, Currency.USD.name());
        }

        public String getDesiredExecutionState() {
            return desiredExecutionState;
        }

        public void setDesiredExecutionState(String desiredExecutionState) {
            this.desiredExecutionState = desiredExecutionState;
        }

        public TaxTotalAmountDTO getTaxes() {
            return taxes;
        }

        public void setTaxes(TaxTotalAmountDTO taxes) {
            this.taxes = taxes;
        }

    }
}
