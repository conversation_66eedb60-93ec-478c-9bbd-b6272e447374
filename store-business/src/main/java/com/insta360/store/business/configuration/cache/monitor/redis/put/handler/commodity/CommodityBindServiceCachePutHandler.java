package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.commodity;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.outgoing.rpc.store.job.CategoryPageCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.HomeItemCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.ProductCachePutService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import feign.RetryableException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/2/18 下午3:58
 */
@Component
public class CommodityBindServiceCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommodityBindServiceCachePutHandler.class);

    @Autowired
    ProductCachePutService productCachePutService;

    @Autowired
    HomeItemCachePutService homeItemCachePutService;

    @Autowired
    CategoryPageCachePutService categoryPageCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws Exception {
        List<Integer> productIds = cachePutKeyParameter.getProductIds();
        List<Integer> homeItemIds = cachePutKeyParameter.getHomeItemIds();
        Collection<Product> products = productService.listByIds(new ArrayList<>(productIds));
        // 如果没有产品数据，则中断流程
        if (CollectionUtils.isEmpty(products)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。产品集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }
        // home item ids为空，中断流程
        if (CollectionUtils.isEmpty(homeItemIds)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。home item ids 为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}。", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(products, countDownLatch));
        cachePutThreadPool.execute(() -> this.task2(null, countDownLatch));
        cachePutThreadPool.execute(() -> this.task3(homeItemIds, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新参数
        StoreCacheDataChangeEventBO storeCacheDataChangeEvent = new StoreCacheDataChangeEventBO();
        storeCacheDataChangeEvent.setProductEvents(this.parseProductEvent(products));
        return storeCacheDataChangeEvent;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.CLIMB_SERVICE_COMMODITY;
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            List<Product> products = (List<Product>) param;
            List<Integer> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
            // 更新产品页
            CacheConstant.COUNTIES.forEach(
                    cacheCounties -> productIds.forEach(productId -> productCachePutService.getInfo(productId, cacheCounties.getCountry(), cacheCounties.getLanguage()))
            );

            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
        try {
            // 更新虚拟服务类目
            CacheConstant.COUNTIES.forEach(
                    cacheCounties -> categoryPageCachePutService.listCategory(ProductCategoryMainType.CM_VIRTUAL_SERVICE.name(), cacheCounties.getCountry(), cacheCounties.getLanguage())
            );

            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务2完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task2}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {
        try {
            // 更新首页配置
            List<Integer> homeItemIds = (List<Integer>) param;
            CacheConstant.COUNTIES.forEach(
                    cacheCounties -> homeItemIds.forEach(homeItemId -> homeItemCachePutService.listHomeItemInfoByHomeItemType(homeItemId, cacheCounties.getCountry(), cacheCounties.getLanguage()))
            );

            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务3完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task3}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.PRODUCT_INFO, CacheableType.CATEGORY_PAGE, CacheableType.HOME_ITEM_KEY);
    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }
}
