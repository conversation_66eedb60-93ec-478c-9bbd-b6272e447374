package com.insta360.store.business.trade.support.handler;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.compass.libs.aliyun.oss.enums.EndpointEnum;
import com.insta360.compass.libs.aliyun.oss.enums.ModuleEnum;
import com.insta360.store.business.common.constants.CommonConstant;
import com.insta360.store.business.common.constants.RedisKeyConstant;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.trade.bo.CustomImageCoordinateBO;
import com.insta360.store.business.trade.bo.SerialNumberImageBO;
import com.insta360.store.business.trade.dto.CustomImageInfoDTO;
import com.insta360.store.business.trade.exception.TradeErrorCode;
import com.insta360.store.business.trade.service.impl.creation.CustomServiceResult;
import com.insta360.store.business.trade.service.impl.helper.bind_service.BigBufferedImageHelper;
import com.insta360.store.business.trade.service.impl.helper.bind_service.enums.CustomImageType;
import com.insta360.store.business.trade.support.CustomImageInterface;
import com.insta360.store.business.trade.support.format.CustomImageMergePack;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;

import javax.imageio.*;
import javax.imageio.metadata.IIOInvalidTreeException;
import javax.imageio.metadata.IIOMetadata;
import javax.imageio.metadata.IIOMetadataNode;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.List;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27
 */
public abstract class BaseCustomImageHandler implements CustomImageInterface {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseCustomImageHandler.class);

    /**
     * 定制铁KEY前缀
     */
    protected static final String REDIS_KEY_PRE_FIX = RedisKeyConstant.CUSTOM_IMAGE_KEY + "export:";

    /**
     * 参数分隔符
     */
    protected static final String PARAM_SEPARATOR = ",";

    /**
     * 分辨率
     */
    protected static final Integer DPI = 600;

    @Autowired
    OSSService ossService;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    CustomImageMergePack customImageMergePack;

    /**
     * 获取定制图像类型
     *
     * @return {@link CustomImageType}
     */
    abstract protected CustomImageType getCustomImageType();

    /**
     * 根据订单合并
     *
     * @param customImageInfoDto 定制图像信息dto
     * @return boolean
     */
    private boolean isOderNumberMerge(CustomImageInfoDTO customImageInfoDto) {
        return StringUtils.isNotBlank(customImageInfoDto.getOrderNumbers());
    }

    /**
     * 定制贴缓存key
     *
     * @param jobNumber 飞书工号
     * @return {@link String}
     */
    public String getImageCacheKey(String jobNumber) {
        return REDIS_KEY_PRE_FIX + getCustomImageType().name() + "_" + jobNumber;
    }

    /**
     * 检查参数
     *
     * @param customImageInfoParam 定制图像信息参数
     */
    @Override
    public void checkParameters(CustomImageInfoDTO customImageInfoParam) {
        // 定制贴类型
        CustomImageType customImageType = customImageInfoParam.parseCustomImageType();
        if (customImageType == null) {
            throw new InstaException(TradeErrorCode.ServiceTypeNotFoundException);
        }

        // 两者都需考虑，且同时只能存在一种（订单号优先判断）
        String imageIds = customImageInfoParam.getImageIds();
        String orderNumbers = customImageInfoParam.getOrderNumbers();
        if (StringUtils.isAllBlank(imageIds, orderNumbers)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "订单号和序号不能都为空");
        }

        // 订单校验
        boolean oderNumberMerge = isOderNumberMerge(customImageInfoParam);
        if (oderNumberMerge) {
            checkOrderNumbers(orderNumbers);
        }

        // 序号校验
        if (!oderNumberMerge && imageIds.startsWith(CommonConstant.ORDER_PREFIX)) {
            throw new InstaException(TradeErrorCode.exportParameterTypeErrors);
        }

        // 定制贴前缀校验
        String customPrefix = customImageType.getSerialPrefix();
        if (StringUtils.isNotBlank(imageIds) && !imageIds.trim().startsWith(customPrefix)) {
            throw new InstaException(TradeErrorCode.exportParameterTypeErrors);
        }
    }

    /**
     * 订单号校验
     *
     * @param orderNumbers 订单编号
     */
    private static void checkOrderNumbers(String orderNumbers) {
        if (!orderNumbers.startsWith(CommonConstant.ORDER_PREFIX)) {
            throw new InstaException(TradeErrorCode.exportParameterTypeErrors);
        }

        List<String> orderNumberList = Arrays.stream(orderNumbers.split(PARAM_SEPARATOR)).collect(Collectors.toList());
        Map<String, Long> orderNumberMap = orderNumberList.stream()
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));

        orderNumberMap.forEach((orderNumber, count) -> {
            if (count > 1) {
                throw new InstaException(-1, String.format("订单号重复：[%s]", orderNumber));
            }
        });
    }

    /**
     * 封装参数
     *
     * @param customImageInfoParam 定制图像信息参数
     * @return {@link List}<{@link SerialNumberImageBO}>
     */
    @Override
    public List<SerialNumberImageBO> packParameters(CustomImageInfoDTO customImageInfoParam) {
        boolean oderNumberMerge = isOderNumberMerge(customImageInfoParam);
        String needParams = oderNumberMerge ? customImageInfoParam.getOrderNumbers() : customImageInfoParam.getImageIds();
        // 区分订单号和图片序号 (order_numbers":",INSxxx" 空元素需要过滤)
        List<String> params = Arrays.stream(needParams.split(PARAM_SEPARATOR)).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        return oderNumberMerge ? customImageMergePack.listNumberImageByOrderNumbersAndCustomType(params, getCustomImageType()) : customImageMergePack.listNumberImageBySerialIdsAndCustomType(params, getCustomImageType());
    }

    /**
     * 创建图像坐标
     *
     * @return {@link List}<{@link CustomImageCoordinateBO}>
     */
    protected List<CustomImageCoordinateBO> createImageCoordinates() {
        CustomImageType customImageType = this.getCustomImageType();
        List<CustomImageCoordinateBO> imageCoordinates = new ArrayList<>();
        int x = customImageType.getLeftSizeDistance();
        int y = customImageType.getTopSizeDistance();
        for (int j = 0; j < customImageType.getRowImageSize(); j++) {
            for (int i = 0; i < customImageType.getLineImageSize(); i++) {
                imageCoordinates.add(new CustomImageCoordinateBO(x, y));
                x = x + customImageType.getLateralImageDistance() + customImageType.getImageWidth();
            }
            y = y + customImageType.getEndianImageDistance() + customImageType.getImageHeight();
            x = customImageType.getLeftSizeDistance();
        }
        return imageCoordinates;
    }

    /**
     * 创建序号的坐标
     *
     * @param serialNumber 序号数量
     * @return {@link List}<{@link CustomImageCoordinateBO}>
     */
    private List<CustomImageCoordinateBO> createSerialNumberCoordinates(int serialNumber) {
        List<CustomImageCoordinateBO> imageCoordinateBos = this.createImageCoordinates();
        List<CustomImageCoordinateBO> needCordinateBoList = imageCoordinateBos.subList(0, serialNumber);
        return needCordinateBoList.stream().peek(coordinate -> {
            coordinate.setCustomImageCoordinateY(coordinate.getCustomImageCoordinateY() + this.getCustomImageType().getImageHeight());
        }).collect(Collectors.toList());
    }

    /**
     * 上传图片到oss
     *
     * @param image 图像
     * @return {@link String}
     */
    protected String uploadImageOss(BufferedImage image) throws IOException {
        ImageOutputStream stream = null;
        try {
            CustomImageType customImageType = this.getCustomImageType();
            Iterator<ImageWriter> iw = ImageIO.getImageWritersByFormatName(customImageType.getImageSuffix());
            ImageWriter writer = iw.next();
            ImageWriteParam writeParam = writer.getDefaultWriteParam();
            IIOMetadata metadata = writer.getDefaultImageMetadata(ImageTypeSpecifier.createFromBufferedImageType(BufferedImage.TYPE_INT_ARGB), writeParam);
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            handleDpi(metadata);
            stream = ImageIO.createImageOutputStream(output);
            writer.setOutput(stream);
            writer.write(metadata, new IIOImage(image, null, metadata), writeParam);
            byte[] bytes = output.toByteArray();
            return ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, bytes, UUIDUtils.generateUuid() + ".png");
        } finally {
            if (stream != null) {
                stream.close();
            }
        }
    }

    /**
     * 修改DPI
     *
     * @param metadata
     * @throws IIOInvalidTreeException
     */
    protected void handleDpi(IIOMetadata metadata) throws IIOInvalidTreeException {
        double dotsPerMilli = 1.0 * DPI / 10 / 2.54;
        IIOMetadataNode horiz = new IIOMetadataNode("HorizontalPixelSize");
        horiz.setAttribute("value", Double.toString(dotsPerMilli));

        IIOMetadataNode vert = new IIOMetadataNode("VerticalPixelSize");
        vert.setAttribute("value", Double.toString(dotsPerMilli));

        IIOMetadataNode dim = new IIOMetadataNode("Dimension");
        dim.appendChild(horiz);
        dim.appendChild(vert);

        IIOMetadataNode root = new IIOMetadataNode("javax_imageio_1.0");
        root.appendChild(dim);

        metadata.mergeTree("javax_imageio_1.0", root);
    }

    /**
     * 获取图像通过filepath
     *
     * @param filePath 自定义图像路径
     * @return {@link BufferedImage}
     */
    protected BufferedImage getImageByFilepath(String filePath) {
        try (InputStream inputStream = resourceLoader.getResource(filePath).getInputStream()) {
            return ImageIO.read(inputStream);
        } catch (IOException e) {
            LOGGER.error(String.format("load material image error:%s,path:%s", e.getMessage(), filePath), e);
            throw new InstaException(-1, String.format("load material image error:%s,path:%s", e.getMessage(), filePath));
        }
    }

    /**
     * 下载Oss图片存入BO中
     *
     * @param serialNumberImageBoList 序列号图片集合
     * @throws InterruptedException 中断异常
     */
    private void downloadOssImages(List<SerialNumberImageBO> serialNumberImageBoList) {
        LOGGER.info("开始加载图片......");
        for (SerialNumberImageBO serialNumberImageBo : serialNumberImageBoList) {
            try {
                serialNumberImageBo.setImage(ImageIO.read(new URL(serialNumberImageBo.getImageUrl())));
            } catch (IOException e) {
                LOGGER.error(String.format("download oss image error:%s,url:%s", e.getMessage(), serialNumberImageBo.getImageUrl()), e);
                throw new InstaException(-1, String.format("download oss image error:%s,url:%s", e.getMessage(), serialNumberImageBo.getImageUrl()));
            }
        }
    }

    /**
     * 将总的图片数量按 枚举类中定义的大小 等分
     *
     * @param bufferedImages 需要分割的缓冲图像
     * @return {@link List}<{@link List}<{@link T}>>
     */
    private <T> List<List<T>> splitImageLists(List<T> bufferedImages) {
        List<List<T>> needLists = new ArrayList<>();
        for (int i = 0, j = bufferedImages.size(); i < j; i += this.getCustomImageType().getPageSize()) {
            List<T> splitList = bufferedImages.subList(i, Math.min(i + this.getCustomImageType().getPageSize(), j));
            needLists.add(splitList);
        }
        return needLists;
    }

    /**
     * 合并生产图像
     *
     * @param bufferedImages 小图
     * @param graphics       图形
     * @param outImage       外层大图
     * @return {@link String}
     * @throws IOException ioexception
     */
    protected String mergeProductionImage(Graphics2D graphics, BufferedImage outImage, List<BufferedImage> bufferedImages) throws IOException {
        LOGGER.info("合成生产图....");
        // 创建序号
        List<CustomImageCoordinateBO> coordinates = this.createImageCoordinates();
        for (int i = 0; i < bufferedImages.size(); i++) {
            graphics.drawImage(bufferedImages.get(i), coordinates.get(i).getCustomImageCoordinateX(), coordinates.get(i).getCustomImageCoordinateY(), null);
        }
        return uploadImageOss(outImage);
    }

    /**
     * 写入序号
     *
     * @param originalImage 原始图像
     * @param serialNumbers 序列号
     * @return {@link BufferedImage}
     */
    protected String editorSerialNumber(Graphics2D graphics, BufferedImage originalImage, List<String> serialNumbers) throws IOException {
        LOGGER.info("写入序号....");
        // 创建序号坐标
        List<CustomImageCoordinateBO> coordinates = this.createSerialNumberCoordinates(serialNumbers.size());
        CustomImageType customImageType = this.getCustomImageType();

        // 序号颜色大小创建
        graphics.setColor(Color.RED);
        Font font = new Font("宋体", Font.PLAIN, customImageType.getSerialNumberSize());
        graphics.setFont(font);
        FontMetrics fontMetrics = graphics.getFontMetrics();

        // 写入序号
        for (int i = 0; i < coordinates.size(); i++) {
            int fontWidth = fontMetrics.stringWidth(serialNumbers.get(i));
            int x = coordinates.get(i).getCustomImageCoordinateX() + ((customImageType.getImageWidth() - fontWidth) / 2);
            int y = coordinates.get(i).getCustomImageCoordinateY() + customImageType.getSerialNumberOfCoordinatesY();
            graphics.drawString(serialNumbers.get(i), x, y);
        }
        return uploadImageOss(originalImage);
    }

    /**
     * 添加图像层
     *
     * @param originalImage 原始图像
     * @param graphics      图形
     * @param imageLayer    图像层
     * @return 修改后的图像URL
     * @throws IOException ioexception
     */
    protected String addOriginalImageLayer(Graphics2D graphics, BufferedImage originalImage, BufferedImage imageLayer) throws IOException {
        LOGGER.info("增加图层...");
        if (Objects.isNull(imageLayer)) {
            return "";
        }
        graphics.drawImage(imageLayer, 0, 0, null);
        return uploadImageOss(originalImage);
    }

    /**
     * 批量创建合并的定制贴图像
     *
     * @param splitBufferedImages 序号图片封装BO集合
     * @return {@link CustomServiceResult}
     * @throws InterruptedException 中断异常
     * @throws ExecutionException   执行异常
     */
    @Override
    public CustomServiceResult.CustomMergeImage batchCreateMergeHandler(List<SerialNumberImageBO> splitBufferedImages, Integer index) throws IOException {
        LOGGER.info("batchCreateMergeHandler 开始合并图片逻辑");

        // 根据图片路径加载file，创建 Image
        this.downloadOssImages(splitBufferedImages);

        CustomImageType customImageType = this.getCustomImageType();
        // 从resource中获取基线图文件
        BufferedImage datumFileImage = this.getImageByFilepath(customImageType.getDatumImagePath());

        LOGGER.info("开始合成图片...");

        // 提取要合成的原图
        List<BufferedImage> bufferedImages = splitBufferedImages.stream().map(SerialNumberImageBO::getImage).collect(Collectors.toList());
        // 提取序号
        List<String> serialNumbers = splitBufferedImages.stream().map(SerialNumberImageBO::getSerialNumber).collect(Collectors.toList());

        // 创建底层大图
        BufferedImage outImage = BigBufferedImageHelper.create(customImageType.getMergeImageWidth(),
                customImageType.getMergeImageHeight(),
                BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = outImage.createGraphics();
        graphics.getDeviceConfiguration().createCompatibleImage(customImageType.getImageWidth(),
                customImageType.getImageHeight(),
                Transparency.TRANSLUCENT);

        // 将定制贴写入大图中
        String productionImage = this.mergeProductionImage(graphics, outImage, bufferedImages);

        // 给图片写入序号
        String serialNumberImage = this.editorSerialNumber(graphics, outImage, serialNumbers);

        // 写入基准线的图层
        String datumImage = this.addOriginalImageLayer(graphics, outImage, datumFileImage);

        // 清理释放资源
        graphics.dispose();
        cleanImage(bufferedImages);
        cleanImage(Arrays.asList(outImage));
        LOGGER.info("生成的图片url=>生产图：{}，序号图：{},线框图：{}", productionImage, serialNumberImage, datumImage);
        CustomServiceResult.CustomMergeImage customMergeImage = new CustomServiceResult.CustomMergeImage(index,
                productionImage,
                serialNumberImage,
                datumImage);

        // 清理资源文件
        cleanImage(Arrays.asList(datumFileImage));

        return customMergeImage;
    }

    /**
     * 异步运行合并处理
     *
     * @param serialNumberImageBoList 序号图片封装集合
     * @param jobNumber               飞书工号
     * @param now
     * @throws IOException          ioexception
     * @throws ExecutionException   执行异常
     * @throws InterruptedException 中断异常
     */
    @Override
    public void asyncRunMergeHandler(List<SerialNumberImageBO> serialNumberImageBoList, String jobNumber, LocalDateTime now) {
        // 执行合成逻辑
        ExecutorService customExecutorService = CustomImageThreadPool.getInstance();

        // 根据小图的数量切分任务
        List<List<SerialNumberImageBO>> subSerialNumberImageBoList = this.splitImageLists(serialNumberImageBoList);
        int customSize = subSerialNumberImageBoList.size();
        CountDownLatch countDownLatch = new CountDownLatch(customSize);
        List<CustomServiceResult.CustomMergeImage> customMergeImageCopyOnWriteList = new CopyOnWriteArrayList<>();

        // 为了保证最终导出的图片序号保持有序，手动控制序号索引位置
        for (int i = 0; i < subSerialNumberImageBoList.size(); i++) {
            int index = i;
            customExecutorService.submit(() -> {
                try {
                    CustomServiceResult.CustomMergeImage customMergeImage = this.batchCreateMergeHandler(subSerialNumberImageBoList.get(index), index);
                    customMergeImageCopyOnWriteList.add(customMergeImage);
                    LOGGER.info("custom merge image .....,now:{},jobNumber:{},data:{}", now, jobNumber, subSerialNumberImageBoList.get(index));
                } catch (Exception e) {
                    LOGGER.error("custom image merge error", e);
                    FeiShuMessageUtil.storeGeneralMessage(String.format("定制贴合成发生异常,error:%s", e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
                } finally {
                    countDownLatch.countDown();
                    if (countDownLatch.getCount() == 0L) {
                        List<CustomServiceResult.CustomMergeImage> customMergeImages = customMergeImageCopyOnWriteList.stream()
                                .sorted(Comparator.comparing(CustomServiceResult.CustomMergeImage::getIndex)).collect(Collectors.toList());
                        LOGGER.info("图片生成完毕，开始写入数据:[{}]", customMergeImages);
                        CustomServiceResult customServiceResult = new CustomServiceResult();
                        customServiceResult.setCustomMergeImageList(customMergeImages);
                        customServiceResult.setCreateTime(now);
                        customServiceResult.setCustomMergeImageNumber(customSize);
                        String imageCacheKey = this.getImageCacheKey(jobNumber);
                        RedisTemplateUtil.setKeyValue(imageCacheKey, customServiceResult);
                    }
                }
            });
        }
    }

    /**
     * 清理图像
     *
     * @param bufferedImages 缓冲图像
     */
    protected void cleanImage(List<BufferedImage> bufferedImages) {
        bufferedImages.forEach(BigBufferedImageHelper::dispose);
    }

    /**
     * 获取合并图像链接
     *
     * @param jobNumber 飞书工号
     * @return {@link CustomServiceResult}
     */
    @Override
    public CustomServiceResult getMergeImageLink(String jobNumber) {
        String imageCacheKey = this.getImageCacheKey(jobNumber);
        Object value = RedisTemplateUtil.getValue(imageCacheKey);
        return value == null ? null : (CustomServiceResult) value;
    }
}
