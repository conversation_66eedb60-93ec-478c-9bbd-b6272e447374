<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.commodity.dao.CommodityDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.commodity.dao.CommodityDao"></cache-ref>

    <!-- 根据套餐区分是否是相机  -->
    <select id="listCameraCommodities" resultType="Integer">
        SELECT
	        pc.id
        FROM
            product AS po
        JOIN
	        product_commodity AS pc ON pc.product = po.id
        WHERE
	        pc.id in
	        <foreach collection="commodity_ids" item="commodity_id" open="(" close=")" separator=",">
                #{commodity_id}
            </foreach>
        AND
	        po.is_camera = true
    </select>

    <!-- 给套餐批量绑定tag   -->
    <update id="bindTagBatch">
        UPDATE
            product_commodity
        SET
            tag_id = #{tagGroupId}
        WHERE
            id IN
            <foreach collection="commodityIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </update>

    <update id="updateOrderIndexByIds">
        <foreach collection="commodities" item="commodity" separator=";">
            UPDATE
            product_commodity
            SET
            order_index= #{commodity.orderIndex}
            WHERE id= #{commodity.id}
        </foreach>
    </update>


    <!-- 根据创建时间范围查询套餐 -->
    <select id="listCommodityByTimeAndEnabled" resultType="com.insta360.store.business.commodity.model.Commodity"
            parameterType="com.insta360.store.business.integration.google.dto.GoogleCommodityInsertQueryDTO">
        select p1.* from
        product_commodity as p1
        join
        product as p2 on p1.product = p2.id
        left join
        product_category_main pcm on p2.category_key = pcm.category_key
        left join
        product_category_subset pcs on (pcs.category_main_key = p2.category_key or pcs.category_subset_key =
        p2.category_key)
        join
        product_commodity_price as p3 on p1.id = p3.commodity_id

        <where>
            (pcm.category_key in ('CM_HAND_STABILIZER', 'CM_WEBCAM')
            or pcs.category_main_key in ('CM_CAMERA', 'CM_ACCESSORY'))
            <if test="state != null">
                and p3.enabled = #{state}
            </if>
            <if test="fromTime != null and endTime != null">
                and p1.create_time between #{fromTime} and #{endTime}
            </if>
            <if test="commodityIds != null">
                and p1.id in
                <foreach collection="commodityIds" item="commodityId" open="(" close=")" separator=",">
                    #{commodityId}
                </foreach>
            </if>
        </where>
        group by p1.id
    </select>

    <!-- 套餐库存不足场景查询 -->
    <select id="listByCommodityLowInventory" resultType="com.insta360.store.business.commodity.bo.CommodityLowInventoryBO" useCache="false">
        select p1.name as 'productName',c1.id as 'commodityId',c1.name as 'commodityName'
            from product p1
                left join product_category_main pcm on p1.category_key = pcm.category_key
                left join product_category_subset pcs on (pcs.category_main_key = p1.category_key or pcs.category_subset_key = p1.category_key)
                join product_commodity c1 on p1.id = c1.product
                join product_commodity_sale_state c2 on c1.id = c2.commodity_id
                join product_commodity_trade_rule c3 on c1.id = c3.commodity_id
        where
                p1.enabled = 1
                and c1.enabled = 1
                and p1.is_repair_service = 0
                and (pcm.category_key not in ('CM_REPAIR_SERVICE', 'CM_INTERIOR', 'CM_THIRD_PARTY') or pcs.category_main_key not in ('CM_REPAIR_SERVICE', 'CM_INTERIOR', 'CM_THIRD_PARTY'))
        <if test="stock != null">
            <![CDATA[ and c3.stock_count <= #{stock} ]]>
        </if>
        <if test="saleState != null">
            and c2.sale_state = #{saleState}
        </if>
            group by c1.id;
    </select>

    <!-- 批量插入 -->
    <insert id="batchImportScenesSave" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into product_commodity (name, product, can_be_gift,normal_sale, bind_service, customs_declaration,order_index)
        values
        <foreach collection="list" item="commodity" separator=",">
            (
            #{commodity.name},
            #{commodity.product},
            #{commodity.canBeGift},
            #{commodity.normalSale},
            #{commodity.bindService},
            #{commodity.customsDeclaration},
            #{commodity.orderIndex}
            )
        </foreach>
    </insert>

    <insert id="batchInsertCommodities" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into product_commodity (name, product, order_index, enabled)
        values
        <foreach collection="list" item="commodity" separator=",">
            (#{commodity.name}, #{commodity.product}, #{commodity.orderIndex}, #{commodity.enabled})
        </foreach>
    </insert>


    <!--  插入数据  -->
    <insert id="saveImport"  keyProperty="id">
        INSERT INTO product_commodity(id, name, product, enabled, order_index, contents, include_commodities, can_be_gift, normal_sale, bind_service, create_time, new_commodity, customs_declaration, standard,  modify_time)
        VALUES (#{id},#{name}, #{product}, #{enabled}, #{orderIndex}, #{contents}, #{includeCommodities}, #{canBeGift}, #{normalSale}, #{bindService}, #{createTime}, #{newCommodity}, #{customsDeclaration}, #{standard} , now());
    </insert>
</mapper>
