package com.insta360.store.service.controller.product.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.admin.order.enums.CommodityAttributeType;
import com.insta360.store.business.commodity.bo.CommodityPackBO;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.commodity.service.impl.helper.CommodityHelper;
import com.insta360.store.business.commodity.service.impl.helper.CommodityStockHelper;
import com.insta360.store.business.insurance.model.ClimbServiceCommodity;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.reseller.dto.condition.ResellerCode;
import com.insta360.store.business.trade.dto.condition.TradePriceParam;
import com.insta360.store.business.trade.service.TradePriceService;
import com.insta360.store.business.utils.CommonUtil;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.meta.format.HomeItemInfoPack;
import com.insta360.store.service.controller.product.vo.CommodityBindClimbServiceVO;
import com.insta360.store.service.controller.product.vo.CommodityCampaignVO;
import com.insta360.store.service.controller.product.vo.CommodityDisplayVO;
import com.insta360.store.service.controller.product.vo.CommodityVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/1/25
 * @Description:
 */
@Component
public class CommodityPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommodityPack.class);

    @Autowired
    ProductService productService;

    @Autowired
    HomeItemInfoPack homeItemInfoPack;

    @Autowired
    TradePriceService tradePriceService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    @Autowired
    CommodityTradeRuleService commodityTradeRuleService;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommodityMetaService commodityMetaService;

    @Autowired
    CommodityStockHelper commodityStockHelper;

    @Autowired
    CommodityHelper commodityHelper;

    /**
     * 批量套餐数据封装
     *
     * @param commodities
     * @param setting
     * @return
     */
    public List<CommodityVO> batchPack(Collection<Commodity> commodities, PackSetting setting) {
        // 过滤禁用产品
        commodities = checkProductEnabled(commodities);
        if (CollectionUtils.isEmpty(commodities)) {
            return new ArrayList<>(0);
        }

        return preBatchPack(commodities, setting);
    }

    /**
     * 产品套餐不校验启用封装
     *
     * @param commodities
     * @param setting
     * @return
     */
    public List<CommodityVO> noCheckBatchPack(Collection<Commodity> commodities, PackSetting setting) {
        if (CollectionUtils.isEmpty(commodities)) {
            return new ArrayList<>(0);
        }
        return preBatchPack(commodities, setting);
    }

    /**
     * 批量封装套餐信息
     *
     * @param commodities
     * @param setting
     */
    private List<CommodityVO> preBatchPack(Collection<Commodity> commodities, PackSetting setting) {
        List<CommodityVO> commodityVOList = new CommodityVO().builds(commodities);
        CommodityPackBO commodityPackBo = new CommodityPackBO();
        List<Integer> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        List<Integer> productIds = commodities.stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());

        long startTime = System.currentTimeMillis();
        packDisplay(commodityIds, commodityPackBo, setting);
        packInfo(commodityIds, commodityPackBo, setting);
        packPrice(commodityIds, commodityPackBo, setting);
        packStock(commodityIds, commodityPackBo, setting);
        packClimbService(commodityIds, commodityPackBo, setting);
        packServiceType(commodityIds, commodityPackBo, setting);
        packTradeRule(commodityIds, commodityPackBo, setting);
        packSaleState(commodityIds, commodityPackBo, setting);
        packDeliveryTimeConfig(commodityIds, commodityPackBo, setting);
        packProductName(productIds, commodityPackBo, setting);
        packProductUrlKey(productIds, commodityPackBo, setting);
        packProductDescription(productIds, commodityPackBo, setting);
        packProductId(productIds, commodityPackBo, setting);
        packCommodityTag(commodityIds, commodityPackBo, setting);
        packSavePrice(commodityIds, commodityPackBo, setting);
        packCommodityAttribute(commodityIds, commodityPackBo, setting);
        LOGGER.info("preBatchPack db consume time...." + commodities.size() + "__" + (System.currentTimeMillis() - startTime));

        return doCommodityBatchPack(commodityVOList, commodityPackBo, setting);
    }

    /**
     * 封装商品属性
     *
     * @param commodityPackBo 商品封装Bo
     * @param setting         设置
     * @param commodityIds    商品id
     */
    private void packCommodityAttribute(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withCommodityAttribute) {
            if (MapUtils.isNotEmpty(commodityPackBo.getCommodityMetaMap())) {
                return;
            }
            List<CommodityMeta> commodityMetas = commodityMetaService.listCommodityMetaByCommodityIds(commodityIds);
            Map<Integer, CommodityMeta> commodityMetaMap = commodityMetas.stream().collect(Collectors.toMap(CommodityMeta::getCommodityId, Function.identity()));
            commodityPackBo.setCommodityMetaMap(commodityMetaMap);
        }
    }

    /**
     * 封装节省金
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packSavePrice(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withSavePrice) {
            if (MapUtils.isNotEmpty(commodityPackBo.getCommodityPriceMap())) {
                return;
            }
            Map<Integer, CommodityPrice> commodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIds, setting.country);
            commodityPackBo.setCommodityPriceMap(commodityPriceMap);

            // 存在价格没配置，取us
            if (commodityIds.size() == commodityPriceMap.size()) {
                return;
            }

            List<Integer> commodityIdList = new ArrayList<>(commodityIds);
            commodityIdList.removeAll(commodityPriceMap.values().stream().map(CommodityPrice::getCommodityId).collect(Collectors.toList()));
            Map<Integer, CommodityPrice> usCommodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIdList, InstaCountry.US);
            usCommodityPriceMap.forEach(commodityPriceMap::put);
        }
    }

    /**
     * 封装产品id
     *
     * @param productIds
     * @param commodityPackBo
     * @param setting
     */
    private void packProductId(List<Integer> productIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductId) {
            if (MapUtils.isNotEmpty(commodityPackBo.getProductMap())) {
                return;
            }
            commodityPackBo.setProductMap(productBatchHelper.productMapProductIds(productIds));
        }
    }

    /**
     * 封装产品描述
     *
     * @param productIds
     * @param commodityPackBo
     * @param setting
     */
    private void packProductDescription(List<Integer> productIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductDescription) {
            if (MapUtils.isNotEmpty(commodityPackBo.getProductInfoMap())) {
                return;
            }
            Map<Integer, ProductInfo> productInfoMap = productBatchHelper.productInfoMapProductIds(productIds, setting.language);
            commodityPackBo.setProductInfoMap(productInfoMap);

            // 产品多语言没配置，取us
            if (productIds.size() == productInfoMap.size()) {
                return;
            }

            List<Integer> productIdList = new ArrayList<>(productIds);
            productIdList.removeAll(productInfoMap.values().stream().map(ProductInfo::getProduct).collect(Collectors.toList()));
            Map<Integer, ProductInfo> usCommodityPriceMap = productBatchHelper.productInfoMapProductIds(productIdList, InstaLanguage.en_US);
            usCommodityPriceMap.forEach(productInfoMap::put);
        }
    }

    /**
     * 套餐数据批量封装
     *
     * @param commodityVOList
     * @param commodityPackBo
     * @param setting
     * @return
     */
    private List<CommodityVO> doCommodityBatchPack(List<CommodityVO> commodityVOList, CommodityPackBO commodityPackBo, PackSetting setting) {
        long startTime = System.currentTimeMillis();
        for (CommodityVO commodityVo : commodityVOList) {
            doPackDisplay(commodityVo, commodityPackBo, setting);
            doPackInfo(commodityVo, commodityPackBo, setting);
            doPackPrice(commodityVo, commodityPackBo, setting);
            doPackStock(commodityVo, commodityPackBo, setting);
            doPackClimbService(commodityVo, commodityPackBo, setting);
            doPackServiceType(commodityVo, commodityPackBo, setting);
            doPackTradeRule(commodityVo, commodityPackBo, setting);
            doPackSaleState(commodityVo, commodityPackBo, setting);
            doPackDeliveryTimeConfig(commodityVo, commodityPackBo, setting);
            doPackProductName(commodityVo, commodityPackBo, setting);
            doPackProductUrlKey(commodityVo, commodityPackBo, setting);
            doPackProductDescription(commodityVo, commodityPackBo, setting);
            doPackProductId(commodityVo, commodityPackBo, setting);
            doPackCommodityTag(commodityVo, commodityPackBo, setting);
            doPackSavePrice(commodityVo, commodityPackBo, setting);
            doPackCommodityAttribute(commodityVo, commodityPackBo, setting);
        }
        LOGGER.info("doCommodityBatchPack consume time...." + (System.currentTimeMillis() - startTime));
        return commodityVOList;
    }

    /**
     * 封装商品属性
     *
     * @param commodityVo     商品vo
     * @param commodityPackBo 商品封装Bo
     * @param setting         设置
     */
    private void doPackCommodityAttribute(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withCommodityAttribute) {
            CommodityMeta commodityMeta = commodityPackBo.getCommodityMetaMap().get(commodityVo.getId());
            if (Objects.isNull(commodityMeta)) {
                LOGGER.error("报关信息缺失..., commodityId:{}", commodityVo.getId());
                // 如果未配置则默认为危险品
                commodityVo.setCommodityAttribute(CommodityAttributeType.DangerousGoods.getCode());
            } else {
                commodityVo.setCommodityAttribute(commodityMeta.getcommodityAttribute());
            }
        }
    }

    /**
     * 套餐节省金额的计算（节省金放在销售状态以后pack）
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackSavePrice(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withSavePrice) {
            // 缺货套餐不予展示节省金
            if (SaleState.out_of_stock.equals(SaleState.parse(commodityVo.getSaleState()))) {
                return;
            }

            // 没有配置价格不予展示节省金
            CommodityPrice commodityPrice = commodityPackBo.getCommodityPriceMap().get(commodityVo.getId());
            if (Objects.isNull(commodityPrice)) {
                LOGGER.error("套餐价格缺失..." + commodityVo.getId() + "_" + setting.country);
                return;
            }

            Price saveAmount = homeItemInfoPack.getCommoditySaveAmount(commodityVo, commodityPrice, setting.resellerCode, setting.country);
            commodityVo.setSavePrice(saveAmount);
        }
    }

    /**
     * 封装套餐标签
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackCommodityTag(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withCommodityTag) {
            commodityVo.setCommodityTagInfos(homeItemInfoPack.packCommodityTagInfo(commodityVo, commodityPackBo.getTagBindMap(), commodityVo.getSaleState(), setting.country, setting.language));
        }
    }

    /**
     * 封装产品id
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackProductId(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductId) {
            Product product = commodityPackBo.getProductMap().get(commodityVo.getProduct());
            if (Objects.isNull(product)) {
                LOGGER.error("产品缺失..." + commodityVo.getProduct() + "_" + setting.country);
                return;
            }
            commodityVo.setProductId(product.getId());
        }
    }

    /**
     * 封装产品描述
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackProductDescription(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductDescription) {
            ProductInfo productInfo = commodityPackBo.getProductInfoMap().get(commodityVo.getProduct());
            if (Objects.isNull(productInfo) || Objects.isNull(productInfo.getDescription())) {
                LOGGER.error("产品描述缺失..." + commodityVo.getProduct() + "_" + setting.country);
                return;
            }
            commodityVo.setProductDescription(productInfo.getDescription());
        }
    }

    /**
     * 封装产品url key
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackProductUrlKey(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductUrlKey) {
            Product product = commodityPackBo.getProductMap().get(commodityVo.getProduct());
            if (Objects.isNull(product)) {
                LOGGER.error("产品url key缺失..." + commodityVo.getProduct());
                return;
            }
            commodityVo.setProductUrlKey(product.getUrlKey());
            commodityVo.setProductInnerName(product.getName());
        }
    }

    /**
     * 封装产品名称
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackProductName(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductName) {
            ProductInfo productInfo = commodityPackBo.getProductInfoMap().get(commodityVo.getProduct());
            if (Objects.isNull(productInfo)) {
                LOGGER.error("产品名称缺失..." + commodityVo.getProduct() + "_" + setting.country);
                return;
            }
            commodityVo.setProductName(productInfo.getName());
        }
    }

    /**
     * 封装套餐发货时间配置
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackDeliveryTimeConfig(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withDeliveryTimeConfig) {
            CommodityDeliveryTimeConfig deliveryTimeConfig = commodityPackBo.getCommodityDeliveryTimeConfigMap().get(commodityVo.getId());
            // 兼容特殊发货时间配置
            deliveryTimeConfig = commodityHelper.getCommodityDeliveryTimeConfig(setting.country, commodityVo.getId(), deliveryTimeConfig);
            commodityVo.setDeliveryTimeConfig(deliveryTimeConfig);
        }
    }

    /**
     * 封装套餐销售状态
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackSaleState(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withSaleState) {
            Map<Integer, CommodityTradeRule> tradeRuleMap = commodityPackBo.getCommodityTradeRuleMap();
            if (MapUtils.isEmpty(tradeRuleMap) || Objects.isNull(tradeRuleMap.get(commodityVo.getId()))) {
                LOGGER.error("套餐销售状态缺失..." + commodityVo.getId() + "_" + setting.country);
                return;
            }

            // key -> commodityId  value -> 库存数量
            Map<Integer, Integer> commodityStockMap = commodityPackBo.getCommodityStockMap();

            // 如果未配置销售状态，则默认未下架。
            CommoditySaleState saleState = commodityPackBo.getSaleStateMap().get(commodityVo.getId());
            if (saleState == null) {
                commodityVo.setSaleState(SaleState.remove.getCode());
                return;
            }

            // 库存是否配置
            boolean isStockConfig = (MapUtils.isEmpty(commodityStockMap) || Objects.isNull(commodityStockMap.get(commodityVo.getId())));

            // 没有配置库存，且套餐状态不是下架，就设置为out_of_stock
            if (isStockConfig) {
                commodityVo.setSaleState(saleState.getSaleState().equals(SaleState.remove.getCode()) ? SaleState.remove.getCode() : SaleState.out_of_stock.getCode());
                return;
            }

            // 如果没有库存了，且套餐状态不是下架，就设置为out_of_stock，其他为数据库状态
            if (commodityStockMap.get(commodityVo.getId()) <= 0 && !saleState.getSaleState().equals(SaleState.remove.getCode())) {
                commodityVo.setSaleState(SaleState.out_of_stock.getCode());
                return;
            }

            commodityVo.setSaleState(saleState.getSaleState());
        }
    }

    /**
     * 封装交易限制
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackTradeRule(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withTradeRule) {
            CommodityTradeRule tradeRule = commodityPackBo.getCommodityTradeRuleMap().get(commodityVo.getId());
            if (Objects.isNull(tradeRule)) {
                LOGGER.error("套餐交易限制缺失..." + commodityVo.getId() + "_" + setting.country);
                return;
            }
            commodityVo.setOrderBuyLimit(tradeRule.getOrderBuyLimit());
        }
    }

    /**
     * 封装套餐库存
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackStock(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withStock) {
            Map<Integer, Integer> commodityStockMap = commodityPackBo.getCommodityStockMap();
            if (MapUtils.isEmpty(commodityStockMap)) {
                commodityVo.setStock(0);
                return;
            }
            Integer stockCount = commodityStockMap.getOrDefault(commodityVo.getId(), 0);
            commodityVo.setStock(stockCount);
        }
    }

    /**
     * 封装套餐绑定的增值服务
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackClimbService(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withClimbService) {
            Map<Integer, List<ClimbServiceCommodity>> commodityBindClimeServiceMap = commodityPackBo.getCommodityBindClimeServiceMap();
            if (CommonUtil.isEmpty(commodityBindClimeServiceMap)) {
                return;
            }

            List<ClimbServiceCommodity> climbServiceCommodities = commodityBindClimeServiceMap.get(commodityVo.getId());
            if (CollectionUtils.isEmpty(climbServiceCommodities)) {
                return;
            }
            List<CommodityBindClimbServiceVO> commodityBindClimbServices = climbServiceCommodities.stream()
                    .map(CommodityBindClimbServiceVO::new)
                    .collect(Collectors.toList());
            commodityVo.setBindClimbServices(commodityBindClimbServices);
        }
    }

    /**
     * 封装套餐增值服务类型
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackServiceType(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withServiceType) {
            Map<Integer, ClimbServiceCommodity> commodityServiceTypeMap = commodityPackBo.getCommodityServiceTypeMap();
            if (CommonUtil.isEmpty(commodityServiceTypeMap)) {
                return;
            }

            ClimbServiceCommodity climbServiceCommodity = commodityServiceTypeMap.get(commodityVo.getId());
            if (Objects.nonNull(climbServiceCommodity)) {
                commodityVo.setServiceType(climbServiceCommodity.getServiceType());
            }
        }
    }

    /**
     * 封装套餐价格
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackPrice(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withPrice) {
            CommodityPrice commodityPrice = commodityPackBo.getCommodityPriceMap().get(commodityVo.getId());
            if (Objects.isNull(commodityPrice)) {
                LOGGER.error("套餐价格缺失..." + commodityVo.getId() + "_" + setting.country);
                return;
            }
            // 根据分销码等情况计算价格
            TradePriceParam tradePriceParam = new TradePriceParam();
            tradePriceParam.setCommodityPrice(commodityPrice);
            tradePriceParam.setResellerCode(setting.resellerCodeRef);

            Price price = tradePriceService.defineTradePrice(tradePriceParam);
            commodityVo.setPrice(price);

            // 原价和现价不一致，弄出原价
            if (commodityVo.getPrice().getAmount() != commodityPrice.getOriginAmount()) {
                commodityVo.setOriginPrice(commodityPrice.originPrice());
            }
        }
    }

    /**
     * 封装套餐多语言
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackInfo(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withCommodityInfo) {
            commodityVo.setInfo(commodityPackBo.getCommodityInfoMap().get(commodityVo.getId()));
        }
    }

    /**
     * 封装展示图
     *
     * @param commodityVo
     * @param commodityPackBo
     * @param setting
     */
    private void doPackDisplay(CommodityVO commodityVo, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withDisplay) {
            List<CommodityDisplay> commodityDisplays = commodityPackBo.getDisplayMap().get(commodityVo.getId());
            if (CollectionUtils.isEmpty(commodityDisplays)) {
                return;
            }
            List<Integer> displayIds = commodityDisplays.stream().map(CommodityDisplay::getId).collect(Collectors.toList());
            // 所有的国家
            Map<Integer, List<String>> countryMap = commodityBatchHelper.listDisplayCountries(displayIds);
            InstaCountry country = setting.getCountry();

            // 全部数据
            List<CommodityDisplay> commodityDisplayList = commodityDisplays.stream().map(commodityDisplay -> {
                Integer displayId = commodityDisplay.getId();

                // 过滤国家
                List<String> displayCountries = countryMap.get(displayId);
                if (CollectionUtils.isEmpty(displayCountries)) {
                    return commodityDisplay;
                }
                if (displayCountries.contains(country.name())) {
                    return commodityDisplay;
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(commodityDisplayList)) {
                commodityDisplayList = new ArrayList<>(0);
            }
            commodityVo.setDisplays(commodityDisplayList);
        }
    }

    /**
     * 套餐推荐标签
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packCommodityTag(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withCommodityTag) {
            Map<Integer, List<CommodityTagBind>> tagBindMap = commodityBatchHelper.commodityTagBindMapCommodityIds(commodityIds);
            commodityPackBo.setTagBindMap(tagBindMap);
        }
    }

    /**
     * 产品url key
     *
     * @param productIds
     * @param commodityPackBo
     * @param setting
     */
    private void packProductUrlKey(List<Integer> productIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductUrlKey) {
            commodityPackBo.setProductMap(productBatchHelper.productMapProductIds(productIds));
        }
    }

    /**
     * 产品多语言
     *
     * @param productIds
     * @param commodityPackBo
     * @param setting
     */
    private void packProductName(List<Integer> productIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withProductName) {
            Map<Integer, ProductInfo> productInfoMap = productBatchHelper.productInfoMapProductIds(productIds, setting.language);
            commodityPackBo.setProductInfoMap(productInfoMap);

            // 产品多语言没配置，取us
            if (productIds.size() == productInfoMap.size()) {
                return;
            }

            List<Integer> productIdList = new ArrayList<>(productIds);
            productIdList.removeAll(productInfoMap.values().stream().map(ProductInfo::getProduct).collect(Collectors.toList()));
            Map<Integer, ProductInfo> usCommodityPriceMap = productBatchHelper.productInfoMapProductIds(productIdList, InstaLanguage.en_US);
            usCommodityPriceMap.forEach(productInfoMap::put);
        }
    }

    /**
     * 套餐发货时间
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packDeliveryTimeConfig(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withDeliveryTimeConfig) {
            commodityPackBo.setCommodityDeliveryTimeConfigMap(commodityBatchHelper.commodityDeliveryTimeMap(commodityIds));
        }
    }

    /**
     * 套餐销售状态
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packSaleState(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withSaleState) {
            commodityPackBo.setSaleStateMap(commodityBatchHelper.saleStateMapCommodityIds(commodityIds, setting.country));
        }
    }

    /**
     * 套餐购买限制
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packTradeRule(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withTradeRule) {
            commodityPackBo.setCommodityTradeRuleMap(commodityBatchHelper.tradeRuleMapCommodityIds(commodityIds));
        }
    }

    /**
     * 套餐库存
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packStock(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withStock) {
            commodityPackBo.setCommodityStockMap(commodityBatchHelper.stockCountMapCommodityIds(commodityIds, setting.country));
        }
    }

    /**
     * 绑定的增值服务信息
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packClimbService(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withClimbService) {
            commodityPackBo.setCommodityBindClimeServiceMap(commodityBatchHelper.climbServiceMapCommodityIds(commodityIds));
        }
    }

    /**
     * 增值服务类型
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packServiceType(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withServiceType) {
            commodityPackBo.setCommodityServiceTypeMap(commodityBatchHelper.serviceTypeMapCommodityIds(commodityIds));
        }
    }

    /**
     * 封装套餐价格
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    private void packPrice(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withPrice) {
            Map<Integer, CommodityPrice> commodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIds, setting.country);
            commodityPackBo.setCommodityPriceMap(commodityPriceMap);

            // 存在价格没配置，取us
            if (commodityIds.size() == commodityPriceMap.size()) {
                return;
            }

            List<Integer> commodityIdList = new ArrayList<>(commodityIds);
            commodityIdList.removeAll(commodityPriceMap.values().stream().map(CommodityPrice::getCommodityId).collect(Collectors.toList()));
            Map<Integer, CommodityPrice> usCommodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIdList, InstaCountry.US);
            usCommodityPriceMap.forEach(commodityPriceMap::put);
        }
    }

    /**
     * 过滤禁用产品套餐
     *
     * @param commodities
     * @return
     */
    private Collection<Commodity> checkProductEnabled(Collection<Commodity> commodities) {
        List<Integer> productIds = commodities.stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());
        Map<Integer, Product> productMap = productBatchHelper.productMapProductIds(productIds);
        return commodities.stream().filter(commodity -> productMap.get(commodity.getProduct()) != null && commodity.getEnabled()).collect(Collectors.toList());
    }

    /**
     * 产品/套餐 未启用的不做封装
     *
     * @param commodity
     * @return
     */
    public boolean checkEnabled(Commodity commodity) {
        if (commodity == null) {
            return false;
        }
        Product product = productService.getById(commodity.getProduct());
        return product.getEnabled() && commodity.getEnabled();
    }

    /**
     * 套餐展示图
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    protected void packDisplay(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withDisplay) {
            commodityPackBo.setDisplayMap(commodityBatchHelper.commodityDisplayMap(commodityIds));
        }
    }

    /**
     * 套餐详细信息
     *
     * @param commodityIds
     * @param commodityPackBo
     * @param setting
     */
    protected void packInfo(List<Integer> commodityIds, CommodityPackBO commodityPackBo, PackSetting setting) {
        if (setting.withCommodityInfo) {
            Map<Integer, CommodityInfo> commodityInfoMap = commodityBatchHelper.commodityInfoMapCommodityIds(commodityIds, setting.language);
            commodityPackBo.setCommodityInfoMap(commodityInfoMap);
            // 存在多语言没配置，取英文
            if (commodityIds.size() == commodityInfoMap.size()) {
                return;
            }

            List<Integer> commodityIdList = new ArrayList<>(commodityIds);
            commodityIdList.removeAll(commodityInfoMap.values().stream().map(CommodityInfo::getCommodity).collect(Collectors.toList()));
            Map<Integer, CommodityInfo> usCommodityInfoMap = commodityBatchHelper.commodityInfoMapCommodityIds(commodityIdList, InstaLanguage.en_US);
            usCommodityInfoMap.forEach(commodityInfoMap::put);
        }
    }

    /**
     * 指定获取某个活动价格的餐列表信息
     *
     * @param commodityIds
     * @param campaign
     * @param language
     * @param country
     * @return
     */
    public List<CommodityCampaignVO> doPackCacheCommodityCampaignInfos(List<Integer> commodityIds, String campaign, InstaCountry country, InstaLanguage language) {
        List<Commodity> commodities = commodityService.listByCommodities(commodityIds);
        return commodities.stream()
                .filter(this::checkEnabled)
                .map(commodity -> {
                    Product product = productService.getById(commodity.getProduct());

                    // 产品多语言信息
                    ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(commodity.getProduct(), language);
                    if (productInfo == null) {
                        String message = "产品多语言配置信息缺失。产品Id:" + commodity.getProduct() + ", 语言:" + language;
                        FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY, FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW,
                                FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
                        return null;
                    }

                    // 套餐多语言信息
                    CommodityInfo commodityInfo = commodityInfoService.getInfoDefaultEnglish(commodity.getId(), language);
                    if (commodityInfo == null) {
                        String message = "套餐多语言配置信息缺失。套餐Id: " + commodity.getId() + ", 语言:" + language;
                        FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                                FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
                        return null;
                    }

                    // 套餐销售状态信息
                    CommoditySaleState commoditySaleState = commoditySaleStateService.getSaleState(commodity.getId(), country);
                    if (commoditySaleState == null || SaleState.remove.equals(commoditySaleState.parseSaleState())) {
                        return null;
                    }

                    // 套餐价格信息
                    CommodityPrice commodityPrice = commodityPriceService.getCampaignPrice(commodity.getId(), country.name(), campaign);
                    if (commodityPrice == null) {
                        String message = "套餐价格信息缺失。套餐Id: " + commodity.getId() + ", 地区:" + country.name() + ", campaign:" + campaign;
                        FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                                FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
                        return null;
                    }

                    // 套餐主图信息
                    CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(commodity.getId());
                    if (commodityDisplay == null) {
                        FeiShuMessageUtil.storeGeneralMessage("套餐主图信息缺失。套餐Id: " + commodity.getId(), FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                                FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
                        return null;
                    }

                    // 套餐库存信息
                    CommodityTradeRule commodityTradeRule = commodityTradeRuleService.getTradeRule(commodity.getId());
                    if (commodityTradeRule == null) {
                        FeiShuMessageUtil.storeGeneralMessage("套餐销售规则信息缺失。套餐Id: " + commodity.getId(), FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                                FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
                        return null;
                    }

                    // 库存数
                    Integer stock = commodityStockHelper.getByCommodityAndArea(commodity.getId(), country);

                    // 数据封装
                    CommodityCampaignVO commodityCampaignVo = new CommodityCampaignVO();
                    commodityCampaignVo.setCommodityId(commodity.getId());
                    commodityCampaignVo.setCommodityName(commodityInfo.getName());
                    commodityCampaignVo.setProductId(commodity.getProduct());
                    commodityCampaignVo.setProductName(productInfo.getName());
                    commodityCampaignVo.setStock(stock);
                    commodityCampaignVo.setSaleState(commoditySaleState.getSaleState());
                    commodityCampaignVo.setProductUrlKey(product.getUrlKey());
                    commodityCampaignVo.setCampagin(campaign);
                    commodityCampaignVo.setCommodityDisplayInfo(new CommodityDisplayVO(commodityDisplay));

                    TradePriceParam tradePriceParam = new TradePriceParam();
                    tradePriceParam.setCommodityPrice(commodityPrice);
                    Price price = tradePriceService.defineTradePrice(tradePriceParam);
                    commodityCampaignVo.setPrice(price);
                    // 原价和现价不一致，弄出原价
                    if (price.getAmount() != commodityPrice.getOriginAmount()) {
                        commodityCampaignVo.setOriginPrice(commodityPrice.originPrice());
                    }
                    return commodityCampaignVo;
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 套餐接口配置
     *
     * @param country
     * @param language
     * @return
     */
    public CommodityPack.PackSetting getCommoditySetting(InstaCountry country, InstaLanguage language) {
        PackSetting setting = new PackSetting(country, language, null);
        setting.setWithCommodityInfo(true);
        setting.setWithPrice(true);
        setting.setWithDisplay(true);
        setting.setWithTradeRule(true);
        setting.setWithStock(true);
        setting.setWithSaleState(true);

        setting.setWithProductUrlKey(true);
        setting.setWithProductName(true);
        setting.setWithDeliveryTimeText(true);
        setting.setWithProductDescription(true);
        setting.setWithDeliveryTimeConfig(true);
        return setting;
    }

    /**
     * 单个套餐详细信息
     *
     * @param commodityId
     * @param country
     * @param language
     */
    public CommodityVO doPackCacheCommodityInfo(Integer commodityId, InstaCountry country, InstaLanguage language) {
        List<CommodityVO> commodityVos = this.batchPack(Collections.singleton(commodityService.getById(commodityId)), getCommoditySetting(country, language));
        return CollectionUtils.isEmpty(commodityVos) ? new CommodityVO() : commodityVos.get(0);
    }

    /**
     * 多个套餐详细信息
     *
     * @param commodityIds
     * @param country
     * @param language
     */
    public List<CommodityVO> doPackCacheCommodityInfos(List<Integer> commodityIds, InstaCountry country, InstaLanguage language) {
        return this.batchPack(commodityService.listByCommodities(commodityIds), getCommoditySetting(country, language));
    }

    public static class PackSetting implements Serializable {

        BaseApi api;
        InstaLanguage language;
        InstaCountry country;
        String resellerCode;

        // 数据库表信息
        boolean withCode;
        boolean withDisplay;
        // 套餐内部信息
        boolean withInfo;
        // 套餐多语言信息
        boolean withCommodityInfo;
        boolean withMeta;
        boolean withPrice;
        boolean withStock;
        boolean withSaleState;
        boolean withTradeRule;
        boolean withSubscribe;
        boolean withDeliveryTimeConfig;
        boolean withDeliveryTimeText;
        boolean withCommodityTag;
        // 套餐绑定增值服务
        boolean withClimbService;
        // 套餐的增值服务类型
        boolean withServiceType;

        /**
         * 组合商品属性
         */
        boolean withCommodityAttribute;

        /**
         * 额外信息
         */
        boolean withTotalSubscribeQuantity;
        boolean withProductName;
        boolean withProductUrlKey;
        boolean withProductDescription;
        boolean withProductId;
        boolean withSavePrice;

        ResellerCode resellerCodeRef;

        public PackSetting(BaseApi api) {
            this.api = api;
            this.language = api.getApiLanguage();
            this.country = api.getApiCountry();
            this.resellerCode = api.getResellerCode();
        }

        public PackSetting() {
        }

        public PackSetting(InstaCountry country, InstaLanguage language, String resellerCode) {
            this.country = country;
            this.language = language;
            this.resellerCode = resellerCode;
        }

        public InstaCountry getCountry() {
            return country;
        }

        public void setCountry(InstaCountry country) {
            this.country = country;
        }

        public String getResellerCode() {
            return resellerCode;
        }

        public void setResellerCode(String resellerCode) {
            this.resellerCode = resellerCode;
        }

        public boolean isWithCode() {
            return withCode;
        }

        public void setWithCode(boolean withCode) {
            this.withCode = withCode;
        }

        public boolean isWithDisplay() {
            return withDisplay;
        }

        public void setWithDisplay(boolean withDisplay) {
            this.withDisplay = withDisplay;
        }

        public boolean isWithCommodityInfo() {
            return withCommodityInfo;
        }

        public boolean isWithInfo() {
            return withInfo;
        }

        public void setWithInfo(boolean withInfo) {
            this.withInfo = withInfo;
        }

        public void setWithCommodityInfo(boolean withCommodityInfo) {
            this.withCommodityInfo = withCommodityInfo;
        }

        public boolean isWithMeta() {
            return withMeta;
        }

        public void setWithMeta(boolean withMeta) {
            this.withMeta = withMeta;
        }

        public boolean isWithPrice() {
            return withPrice;
        }

        public void setWithPrice(boolean withPrice) {
            this.withPrice = withPrice;
        }

        public boolean isWithStock() {
            return withStock;
        }

        public void setWithStock(boolean withStock) {
            this.withStock = withStock;
        }

        public boolean isWithTradeRule() {
            return withTradeRule;
        }

        public void setWithTradeRule(boolean withTradeRule) {
            this.withTradeRule = withTradeRule;
        }

        public boolean isWithCommodityTag() {
            return withCommodityTag;
        }

        public void setWithCommodityTag(boolean withCommodityTag) {
            this.withCommodityTag = withCommodityTag;
        }

        public boolean isWithClimbService() {
            return withClimbService;
        }

        public void setWithClimbService(boolean withClimbService) {
            this.withClimbService = withClimbService;
        }

        public boolean isWithServiceType() {
            return withServiceType;
        }

        public void setWithServiceType(boolean withServiceType) {
            this.withServiceType = withServiceType;
        }

        public boolean isWithTotalSubscribeQuantity() {
            return withTotalSubscribeQuantity;
        }

        public void setWithTotalSubscribeQuantity(boolean withTotalSubscribeQuantity) {
            this.withTotalSubscribeQuantity = withTotalSubscribeQuantity;
        }

        public boolean isWithSubscribe() {
            return withSubscribe;
        }

        public void setWithSubscribe(boolean withSubscribe) {
            this.withSubscribe = withSubscribe;
        }

        public boolean isWithProductName() {
            return withProductName;
        }

        public void setWithProductName(boolean withProductName) {
            this.withProductName = withProductName;
        }

        public boolean isWithProductUrlKey() {
            return withProductUrlKey;
        }

        public void setWithProductUrlKey(boolean withProductUrlKey) {
            this.withProductUrlKey = withProductUrlKey;
        }

        public ResellerCode getResellerCodeRef() {
            return resellerCodeRef;
        }

        public void setResellerCodeRef(ResellerCode resellerCodeRef) {
            this.resellerCodeRef = resellerCodeRef;
        }

        public boolean isWithSaleState() {
            return withSaleState;
        }

        public void setWithSaleState(boolean withSaleState) {
            this.withSaleState = withSaleState;
        }

        public InstaLanguage getLanguage() {
            return language;
        }

        public void setLanguage(InstaLanguage language) {
            this.language = language;
        }

        public boolean isWithDeliveryTimeConfig() {
            return withDeliveryTimeConfig;
        }

        public void setWithDeliveryTimeConfig(boolean withDeliveryTimeConfig) {
            this.withDeliveryTimeConfig = withDeliveryTimeConfig;
        }

        public boolean isWithDeliveryTimeText() {
            return withDeliveryTimeText;
        }

        public void setWithDeliveryTimeText(boolean withDeliveryTimeText) {
            this.withDeliveryTimeText = withDeliveryTimeText;
        }

        public void setWithProductDescription(boolean withProductDescription) {
            this.withProductDescription = withProductDescription;
        }

        public boolean isWithProductId() {
            return withProductId;
        }

        public void setWithProductId(boolean withProductId) {
            this.withProductId = withProductId;
        }

        public boolean isWithSavePrice() {
            return withSavePrice;
        }

        public void setWithSavePrice(boolean withSavePrice) {
            this.withSavePrice = withSavePrice;
        }

        public boolean isWithCommodityAttribute() {
            return withCommodityAttribute;
        }

        public void setWithCommodityAttribute(boolean withCommodityAttribute) {
            this.withCommodityAttribute = withCommodityAttribute;
        }

        public boolean isWithProductDescription() {
            return withProductDescription;
        }

        @Override
        public String toString() {
            return "PackSetting{" +
                    "api=" + api +
                    ", language=" + language +
                    ", country=" + country +
                    ", resellerCode='" + resellerCode + '\'' +
                    ", withCode=" + withCode +
                    ", withDisplay=" + withDisplay +
                    ", withInfo=" + withInfo +
                    ", withCommodityInfo=" + withCommodityInfo +
                    ", withMeta=" + withMeta +
                    ", withPrice=" + withPrice +
                    ", withStock=" + withStock +
                    ", withSaleState=" + withSaleState +
                    ", withTradeRule=" + withTradeRule +
                    ", withSubscribe=" + withSubscribe +
                    ", withDeliveryTimeConfig=" + withDeliveryTimeConfig +
                    ", withDeliveryTimeText=" + withDeliveryTimeText +
                    ", withCommodityTag=" + withCommodityTag +
                    ", withClimbService=" + withClimbService +
                    ", withServiceType=" + withServiceType +
                    ", withCommodityAttribute=" + withCommodityAttribute +
                    ", withTotalSubscribeQuantity=" + withTotalSubscribeQuantity +
                    ", withProductName=" + withProductName +
                    ", withProductUrlKey=" + withProductUrlKey +
                    ", withProductDescription=" + withProductDescription +
                    ", withProductId=" + withProductId +
                    ", withSavePrice=" + withSavePrice +
                    ", resellerCodeRef=" + resellerCodeRef +
                    '}';
        }
    }
}
