package com.insta360.store.business.meta.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/5/21
 */
public enum StoreSdkCallApiType {

    /**
     * 未知
     */
    UNKNOWN(0, "未知"),

    /**
     * adaptiveAuth
     */
    FORTER_ADAPTIVE_AUTH(1, "adaptiveAuth"),

    /**
     * orderStatus
     */
    FORTER_ORDER_STATUS(2, "orderStatus"),

    /**
     * claims
     */
    FORTER_CLAIMS(3, "claims"),

    /**
     * show order detail
     */
    PAYPAL_SHOW_ORDER_DETAIL(4, "show order detail"),

    /**
     * invoice
     */
    YIPIAOYUN_INVOCE(5, "order_invoice"),

    /**
     * 易票云直接开票异步回调
     */
    YIPIAOYUN_BILLING_ASYNC(6, "billing_async"),

    /**
     * 商城退款同步管易
     */
    GUAN_YI_ERP_REFUND(8, "refund_status_sync_guanyi"),

    /**
     * 商城退款同步管易货物拦截
     */
    GUAN_YI_ERP_REFUND_CARGO_INTERCEPTION(9, "refund_cargo_interception"),

    /**
     * 商城订单同步管易
     */
    GUAN_YI_ERP_ORDER_SYNC(10, "syncToGuanyi"),

    /**
     * 钱海支付api
     */
    OCEAN_PAYMENT_API(11, "ocean_payment_api"),

    /**
     * paypal订阅管理api
     */
    PAYPAL_SUBSCRIBE_API(12, "paypal_subscribe_api"),

    /**
     * cko订阅管理api
     */
    CKO_SUBSCRIBE_API(13, "cko_subscribe_api"),

    /**
     * klarna支付相关api
     */
    KLARNA_PAYMENT_API(14, "klarna_payment_api"),

    /**
     * oms 退款售后单同步(仅退款、退货退款)
     */
    OMS_RMA_REFUND_ORDER_SYNC(15, "greatonce.oms.refunds.synchronize"),

    /**
     * oms 售后换货单同步(换货)
     */
    OMS_RMA_RETURN_CHANGE_ORDER_SYNC(16, "greatonce.oms.exchange.synchronize"),

    /**
     * oms 仅退款货物拦截回传通知
     */
    OMS_RMA_CARGO_INTERCEPTION_NOTIFY(17, "orderRefundInterceptNotification"),

    /**
     * oms 售后退货商品入库回传通知
     */
    OMS_RMA_RETURN_ITEM_TURN_TO_WAREHOUSE(18, "orderProductReturnNotification"),

    /**
     * OMS 赠品
     */
    OMS_GIFT(19, "oms-gift"),

    /**
     * OMS 赠品
     */
    OMS_DELIVERY_UPDATE(20, "oms-delivery-update"),

    /**
     * 商城推单至oms
     */
    STORE_PUSH_ORDER_TO_OMS(21, "deliveryorder.create"),

    /**
     * 商城推单至oms
     */
    PRIME_GRAPHQL(22, "prime_graphql"),


    PRIME_BUYABILITY_CHANGE(23, "prime_buyability_change");

    ;
    private final int type;

    private final String value;

    StoreSdkCallApiType(int type, String value) {
        this.type = type;
        this.value = value;
    }

    public static StoreSdkCallApiType getEnumByType(int type) {
        for (StoreSdkCallApiType apiType : StoreSdkCallApiType.values()) {
            if (apiType.type == type) {
                return apiType;
            }
        }
        return UNKNOWN;
    }

    public int getType() {
        return type;
    }

    public String getValue() {
        return value;
    }
}
