@startuml
'https://plantuml.com/sequence-diagram

title

商城推单至OMS时序图

end title

'推单入口
participant push_order  as  p

'队列
queue queue as queue

'服务端（服务器）
participant server  as  s

'数据库
database db as db

'oms
participant oms as oms

'客户端（飞书）
participant feishu  as  f


p -> queue : 1. 触发推单场景
note left
1、运营后台单订单配货
2、运营后台批量订单配货
3、系统支付后自动备货推单
end note

queue -> s: 2.推送推单OMS消息

s -> db: 3.初始化推单相关信息
note left
1、订单数据
2、支付数据
3、收货数据
4、套餐数据
...
end note
db --> s: 4.响应初始化推单相关信息

s -> s: 5. 封装订单相关信息
note left
1、订单信息（卖家备注、买家备注、订单号、优先发货、订单类型（JYCK）、订单状态（WAIT_SELLER_SEND_GOODS））
2、支付信息（支付金额、支付渠道映射店铺、运费）
3、时间（订单创建时间，修改时间用推单时间）
4、收件人信息（省市区信息、电话、名称、邮编、地址....）
港澳台省份传中文值（如中国香港）
end note

s -> s:6. 封装子项信息
note left
1、单子项实际支付金额（price-discount）折扣按保留两位小数
2、子项id、数量、料号
end note

s -> s:7. 封装扩展参数
note left
1、支付货币、总税费、以旧换新标签（yjhx）
2、七级税费
3、订单子项明细（子项id、整个子项支付金额（price * number - totalDiscount）
子项总折扣金额、子项总税费）
end note

s -> oms :8. 发起推单请求
oms --> s :9. 响应推单结果
s -> db :10. 记录推单请求和结果

group 失败
s -> f:11. 推送订单失败告警
else 成功
s -> db :12.记录系统自动推单日志
end
@enduml