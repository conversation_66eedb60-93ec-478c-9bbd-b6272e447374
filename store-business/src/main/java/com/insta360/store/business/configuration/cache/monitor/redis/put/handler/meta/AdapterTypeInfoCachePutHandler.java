package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CacheCountiesBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import com.insta360.store.business.outgoing.rpc.store.job.CategoryPageCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.HomeItemCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.HomepageCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.ProductCachePutService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductAdapterType;
import feign.RetryableException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/11/15
 * @Description:
 */
@Component
public class AdapterTypeInfoCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdapterTypeInfoCachePutHandler.class);

    @Autowired
    HomeItemCachePutService homeItemCachePutService;

    @Autowired
    HomepageCachePutService homepageCachePutService;

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Autowired
    CategoryPageCachePutService categoryPageCachePutService;

    @Autowired
    ProductCachePutService productCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        InstaLanguage language = cachePutKeyParameter.getLanguage();
        if (Objects.isNull(language)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。语言为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        Integer adapterTypeId = cachePutKeyParameter.getAdapterTypeId();
        if (Objects.isNull(adapterTypeId)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。适配机型ID为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }
        List<ProductAdapterType> productAdapterTypes = productAdapterTypeService.listByAdapterType(adapterTypeId);
        if (CollectionUtils.isEmpty(productAdapterTypes)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程.配置了该适配机型id的产品为空,不予处理.请求数据:{%s}", this.getCachePutType(), cachePutKeyParameter));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }
        List<Integer> productIds = productAdapterTypes.stream().map(ProductAdapterType::getProductId).distinct().collect(Collectors.toList());
        Collection<Product> products = productService.listByIds(productIds);
        if (CollectionUtils.isEmpty(products)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程.配置了该适配机型id的产品,均未启用,不予处理.请求数据:{%s},产品id:{%s}", this.getCachePutType(), cachePutKeyParameter, productIds));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 参数封装
        List<HomepageItemMain> homepageItemMains = homepageItemMainService.listEnableHomeItem();
        List<CacheCountiesBO> filterCounties = CacheConstant.COUNTIES
                .stream()
                .filter(cacheCounties -> cacheCounties.getLanguage().equals(language))
                .collect(Collectors.toList());
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("filterCounties", filterCounties);
        paramMap.put("homeItemIds", homepageItemMains.stream().map(HomepageItemMain::getId).collect(Collectors.toList()));
        paramMap.put("products", products);

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(paramMap, countDownLatch));
        cachePutThreadPool.execute(() -> this.task2(paramMap, countDownLatch));
        cachePutThreadPool.execute(() -> this.task3(paramMap, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新参数
        StoreCacheDataChangeEventBO storeCacheDataChangeEvent = new StoreCacheDataChangeEventBO();
        storeCacheDataChangeEvent.setProductEvents(this.parseProductEvent(products));
        return storeCacheDataChangeEvent;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.ADAPTER_TYPE_INFO;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.HOME_PAGE_KEY, CacheableType.HOME_ITEM_KEY, CacheableType.CATEGORY_PAGE, CacheableType.CATEGORY_PAGE_FILTER, CacheableType.PRODUCT_INFO);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            Map<String, Object> paramMap = (Map<String, Object>) param;

            for (CacheCountiesBO cacheCounties : (List<CacheCountiesBO>) paramMap.get("filterCounties")) {
                // 更新首页
                homepageCachePutService.listHomepage(cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新产品页
                List<Product> products = (List<Product>) paramMap.get("products");
                List<Integer> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
                for (Integer productId : productIds) {
                    productCachePutService.getInfo(productId, cacheCounties.getCountry(), cacheCounties.getLanguage());
                }
            }

            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
        try {
            Map<String, Object> paramMap = (Map<String, Object>) param;
            for (CacheCountiesBO cacheCounties : (List<CacheCountiesBO>) paramMap.get("filterCounties")) {
                // 更新全部类目
                categoryPageCachePutService.listAllCategory(cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新类目筛选器
                categoryPageCachePutService.listAllCategoryFilter(cacheCounties.getCountry(), cacheCounties.getLanguage());
            }
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务2完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task2}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {
        try {
            Map<String, Object> paramMap = (Map<String, Object>) param;
            List<Integer> homeItemIds = (List<Integer>) paramMap.get("homeItemIds");
            List<CacheCountiesBO> filterCounties = (List<CacheCountiesBO>) paramMap.get("filterCounties");

            filterCounties.forEach(cacheCounties -> {
                // 更新类目页（旧）
                for (Integer homeItemId : homeItemIds) {
                    homeItemCachePutService.listHomeItemInfoByHomeItemType(homeItemId, cacheCounties.getCountry(),
                            cacheCounties.getLanguage());
                }
            });
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务3完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task3}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }
}
