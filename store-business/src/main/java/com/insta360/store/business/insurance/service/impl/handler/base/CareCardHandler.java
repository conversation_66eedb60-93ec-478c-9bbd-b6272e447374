package com.insta360.store.business.insurance.service.impl.handler.base;

import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.bo.InsuranceBO;
import com.insta360.store.business.insurance.bo.InsuranceSwitchBindingResultBO;
import com.insta360.store.business.insurance.email.BaseInsuranceEmail;
import com.insta360.store.business.insurance.email.CareActivationCardEmail;
import com.insta360.store.business.insurance.enums.CardEmailType;
import com.insta360.store.business.insurance.enums.InsuranceRebindResponseStatus;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.insurance.model.*;
import com.insta360.store.business.insurance.service.CareActivationCardService;
import com.insta360.store.business.insurance.service.CareCardDeviceTypeService;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceActivationInfo;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2024-02-01 12:06
 */
@Component
public class CareCardHandler extends BaseInsuranceHandler {

    public static final Logger LOGGER = LoggerFactory.getLogger(CareCardHandler.class);

    @Autowired
    CareActivationCardService careActivationCardService;

    @Autowired
    CareCardDeviceTypeService careCardDeviceTypeService;

    @Override
    public ServiceType getServiceType() {
        return ServiceType.care_card;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void bindInsurance(InsuranceBO insuranceParam) {
        if (StringUtil.isBlank(insuranceParam.getEmail())
                || StringUtil.isBlank(insuranceParam.getName())
                || StringUtil.isBlank(insuranceParam.getPhone())) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        String deviceSerial = insuranceParam.getDeviceSerial();
        // 通用校验
        this.checkCamera(deviceSerial);

        // 激活码校验
        InsuranceBO checkActivationCode = this.checkActivationCode(deviceSerial, insuranceParam.getActivationCode());
        String insuranceType = checkActivationCode.getInsuranceType();
        CareActivationCard careActivationCard = checkActivationCode.getCareActivationCard();

        ServiceType serviceType = ServiceType.parse(insuranceType);
        if (Objects.isNull(serviceType)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }
        this.isCameraCheck(deviceSerial, careActivationCard.getDeviceType(), serviceType);

        // 客户端需要校验激活时间
        LocalDateTime createTime = this.getCreateTime(deviceSerial, insuranceParam.getStore());

        // 校验序列号是否绑定过care，需兼容以前的care，禁用掉的可以再次绑定
        this.checkSerial(deviceSerial);

        // 绑定
        CareInsuranceActivationCard careInsuranceActivationCard = this.buildCareInsuranceActivationCard(insuranceType, insuranceParam, careActivationCard.getDeviceType(), createTime);
        careInsuranceActivationCardService.save(careInsuranceActivationCard);
        // 更新激活信息
        careActivationCard.setIsActivation(true);
        careActivationCardService.updateById(careActivationCard);

        // 邮件发送
        CardEmailType cardEmailType = CardEmailType.parse(insuranceType);
        String templateKey = Objects.isNull(cardEmailType) ? CardEmailType.care.getTemplateKey() : cardEmailType.getTemplateKey();
        BaseInsuranceEmail insuranceEmail = insuranceEmailFactory.getEmail(insuranceParam.getInstaLanguage(), careInsuranceActivationCard, CareActivationCardEmail.class, templateKey);
        insuranceEmail.doSend(careInsuranceActivationCard.getEmail());
        LOGGER.info(String.format("[care实体卡激活]care实体卡激活成功,序列号:[%s]", deviceSerial));
    }

    @Override
    public InsuranceSwitchBindingResultBO switchBinding(String oldDeviceSerial, String newDeviceSerial) {
        // 只进行换绑操作，前置过滤在api层已完成
        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getByDeviceSerial(oldDeviceSerial);
        if (activationCard == null) {
            throw new InstaException(InsuranceErrorCode.InsuranceNotFoundException);
        }
        activationCard.setDeviceSerial(newDeviceSerial);
        try {
            careInsuranceActivationCardService.updateById(activationCard);
        } catch (Exception e) {
            LOGGER.error(String.format("care_card商城换绑失败，旧设备序列号：%s，新设备序列号：%s，失败原因：%s", oldDeviceSerial, newDeviceSerial, e.getMessage()), e);
            return new InsuranceSwitchBindingResultBO(Boolean.FALSE, InsuranceRebindResponseStatus.FAILURE.getStatus());
        }
        LOGGER.info(String.format("care_card商城换绑成功，旧设备序列号：%s，新设备序列号：%s", oldDeviceSerial, newDeviceSerial));
        return new InsuranceSwitchBindingResultBO(Boolean.TRUE, InsuranceRebindResponseStatus.SUCCESS.getStatus());
    }

    @Override
    public void adminCheckSerial(String deviceSerial, String deviceType) {
        // 验证序列号是否存在
        DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(deviceSerial);
        if (deviceInfo == null) {
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }
        LOGGER.info(String.format("后台序列号校验,deviceInfo:[%S]", deviceInfo.toString()));

        // 是否属于该保险机型的序列号
        if (!deviceType.equals(deviceInfo.getDeviceType())) {
            throw new InstaException(InsuranceErrorCode.DeviceMismatchException);
        }

        // 一个序列号只让绑定一次
        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
        if (activationCard != null) {
            throw new InstaException(InsuranceErrorCode.DeviceAlreadyBindException);
        }
    }

    @Override
    public Boolean checkAllowBinding(DeviceActivationInfo deviceActivationInfo) {
        // 未激活直接返回false
        if (deviceActivationInfo == null) {
            return false;
        }
        InsuranceServiceType insuranceServiceType = insuranceServiceTypeService.getByServiceType(getServiceType().name());
        if (Objects.isNull(insuranceServiceType)) {
            return true;
        }
        List<InsuranceServiceCommodityBind> insuranceServiceCommodityBindList =
                serviceCommodityBindService.getByDeviceTypeAndService(ServiceType.care_card.name(), insuranceServiceType.getId());
        if (CollectionUtils.isEmpty(insuranceServiceCommodityBindList)) {
            return true;
        }
        InsuranceServiceCommodityBind insuranceServiceCommodityBind = insuranceServiceCommodityBindList.get(0);
        Integer activationTimeLimit = insuranceServiceCommodityBind.getActivationDay();
        return !deviceActivationInfo.getCreateTime().plusDays(activationTimeLimit).isAfter(LocalDateTime.now());
    }

    @Override
    public InsuranceBO checkActivationCode(String deviceSerial, String activationCode) {
        // 不可重复绑定（一个激活码只能绑定一次，不管是否禁用掉！）
        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getByActivationCode(activationCode);
        if (activationCard != null) {
            throw new InstaException(CommonErrorCode.ActivationCodeAlreadyBind);
        }

        // 校验激活码是否有效
        CareActivationCard careActivationCard = careActivationCardService.getByActivationCode(activationCode);
        if (careActivationCard == null) {
            throw new InstaException(CommonErrorCode.InvalidActivationCode);
        }

        // 获取保险类型
        Integer deviceTypeId = careActivationCard.getDeviceTypeId();
        CareCardDeviceType careCardDeviceType = careCardDeviceTypeService.getById(deviceTypeId);
        String insuranceType = Objects.isNull(careCardDeviceType) ? ServiceType.care.name() : careCardDeviceType.getInsuranceType();

        // 设备类型校验
        this.CheckDeviceSerialExist(deviceSerial, insuranceType, careActivationCard.getDeviceType());
        InsuranceBO insuranceParam = new InsuranceBO();
        insuranceParam.setInsuranceType(insuranceType);
        insuranceParam.setCareActivationCard(careActivationCard);
        return insuranceParam;
    }
}
