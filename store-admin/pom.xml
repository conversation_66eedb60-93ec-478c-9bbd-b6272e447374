<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.insta360.store</groupId>
        <artifactId>parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>store-admin</artifactId>
    <packaging>jar</packaging>
    <name>store-admin</name>
    <version>${store-version}</version>

    <dependencies>
        <dependency>
            <groupId>com.insta360.store</groupId>
            <artifactId>store-business</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.insta360.compass</groupId>
            <artifactId>compass-admin</artifactId>
            <version>1.11.2-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.insta360.store.admin.StoreAdminApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
