package com.insta360.store.business.commodity.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.commodity.bo.CommodityLowInventoryBO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.integration.google.dto.GoogleCommodityInsertQueryDTO;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface CommodityDao extends BaseDao<Commodity> {

    void saveImport(Commodity commodity);

    /**
     * 根据套餐区分是否是相机
     *
     * @param commodityIds
     * @return
     */
    List<Integer> listCameraCommodities(@Param("commodity_ids") List<Integer> commodityIds);

    /**
     * 根据时间查询套餐信息
     *
     * @param googleCommodityInsertQueryDTO
     * @return
     */
    List<Commodity> listCommodityByTimeAndEnabled(GoogleCommodityInsertQueryDTO googleCommodityInsertQueryDTO);

    /**
     * 查询低库存的套餐
     *
     * @param stock
     * @param saleState
     * @return
     */
    List<CommodityLowInventoryBO> listByCommodityLowInventory(@Param("stock") Integer stock, @Param("saleState") Integer saleState);

    /**
     * 批量更新序号
     *
     * @param commodities
     */
    void updateOrderIndexByIds(@Param("commodities") List<Commodity> commodities);

    /**
     * 批量插入
     *
     * @param commodityList
     * @return
     */
    int batchImportScenesSave(List<Commodity> commodityList);

    /**
     * 批量插入套餐
     * @param commodities
     * @return
     */
    void batchInsertCommodities(List<Commodity> commodities);
}
