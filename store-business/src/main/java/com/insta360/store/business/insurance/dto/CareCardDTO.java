package com.insta360.store.business.insurance.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 实体卡管理DTO
 * @author: py
 * @create: 2023-04-25 15:00
 */
public class CareCardDTO {

    /**
     * 厂商
     */
    private String companyCode;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡片版本
     */
    private String cardVersion;

    /**
     * 生产数量
     */
    private Integer number;

    /**
     * 导出模式
     */
    private Boolean isTest;

    /**
     * 卡号list
     */
    private List<String> cardNumberList;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 产品类型id
     */
    private Integer deviceTypeId;

    /**
     * 金蝶工单号
     */
    private String workNumber;

    /**
     * 是否绑定状态码
     * 第一次0（无状态）第二次 1（拒绝覆盖机型）2（覆盖机型并绑定）
     */
    private Integer override;

    /**
     * 页数
     */
    private Integer pageNumber;

    /**
     * 每页大小
     */
    private Integer pageSize;


    /**
     * 创建开始时间
     */
    private LocalDateTime cardCreateFromTime;

    /**
     * 创建结束时间
     */
    private LocalDateTime cardCreateToTime;

    /**
     * 绑定开始时间
     */
    private LocalDateTime bindFromTime;

    /**
     * 绑定结束时间
     */
    private LocalDateTime bindToTime;

    /**
     * 是否绑定
     */
    private Boolean isBind;

    /**
     * 是否激活
     */
    private Boolean isActivation;

    /**
     * 作废原因
     */
    private String disableReason;

    /**
     * 是否作废
     */
    private Boolean disabled;

    /**
     * care卡的类型
     */
    private String cardType;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCardVersion() {
        return cardVersion;
    }

    public void setCardVersion(String cardVersion) {
        this.cardVersion = cardVersion;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Boolean getIsTest() {
        return isTest;
    }

    public void setIsTest(Boolean isTest) {
        this.isTest = isTest;
    }

    public List<String> getCardNumberList() {
        return cardNumberList;
    }

    public void setCardNumberList(List<String> cardNumberList) {
        this.cardNumberList = cardNumberList;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public Integer getDeviceTypeId() {
        return deviceTypeId;
    }

    public void setDeviceTypeId(Integer deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public Integer getOverride() {
        return override;
    }

    public void setOverride(Integer override) {
        this.override = override;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Boolean getIsBind() {
        return isBind;
    }

    public void setIsBind(Boolean isBind) {
        this.isBind = isBind;
    }

    public Boolean getIsActivation() {
        return isActivation;
    }

    public void setIsActivation(Boolean isActivation) {
        this.isActivation = isActivation;
    }

    public LocalDateTime getCardCreateFromTime() {
        return cardCreateFromTime;
    }

    public void setCardCreateFromTime(LocalDateTime cardCreateFromTime) {
        this.cardCreateFromTime = cardCreateFromTime;
    }

    public LocalDateTime getCardCreateToTime() {
        return cardCreateToTime;
    }

    public void setCardCreateToTime(LocalDateTime cardCreateToTime) {
        this.cardCreateToTime = cardCreateToTime;
    }

    public LocalDateTime getBindFromTime() {
        return bindFromTime;
    }

    public void setBindFromTime(LocalDateTime bindFromTime) {
        this.bindFromTime = bindFromTime;
    }

    public LocalDateTime getBindToTime() {
        return bindToTime;
    }

    public void setBindToTime(LocalDateTime bindToTime) {
        this.bindToTime = bindToTime;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getDisableReason() {
        return disableReason;
    }

    public void setDisableReason(String disableReason) {
        this.disableReason = disableReason;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    @Override
    public String toString() {
        return "CareCardDTO{" +
                "companyCode='" + companyCode + '\'' +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardVersion='" + cardVersion + '\'' +
                ", number=" + number +
                ", isTest=" + isTest +
                ", cardNumberList=" + cardNumberList +
                ", deviceType='" + deviceType + '\'' +
                ", deviceTypeId=" + deviceTypeId +
                ", workNumber='" + workNumber + '\'' +
                ", override=" + override +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", cardCreateFromTime=" + cardCreateFromTime +
                ", cardCreateToTime=" + cardCreateToTime +
                ", bindFromTime=" + bindFromTime +
                ", bindToTime=" + bindToTime +
                ", isBind=" + isBind +
                ", isActivation=" + isActivation +
                ", disableReason='" + disableReason + '\'' +
                ", disabled=" + disabled +
                ", cardType='" + cardType + '\'' +
                '}';
    }
}
