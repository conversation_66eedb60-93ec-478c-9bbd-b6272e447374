package com.insta360.store.business.payment.service.impl.handler;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.ThreadUtil;
import com.insta360.store.business.cloud.constant.CloudSubscribeTextConstant;
import com.insta360.store.business.cloud.enums.CloudSubscribeActionEnum;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.service.impl.helper.StoreSubscribeHelper;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.configuration.utils.EncodingUtil;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.integration.checkout.lib.response.CreateGetCkoOrderDetailResponse;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.outgoing.mq.check.bo.PaymentParamCheckBO;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.bo.PaymentSubscribeBO;
import com.insta360.store.business.payment.bo.PaymentSubscribeResultBO;
import com.insta360.store.business.payment.bo.ThreeDomainSecureBO;
import com.insta360.store.business.payment.constants.CkoPaymentConstant;
import com.insta360.store.business.payment.constants.PaymentConstant;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentMethodEnum;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentRequestEnum;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentStatusEnum;
import com.insta360.store.business.payment.lib.checkout.model.Data;
import com.insta360.store.business.payment.lib.checkout.model.Source;
import com.insta360.store.business.payment.lib.checkout.request.BaseCkoPaymentRequest;
import com.insta360.store.business.payment.lib.checkout.response.CreateCkoCreatePaymentResponse;
import com.insta360.store.business.payment.service.impl.helper.CkoPaymentHelper;
import com.insta360.store.business.payment.service.impl.helper.PaymentHelper;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.UserPayInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2024/06/24
 * @Description:
 */
public abstract class BaseCkoPaymentSubscribeHandler extends BaseCkoPaymentHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseCkoPaymentSubscribeHandler.class);

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    CkoPaymentHelper ckoPaymentHelper;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Override
    protected BaseCkoPaymentRequest buildPaymentRequest(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        BaseCkoPaymentRequest request = super.buildPaymentRequest(paymentInfo, paymentExtra);
        request.setPaymentType(CkoPaymentRequestEnum.RECURRING.getName());
        // 续费订单处理
        if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            processRenewOrderRequest(request, paymentExtra);
        }
        return request;
    }

    @Override
    protected Boolean isNeedPaymentCheck() {
        return Boolean.FALSE;
    }

    /**
     * 处理续费订单请求
     *
     * @param request
     * @param paymentExtra
     */
    private void processRenewOrderRequest(BaseCkoPaymentRequest request, PaymentExtra paymentExtra) {
        StoreAccount storeAccount = paymentExtra.getStoreAccount();
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(storeAccount.getInstaAccount());
        if (Objects.isNull(userPayInfo) || StringUtil.isBlank(userPayInfo.getPayId())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        Source source = new Source();
        source.setType("id");
        source.setId(AESUtil.decode(AESUtil.PAY_TOKEN_KEY, userPayInfo.getPayId()));
        source.setBillingAddress(this.getBillingAddress());
        request.setSource(source);
        request.setPreviousPaymentId(userPayInfo.getPaymentTradeId());
        request.setMerchantInitiated(Boolean.TRUE);
        request.setThreeDomainSecure(null);
        request.setSuccessUrl(null);
        request.setFailureUrl(null);
    }

    @Override
    protected <T> T preHandlePaymentResult(BaseCkoPaymentRequest request, PaymentExtra paymentExtra) {
        // 升级订单初始化payment info
        if (ServiceScenesType.UPGRADE.name().equals(order.getSubscribeScenesType())) {
            this.saveOrUpdatePaymentInfo(order, paymentExtra.getPaymentChannelId(), null);
        }

        if (!PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            return super.preHandlePaymentResult(request, paymentExtra);
        }
        // 续费订单处理
        CreateCkoCreatePaymentResponse paymentResponse = this.parsePaymentResponseWithKey(request);
        doPreHandlerPaymentResult(request, paymentResponse);
        String paymentStatus = paymentResponse.getStatus();
        String responseCode = paymentResponse.getResponseCode();

        // auth result save
        ThreadUtil.execute(() -> {
            this.saveOrUpdatePaymentInfo(order, paymentExtra.getPaymentChannelId(), paymentResponse);
            this.saveAuthResultPaymentInfo(paymentResponse);
        });
        // 支付成功
        boolean success = CkoPaymentStatusEnum.isAuthSuccess(paymentStatus) && CkoPaymentConstant.SUCCESS_CODE.equals(responseCode);
        if (success) {
            return (T) Boolean.TRUE;
        }
        // 处理重试逻辑
        ckoPaymentHelper.handleRetryLogic(order, responseCode, paymentExtra.getIsRenewDeductionRetry());
        // 发送续费订单扣款失败通知
        FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.RENEW_ORDER_PAYMENT_EXCEPTION_TEXT,
                order.getOrderNumber(), this.getPaymentChannel().name(), responseCode, paymentStatus),
                FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.LCX);
        return (T) Boolean.FALSE;
    }

    @Override
    public <T> T updatePaymentSubscribeInfo(PaymentSubscribeBO paymentSubscribeParam) {
        // 订单信息
        Order order = paymentSubscribeParam.getOrder();
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 订单支付信息
        OrderPayment orderPayment = paymentSubscribeParam.getOrderPayment();
        if (orderPayment == null) {
            throw new InstaException(OrderErrorCode.OrderPaymentNotFoundException);
        }

        PaymentExtra paymentExtra = new PaymentExtra();
        paymentExtra.setOrder(order);
        paymentExtra.setCardNumber(paymentSubscribeParam.getCardNumber());
        paymentExtra.setCvv(paymentSubscribeParam.getCardCvv());
        paymentExtra.setCardYear(paymentSubscribeParam.getCardYear());
        paymentExtra.setCardMonth(paymentSubscribeParam.getCardMonth());
        paymentExtra.setName(paymentSubscribeParam.getNameOnCard());
        paymentExtra.setCkoPaymentMethod(CkoPaymentMethodEnum.CARD.getName());
        paymentExtra.setThreeDomainTrade(paymentSubscribeParam.getThreeDomainTrade());
        paymentExtra.setExemption(paymentSubscribeParam.getExemption());
        paymentExtra.setPaymentChannelId(paymentSubscribeParam.getPaymentChannelId());
        Source sourceInfo = this.getSourceInfo(paymentExtra);
        sourceInfo.setPhone(null);
        sourceInfo.setBillingAddress(null);

        BaseCkoPaymentRequest request = this.newPaymentRequest();
        request.setSource(sourceInfo);
        request.setCurrency(orderPayment.getCurrency());
        request.setThreeDomainSecure(this.getThreeDomainSecureInfo(paymentExtra));
        request.setReference(order.getOrderNumber());
        request.setSuccessUrl(getCheckoutConfigInfo().getApproveCallbackUrl() + String.format("?insta_callback=%s", EncodingUtil.encode(RSAUtil.encryptByMyPub(order.getOrderNumber()))));
        request.setFailureUrl(paymentHelper.getSubscribeCancelUrl(StorePaymentMethodEnum.CKO_PAYMENT, paymentSubscribeParam.getSubscribeActionType()));
        request.setProcessingChannelId(getProcessingChannel(paymentExtra.getPaymentChannelId()));


        // 结果解析
        CreateCkoCreatePaymentResponse paymentResponse = this.parsePaymentResponse(request);
        ckoPaymentHelper.saveCkoSubscribeCallRecord(order.getOrderNumber(), request, paymentResponse);

        // 保存订阅auth result
        StoreSubscribeHelper subscribeHelper = ApplicationContextHolder.getApplicationContext().getBean(StoreSubscribeHelper.class);
        UserPayInfo userPayInfo = paymentSubscribeParam.getUserPayInfo();
        subscribeHelper.saveSubscribeAuthResult(paymentSubscribeParam.getStoreAccount(), userPayInfo.parsePaymentMethod(), CloudSubscribeActionEnum.CKO_CARD_CHECK,
                userPayInfo.parsePaymentMethod().name(), StringUtils.EMPTY, this.getPaymentChannel().name());

        CkoPaymentStatusEnum paymentStatusEnum = CkoPaymentStatusEnum.parseName(paymentResponse.getStatus());
        if (paymentStatusEnum == null) {
            LOGGER.error("cko相应的状态无法识别。payment response:{}", paymentResponse);
            FeiShuMessageUtil.storeGeneralMessage(String.format("cko更新订阅响应状态无法识别。订单号{%s} response {%s}", order.getOrderNumber(), paymentResponse), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(PaymentErrorCode.TransactionFailedException);
        }

        PaymentSubscribeResultBO paymentSubscribeResult = new PaymentSubscribeResultBO();
        switch (paymentStatusEnum) {
            // 3ds
            case PENDING:
                paymentSubscribeResult.setPayUrl(this.isThreeDS(paymentResponse));
                break;
            // card verified
            case CARD_VERIFIED:
                // 保存订阅auth result
                subscribeHelper.saveSubscribeAuthResult(paymentSubscribeParam.getStoreAccount(), userPayInfo.parsePaymentMethod(), CloudSubscribeActionEnum.CKO_CARD_CHECK_RESULT,
                        userPayInfo.parsePaymentMethod().name(), paymentResponse.getResponseCode(), paymentResponse.getResponseSummary());
                // 验卡成功处理
                if (Boolean.TRUE.equals(paymentResponse.getApproved())) {
                    Source source = paymentResponse.getSource();
                    paymentSubscribeResult.setPaymentId(paymentResponse.getId());
                    paymentSubscribeResult.setPayId(source.getId());
                    paymentSubscribeResult.setCardType(source.getScheme());
                    paymentSubscribeResult.setCardCountry(source.getIssuerCountry());
                    paymentSubscribeResult.setCardNumber(source.getBin() + "***" + source.getLast4());

                    // 保存订阅auth result
                    subscribeHelper.saveSubscribeAuthResult(paymentSubscribeParam.getStoreAccount(), userPayInfo.parsePaymentMethod(), CloudSubscribeActionEnum.CKO_GET_PAYMENT_ID,
                            userPayInfo.parsePaymentMethod().name(), PaymentConstant.SUCCESS_TEXT, paymentResponse.getResponseSummary());
                }
                break;
            default:
                throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        return (T) paymentSubscribeResult;
    }

    @Override
    public <T> T threeDomainSecurePayment(Object paymentResult) {
        ThreeDomainSecureBO threeDomainSecure = (ThreeDomainSecureBO) paymentResult;
        LOGGER.info("3d payment result:{}, 3d payment result BO:{}", paymentResult, threeDomainSecure);

        OrderPayment orderPayment = orderPaymentService.getByOrderNumber(threeDomainSecure.getOrderNumber());
        if (orderPayment == null) {
            LOGGER.error("cko 3ds capture fail. message: order payment not found. paymentResult:{}", paymentResult);
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        // 非订阅信息变更授权
        if (Objects.isNull(threeDomainSecure.getSubscribeActionType())) {
            return super.threeDomainSecurePayment(paymentResult);
        }

        // 获取3D订单支付结果
        CreateGetCkoOrderDetailResponse ckoOrderStateResponse = ckoPaymentSyncService.getCkoOrderStateResponse(getCheckoutConfigInfo(), threeDomainSecure.getOrderNumber(), threeDomainSecure.getSid());
        if (Objects.isNull(ckoOrderStateResponse)) {
            return null;
        }

        // 交易类型
        CkoPaymentStatusEnum paymentStatusEnum = CkoPaymentStatusEnum.parseName(ckoOrderStateResponse.getStatus());
        if (!CkoPaymentStatusEnum.CARD_VERIFIED.equals(paymentStatusEnum)) {
            return null;
        }
        PaymentSubscribeResultBO paymentSubscribeResult = new PaymentSubscribeResultBO();
        if (Boolean.TRUE.equals(ckoOrderStateResponse.getApproved())) {
            Source source = ckoOrderStateResponse.getSource();
            paymentSubscribeResult.setPaymentId(ckoOrderStateResponse.getId());
            paymentSubscribeResult.setPayId(source.getId());
            paymentSubscribeResult.setCardType(source.getScheme());
            paymentSubscribeResult.setCardCountry(source.getIssuerCountry());
            paymentSubscribeResult.setCardNumber(source.getBin() + "***" + source.getLast4());
            paymentSubscribeResult.setResponseCode(CkoPaymentConstant.DEFAULT_RESPONSE_CODE);
        }
        return (T) paymentSubscribeResult;
    }

    @Override
    protected void ckoCardVerifiedProcessor(Data responseData) {
        Order order = orderService.getByOrderNumber(responseData.getReference());
        if (order == null) {
            return;
        }

        // 只关注成功事件
        if (!CkoPaymentConstant.SUCCESS_CODE.equals(responseData.getResponseCode())) {
            return;
        }

        // 订阅发送延迟校验消息
        DoubleCheckBO doubleCheckBo = new DoubleCheckBO();
        doubleCheckBo.setBusinessId(order.getId());
        doubleCheckBo.setCheckType(DoubleCheckEnum.PaymentCkoUpdateCardCheck);
        PaymentParamCheckBO paymentParamCheckBo = new PaymentParamCheckBO();
        paymentParamCheckBo.setPayId(responseData.getId());
        paymentParamCheckBo.setPaymentChannel(getPaymentChannel());
        Source source = responseData.getSource();
        paymentParamCheckBo.setCkoSourceId(source.getId());
        paymentParamCheckBo.setCardType(source.getScheme());
        paymentParamCheckBo.setCardCountry(source.getIssuerCountry());
        paymentParamCheckBo.setCardNumber(source.getBin() + "***" + source.getLast4());

        doubleCheckBo.setPaymentParamCheckBo(paymentParamCheckBo);
        doubleCheckSendHelper.sendDoubleCheckMessage(doubleCheckBo);
    }
}
