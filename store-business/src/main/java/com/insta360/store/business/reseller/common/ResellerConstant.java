package com.insta360.store.business.reseller.common;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Description 分销常量类
 * @Date 2021/7/8
 */
public class ResellerConstant {

    public static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 默认分销（所有配件）产品ID
     */
    public static final int defaultAccessoriesId = 339;

    /**
     * 分销推广条款协议运营后台提醒文本-用户未阅读场景
     */
    public static final String defaultClauseStateWarnText = "该分销商还未阅读最新【分销商推广条款协议】";

    /**
     * 分销推广条款协议运营后台提醒文本-用户已阅读同意场景
     */
    public static final String consentClauseStateWarnText = "该分销商已阅读最新【分销商推广条款协议】,同意时间：%s";

    /**
     * 分销推广条款协议运营后台提醒文本-用户已阅读不同意场景
     */
    public static final String refusalClauseStateWarnText = "该分销商已阅读最新【分销商推广条款协议】,不同意时间：%s";

    /**
     * 提现账户用户名对应JsonKey
     */
    public static final String withdrawAccountUsernameJsonKey = "username";

    /**
     * 特殊分销商的专属优惠码
     */
    public static final String COUPON_CODE = "VTEVIP5";
}
