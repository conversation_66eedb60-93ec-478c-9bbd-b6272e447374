package com.insta360.store.business.review.service.impl.hepler;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.review.enums.ReviewRateLevelEnum;
import com.insta360.store.business.review.enums.ReviewStateEnum;
import com.insta360.store.business.review.model.Review;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2022/07/06
 * @Description:
 */
@Component
public class ReviewQueryHelper {

    /**
     * 选择对应的排序规则
     *
     * @param productId
     * @param rate
     * @param sortKey
     */
    public QueryWrapper<Review> getQueryWrapperBySort(Integer productId, Integer rate, String sortKey) {
        QueryWrapper<Review> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        // 只有已发布的才允许展示
        qw.eq("review_state", ReviewStateEnum.released.getCode());

        // 评分筛选规则，如果为lv0即表示没有特殊选择某个评分
        boolean isAllStarts = ReviewRateLevelEnum.LV0.equals(ReviewRateLevelEnum.parseScoreDefault(rate));
        if (!isAllStarts) {
            qw.eq("review_rate", rate);
        }

        // 排序规则
        switch (StringUtil.isBlank(sortKey) ? ReviewQuerySortKeys.CREATE_TIME_DESC : sortKey) {
            case ReviewQuerySortKeys.REVIEW_RATE_ASC:
                qw.orderByAsc("review_rate");
                break;
            case ReviewQuerySortKeys.REVIEW_RATE_DESC:
                // 只有该排序规则才兼容【置顶】功能
                // 选择了具体评分的话，【置顶】功能失效。
                qw = !isAllStarts ? qw.orderByDesc("review_rate") : qw.orderByDesc("top_tag", "review_rate");
                break;
            case ReviewQuerySortKeys.REVIEW_LIKE_COUNT_DESC:
                qw.orderByDesc("like_count", "review_rate");
                break;
            case ReviewQuerySortKeys.CREATE_TIME_DESC:
            default:
                break;
        }
        qw.orderByDesc("create_time");
        return qw;
    }

    /**
     * 评论排序规则
     */
    public interface ReviewQuerySortKeys {

        /**
         * 创建时间降序
         */
        String CREATE_TIME_DESC = "create_time_desc";

        /**
         * 评分降序
         */
        String REVIEW_RATE_DESC = "review_rate_desc";

        /**
         * 评分升序
         */
        String REVIEW_RATE_ASC = "review_rate_asc";

        /**
         * 点赞数降序
         */
        String REVIEW_LIKE_COUNT_DESC = "review_like_count_desc";
    }
}
