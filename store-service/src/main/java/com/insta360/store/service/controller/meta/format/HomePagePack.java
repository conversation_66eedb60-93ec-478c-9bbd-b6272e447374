package com.insta360.store.service.controller.meta.format;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.HomeItemGroupType;
import com.insta360.store.business.meta.model.AdapterTypeInfo;
import com.insta360.store.business.meta.model.HomepageItemCommodityGroup;
import com.insta360.store.business.meta.model.HomepageItemCommodityInfo;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.HomepageItemCommodityGroupService;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import com.insta360.store.business.meta.service.impl.helper.MetaBatchHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductAdapterType;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.review.model.ReviewRateCount;
import com.insta360.store.business.review.service.ReviewRateCountService;
import com.insta360.store.service.controller.meta.vo.HomeItemCommodityGroupVO;
import com.insta360.store.service.controller.meta.vo.HomeItemCommodityInfoVO;
import com.insta360.store.service.controller.meta.vo.HomeItemMainVO;
import com.insta360.store.service.controller.meta.vo.HomeItemPriceVO;
import com.insta360.store.service.controller.product.vo.CommodityTagInfoVO;
import com.insta360.store.service.controller.review.format.ReviewPack;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description:
 * @author: py
 * @create: 2023-08-23 18:55
 */
@Component
public class HomePagePack {

    /**
     * 特殊处理的套餐id：展示sku价格
     */
    private static final List<Integer> COMMODITY_ID_LIST = Lists.newArrayList(2492, 2607, 2870);

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    HomepageItemCommodityGroupService homepageItemCommodityGroupService;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    MetaBatchHelper metaBatchHelper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ReviewPack reviewPack;

    @Autowired
    ReviewRateCountService reviewRateCountService;

    /**
     * @param language
     * @param country
     * @return
     */
    public List<HomeItemMainVO> doPackCacheHomePageInfos(InstaLanguage language, InstaCountry country) {
        // 有效的模块类型
        List<HomepageItemMain> homeItems = homepageItemMainService.listEnableHomeItem();
        return homeItems.stream().map(homepageItemMain -> {
            HomeItemMainVO homeItemMainVo = new HomeItemMainVO(homepageItemMain);
            homeItemMainVo.setCommodityGroups(packHomepageItemCommodityGroup(homepageItemMain, language, country));
            return homeItemMainVo;
        }).collect(Collectors.toList());
    }

    /**
     * 封装套餐分组信息
     *
     * @param homepageItemMain
     * @param language
     * @param country
     * @return
     */
    private List<HomeItemCommodityGroupVO> packHomepageItemCommodityGroup(HomepageItemMain homepageItemMain, InstaLanguage language, InstaCountry country) {
        // 所有的首页套餐配置信息
        List<HomepageItemCommodityGroup> homepageItemCommodityGroups = homepageItemCommodityGroupService.listAllHomepageItemCommodityInfos(homepageItemMain.getId());
        List<Integer> commodityIds = homepageItemCommodityGroups.stream().map(HomepageItemCommodityGroup::getCommodityId).collect(Collectors.toList());

        // list to map --配置的套餐相关的信息
        Map<Integer, CommoditySaleState> commoditySaleStateMap = commodityBatchHelper.saleStateMapCommodityIds(commodityIds, country);
        Map<Integer, Commodity> commodityMap = commodityBatchHelper.commodityMapCommodityIds(commodityIds);
        List<Integer> productIds = commodityMap.values().stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());
        Map<Integer, Product> productMap = productBatchHelper.productMapProductIds(productIds);
        Map<Integer, CommodityPrice> commodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIds, country);

        homepageItemCommodityGroups = homepageItemCommodityGroups.stream()
                // 过滤下架的套餐 和 没有价格的套餐
                .filter(commodityGroup -> checkCommodityEnabled(commodityGroup.getCommodityId(), commodityMap, productMap))
                .filter(commodityGroup -> checkCommoditySaleState(commodityGroup.getCommodityId(), commoditySaleStateMap, commodityPriceMap, country))
                .collect(Collectors.toList());
        // empty list
        if (CollectionUtils.isEmpty(homepageItemCommodityGroups)) {
            return new ArrayList<>(0);
        }

        // 配件限制6个
        boolean isAccessory = HomeItemGroupType.TYPE_ACCESSORY.getKey().equals(homepageItemMain.getHomeItemType());
        homepageItemCommodityGroups = isAccessory ?
                homepageItemCommodityGroups.stream().limit(6).collect(Collectors.toList()) : homepageItemCommodityGroups;

        commodityIds = homepageItemCommodityGroups.stream().map(HomepageItemCommodityGroup::getCommodityId).collect(Collectors.toList());
        // list to map
        List<Integer> commodityGroupsIds = homepageItemCommodityGroups.stream().map(HomepageItemCommodityGroup::getId).collect(Collectors.toList());
        Map<Integer, HomepageItemCommodityInfo> itemCommodityInfoMap = metaBatchHelper.homepageItemInfoMapAdapterTypeIds(commodityGroupsIds, language);
        Map<Integer, CommodityInfo> commodityInfoMap = commodityBatchHelper.commodityInfoMapCommodityIds(commodityIds, language);
        Map<Integer, ProductInfo> productInfoMap = productBatchHelper.productInfoMapProductIds(productIds, language);
        Map<Integer, List<CommodityTagBind>> tagBindMap = commodityBatchHelper.commodityTagBindMapCommodityIds(commodityIds);
        Map<Integer, List<ProductAdapterType>> productAdapterMap = metaBatchHelper.productAdapterMapByProductIds(productIds);
        List<Integer> adapterTypeIds = new ArrayList<>(productAdapterMap.size() * 4);
        productAdapterMap.values().forEach(adapterTypes -> adapterTypes.forEach(adapterType -> adapterTypeIds.add(adapterType.getAdapterTypeId())));
        Map<Integer, AdapterTypeInfo> adapterTypeInfoMap = metaBatchHelper.adapterTypeMapAdapterTypeIds(adapterTypeIds, language);

        // 全部的套餐
        List<Integer> productIdList = homepageItemCommodityGroups.stream().map(HomepageItemCommodityGroup::getProductId).distinct().collect(Collectors.toList());
        List<Commodity> commodities = commodityService.listByProductIdsEnable(productIdList);
        List<Integer> commodityIdList = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        // list to map
        Map<Integer, CommodityTradeRule> tradeRuleMap = commodityBatchHelper.tradeRuleMapCommodityIds(commodityIdList);
        Map<Integer, Integer> stockCountMap = commodityBatchHelper.stockCountMapCommodityIds(commodityIdList, country);
        Map<Integer, CommoditySaleState> commoditySaleStateMapAll = commodityBatchHelper.saleStateMapCommodityIds(commodityIdList, country);
        Map<Integer, CommodityPrice> commodityPriceMapAll = commodityBatchHelper.priceMapCommodityIds(commodityIdList, country);

        // 过滤没有销售状态和价格的套餐
        List<Commodity> commodityListFinal = commodities.stream()
                .filter(commodity -> checkCommoditySaleState(commodity.getId(), commoditySaleStateMapAll, commodityPriceMapAll, country))
                .collect(Collectors.toList());
        Map<Integer, List<Commodity>> productCommodityMap = commodityListFinal.stream().collect(Collectors.groupingBy(Commodity::getProduct));

        // 选择新品标签
        Map<Integer, CommodityTagGroup> newCommodityTagGroupMap = commodityBatchHelper.newTagGroupMapTagIds();
        List<Integer> newTagIds = new ArrayList<>(newCommodityTagGroupMap.keySet());
        Map<Integer, CommodityTagInfo> commodityTagInfoMap = commodityBatchHelper.commodityTagInfoMapIds(newTagIds, language);
        // 评论星级
        Map<Integer, ReviewRateCount> reviewRateCountMap = reviewRateCountService.listByProductIds(productIds)
                .stream().collect(Collectors.toMap(ReviewRateCount::getProductId, r -> r));

        // 数据封装
        return homepageItemCommodityGroups.stream()
                .map(commodityGroup -> {
                    HomeItemCommodityGroupVO commodityGroupVo = new HomeItemCommodityGroupVO(commodityGroup);
                    // 产品支持的适配类型
                    commodityGroupVo.setAdapterTypeNames(packProductAdapterType(commodityGroup.getProductId(), productAdapterMap, adapterTypeInfoMap));

                    Commodity commodity = commodityMap.get(commodityGroup.getCommodityId());
                    Integer productId = commodity.getProduct();

                    // 套餐-产品内部名称
                    Product product = productMap.get(productId);
                    commodityGroupVo.setProductInternalName(product.getName());

                    // 套餐配置信息
                    commodityGroupVo.setItemInfos(packHomeItemCommodityInfo(commodityGroup, productMap, productInfoMap,
                            commodityInfoMap, itemCommodityInfoMap, language));

                    // 套餐关联的tag信息-首页只展示新品标签
                    CommoditySaleState commoditySaleState = commoditySaleStateMap.get(commodityGroup.getCommodityId());
                    commodityGroupVo.setTagInfos(packCommodityTagInfo(commodity,
                            tagBindMap, commoditySaleState.getSaleState(), newCommodityTagGroupMap, newTagIds, commodityTagInfoMap));

                    // 评论星级-配件才显示
                    if (isAccessory) {
                        commodityGroupVo.setReviewRateInfo(reviewPack.doPackReviewRateInfo(productId, reviewRateCountMap));
                    }

                    // 套餐价格(剔除了没有配置套餐价格和销售状态的套餐)
                    List<Commodity> allCommodities = productCommodityMap.get(productId);
                    if (CollectionUtils.isEmpty(allCommodities)) {
                        return null;
                    }
                    commodityGroupVo.setHomeItemPrice(packCommodityPrice(allCommodities, commodity,
                            tradeRuleMap, stockCountMap, commoditySaleStateMapAll, commodityPriceMapAll));

                    return commodityGroupVo;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 封装套餐配置信息
     *
     * @param commodityGroup
     * @param productMap
     * @param productInfoMap
     * @param commodityInfoMap
     * @param itemCommodityInfoMap
     * @return
     */
    private HomeItemCommodityInfoVO packHomeItemCommodityInfo(HomepageItemCommodityGroup commodityGroup, Map<Integer, Product> productMap,
                                                              Map<Integer, ProductInfo> productInfoMap, Map<Integer, CommodityInfo> commodityInfoMap,
                                                              Map<Integer, HomepageItemCommodityInfo> itemCommodityInfoMap, InstaLanguage language) {
        HomepageItemCommodityInfo homepageItemCommodityInfo = itemCommodityInfoMap.get(commodityGroup.getId());

        // 数据封装
        HomeItemCommodityInfoVO homeItemCommodityInfo = new HomeItemCommodityInfoVO(homepageItemCommodityInfo);

        // 产品名
        ProductInfo productInfo = productInfoMap.get(commodityGroup.getProductId());
        if (productInfo == null) {
            String message = "主页/类目页产品配置信息缺失。产品Id:" + commodityGroup.getProductId() + ", 语言:" + language;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return null;
        }
        homeItemCommodityInfo.setProductName(productInfo.getName());
        homeItemCommodityInfo.setDescription(productInfo.getDescription());

        // 产品跳转链接
        Product product = productMap.get(commodityGroup.getProductId());
        homeItemCommodityInfo.setProductKey(product.getUrlKey());

        // 产品名称标注（为空字符串则取产品名）
        if (StringUtil.isBlank(homeItemCommodityInfo.getDisplayName())) {
            homeItemCommodityInfo.setDisplayName(productInfo.getName());
        }

        // 套餐主图
        homeItemCommodityInfo.setCommodityDisplayImage(packDisplayImage(commodityGroup.getCommodityId()));

        // 套餐名
        CommodityInfo commodityInfo = commodityInfoMap.get(commodityGroup.getCommodityId());
        if (commodityInfo == null) {
            String message = "主页/类目页套餐配置信息缺失。套餐Id:" + commodityGroup.getCommodityId() + ", 语言:" + language;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return null;
        }
        homeItemCommodityInfo.setCommodityName(commodityInfo.getName());

        return homeItemCommodityInfo;
    }

    /**
     * 封装产品支持的适配类型
     *
     * @param productId
     * @param productAdapterMap
     * @param adapterTypeInfoMap
     * @return
     */
    private List<String> packProductAdapterType(Integer productId, Map<Integer, List<ProductAdapterType>> productAdapterMap, Map<Integer, AdapterTypeInfo> adapterTypeInfoMap) {
        // 产品支持的适配类型
        List<ProductAdapterType> productAdapterTypes = productAdapterMap.get(productId);
        if (CollectionUtils.isEmpty(productAdapterTypes)) {
            return null;
        }

        // 适配类型列表名称
        return productAdapterTypes.stream().map(productAdapterType -> {
            // 适配类型文案集合
            AdapterTypeInfo adapterTypeInfo = adapterTypeInfoMap.get(productAdapterType.getAdapterTypeId());
            if (adapterTypeInfo == null) {
                return null;
            }
            return adapterTypeInfo.getInfoName();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 套餐关联的tag信息
     *
     * @param commodity
     * @param tagBindMap
     * @param commoditySaleState
     * @param newCommodityTagGroupMap 所有启用的新品标签
     * @param commodityTagInfoMap
     * @param newTagIds
     * @return
     */
    public List<CommodityTagInfoVO> packCommodityTagInfo(Commodity commodity, Map<Integer, List<CommodityTagBind>> tagBindMap,
                                                         Integer commoditySaleState, Map<Integer, CommodityTagGroup> newCommodityTagGroupMap,
                                                         List<Integer> newTagIds, Map<Integer, CommodityTagInfo> commodityTagInfoMap) {
        // 没有绑定tag 和 缺货的套餐 则不进行展示
        List<CommodityTagBind> commodityTagBinds = tagBindMap.get(commodity.getId());
        if (CollectionUtils.isEmpty(commodityTagBinds) || SaleState.out_of_stock.equals(SaleState.parse(commoditySaleState))) {
            return new ArrayList<>();
        }

        // 只选择新品标签
        List<CommodityTagBind> newCommodityTagBinds = commodityTagBinds.stream()
                .filter(commodityTagBind -> newTagIds.contains(commodityTagBind.getTagId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(newCommodityTagBinds)) {
            return new ArrayList<>();
        }

        // 封装tag信息
        return newCommodityTagBinds
                .stream()
                .map(commodityTagBind -> doPackCommodityTagInfo(commodityTagBind, newCommodityTagGroupMap, commodityTagInfoMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 封装套餐主图信息
     *
     * @param commodityId
     */
    private String packDisplayImage(Integer commodityId) {
        CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(commodityId);
        if (commodityDisplay == null) {
            String message = "主页/类目页套餐主图信息缺失。套餐Id:" + commodityId;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            return null;
        }
        return commodityDisplay.getUrl();
    }

    /**
     * 封装套餐的价格信息
     *
     * @param allCommodities
     * @param commodity
     * @param tradeRuleMap
     * @param commoditySaleStateMap
     * @param commodityPriceMap
     * @return
     */
    private HomeItemPriceVO packCommodityPrice(List<Commodity> allCommodities, Commodity commodity, Map<Integer, CommodityTradeRule> tradeRuleMap,
                                               Map<Integer, Integer> stockCountMap, Map<Integer, CommoditySaleState> commoditySaleStateMap,
                                               Map<Integer, CommodityPrice> commodityPriceMap) {
        List<Integer> commodityIdList = allCommodities.stream().map(Commodity::getId).collect(Collectors.toList());
        boolean showSpuPrice = true;
        if (commodityIdList.size() == 1) {
            showSpuPrice = false;
        }

        // todo:后期迭代，目前特殊处理x3潜水壳套餐、x3车载三吸盘套餐
        Integer skuCommodityId = commodity.getId();
        if (COMMODITY_ID_LIST.contains(skuCommodityId)) {
            showSpuPrice = false;
            commodityIdList = Collections.singletonList(skuCommodityId);
        }

        // 销售状态为缺货
        List<Integer> outOfStockStateCommodityIds = commodityIdList
                .stream()
                .filter(commodityId -> SaleState.outOfStockStates().contains(SaleState.parse(commoditySaleStateMap.get(commodityId).getSaleState())))
                .collect(Collectors.toList());

        // 库存为0
        List<Integer> noStockCommodityIds = commodityIdList
                .stream()
                .filter(commodityId -> Objects.isNull(stockCountMap.get(commodityId)) || stockCountMap.get(commodityId) == 0)
                .collect(Collectors.toList());

        // 缺货总list
        List<Integer> finalStockCommodityIds = Stream.concat(outOfStockStateCommodityIds.stream(), noStockCommodityIds.stream())
                .distinct()
                .collect(Collectors.toList());

        // 非缺货的总list
        List<Integer> normalCommodityIds = commodityIdList
                .stream()
                .filter(commodityId -> !finalStockCommodityIds.contains(commodityId))
                .collect(Collectors.toList());

        // 单个sku的价格
        CommodityPrice commodityPrice = commodityPriceMap.get(commodity.getId());

        // 是否展示spu的价格
        if (showSpuPrice) {
            // 缺货、即将上架、下架sku
            List<CommodityPrice> outOfStockStateCommodityPrices = finalStockCommodityIds.stream().map(commodityPriceMap::get)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            CommodityPrice priceStock = outOfStockStateCommodityPrices.stream().min(Comparator.comparing(CommodityPrice::getOriginAmount)).orElse(null);

            // 日常sku
            List<CommodityPrice> normalCommodityPrices = normalCommodityIds.stream().map(commodityPriceMap::get)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            CommodityPrice priceNormal = normalCommodityPrices.stream().min(Comparator.comparing(CommodityPrice::getAmount)).orElse(null);

            if (Objects.nonNull(priceStock) && Objects.nonNull(priceNormal)) {
                commodityPrice = priceStock.getOriginAmount() >= priceNormal.getAmount() ? priceNormal : priceStock;
            } else {
                commodityPrice = Objects.isNull(priceNormal) ? priceStock : priceNormal;
            }
        }

        // 套餐价格VO
        HomeItemPriceVO homeItemPriceVo = new HomeItemPriceVO();

        // 控制卡片价格展示
        homeItemPriceVo.setShowSpuPrice(showSpuPrice);

        // 现价
        homeItemPriceVo.setPrice(commodityPrice.price());
        // 原价（不相等才展示）
        if (!commodityPrice.getAmount().equals(commodityPrice.getOriginAmount())) {
            homeItemPriceVo.setOriginPrice(commodityPrice.originPrice());
        }

        // 库存为0、缺货-原价->现价
        Integer id = commodityPrice.getCommodityId();
        if (finalStockCommodityIds.contains(id)) {
            homeItemPriceVo.setPrice(commodityPrice.originPrice());
            homeItemPriceVo.setOriginPrice(null);
        }

        // 全部的sku都缺货、下架、即将上架、库存为0
        if (finalStockCommodityIds.size() == commodityIdList.size()) {
            //（缺货套餐不予展示折扣信息）
            return homeItemPriceVo;
        }
        commodityIdList.removeAll(finalStockCommodityIds);
        List<CommodityPrice> commodityPrices = commodityIdList.stream().map(commodityPriceMap::get).filter(Objects::nonNull).collect(Collectors.toList());

        // 最大折扣力度
        Price saveAmount = getCommoditySaveAmount(commodityPrices);
        if (Objects.isNull(saveAmount)) {
            return homeItemPriceVo;
        }
        homeItemPriceVo.setMaxDiscount(saveAmount);
        return homeItemPriceVo;
    }

    /**
     * 套餐禁用 && 产品禁用 不予展示
     *
     * @param commodityId
     * @param commodityMap
     * @param productMap
     * @return
     */
    private Boolean checkCommodityEnabled(Integer commodityId, Map<Integer, Commodity> commodityMap, Map<Integer, Product> productMap) {
        Commodity commodity = commodityMap.get(commodityId);
        if (commodity == null) {
            return false;
        }

        Product product = productMap.get(commodity.getProduct());
        return product != null;
    }

    /**
     * 套餐下架 && 没有价格 不予展示
     * 销售状态不存在
     *
     * @param commodityId
     * @param saleStateMap
     * @param priceMap
     * @param country
     * @return
     */
    private Boolean checkCommoditySaleState(Integer commodityId, Map<Integer, CommoditySaleState> saleStateMap, Map<Integer, CommodityPrice> priceMap, InstaCountry country) {
        CommoditySaleState saleState = saleStateMap.get(commodityId);
        if (saleState == null) {
            return false;
        }

        // 过滤掉下架的套餐和没有价格的套餐
        CommodityPrice commodityPrice = priceMap.get(commodityId);
        if (SaleState.remove.getCode() != saleState.getSaleState() && commodityPrice != null) {
            return true;
        }

        if (SaleState.remove.getCode() != saleState.getSaleState() && commodityPrice == null) {
            String msg = "主页/类目页套餐价格未找到。套餐id：" + commodityId + ", 地区：" + country.name();
            FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                    FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY
            );
        }
        return false;
    }

    /**
     * 获取套餐最大折扣价格（单个套餐的节省金 = 原价 - 现价）
     *
     * @param commodityPrices
     * @return
     */
    public Price getCommoditySaveAmount(List<CommodityPrice> commodityPrices) {
        List<Price> priceList = commodityPrices.stream().map(commodityPrice -> {
            // 总的赠品折扣
            BigDecimal originAmount = new BigDecimal(String.valueOf(commodityPrice.getOriginAmount()));
            BigDecimal amount = new BigDecimal(String.valueOf(commodityPrice.getAmount()));
            BigDecimal giftAllSaveAmount = originAmount.subtract(amount);
            return new Price(commodityPrice.currency(), formatFloat(giftAllSaveAmount.floatValue()));
        }).collect(Collectors.toList());

        Price price = priceList.stream().max(Comparator.comparing(Price::getAmount)).orElse(null);
        if (price != null && price.getAmount() != 0) {
            return price;
        }
        return null;
    }

    /**
     * 格式化
     *
     * @param amount
     * @return
     */
    public static Float formatFloat(Float amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return Float.valueOf(decimalFormat.format(amount));
    }

    /**
     * 封装套餐的tag信息
     *
     * @param commodityTagBind
     * @param tagGroupMap
     * @param tagInfoMap
     * @return
     */
    private CommodityTagInfoVO doPackCommodityTagInfo(CommodityTagBind commodityTagBind, Map<Integer, CommodityTagGroup> tagGroupMap,
                                                      Map<Integer, CommodityTagInfo> tagInfoMap) {
        CommodityTagGroup commodityTagGroup = tagGroupMap.get(commodityTagBind.getTagId());
        CommodityTagInfo tagInfo = tagInfoMap.get(commodityTagBind.getTagId());

        // tag分组、info信息要真实有效
        if (Objects.isNull(commodityTagGroup) || Objects.isNull(tagInfo)) {
            return null;
        }

        // 数据转换
        CommodityTagInfoVO commodityTagInfoVo = new CommodityTagInfoVO(tagInfo);
        commodityTagInfoVo.setTagGroupType(commodityTagGroup.getGroupType());
        commodityTagInfoVo.setFontColor(commodityTagGroup.getFontColor());
        commodityTagInfoVo.setFontHoverColor(commodityTagGroup.getFontHoverColor());
        commodityTagInfoVo.setBackGroundColor(commodityTagGroup.getBackGroundColor());
        commodityTagInfoVo.setHoverBackGroundColor(commodityTagGroup.getHoverBackGroundColor());
        return commodityTagInfoVo;
    }
}
