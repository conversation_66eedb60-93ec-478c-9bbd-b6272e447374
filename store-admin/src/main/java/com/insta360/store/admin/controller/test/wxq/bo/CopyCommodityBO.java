package com.insta360.store.admin.controller.test.wxq.bo;

import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.product.model.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/17
 */
public class CopyCommodityBO implements Serializable {

    private Product product;

    // 强制ID写入不要自增
    private List<Commodity> commodities;

    // 去除自增ID
    private List<FaqQuestion> faqQuestions;

    private List<ProductInfo> productInfos;

    private List<CommodityInfo> commodityInfos;

    private List<CommodityMeta> commodityMetas;

    // 去除自增ID
    private List<CommodityPrice> commodityPrices;

    // 去除自增ID
    private List<CommodityDisplay> commodityDisplays;

    private List<CommoditySaleState> commoditySaleStates;

    private List<CommodityTradeRule> commodityTradeRules;

    private List<ProductCommodityStock> productCommodityStocks;

    private List<CommodityDeliveryTimeConfig> commodityDeliveryTimeConfigs;

    // 去除自增ID
    private List<CommodityDifference> commodityDifferences;

    // 去除自增ID
    private List<CommodityDifferencePoint> commodityDifferencePoints;

    private List<CommodityDifferencePointText> commodityDifferencePointTexts;

    private List<ProductCommodityOverview> commodityOverviews;

    private List<ProductCommodityOverviewContent> productCommodityOverviewContents;

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public List<Commodity> getCommodities() {
        return commodities;
    }

    public void setCommodities(List<Commodity> commodities) {
        this.commodities = commodities;
    }

    public List<FaqQuestion> getFaqQuestions() {
        return faqQuestions;
    }

    public void setFaqQuestions(List<FaqQuestion> faqQuestions) {
        this.faqQuestions = faqQuestions;
    }

    public List<ProductInfo> getProductInfos() {
        return productInfos;
    }

    public void setProductInfos(List<ProductInfo> productInfos) {
        this.productInfos = productInfos;
    }

    public List<CommodityInfo> getCommodityInfos() {
        return commodityInfos;
    }

    public void setCommodityInfos(List<CommodityInfo> commodityInfos) {
        this.commodityInfos = commodityInfos;
    }

    public List<CommodityMeta> getCommodityMetas() {
        return commodityMetas;
    }

    public void setCommodityMetas(List<CommodityMeta> commodityMetas) {
        this.commodityMetas = commodityMetas;
    }

    public List<CommodityPrice> getCommodityPrices() {
        return commodityPrices;
    }

    public void setCommodityPrices(List<CommodityPrice> commodityPrices) {
        this.commodityPrices = commodityPrices;
    }

    public List<CommodityDisplay> getCommodityDisplays() {
        return commodityDisplays;
    }

    public void setCommodityDisplays(List<CommodityDisplay> commodityDisplays) {
        this.commodityDisplays = commodityDisplays;
    }

    public List<CommoditySaleState> getCommoditySaleStates() {
        return commoditySaleStates;
    }

    public void setCommoditySaleStates(List<CommoditySaleState> commoditySaleStates) {
        this.commoditySaleStates = commoditySaleStates;
    }

    public List<ProductCommodityOverview> getCommodityOverviews() {
        return commodityOverviews;
    }

    public void setCommodityOverviews(List<ProductCommodityOverview> commodityOverviews) {
        this.commodityOverviews = commodityOverviews;
    }

    public List<CommodityTradeRule> getCommodityTradeRules() {
        return commodityTradeRules;
    }

    public void setCommodityTradeRules(List<CommodityTradeRule> commodityTradeRules) {
        this.commodityTradeRules = commodityTradeRules;
    }

    public List<ProductCommodityStock> getProductCommodityStocks() {
        return productCommodityStocks;
    }

    public void setProductCommodityStocks(List<ProductCommodityStock> productCommodityStocks) {
        this.productCommodityStocks = productCommodityStocks;
    }

    public List<CommodityDeliveryTimeConfig> getCommodityDeliveryTimeConfigs() {
        return commodityDeliveryTimeConfigs;
    }

    public void setCommodityDeliveryTimeConfigs(List<CommodityDeliveryTimeConfig> commodityDeliveryTimeConfigs) {
        this.commodityDeliveryTimeConfigs = commodityDeliveryTimeConfigs;
    }

    public List<ProductCommodityOverviewContent> getProductCommodityOverviewContents() {
        return productCommodityOverviewContents;
    }

    public void setProductCommodityOverviewContents(List<ProductCommodityOverviewContent> productCommodityOverviewContents) {
        this.productCommodityOverviewContents = productCommodityOverviewContents;
    }

    public List<CommodityDifference> getCommodityDifferences() {
        return commodityDifferences;
    }

    public void setCommodityDifferences(List<CommodityDifference> commodityDifferences) {
        this.commodityDifferences = commodityDifferences;
    }

    public List<CommodityDifferencePoint> getCommodityDifferencePoints() {
        return commodityDifferencePoints;
    }

    public void setCommodityDifferencePoints(List<CommodityDifferencePoint> commodityDifferencePoints) {
        this.commodityDifferencePoints = commodityDifferencePoints;
    }

    public List<CommodityDifferencePointText> getCommodityDifferencePointTexts() {
        return commodityDifferencePointTexts;
    }

    public void setCommodityDifferencePointTexts(List<CommodityDifferencePointText> commodityDifferencePointTexts) {
        this.commodityDifferencePointTexts = commodityDifferencePointTexts;
    }

    @Override
    public String toString() {
        return "CopyCommodityBO{" +
                "product=" + product +
                ", commodities=" + commodities +
                ", faqQuestions=" + faqQuestions +
                ", productInfos=" + productInfos +
                ", commodityInfos=" + commodityInfos +
                ", commodityMetas=" + commodityMetas +
                ", commodityPrices=" + commodityPrices +
                ", commodityDisplays=" + commodityDisplays +
                ", commoditySaleStates=" + commoditySaleStates +
                ", productCommodityStocks=" + productCommodityStocks +
                ", commodityDifferences=" + commodityDifferences +
                ", commodityDifferencePoints=" + commodityDifferencePoints +
                ", commodityDifferencePointTexts=" + commodityDifferencePointTexts +
                ", commodityOverviews=" + commodityOverviews +
                ", productCommodityOverviewContents=" + productCommodityOverviewContents +
                '}';
    }
}
