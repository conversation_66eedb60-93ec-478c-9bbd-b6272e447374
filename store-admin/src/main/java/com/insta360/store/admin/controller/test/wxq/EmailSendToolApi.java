package com.insta360.store.admin.controller.test.wxq;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.test.wxq.dto.CartEmailSendDTO;
import com.insta360.store.admin.controller.test.wxq.dto.SendTestCartEmail;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.outgoing.rpc.user.service.UserAccountService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.trade.bo.UserCartCommodityInfoBO;
import com.insta360.store.business.trade.email.BaseTradeEmail;
import com.insta360.store.business.trade.email.TradeEmailFactory;
import com.insta360.store.business.trade.email.TradeUserCartAbandonFlow2ProEmail;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import com.insta360.store.business.trade.model.UserCart;
import com.insta360.store.business.trade.model.UserCartEmailSendRecord;
import com.insta360.store.business.trade.model.UserCartEmailSendRule;
import com.insta360.store.business.trade.service.UserCartEmailSendRecordService;
import com.insta360.store.business.trade.service.UserCartEmailSendRuleService;
import com.insta360.store.business.trade.service.UserCartService;
import com.insta360.store.business.trade.service.impl.helper.CartItemsParser;
import com.insta360.store.business.trade.service.impl.helper.cart.UserCartHelper;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.EmailSubscribeService;
import com.insta360.store.business.user.service.StoreAccountService;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * tool/邮件发送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6
 */
@RestController
public class EmailSendToolApi extends BaseAdminApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmailSendToolApi.class);

    @Autowired
    UserCartHelper userCartHelper;

    @Autowired
    CartItemsParser cartItemsParser;

    @Autowired
    UserCartService userCartService;

    @Autowired
    TradeEmailFactory tradeEmailFactory;

    @Autowired
    UserAccountHelper userAccountHelper;

    @Autowired
    UserAccountService userAccountService;

    @Autowired
    StoreAccountService storeAccountService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    UserCartEmailSendRuleService emailSendRuleService;

    @Autowired
    UserCartEmailSendRecordService emailSendRecordService;

    /**
     * 发送购物车邮件
     *
     * @param cartEmailSendDto 邮件发送DTO
     * @return {@link Response }<{@link Object }>
     */
    @PostMapping("/admin/test/wxq/email/sendCartEmail")
    public Response<Object> sendCartEmail(@RequestBody @Validated CartEmailSendDTO cartEmailSendDto) {
        StringBuilder notSendMessage = new StringBuilder();
        List<String> emailList = cartEmailSendDto.getEmailList();
        List<String> sendEmailList = new ArrayList<>();
        for (String email : emailList) {
            StoreAccount storeAccount = userAccountHelper.getStoreAccountByEmail(email);
            UserCart userCart = userCartService.getByUserId(storeAccount.getInstaAccount());
            if (userCart == null) {
                notSendMessage.append(String.format("%s 购物车信息不存在不发送\n", email));
                continue;
            }

            EmailSubscribe emailSubscribe = emailSubscribeService.getByEmailLast(email);
            if (emailSubscribe == null) {
                notSendMessage.append(String.format("%s 未订阅不发送\n", email));
                continue;
            }

            UserCartEmailSendRule userCartEmailSendRule = emailSendRuleService.getByCountry(emailSubscribe.country());
            InstaLanguage language = userCartEmailSendRule.language();

            // 解析购物车商品项
            List<OrderSheet.SheetItem> items = cartItemsParser.parse(JSONArray.parseArray(userCart.getItems()));
            List<Integer> commodities = items.stream().map(OrderSheet.SheetItem::getCommodityId).collect(Collectors.toList());
            Map<Integer, Integer> commodityIdNumberMap = new HashMap<>();
            items.forEach(item -> commodityIdNumberMap.put(item.getCommodityId(), item.getNumber()));
            if (CollectionUtils.isEmpty(items)) {
                notSendMessage.append(String.format("%s 购物车为空不发送\n", email));
            }
            Currency currency = countryConfigService.getCountryCurrency(emailSubscribe.country());
            List<UserCartCommodityInfoBO> userCartCommodityInfoBos = userCartHelper.packCartCommodityInfo(cartEmailSendDto.getUuid(), language, commodities, emailSubscribe.country(), commodityIdNumberMap);

            // 根据商品和用户偏好创建并发送邮件
            BaseTradeEmail userCartEmail = tradeEmailFactory.getEmail(cartEmailSendDto.getUuid(),
                    language,
                    emailSubscribe.country(),
                    currency,
                    cartEmailSendDto.getEmailType(),
                    userCartCommodityInfoBos,
                    cartEmailSendDto.getEmailType().getTemplate());
            userCartEmail.doSend(email);
            sendEmailList.add(email);
            LOGGER.info("[测试接口邮件发送成功] uuid:{} email:{}", cartEmailSendDto.getUuid(), email);
        }
        String message = String.format("类型:%s 发送完毕 \n发送列表邮件:%s \n不发送列表:%s", cartEmailSendDto.getEmailType(), sendEmailList, notSendMessage);
        FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning);

        return Response.ok(message);
    }

    /**
     * 发送购物车邮件
     *
     * @param cartEmailSendDto 邮件发送DTO
     * @return {@link Response }<{@link Object }>
     */
    @PostMapping("/admin/test/wxq/email/sendCartEmail2")
    public Response<Object> sendCartEmail2(@RequestBody @Validated CartEmailSendDTO cartEmailSendDto) {
        StringBuilder notSendMessage = new StringBuilder();
        List<String> emailList = cartEmailSendDto.getEmailList();
        List<String> sendEmailList = new ArrayList<>();
        for (String email : emailList) {
            StoreAccount storeAccount = userAccountHelper.getStoreAccountByEmail(email);
            UserCart userCart = userCartService.getByUserId(storeAccount.getInstaAccount());

            EmailSubscribe emailSubscribe = emailSubscribeService.getByEmailLast(email);
            if (emailSubscribe == null) {
                notSendMessage.append(String.format("%s 未订阅不发送\n", email));
                continue;
            }

            UserCartEmailSendRule userCartEmailSendRule = emailSendRuleService.getByCountry(emailSubscribe.country());
            InstaLanguage language = userCartEmailSendRule.language();

            // 解析购物车商品项
            List<OrderSheet.SheetItem> items = cartItemsParser.parse(JSONArray.parseArray(userCart.getItems()));
            List<Integer> commodities = items.stream().map(OrderSheet.SheetItem::getCommodityId).collect(Collectors.toList());
            Map<Integer, Integer> commodityIdNumberMap = new HashMap<>();
            items.forEach(item -> commodityIdNumberMap.put(item.getCommodityId(), item.getNumber()));
            if (CollectionUtils.isEmpty(items)) {
                notSendMessage.append(String.format("%s 购物车为空不发送\n", email));
            }
            Currency currency = countryConfigService.getCountryCurrency(emailSubscribe.country());
            List<UserCartCommodityInfoBO> userCartCommodityInfoBos = userCartHelper.packParticularCommodityInfo(cartEmailSendDto.getUuid(), language, commodities, Product.X5_ID, emailSubscribe.country(), commodityIdNumberMap);

            // 根据商品和用户偏好创建并发送邮件
            BaseTradeEmail userCartEmail = tradeEmailFactory.getEmail(cartEmailSendDto.getUuid(),
                    language,
                    emailSubscribe.country(),
                    currency,
                    cartEmailSendDto.getEmailType(),
                    userCartCommodityInfoBos,
                    cartEmailSendDto.getEmailType().getTemplate());
            userCartEmail.doSend(email);
            sendEmailList.add(email);
            LOGGER.info("[测试接口邮件发送成功] uuid:{} email:{}", cartEmailSendDto.getUuid(), email);
        }
        String message = String.format("类型:%s 发送完毕 \n发送列表邮件:%s \n不发送列表:%s", cartEmailSendDto.getEmailType(), sendEmailList, notSendMessage);
        FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning);

        return Response.ok(message);
    }

    /**
     * 发送测试邮件
     *
     * @param testCartEmail
     * @return
     */
    @PostMapping("/admin/test/wxq/email/sendTestEmail")
    public Response<Object> sendTestEmail(@RequestBody SendTestCartEmail testCartEmail) {
        String email = testCartEmail.getEmail();
        StoreAccount storeAccount = userAccountHelper.getStoreAccountByEmail(email);
        userCartHelper.sendNewEmailMessage(testCartEmail.getUserCartEmailEnum(), storeAccount, testCartEmail.getUuid());
        return Response.ok(testCartEmail.getUuid());
    }

    /**
     * 删除邮件发送记录
     *
     * @param email 邮件
     * @return
     */
    @GetMapping("/admin/test/wxq/email/deleteEmailRecord")
    public Response<Object> deleteEmailRecord(@RequestParam String email) {
        LambdaQueryWrapper<UserCartEmailSendRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserCartEmailSendRecord::getEmail, email);
        emailSendRecordService.remove(lqw);
        return Response.ok();
    }

    /**
     * 发送测试邮件
     *
     * @param param
     * @return
     */
    @PostMapping("/admin/test/zhangcheng/email/sendTestEmail")
    public Response<Object> sendFlowEmail(@RequestBody SendTestCartEmail param) {
        BaseTradeEmail email1 = tradeEmailFactory.getEmail(
                param.getUuid(),
                InstaLanguage.en_US,
                InstaCountry.US,
                Currency.USD,
                UserCartEmailEnum.FLOW2PRO_FIRST_EMAIL,
                null,
                TradeUserCartAbandonFlow2ProEmail.class);
        email1.doSend("<EMAIL>");
        return Response.ok(param.getUuid());
    }

}
