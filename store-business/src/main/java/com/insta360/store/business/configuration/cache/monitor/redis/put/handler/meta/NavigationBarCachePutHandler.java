package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CacheCountiesBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.outgoing.rpc.store.job.NavigationBarCachePutService;
import feign.RetryableException;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: wbt
 * @Date: 2023/11/03
 * @Description:
 */
@Component
public class NavigationBarCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(NavigationBarCachePutHandler.class);

    @Autowired
    NavigationBarCachePutService navigationBarCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        CacheConstant.PARTITION_COUNTIES.forEach(cacheCounties -> cachePutThreadPool.execute(() -> this.task1(cacheCounties, countDownLatch)));
        countDownLatch.await();

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    public CachePutKeyParameterBO cacheParamParse(JoinPoint joinPoint) {
        return null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.NAVIGATION_BAR_CATEGORY_KEY;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.NAVIGATION_BAR_CATEGORY_KEY);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            for (CacheCountiesBO cacheCounties : (List<CacheCountiesBO>) param) {
                navigationBarCachePutService.listNavigationBarCategoryInfos(cacheCounties.getCountry(), cacheCounties.getLanguage());
            }
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {
    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }

    @Override
    public Integer getTaskNumber() {
        return super.getTaskNumber() - 2;
    }
}
