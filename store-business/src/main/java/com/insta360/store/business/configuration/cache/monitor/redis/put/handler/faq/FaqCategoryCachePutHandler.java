package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.faq;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.faq.model.FaqQuestionBind;
import com.insta360.store.business.faq.service.FaqQuestionBindService;
import com.insta360.store.business.outgoing.rpc.store.job.FaqCachePutService;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: wkx
 * @Date: 2023/11/16
 * @Description:
 */
@Component
public class FaqCategoryCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(FaqCategoryCachePutHandler.class);

    @Autowired
    FaqCachePutService faqCachePutService;

    @Autowired
    FaqQuestionBindService faqQuestionBindService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) {
        List<FaqQuestionBind> questionBinds = faqQuestionBindService.listQuestionBinds();
        if (CollectionUtils.isEmpty(questionBinds)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。questionBinds为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 更新页面绑定QA（类目结构）
        questionBinds.forEach(questionBind ->
                CacheConstant.COUNTIES.forEach(cacheCounties ->
                        faqCachePutService.listQuestionBind(questionBind.getType(), questionBind.getPageKey(),
                                cacheCounties.getCountry(), cacheCounties.getLanguage())));

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    public CachePutKeyParameterBO cacheParamParse(JoinPoint joinPoint) {
        return null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.FAQ_QUESTION;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.FAQ_QUESTION);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.FALSE;
    }
}
