package com.insta360.store.service.controller.search.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/4
 */
public class CommodityResultVO implements Serializable {

    /**
     * 套餐ID
     */
    private Integer commodityId;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 套餐功能描述
     */
    private String functionDescription;

    /**
     * 套餐主图
     */
    private String imageUrl;

    /**
     * 套餐创建时间
     */
    private LocalDateTime createTime;

    /**
     * 套餐销售状态
     */
    private Integer saleState;

    /**
     * 套餐库存
     */
    private Integer stockCount;

    /**
     * 套餐购买限制数量
     */
    private Integer orderBuyLimit;

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getFunctionDescription() {
        return functionDescription;
    }

    public void setFunctionDescription(String functionDescription) {
        this.functionDescription = functionDescription;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getSaleState() {
        return saleState;
    }

    public void setSaleState(Integer saleState) {
        this.saleState = saleState;
    }

    public Integer getStockCount() {
        return stockCount;
    }

    public void setStockCount(Integer stockCount) {
        this.stockCount = stockCount;
    }

    public Integer getOrderBuyLimit() {
        return orderBuyLimit;
    }

    public void setOrderBuyLimit(Integer orderBuyLimit) {
        this.orderBuyLimit = orderBuyLimit;
    }

    @Override
    public String toString() {
        return "CommodityResultVO{" +
                "commodityId=" + commodityId +
                ", commodityName='" + commodityName + '\'' +
                ", functionDescription='" + functionDescription + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", createTime=" + createTime +
                ", saleState=" + saleState +
                ", stockCount=" + stockCount +
                ", orderBuyLimit=" + orderBuyLimit +
                '}';
    }
}
