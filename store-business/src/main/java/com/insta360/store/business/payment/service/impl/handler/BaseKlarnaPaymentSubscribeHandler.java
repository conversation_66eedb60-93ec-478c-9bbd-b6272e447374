package com.insta360.store.business.payment.service.impl.handler;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.ThreadUtil;
import com.insta360.store.business.cloud.constant.CloudSubscribeTextConstant;
import com.insta360.store.business.cloud.email.BaseCloudEmail;
import com.insta360.store.business.cloud.email.CloudEmailFactory;
import com.insta360.store.business.cloud.email.CloudSubscribeKlarnaUpdateEmail;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.enums.SkuSubscribeType;
import com.insta360.store.business.cloud.enums.SubscribeActionType;
import com.insta360.store.business.cloud.model.SubscribeBillingAddress;
import com.insta360.store.business.cloud.service.SubscribeBillingAddressService;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.configuration.utils.EncodingUtil;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderBillingAddress;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.bo.PaymentSubscribeBO;
import com.insta360.store.business.payment.bo.PaymentSubscribeResultBO;
import com.insta360.store.business.payment.constants.KlarnaConstant;
import com.insta360.store.business.payment.constants.PaymentConstant;
import com.insta360.store.business.payment.enums.*;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.klarna.bo.KlarnaResponseBO;
import com.insta360.store.business.payment.lib.klarna.config.KlarnaPaymentConfiguration;
import com.insta360.store.business.payment.lib.klarna.enums.KlarnaFraudStatusEnum;
import com.insta360.store.business.payment.lib.klarna.module.*;
import com.insta360.store.business.payment.lib.klarna.request.*;
import com.insta360.store.business.payment.lib.klarna.response.*;
import com.insta360.store.business.payment.service.impl.helper.KlarnaPaymentHelper;
import com.insta360.store.business.payment.service.impl.helper.PaymentHelper;
import com.insta360.store.business.product.enums.ProductCategoryFinalType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.trade.enums.CreditCardPaymentActionEnum;
import com.insta360.store.business.trade.exception.TradeErrorCode;
import com.insta360.store.business.trade.model.KlarnaPaymentConfig;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.UserPayInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2024/07/31
 * @Description:
 */
public abstract class BaseKlarnaPaymentSubscribeHandler extends BaseKlarnaPaymentHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseKlarnaPaymentSubscribeHandler.class);

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    KlarnaPaymentHelper klarnaPaymentHelper;

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    SubscribeBillingAddressService subscribeBillingAddressService;

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    CloudEmailFactory cloudEmailFactory;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Override
    protected <T> T _payOrder(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        // 非订阅地区限制
        if (KlarnaConstant.KLARNA_UNSUBSCRIBE_COUNTRY.contains(order.getArea())) {
            FeiShuMessageUtil.storeGeneralMessage(String.format(KlarnaConstant.KLARNA_UNSUBSCRIBE_COUNTRY_ERROR_TEXT, order.getOrderNumber()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(PaymentErrorCode.InvalidPaymentMethodException);
        }

        // 处理续费订单扣款
        if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            return this.processRenewOrderPayment(paymentInfo, paymentExtra.getStoreAccount());
        }
        return super._payOrder(paymentInfo, paymentExtra);
    }

    /**
     * 处理续费订单扣款
     *
     * @param paymentInfo
     * @param storeAccount
     * @param <T>
     * @return
     */
    protected <T> T processRenewOrderPayment(PaymentInfo paymentInfo, StoreAccount storeAccount) {
        if (Objects.isNull(storeAccount)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(storeAccount.getInstaAccount());
        String payId = AESUtil.decode(AESUtil.PAY_TOKEN_KEY, userPayInfo.getPayId());
        KlarnaCustomerTokenOrderRequest tokenOrderRequest = new KlarnaCustomerTokenOrderRequest(payId, this.getKlarnaPaymentConfiguration(this.order.getArea()));
        // 构建续费订单支付参数
        buildRenewOrderRequestParam(paymentInfo, order, tokenOrderRequest, storeAccount);

        LOGGER.info(String.format("klarna 续费扣款请求参数 {%s}", tokenOrderRequest));
        String executePost = tokenOrderRequest.executePost(order.getOrderNumber());
        KlarnaCustomerTokenOrderResponse tokenOrderResponse = KlarnaCustomerTokenOrderResponse.parse(executePost);
        LOGGER.info(String.format("klarna 续费扣款响应参数 {%s}", tokenOrderResponse));
        // 创建klarna订单成功
        if (KlarnaFraudStatusEnum.ACCEPTED.name().equals(tokenOrderResponse.getFraudStatus()) && StringUtil.isNotBlank(tokenOrderResponse.getOrderId())) {
            this.processPaymentSuccess(order.getOrderNumber(), tokenOrderResponse.getOrderId(), StringUtils.EMPTY);
            // 记录必要的支付数据
            KlarnaResponseBO klarnaResponse = new KlarnaResponseBO();
            klarnaResponse.setOrderNumber(order.getOrderNumber());
            klarnaResponse.setStatus(tokenOrderResponse.getFraudStatus());
            klarnaResponse.setAuthType(tokenOrderResponse.getFraudStatus());
            klarnaResponse.setAuthCode(StringUtils.EMPTY);
            klarnaResponse.setAuthText(tokenOrderResponse.getOrderId());
            klarnaResponse.setActionType(CreditCardPaymentActionEnum.PAYED_RESULT.getActionName());
            this.savePaymentInfo(klarnaResponse);
            return (T) Boolean.TRUE;
        }
        // 发送续费失败邮件
        paymentHelper.processSubscribeRenewOrderFailed(order);
        // 发送续费重试
        paymentHelper.doRenewOrderPayRetryLogic(order);
        // 发送续费订单扣款失败通知
        FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.RENEW_ORDER_PAYMENT_EXCEPTION_TEXT,
                order.getOrderNumber(), this.getPaymentChannel().name(), tokenOrderResponse.getError_code(), tokenOrderResponse.getError_messages()),
                FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.LCX);
        return (T) Boolean.FALSE;
    }

    @Override
    protected void processPaymentSuccess(String orderNumber, String klarnaOrderId, String kpSid) {
        super.processPaymentSuccess(orderNumber, klarnaOrderId, kpSid);
        // 获取&保存pay token
        ApplicationContextHolder.getApplicationContext().getBean(KlarnaSubscribePaymentHandler.class).handlePayToken(orderNumber, kpSid);
    }

    /**
     * 获取auth token & 创建保存pay token
     *
     * @param orderNumber
     * @param kpSid
     */
    @Async
    public void handlePayToken(String orderNumber, String kpSid) {
        Order order = orderService.getByOrderNumber(orderNumber);
        // 非首订订单不处理
        if (!PaymentSubscribeType.FIRST_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            return;
        }

        // 提前初始化pay info
        UserPayInfo klarnaUserPayInfo = klarnaPaymentHelper.getKlarnaUserPayInfo(orderNumber);
        paymentHelper.saveOrUpdatePayInfo(klarnaUserPayInfo);

        KlarnaKpDetailRequest detailRequest = new KlarnaKpDetailRequest(kpSid, this.getKlarnaPaymentConfiguration(order.getArea()));
        String detailResponseStr = detailRequest.executeGet();
        LOGGER.info(String.format("klarnaKpDetailResponse {%s}", detailResponseStr));
        KlarnaKpDetailResponse klarnaKpDetailResponse = KlarnaKpDetailResponse.parse(detailResponseStr);
        if (klarnaKpDetailResponse.isErrorResponse() || StringUtil.isBlank(klarnaKpDetailResponse.getAuthToken())) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("kp detail获取失败！订单号{%s}", orderNumber), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 发送飞书通知
            FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.KLARNA_FIRST_SUBSCRIBE_PAY_TOKEN_FAIL_TEXT,
                    order.getContactEmail(), klarnaKpDetailResponse.getError_code(), klarnaKpDetailResponse.getError_messages()),
                    FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.LCX, FeiShuAtUser.TW);
            // 发送更新邮件
            BaseCloudEmail subscribeOrderEmail = cloudEmailFactory.getSubscribeOrderEmail(order, CloudSubscribeKlarnaUpdateEmail.class);
            subscribeOrderEmail.doSend(order.getContactEmail());
            return;
        }

        KlarnaPayTokenRequest payTokenRequest = new KlarnaPayTokenRequest(klarnaKpDetailResponse.getAuthToken(), this.getKlarnaPaymentConfiguration(order.getArea()));
        this.buildRequestParam(order, payTokenRequest, null);
        String payTokenResponseStr = payTokenRequest.executePost(orderNumber);
        LOGGER.info(String.format("klarnaPayTokenResponse {%s}", payTokenResponseStr));
        KlarnaPayTokenResponse payTokenResponse = KlarnaPayTokenResponse.parse(payTokenResponseStr);
        if (payTokenResponse.isErrorResponse() || StringUtil.isBlank(payTokenResponse.getTokenId())) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("klarna pay token获取失败！订单号{%s}", orderNumber), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 发送飞书通知
            FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.KLARNA_FIRST_SUBSCRIBE_PAY_TOKEN_FAIL_TEXT,
                    order.getContactEmail(), klarnaKpDetailResponse.getError_code(), klarnaKpDetailResponse.getError_messages()),
                    FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.LCX, FeiShuAtUser.TW);
            // 发送更新邮件
            BaseCloudEmail subscribeOrderEmail = cloudEmailFactory.getSubscribeOrderEmail(order, CloudSubscribeKlarnaUpdateEmail.class);
            subscribeOrderEmail.doSend(order.getContactEmail());
            return;
        }

        String oldPayId = klarnaUserPayInfo.getPayId();
        klarnaUserPayInfo.setPayId(AESUtil.encode(AESUtil.PAY_TOKEN_KEY, payTokenResponse.getTokenId()));
        klarnaUserPayInfo.setUpdateTime(LocalDateTime.now());
        klarnaUserPayInfo.setEnabled(true);
        paymentHelper.saveOrUpdatePayInfo(klarnaUserPayInfo);
        // 处理升级订单旧pay id
        if (ServiceScenesType.UPGRADE.equals(ServiceScenesType.parse(order.getSubscribeScenesType()))) {
            this.handleUpGradePayId(order, oldPayId);
        }

        // 记录必要的支付数据
        KlarnaResponseBO klarnaResponse = new KlarnaResponseBO();
        klarnaResponse.setOrderNumber(order.getOrderNumber());
        klarnaResponse.setAuthType(CreditCardPaymentActionEnum.KLARNA_GET_PAYMENT_ID.getAuthType());
        klarnaResponse.setAuthCode(PaymentConstant.SUCCESS_TEXT);
        klarnaResponse.setAuthText(StringUtils.EMPTY);
        klarnaResponse.setActionType(CreditCardPaymentActionEnum.KLARNA_GET_PAYMENT_ID.getActionName());
        this.savePaymentInfo(klarnaResponse);
    }

    /**
     * 处理升级订单pay id
     *
     * @param order
     * @param oldPayId
     */
    protected void handleUpGradePayId(Order order, String oldPayId) {
        PaymentSubscribeBO paymentSubscribeBo = new PaymentSubscribeBO();
        paymentSubscribeBo.setPayId(AESUtil.decode(AESUtil.PAY_TOKEN_KEY, oldPayId));
        paymentSubscribeBo.setOrder(order);
        this.updateCancelSubscribeInfo(paymentSubscribeBo);
    }

    @Override
    public <T> T updateCancelSubscribeInfo(PaymentSubscribeBO paymentSubscribeParam) {
        Order order = paymentSubscribeParam.getOrder();
        KlarnaUpdateTokenStatusRequest tokenStatusRequest = new KlarnaUpdateTokenStatusRequest(paymentSubscribeParam.getPayId(), this.getKlarnaPaymentConfiguration(order.getArea()));
        tokenStatusRequest.setStatus(KlarnaConstant.CANCELLED);

        String tokenStatusResponse = tokenStatusRequest.executePatch();
        KlarnaUpdateTokenStatusResponse updateTokenStatusResponse = KlarnaUpdateTokenStatusResponse.parse(tokenStatusResponse);
        LOGGER.info(String.format("klarnaUpdateTokenStatusResponse {%s}", updateTokenStatusResponse));
        if (updateTokenStatusResponse.isErrorResponse()) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("klarna pay token cancel失败！订单号{%s}", order.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }

        return null;
    }

    /**
     * 构建续费订单请求参数
     *
     * @param paymentInfo
     * @param order
     * @param tokenOrderRequest
     * @param storeAccount
     */
    protected void buildRenewOrderRequestParam(PaymentInfo paymentInfo, Order order, KlarnaCustomerTokenOrderRequest tokenOrderRequest, StoreAccount storeAccount) {
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        OrderItem orderItem = orderHelper.getSubscribeProductItem(order);
        if (Objects.isNull(orderItem)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        tokenOrderRequest.setAutoCapture(Boolean.FALSE);
        tokenOrderRequest.setPurchaseCountry(order.getArea());
        tokenOrderRequest.setPurchaseCurrency(orderPayment.getCurrency());
        tokenOrderRequest.setBillingAddress(this.getKlarnaSubscribeBillingAddress(order, storeAccount.getInstaAccount()));
        tokenOrderRequest.setOrderLines(this.getKlarnaOrderLines(order, orderItem, orderPayment.currency()));
        tokenOrderRequest.setMerchantReference1(order.getOrderNumber());
        tokenOrderRequest.setOrderAmount(this.changeAmount(paymentInfo.amount, paymentInfo.currency));
        tokenOrderRequest.setOrderTaxAmount(this.changeAmount(orderPayment.getTax(), paymentInfo.currency));
    }

    /**
     * 构建请求参数
     *
     * @param order
     * @param payTokenRequest
     * @param instaAccount
     */
    private void buildRequestParam(Order order, KlarnaPayTokenRequest payTokenRequest, Integer instaAccount) {
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        CountryConfig countryConfig = countryConfigService.getByCountry(order.country());
        KlarnaPaymentConfig klarnaPaymentConfig = klarnaPaymentConfigService.getByCountry(order.getArea());
        ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(Product.cloudProductId, countryConfig.language());

        payTokenRequest.setPurchaseCountry(order.getArea());
        payTokenRequest.setPurchaseCurrency(orderPayment.getCurrency());
        payTokenRequest.setDescription(Objects.isNull(productInfo) ? StringUtils.EMPTY : productInfo.getName());
        payTokenRequest.setIntendedUse(KlarnaConstant.INTENDED_USE);
        payTokenRequest.setLocale(klarnaPaymentConfig.getLocale());
        payTokenRequest.setBillingAddress(this.getKlarnaSubscribeBillingAddress(order, instaAccount));
    }

    /**
     * 订单账单地址
     *
     * @param order
     * @param contactEmail
     * @return
     */
    private KlarnaBillingAddress getKlarnaBillingAddress(Order order, String contactEmail) {
        OrderBillingAddress orderBillingAddress = orderBillingAddressService.getOrderBillingAddressByOrderId(order.getId());

        KlarnaBillingAddress billingAddress = new KlarnaBillingAddress();
        billingAddress.setCountry(orderBillingAddress.getCountry());
        billingAddress.setCity(orderBillingAddress.getCity());
        billingAddress.setEmail(contactEmail);
        billingAddress.setGiven_name(orderBillingAddress.getFirstName());
        billingAddress.setFamily_name(orderBillingAddress.getLastName());
        billingAddress.setRegion(orderBillingAddress.getProvince());
        billingAddress.setPhone(orderBillingAddress.getPhone());
        billingAddress.setPostal_code(orderBillingAddress.getZipCode());
        billingAddress.setStreet_address(orderBillingAddress.getAddress());
        billingAddress.setCountry(order.getArea());
        return billingAddress;
    }

    @Override
    protected List<KlarnaOrderLine> listKlarnaOrderLines(Currency currency) {
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());

        CountryConfig countryConfig = countryConfigService.getByCountry(order.country());
        List<Integer> productIds = orderItems.stream().map(OrderItem::getProduct).collect(Collectors.toList());
        List<Integer> commodityIds = orderItems.stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        Map<Integer, Product> productMap = productBatchHelper.productMapProductIds(productIds);
        Map<Integer, ProductInfo> productInfoMap = productBatchHelper.listDefaultEnglishInfos(productIds, countryConfig.language());
        Map<Integer, CommodityDisplay> commodityDisplayMap = commodityBatchHelper.listCommodityDisplayMap(commodityIds);

        List<KlarnaOrderLine> orderLines = orderItems.stream().map(orderItem -> {
            Product product = productMap.get(orderItem.getProduct());
            ProductInfo productInfo = productInfoMap.get(orderItem.getProduct());
            CommodityDisplay commodityDisplay = commodityDisplayMap.get(orderItem.getCommodity());

            // 子项总折扣价
            Price discountPrice = orderItemAverageHelper.getOrderItemTotalDiscountAmount(orderItem.getId());

            // 子项总支付价
            float itemPayAmount = orderItem.getPrice() * orderItem.getNumber() - discountPrice.getAmount();
            if (itemPayAmount < 0) {
                String message = String.format("Klarna支付子项价格小于0。请及时查看处理。order_number:{%s}", order.getOrderNumber());
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }

            // 子项信息
            KlarnaOrderLine itemLine = new KlarnaOrderLine();
            itemLine.setQuantity(orderItem.getNumber());
            itemLine.setUnit_price(this.changeAmount(orderItem.getTotalItemPayPrice().getAmount(), currency));
            itemLine.setTotal_amount(this.changeAmount(itemPayAmount, currency));
            itemLine.setName(productInfo == null ? "" : productInfo.getName());
            itemLine.setProductUrl(product == null ? "" : gatewayConfiguration.getStoreUrl() + "/product/" + product.getUrlKey());
            itemLine.setImage_url(commodityDisplay == null ? "" : commodityDisplay.getUrl());
            itemLine.setType(KlarnaItemLineType.PHYSICAL.getName());

            // 虚拟类目判断
            ProductCategoryFinalType categoryFinalType = ProductCategoryFinalType.parse(product == null ? "" : product.getCategoryKey());
            if (ProductCategoryFinalType.isDigital(categoryFinalType)) {
                itemLine.setType(KlarnaItemLineType.DIGITAL.getName());
                itemLine.setSubscription(this.getSubscription(order, productInfo, orderItem));
            }

            return itemLine;
        }).collect(Collectors.toList());

        // 税费
        Float tax = payment.getTax();
        if (tax != 0) {
            Integer taxChange = this.changeAmount(tax, payment.currency());
            KlarnaOrderLine taxLine = new KlarnaOrderLine();
            taxLine.setQuantity(1);
            taxLine.setName(taxFeeText);
            taxLine.setTotal_amount(taxChange);
            taxLine.setUnit_price(taxChange);
            taxLine.setType(KlarnaItemLineType.SALES_TAX.getName());
            orderLines.add(taxLine);
        }

        // 运费
        Float shippingCost = payment.getShippingCost();
        if (shippingCost != 0) {
            Integer shippingCostChange = this.changeAmount(shippingCost, payment.currency());
            KlarnaOrderLine shippingCostLine = new KlarnaOrderLine();
            shippingCostLine.setQuantity(1);
            shippingCostLine.setName(shippingFeeText);
            shippingCostLine.setTotal_amount(shippingCostChange);
            shippingCostLine.setUnit_price(shippingCostChange);
            shippingCostLine.setType(KlarnaItemLineType.SHIPPING_FEE.getName());
            orderLines.add(shippingCostLine);
        }
        return orderLines;
    }

    /**
     * 获取订阅商品子项
     *
     * @param order
     * @param productInfo
     * @param orderItem
     * @return
     */
    protected KlarnaSubscription getSubscription(Order order, ProductInfo productInfo, OrderItem orderItem) {
        KlarnaSubscription subscription = new KlarnaSubscription();
        subscription.setName(productInfo == null ? "" : productInfo.getName() + String.format("{{%s}}", orderItem.getCommodity()));
        SkuSubscribeType skuSubscribeType = orderHelper.getSkuSubscribeType(order);
        subscription.setInterval(SkuSubscribeType.MONTHLY.equals(skuSubscribeType) ? KlarnaConstant.SUBSCRIBE_INTERVAL_MONTH : KlarnaConstant.SUBSCRIBE_INTERVAL_YEAR);
        subscription.setInterval_count(KlarnaConstant.SUBSCRIBE_INTERVAL_COUNT);
        return subscription;
    }

    @Override
    public <T> T updatePaymentSubscribeInfo(PaymentSubscribeBO paymentSubscribeParam) {
        // 订单信息
        Order order = paymentSubscribeParam.getOrder();
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 订单支付信息
        OrderPayment orderPayment = paymentSubscribeParam.getOrderPayment();
        if (orderPayment == null) {
            throw new InstaException(OrderErrorCode.OrderPaymentNotFoundException);
        }

        KlarnaPaymentConfig klarnaPaymentConfig = klarnaPaymentConfigService.getByCountry(order.getArea());
        if (klarnaPaymentConfig == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 配置信息
        KlarnaPaymentConfiguration klarnaPaymentConfiguration = this.getKlarnaPaymentConfiguration(order.getArea());

        // 支付链接获取失败则返回订单号
        String orderNumber = order.getOrderNumber();
        String payUrl = null;

        try {
            // 1、先创建 kp session
            BaseKlarnaPaymentRequest kpSessionRequest = this.getKpSessionSubscribeUpdateRequest(order, orderPayment,
                    paymentSubscribeParam.getStoreAccount().getInstaAccount(), klarnaPaymentConfiguration);
            LOGGER.info("klarna kp session【klarna订阅更新】请求参数:{}", kpSessionRequest);
            String kpResult = kpSessionRequest.executePost(orderNumber);
            LOGGER.info("klarna kp session【klarna订阅更新】响应参数:{}", kpResult);
            KlarnaKpSessionResponse klarnaPaymentResponse = KlarnaKpSessionResponse.parse(kpResult);
            if (klarnaPaymentResponse.isErrorResponse()) {
                LOGGER.error("kp session create fail. order_number:{}, error_message:{}", orderNumber,
                        klarnaPaymentResponse.getError_code() + " " + klarnaPaymentResponse.getError_messages());
                return (T) payUrl;
            }

            // 2、根据 kp session id 创建 hpp session
            String kpSessionId = klarnaPaymentResponse.getSession_id();
            BaseKlarnaPaymentRequest hppSessionRequest = this.getHppSessionSubscribeUpdateRequest(kpSessionId,
                    klarnaPaymentConfiguration, paymentSubscribeParam.getSubscribeActionType(), orderNumber);
            LOGGER.info("klarna hpp session【klarna订阅更新】请求参数:{}", hppSessionRequest);
            String hppResult = hppSessionRequest.executePost(orderNumber);
            LOGGER.info("klarna hpp session【klarna订阅更新】响应参数:{}", hppResult);
            KlarnaHppSessionResponse hostPaymentPageSessionResponse = KlarnaHppSessionResponse.parse(hppResult);
            if (hostPaymentPageSessionResponse.isErrorResponse()) {
                LOGGER.error("hpp session create fail. order_number:{}, kp_session_id:{}, error_message:{}", orderNumber,
                        kpSessionId, klarnaPaymentResponse.getError_code() + " " + klarnaPaymentResponse.getError_messages());
                return (T) payUrl;
            }

            // 发送交易校验监控事件
            String hppSession = hostPaymentPageSessionResponse.getSession_id();
            this.sendOrderPaymentCheckMessage(orderNumber, hppSession);

            // 发送 hpp session 禁用事件，且更新缓存
            ThreadUtil.execute(() -> this.sendHppSessionDisableMessageAndAddCache(orderNumber, hppSession));

            // 支付跳转链接
            payUrl = hostPaymentPageSessionResponse.getRedirect_url();
        } catch (Exception e) {
            LOGGER.error(String.format("klarna订阅更新创建失败。order_number:{%s}, error_message:{%s}", orderNumber, e.getMessage()), e);
        }

        // 3、得到跳转链接
        return (T) payUrl;
    }

    @Override
    public PaymentSubscribeResultBO handleSubscribeAuthResult(PaymentSubscribeBO paymentSubscribeBo) {
        String authToken = paymentSubscribeBo.getAuthToken();
        String orderNumber = paymentSubscribeBo.getOrderNumber();
        String kpSid = paymentSubscribeBo.getKpSid();
        Order order = orderService.getByOrderNumber(orderNumber);

        // 如果没返回auth token则查询，在令牌化意图下，理论上不会出现
        if (StringUtil.isBlank(authToken)) {
            KlarnaKpDetailRequest detailRequest = new KlarnaKpDetailRequest(kpSid, this.getKlarnaPaymentConfiguration(order.getArea()));
            String detailResponseStr = detailRequest.executeGet();
            LOGGER.info(String.format("klarnaKpDetailResponse {%s}", detailResponseStr));
            KlarnaKpDetailResponse klarnaKpDetailResponse = KlarnaKpDetailResponse.parse(detailResponseStr);
            if (klarnaKpDetailResponse.isErrorResponse() || StringUtil.isBlank(klarnaKpDetailResponse.getAuthToken())) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("更新订阅 kp detail获取失败！订单号{%s}", orderNumber), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return null;
            }
            authToken = klarnaKpDetailResponse.getAuthToken();
        }

        KlarnaPayTokenRequest payTokenRequest = new KlarnaPayTokenRequest(authToken, this.getKlarnaPaymentConfiguration(order.getArea()));
        this.buildRequestParam(order, payTokenRequest, paymentSubscribeBo.getStoreAccount().getInstaAccount());
        String payTokenResponseStr = payTokenRequest.executePost(orderNumber);
        LOGGER.info(String.format("klarnaPayTokenResponse {%s}", payTokenResponseStr));
        KlarnaPayTokenResponse payTokenResponse = KlarnaPayTokenResponse.parse(payTokenResponseStr);
        if (payTokenResponse.isErrorResponse() || StringUtil.isBlank(payTokenResponse.getTokenId())) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("pay token获取失败！订单号{%s}", orderNumber), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 发送飞书通知
            FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.KLARNA_UPDATE_PAY_TOKEN_FAIL_TEXT,
                    order.getContactEmail(), payTokenResponse.getError_code(), payTokenResponse.getError_messages()),
                    FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.LCX, FeiShuAtUser.TW);
            return null;
        }

        PaymentSubscribeResultBO paymentSubscribeResultBo = new PaymentSubscribeResultBO();
        paymentSubscribeResultBo.setPayId(payTokenResponse.getTokenId());
        return paymentSubscribeResultBo;
    }

    /**
     * 订阅hpp session 请求构建
     *
     * @param kpSessionId
     * @param klarnaPaymentConfiguration
     * @param actionType
     * @param orderNumber
     * @return
     */
    private BaseKlarnaPaymentRequest getHppSessionSubscribeUpdateRequest(String kpSessionId, KlarnaPaymentConfiguration klarnaPaymentConfiguration,
                                                                         SubscribeActionType actionType, String orderNumber) {
        KlarnaHppSessionRequest request = new KlarnaHppSessionRequest(klarnaPaymentConfiguration);
        request.setOptions(this.getSubscribeUpdateOption());
        request.setPayment_session_url(String.format(KlarnaConstant.PAYMENT_SESSION_URL, kpSessionId));
        request.setMerchant_urls(this.getKlarnaSubscribeUpdateMerchantUrl(klarnaPaymentConfiguration, actionType, kpSessionId, orderNumber));
        return request;
    }

    /**
     * 获取订阅更新执行操作
     *
     * @return
     */
    private KlarnaOption getSubscribeUpdateOption() {
        KlarnaOption option = new KlarnaOption();
        option.setPlace_order_mode(KlarnaPlaceOrderMode.NONE.getName());
        return option;
    }

    /**
     * 获取订阅更新merchant url
     *
     * @param klarnaPaymentConfiguration
     * @param actionType
     * @param kpSessionId
     * @param orderNumber
     * @return
     */
    private KlarnaMerchantUrl getKlarnaSubscribeUpdateMerchantUrl(KlarnaPaymentConfiguration klarnaPaymentConfiguration,
                                                                  SubscribeActionType actionType, String kpSessionId, String orderNumber) {
        String subscribeCancelUrl = paymentHelper.getSubscribeCancelUrl(StorePaymentMethodEnum.KLARNA_PAYMENT, actionType);
        KlarnaMerchantUrl merchantUrl = new KlarnaMerchantUrl();
        merchantUrl.setBack(paymentHelper.getApproveSubscribeResultUrl());
        merchantUrl.setCacnel(paymentHelper.getApproveSubscribeResultUrl());
        merchantUrl.setError(subscribeCancelUrl);
        merchantUrl.setFailure(subscribeCancelUrl);
        merchantUrl.setSuccess(this.getSubscribeApproveUrl(klarnaPaymentConfiguration, kpSessionId, orderNumber));
        return merchantUrl;
    }

    /**
     * 获取更新订阅授权回调地址
     *
     * @param klarnaPaymentConfiguration
     * @param kpSessionId
     * @param orderNumber
     * @return
     */
    private String getSubscribeApproveUrl(KlarnaPaymentConfiguration klarnaPaymentConfiguration, String kpSessionId, String orderNumber) {
        String subscribeApproveUrl = klarnaPaymentConfiguration.getSubscribeApproveCallbackUrl() + "?auth_token={{authorization_token}}&kp_sid=%s&order=%s&sign=%s";
        return String.format(subscribeApproveUrl, kpSessionId, orderNumber, EncodingUtil.encode(RSAUtil.encryptByMyPub(orderNumber)));
    }


    /**
     * 订阅kp session 请求构建
     *
     * @param order
     * @param orderPayment
     * @param instaAccount
     * @param klarnaPaymentConfiguration
     * @return
     */
    private BaseKlarnaPaymentRequest getKpSessionSubscribeUpdateRequest(Order order, OrderPayment orderPayment, Integer instaAccount,
                                                                        KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        OrderItem orderItem = orderHelper.getSubscribeProductItem(order);
        if (Objects.isNull(orderItem)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        KlarnaKpSessionRequest request = new KlarnaKpSessionRequest(klarnaPaymentConfiguration);
        request.setMerchant_reference1(order.getOrderNumber());
        request.setPurchase_country(order.getArea());
        request.setPurchase_currency(orderPayment.getCurrency());
        request.setOrder_amount(this.changeAmount(orderItem.getPrice(), orderPayment.currency()));
        request.setBilling_address(this.getKlarnaSubscribeBillingAddress(order, instaAccount));
        request.setOrder_lines(this.getKlarnaOrderLines(order, orderItem, orderPayment.currency()));
        request.setIntent(KlarnaPayIntentType.TOKENIZE.getName());
        KlarnaPaymentConfig klarnaPaymentConfig = klarnaPaymentConfigService.getByCountry(order.getArea());
        request.setLocale(klarnaPaymentConfig.getLocale());
        // 参数校验
        if (!request.checker()) {
            LOGGER.error("klarna支付，支付总金额与子项金额汇总不一致。order_number:{}", order.getOrderNumber());
            throw new InstaException(TradeErrorCode.KlarnaAmountLimitException);
        }
        return request;
    }

    /**
     * 封装klarna 订阅子项
     *
     * @param order
     * @param orderItem
     * @param currency
     * @return
     */
    private List<KlarnaOrderLine> getKlarnaOrderLines(Order order, OrderItem orderItem, Currency currency) {
        Product product = productService.getById(orderItem.getProduct());
        CountryConfig countryConfig = countryConfigService.getByCountry(order.country());
        ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(orderItem.getProduct(), countryConfig.language());
        CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(orderItem.getCommodity());

        // 子项信息
        Integer amount = this.changeAmount(orderItem.getPrice(), currency);
        KlarnaOrderLine itemLine = new KlarnaOrderLine();
        itemLine.setQuantity(orderItem.getNumber());
        itemLine.setUnit_price(amount);
        itemLine.setTotal_amount(amount);
        itemLine.setName(productInfo == null ? "" : productInfo.getName());
        itemLine.setProductUrl(product == null ? "" : gatewayConfiguration.getStoreUrl() + "/product/" + product.getUrlKey());
        itemLine.setImage_url(commodityDisplay == null ? "" : commodityDisplay.getUrl());
        itemLine.setType(KlarnaItemLineType.DIGITAL.getName());
        itemLine.setSubscription(this.getSubscription(order, productInfo, orderItem));
        return Collections.singletonList(itemLine);
    }

    /**
     * 获取最新账单地址
     *
     * @param order
     * @param instaAccount
     * @return
     */
    private KlarnaBillingAddress getKlarnaSubscribeBillingAddress(Order order, Integer instaAccount) {
        // 游客订阅
        if (Objects.isNull(instaAccount) || order.isGuestOrder()) {
            return this.getKlarnaBillingAddress(order, order.getContactEmail());
        }

        SubscribeBillingAddress billingAddress = subscribeBillingAddressService.getByInstaAccount(instaAccount);
        KlarnaBillingAddress klarnaBillingAddress = new KlarnaBillingAddress();
        if (Objects.nonNull(billingAddress)) {
            klarnaBillingAddress.setCountry(order.getArea());
            klarnaBillingAddress.setCity(billingAddress.getCity());
            klarnaBillingAddress.setEmail(order.getContactEmail());
            klarnaBillingAddress.setGiven_name(billingAddress.getFirstName());
            klarnaBillingAddress.setFamily_name(billingAddress.getLastName());
            klarnaBillingAddress.setRegion(billingAddress.getProvince());
            klarnaBillingAddress.setPhone(billingAddress.getPhone());
            klarnaBillingAddress.setPostal_code(billingAddress.getZipCode());
            klarnaBillingAddress.setStreet_address(billingAddress.getAddress());
            klarnaBillingAddress.setCountry(order.getArea());
            return klarnaBillingAddress;
        }

        return this.getKlarnaBillingAddress(order, order.getContactEmail());
    }

    @Override
    protected String getSuccess(String kpSessionId) {
        return String.format(this.getNotifyUrl(), this.getOrderNumberSignValue(false), kpSessionId);
    }

    @Override
    protected String getNotifyUrl() {
        return super.getNotifyUrl() + "&kp_sid=%s";
    }

    @Override
    protected String getIntent() {
        return KlarnaPayIntentType.BUY_AND_TOKENIZE.getName();
    }
}
