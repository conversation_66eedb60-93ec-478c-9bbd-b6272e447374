package com.insta360.store.business.cloud.service.impl.handler.insurance;

import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.cloud.bo.CloudBenefitBindResultBO;
import com.insta360.store.business.cloud.bo.CloudStorageBenefitBO;
import com.insta360.store.business.cloud.enums.BenefitBusinessTypeType;
import com.insta360.store.business.cloud.exception.CloudStorageBenefitErrorCode;
import com.insta360.store.business.cloud.service.CloudStorageCardBenefitDetailService;
import com.insta360.store.business.cloud.service.CloudStorageInsuranceBenefitBindService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitDetailService;
import com.insta360.store.business.cloud.service.StoreBenefitBindService;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitBindContext;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.bo.InsuranceBO;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.insurance.model.InsuranceServiceCommodityBind;
import com.insta360.store.business.insurance.service.ServiceCommodityBindService;
import com.insta360.store.business.insurance.service.impl.fatory.InsuranceFactory;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceCheckHelper;
import com.insta360.store.business.insurance.service.impl.helper.http.DeviceInfoHelper;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceInfo;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2024-05-17 15:23
 */
public abstract class BaseStoreBenefitBindHandler implements StoreBenefitBindService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseStoreBenefitBindHandler.class);

    @Autowired
    InsuranceFactory insuranceFactory;

    @Autowired
    DeviceInfoHelper deviceInfoHelper;

    @Autowired
    ServiceCommodityBindService serviceCommodityBindService;

    @Autowired
    UserAccountHelper userAccountHelper;

    @Autowired
    CloudStorageInsuranceBenefitBindService cloudStorageInsuranceBenefitBindService;

    @Autowired
    InsuranceCheckHelper insuranceCheckHelper;

    @Autowired
    CloudStorageStoreBenefitDetailService cloudStorageStoreBenefitDetailService;

    @Autowired
    CloudStorageCardBenefitDetailService cloudStorageCardBenefitDetailService;

    @Override
    public void checkActivation(CloudStorageBenefitBO cloudStorageBenefitParam, StoreBenefitBindContext storeBenefitBindContext) {
        // 校验权益的存在性
        if (Objects.isNull(cloudStorageBenefitParam)) {
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotExistException);
        }

        if (cloudStorageBenefitParam.getExpired() || cloudStorageBenefitParam.getUsed()) {
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotExistException);
        }

        LOGGER.info(String.format("[云服务权益]准备激活增值服务,参数:%s", storeBenefitBindContext.toString()));
        StoreAccount storeAccount = null;
        try {
            storeAccount = userAccountHelper.getStoreAccountByUserId(storeBenefitBindContext.getUserId());
        } catch (Exception e) {
            LOGGER.error(String.format("[云服务权益]准备激活增值服务,获取用户信息失败. email: {%s}", storeBenefitBindContext.getEmail()), e);
        }
        // 验证序列号是否存在
        DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(storeBenefitBindContext.getDeviceSerial());
        if (deviceInfo == null) {
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }

        // 当前机型支持的增值服务类型
        List<InsuranceServiceCommodityBind> serviceCommodityBinds = serviceCommodityBindService.listByDeviceTypeCloud(deviceInfo.getDeviceType(), true);
        if (CollectionUtils.isEmpty(serviceCommodityBinds)) {
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotSupportException);
        }

        buildContext(storeBenefitBindContext, storeAccount, deviceInfo, serviceCommodityBinds);
    }

    @Override
    public ServiceType getServiceType(StoreBenefitBindContext storeBenefitBindContext, String bindType) {
        List<ServiceType> serviceTypeList = ServiceType.getServiceTypeList(bindType);
        if (CollectionUtils.isEmpty(serviceTypeList)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }
        List<Integer> serviceIds = serviceTypeList.stream().map(ServiceType::getServiceId).collect(Collectors.toList());

        List<InsuranceServiceCommodityBind> commodityBinds = storeBenefitBindContext.getServiceCommodityBinds().stream()
                .filter(commodityBind -> serviceIds.contains(commodityBind.getServiceId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(commodityBinds)) {
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotSupportException);
        }

        InsuranceServiceCommodityBind insuranceServiceCommodityBind = commodityBinds.get(0);

        Integer serviceId = insuranceServiceCommodityBind.getServiceId();
        ServiceType serviceType = ServiceType.parse(serviceId);
        if (Objects.isNull(serviceType)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 判断是否为主机
        LOGGER.info(String.format("[云服务权益]序列号校验是否为主机,参数:%s", storeBenefitBindContext));

        // 是否为相机 -判断是什么相机类型
        if (!insuranceCheckHelper.isCamera(storeBenefitBindContext.getDeviceSerial(), insuranceServiceCommodityBind)) {
            LOGGER.error(String.format("[云服务权益]校验相机类型失败,参数:%s", storeBenefitBindContext));
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }
        return serviceType;
    }

    /**
     * 保存绑定记录
     *
     * @param storeBenefitBindContext
     * @param serviceType
     * @param insuranceParam
     * @param benefitDetailId
     */
    public void saveBindRecord(StoreBenefitBindContext storeBenefitBindContext, ServiceType serviceType, InsuranceBO insuranceParam, Long benefitDetailId) {
        BenefitBusinessTypeType businessType = storeBenefitBindContext.getBusinessType();
        switch (businessType) {
            case cloud_card:
                cloudStorageCardBenefitDetailService.updateCloudRecord(serviceType, getBindType(), insuranceParam, benefitDetailId);
                break;
            case cloud_subscribe:
                cloudStorageStoreBenefitDetailService.updateCloudRecord(serviceType, getBindType(), insuranceParam, benefitDetailId);
                break;
            default:
                break;
        }
    }

    /**
     * 构建上下文
     *
     * @param storeBenefitBindContext
     * @param storeAccount
     * @param deviceInfo
     * @param serviceCommodityBinds
     */
    public void buildContext(StoreBenefitBindContext storeBenefitBindContext, StoreAccount storeAccount,
                             DeviceInfo deviceInfo, List<InsuranceServiceCommodityBind> serviceCommodityBinds) {
        storeBenefitBindContext.setCloudBind(true);
        storeBenefitBindContext.setDeviceType(deviceInfo.getDeviceType());
        storeBenefitBindContext.setServiceCommodityBinds(serviceCommodityBinds);
        if (Objects.nonNull(storeAccount)) {
            storeBenefitBindContext.setEmail(storeAccount.getUsername());
            storeBenefitBindContext.setArea(storeAccount.getCountry());
        }
    }

    /**
     * 构建激活返回结果
     *
     * @param storeBenefitBindContext
     * @param serviceType
     * @return
     */
    public CloudBenefitBindResultBO buildResult(StoreBenefitBindContext storeBenefitBindContext, ServiceType serviceType) {
        CloudBenefitBindResultBO cloudBenefitBindResultParam = new CloudBenefitBindResultBO();
        cloudBenefitBindResultParam.setInsuranceType(serviceType.name());
        cloudBenefitBindResultParam.setDeviceType(storeBenefitBindContext.getDeviceType());
        cloudBenefitBindResultParam.setDeviceSerial(storeBenefitBindContext.getDeviceSerial());
        return cloudBenefitBindResultParam;
    }
}
