package com.insta360.store.business.commodity.service.impl.helper;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.enums.CommodityStockLimitType;
import com.insta360.store.business.commodity.enums.CommodityTagType;
import com.insta360.store.business.commodity.enums.DifferenceContentType;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.configuration.search.bo.SearchCountiesBO;
import com.insta360.store.business.configuration.search.constant.SearchConstant;
import com.insta360.store.business.insurance.model.ClimbServiceCommodity;
import com.insta360.store.business.insurance.service.ClimbServiceCommodityService;
import com.insta360.store.business.insurance.service.ProductBindClimbServiceService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/2/27
 * @Description:
 */
@Component
public class CommodityBatchHelper {

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityTradeRuleService commodityTradeRuleService;

    @Autowired
    CommodityFunctionDescriptionService commodityFunctionDescriptionService;

    @Autowired
    CommodityTagBindService commodityTagBindService;

    @Autowired
    CommodityTagGroupService commodityTagGroupService;

    @Autowired
    CommodityTagInfoService commodityTagInfoService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    CommodityDeliveryTimeConfigService commodityDeliveryTimeConfigService;

    @Autowired
    CommodityDifferenceService commodityDifferenceService;

    @Autowired
    CommodityDifferencePointTextService commodityDifferencePointTextService;

    @Autowired
    CommodityMetaService commodityMetaService;

    @Autowired
    CommodityDisplayCountryService commodityDisplayCountryService;

    @Autowired
    ProductCommodityStockService productCommodityStockService;

    @Autowired
    StorageDeliveryAreaService storageDeliveryAreaService;

    @Autowired
    StorageInfoService storageInfoService;

    @Autowired
    ProductBindClimbServiceService productBindClimbServiceService;

    @Autowired
    ClimbServiceCommodityService climbServiceCommodityService;

    /**
     * 批量查询启用套餐
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, Commodity> commodityMapCommodityIds(List<Integer> commodityIds) {
        return commodityService.listByCommodities(commodityIds).stream().collect(Collectors.toMap(Commodity::getId, c -> c));
    }

    /**
     * 批量查询套餐（未限制启用状态）
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, Commodity> commodityAllMapCommodityIds(List<Integer> commodityIds) {
        return commodityService.listCommodities(commodityIds).stream().collect(Collectors.toMap(Commodity::getId, c -> c));
    }

    /**
     * 批量查询销售状态
     *
     * @param commodityIds
     * @param country
     * @return
     */
    public Map<Integer, CommoditySaleState> saleStateMapCommodityIds(List<Integer> commodityIds, InstaCountry country) {
        return commoditySaleStateService.listSaleStates(commodityIds, country).stream().collect(Collectors.toMap(CommoditySaleState::getCommodityId, s -> s));
    }

    /**
     * 批量查询套餐启用价格
     *
     * @param commodityIds
     * @param country
     * @return
     */
    public Map<Integer, CommodityPrice> priceMapCommodityIds(List<Integer> commodityIds, InstaCountry country) {
        return commodityPriceService.getPriceByCommodityIds(commodityIds, country).stream().collect(Collectors.toMap(CommodityPrice::getCommodityId, p -> p));
    }

    /**
     * 批量查询套餐单语言信息
     *
     * @param commodityIds
     * @param language
     * @return
     */
    public Map<Integer, CommodityInfo> commodityInfoMapCommodityIds(List<Integer> commodityIds, InstaLanguage language) {
        return commodityInfoService.getInfos(commodityIds, language).stream().collect(Collectors.toMap(CommodityInfo::getCommodity, c -> c));
    }

    /**
     * 批量查询套餐交易规则
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, CommodityTradeRule> tradeRuleMapCommodityIds(List<Integer> commodityIds) {
        return commodityTradeRuleService.listByCommodityIds(commodityIds).stream().collect(Collectors.toMap(CommodityTradeRule::getCommodityId, t -> t));
    }

    /**
     * 批量查询套餐库存
     *
     * @param commodityIds 套餐id
     * @param instaCountry 国家
     * @return 套餐库存map
     */
    public Map<Integer, Integer> stockCountMapCommodityIds(List<Integer> commodityIds, InstaCountry instaCountry) {
        return productCommodityStockService.listByCommodityIdsAndArea(commodityIds, instaCountry.name())
                .stream()
                .collect(Collectors.toMap(
                        ProductCommodityStock::getCommodityId,
                        stock -> Math.max(0, stock.getStockCount()),
                        // 自定义合并逻辑：相加后不超过 999999999
                        (sum1, sum2) -> (int) Math.min((long) sum1 + (long) sum2, (long) CommodityStockLimitType.unlimited_stock.getQuantity())
                ));
    }

    /**
     * 批量查询套餐增值服务绑定信息
     *
     * @param commodityIds 套餐id
     * @return
     */
    public Map<Integer, List<ClimbServiceCommodity>> climbServiceMapCommodityIds(List<Integer> commodityIds) {
        return productBindClimbServiceService.getServiceCommodityMap(commodityIds);
    }

    /**
     * 批量查询套餐的增值服务类型
     *
     * @param commodityIds 套餐id
     * @return
     */
    public Map<Integer, ClimbServiceCommodity> serviceTypeMapCommodityIds(List<Integer> commodityIds) {
        return climbServiceCommodityService.listByCommodityIds(commodityIds)
                .stream()
                .collect(Collectors.toMap(
                        ClimbServiceCommodity::getServiceCommodityId,
                        c -> c,
                        (c1, c2) -> c2
                ));
    }

    /**
     * 按国家和套餐id对库存进行合计
     *
     * @param commodityIds
     * @return
     */
    public Map<InstaCountry, Map<Integer, Integer>> globalStockMapCommodityIds(List<Integer> commodityIds) {
        Integer generalStorageId = storageInfoService.getGeneralStorageId();
        List<ProductCommodityStock> commodityStockList = productCommodityStockService.listByCommodityIds(commodityIds);
        if (CollectionUtils.isEmpty(commodityStockList)) {
            return new HashMap<>();
        }

        // 总仓库存  key -> stockId value -> stockCount
        List<ProductCommodityStock> generalStocks = commodityStockList.stream()
                .filter(stock -> generalStorageId.equals(stock.getStorageId()))
                .collect(Collectors.toList());

        // key -> commodityStockId  value -> ProductCommodityStock
        Map<Integer, ProductCommodityStock> commodityStockMap = commodityStockList.stream()
                .collect(Collectors.toMap(ProductCommodityStock::getId, p -> p));

        List<Integer> commodityStockIds = commodityStockList.stream().map(ProductCommodityStock::getId).collect(Collectors.toList());
        List<StorageDeliveryArea> deliveryAreas = storageDeliveryAreaService.listByStockIds(commodityStockIds);

        // 初始化结果映射 key -> instaCountry  key -> commodityId  value -> 库存总数
        Map<InstaCountry, Map<Integer, Integer>> countryCommodityStockMap = new HashMap<>();

        // 设置总仓库存为每个国家和套餐的初始库存
        for (SearchCountiesBO searchCountiesBo : SearchConstant.COUNTIES) {
            InstaCountry country = searchCountiesBo.getCountry();
            // us地区剔除总仓库存
            if (InstaCountry.US.equals(country)) {
                continue;
            }

            for (ProductCommodityStock generalStock : generalStocks) {
                Integer commodityId = generalStock.getCommodityId();
                Integer generalStockCount = generalStock.getStockCount();
                countryCommodityStockMap.computeIfAbsent(country, k -> new HashMap<>())
                        .merge(commodityId, Math.max(generalStockCount, 0), Integer::sum);
            }
        }

        // 遍历deliveryAreas，累加分仓库存
        for (StorageDeliveryArea sda : deliveryAreas) {
            InstaCountry country = InstaCountry.parse(sda.getDeliveryArea());
            Integer commodityStockId = sda.getCommodityStockId();
            // commodityStock不会为空
            ProductCommodityStock commodityStock = commodityStockMap.get(commodityStockId);
            Integer commodityId = commodityStock.getCommodityId();
            Integer stockCount = commodityStock.getStockCount();
            // 更新或创建国家和商品ID的库存映射
            countryCommodityStockMap.computeIfAbsent(country, k -> new HashMap<>())
                    .merge(commodityId, Math.max(stockCount, 0), Integer::sum);
        }

        return countryCommodityStockMap;
    }

    /**
     * 批量查询套餐描述单语言
     *
     * @param commodityIds
     * @param language
     * @return
     */
    public Map<Integer, CommodityFunctionDescription> functionDescriptionMapCommodityIds(List<Integer> commodityIds, InstaLanguage language) {
        return commodityFunctionDescriptionService.listFunctionDescription(commodityIds, language).stream().collect(Collectors.toMap(CommodityFunctionDescription::getCommodityId, f -> f));
    }

    /**
     * 批量查询套餐tag绑定
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, List<CommodityTagBind>> commodityTagBindMapCommodityIds(List<Integer> commodityIds) {
        return commodityTagBindService.listByCommodityIds(commodityIds).stream().collect(Collectors.groupingBy(CommodityTagBind::getCommodityId));
    }

    /**
     * 批量查询套餐交易规则
     *
     * @param tagBindIds
     * @return
     */
    public Map<Integer, CommodityTagGroup> commodityTagGroupMapIds(List<Integer> tagBindIds) {
        return commodityTagGroupService.listByIds(tagBindIds).stream().collect(Collectors.toMap(CommodityTagGroup::getId, g -> g));
    }

    /**
     * 批量查询套餐tag单语言
     *
     * @param tagBindIds
     * @param language
     * @return
     */
    public Map<Integer, CommodityTagInfo> commodityTagInfoMapIds(List<Integer> tagBindIds, InstaLanguage language) {
        return commodityTagInfoService.listByGroupIdsAndLanguage(tagBindIds, language).stream().collect(Collectors.toMap(CommodityTagInfo::getTagGroupId, g -> g));
    }

    /**
     * 展示图批量查询
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, List<CommodityDisplay>> commodityDisplayMap(List<Integer> commodityIds) {
        return commodityDisplayService.listByCommodityIds(commodityIds).stream().collect(Collectors.groupingBy(CommodityDisplay::getCommodity, LinkedHashMap::new, Collectors.toList()));
    }

    /**
     * 套餐发货时间
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, CommodityDeliveryTimeConfig> commodityDeliveryTimeMap(List<Integer> commodityIds) {
        return commodityDeliveryTimeConfigService.listByCommodityIds(commodityIds).stream().collect(Collectors.toMap(CommodityDeliveryTimeConfig::getCommodity, d -> d));
    }

    /**
     * 套餐信息默认英文list
     *
     * @param commodityIdList
     * @param language
     * @return
     */
    public Map<Integer, CommodityInfo> listDefaultEnglishInfos(List<Integer> commodityIdList, InstaLanguage language) {
        return commodityInfoService.listDefaultEnglishInfos(commodityIdList, language).stream().collect(Collectors.toMap(CommodityInfo::getCommodity, c -> c));
    }

    /**
     * 获取套餐价格信息
     *
     * @param commodityIdList
     * @param country
     * @return
     */
    public Map<Integer, CommodityPrice> getPriceByCommodityIds(List<Integer> commodityIdList, InstaCountry country) {
        return commodityPriceService.getPriceByCommodityIds(commodityIdList, country).stream().collect(Collectors.toMap(CommodityPrice::getCommodityId, c -> c));
    }

    /**
     * 通过套餐id批量查询套餐difference信息
     *
     * @param commodityIdList
     * @param language
     * @return
     */
    public Map<Integer, List<CommodityDifference>> listDifferences(List<Integer> commodityIdList, InstaLanguage language) {
        List<CommodityDifference> differenceList = commodityDifferenceService.listDifferences(commodityIdList);
        differenceList.forEach(d -> {
            if (DifferenceContentType.text.equals(d.contentType())) {
                String content = d.getContent();
                JSONObject contentJson = JSONObject.parseObject(content);
                d.setContent(contentJson.getString(language.name()));
            }
        });

        return differenceList.stream().collect(Collectors.groupingBy(CommodityDifference::getCommodityId));
    }

    /**
     * 通过point id list和语言查询DifferencePointText
     *
     * @param pointIdList
     * @param language
     * @return
     */
    public Map<Integer, CommodityDifferencePointText> listText(List<Integer> pointIdList, InstaLanguage language) {
        return commodityDifferencePointTextService.listText(pointIdList, language).stream().collect(Collectors.toMap(CommodityDifferencePointText::getDifferencePointId, o -> o));
    }

    /**
     * 展示图批量查询（首图）
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, CommodityDisplay> listCommodityDisplayMap(List<Integer> commodityIds) {
        return commodityDisplayService.listByCommodityIds(commodityIds).stream().collect(Collectors.groupingBy(CommodityDisplay::getCommodity, LinkedHashMap::new, Collectors.collectingAndThen(Collectors.toList(), displays -> displays.get(0))));
    }

    /**
     * 展示图批量查询
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, List<CommodityDisplay>> commodityDisplayMapIds(List<Integer> commodityIds) {
        return commodityDisplayService.listByCommodityIds(commodityIds).stream().collect(Collectors.groupingBy(CommodityDisplay::getCommodity));
    }

    /**
     * 通过套餐ID集合获取对应报关配置信息Map
     *
     * @param commodityIds
     * @return
     */
    public Map<Integer, CommodityMeta> commodityMetaMapByCommodityIds(List<Integer> commodityIds) {
        return Optional.ofNullable(commodityMetaService.listCommodityMetaByCommodityIds(commodityIds))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .collect(Collectors.toMap(CommodityMeta::getCommodityId, c -> c)))
                .orElse(new HashMap<>());
    }

    /**
     * 查询新品标签map
     *
     * @return
     */
    public Map<Integer, CommodityTagGroup> newTagGroupMapTagIds() {
        return commodityTagGroupService.listNewTag(CommodityTagType.NEW.getType())
                .stream()
                .collect(Collectors.toMap(CommodityTagGroup::getId, o -> o));
    }

    /**
     * 获取配件产品第一个启用套餐列表
     *
     * @param productIds
     * @param country
     */
    public List<Commodity> listByGroupProductIdsEnable(List<Integer> productIds, InstaCountry country) {
        List<Commodity> commodities = commodityService.listByProductIdsEnable(productIds);
        if (CollectionUtils.isEmpty(commodities)) {
            return new ArrayList<>(0);
        }

        List<Integer> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        Map<Integer, CommoditySaleState> saleStateMap = this.saleStateMapCommodityIds(commodityIds, country);
        Map<Integer, List<Commodity>> commodityGroupMap = commodities.stream().collect(Collectors.groupingBy(Commodity::getProduct));
        return commodityGroupMap.values().stream().map(commodityList -> {
            // 配件大部分只有一个sku
            if (commodityList.size() == 1) {
                return commodityList.get(0);
            }

            if (MapUtils.isEmpty(saleStateMap)) {
                return commodityList.get(0);
            }

            // 优先找到选购状态sku
            commodityList.sort(Comparator.comparingInt(Commodity::getOrderIndex));
            for (Commodity commodity : commodityList) {
                CommoditySaleState saleState = saleStateMap.get(commodity.getId());
                if (Objects.nonNull(saleState) && SaleState.normal.getCode() == saleState.getSaleState()) {
                    return commodity;
                }
            }
            return commodityList.get(0);
        }).collect(Collectors.toList());
    }

    /**
     * 批量查询套餐展示图的适用国家
     * key：displayId value:适用国家
     *
     * @param displayIds
     * @return
     */
    public Map<Integer, List<String>> listDisplayCountries(List<Integer> displayIds) {
        List<CommodityDisplayCountry> commodityDisplayCountries = commodityDisplayCountryService.listByDisplayIds(displayIds);
        return commodityDisplayCountries.stream()
                .collect(Collectors.groupingBy(CommodityDisplayCountry::getDisplayId, Collectors.mapping(CommodityDisplayCountry::getCountry, Collectors.toList())));
    }
}
