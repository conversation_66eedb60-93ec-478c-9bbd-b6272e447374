package com.insta360.store.business.order.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteResultBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsGiftBO;
import com.insta360.store.business.integration.wto.oms.exception.OmsErrorCode;
import com.insta360.store.business.integration.wto.oms.lib.enums.OmsGiftHandlerType;
import com.insta360.store.business.integration.wto.oms.lib.response.BaseOmsResponse;
import com.insta360.store.business.integration.wto.oms.service.handler.admin.StoreOrderGiftHandler;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4
 */
@Component
public class OrderGiftHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderGiftHelper.class);

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    StoreOrderGiftHandler storeOrderGiftHandler;

    /**
     * 更新订单收货地址
     *
     * @param order       订单
     * @param orderId     订单ID
     * @param commodityId 套餐ID
     */
    public void addGift(Order order, Integer orderId, Integer commodityId) {
        Currency currency = orderPaymentService.getByOrder(order.getId()).currency();
        OrderItem orderItem = orderItemService.getGift(orderId, commodityId);
        LOGGER.info("[addGift] 添加赠品 订单号 {} 套餐ID {}", order.getOrderNumber(), commodityId);
        if (orderItem == null) {
            Commodity commodity = commodityService.getById(commodityId);
            CommodityPrice price = commodityPriceService.getPrice(commodityId, order.country());

            orderItem = new OrderItem();
            orderItem.setOrder(order.getId());
            orderItem.setProduct(commodity.getProduct());
            orderItem.setCommodity(commodityId);
            orderItem.setOriginAmount(price.getOriginAmount());
            orderItem.setPrice(0f);
            orderItem.setCurrency(currency.name());
            orderItem.setNumber(1);
            orderItem.setIsGift(true);
            orderItemService.save(orderItem);
            LOGGER.info("[addGift] 新增赠品 订单号 {} 套餐ID {} orderItemId {}", order.getOrderNumber(), commodityId, orderItem.getId());
        } else {
            Integer number = orderItem.getNumber();
            orderItem.setNumber(number + 1);
            orderItemService.updateById(orderItem);
            LOGGER.info("[addGift] 赠品数量 +1 订单号 {} 套餐ID {} orderItemId {}", order.getOrderNumber(), commodityId, orderItem.getId());
        }

        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        omsExecuteBo.setOrderId(order.getId());
        OmsGiftBO omsGiftBO = new OmsGiftBO(OmsGiftHandlerType.ADD_GIFT, order.getOrderNumber(), String.valueOf(orderItem.getId()), commodityId, orderItem.getNumber());

        // 执行变更OMS赠品项目的逻辑
        doChangeOmsGiftItem(order, omsExecuteBo, omsGiftBO);
    }

    /**
     * 删除赠品项
     *
     * @param orderItem 订单项目对象，代表需要删除的赠品项目
     */
    public void deleteGiftItem(OrderItem orderItem) {
        // 获取与订单项目关联的订单信息
        Order order = orderService.getById(orderItem.getOrder());

        // 创建OMS操作对象，并设置订单ID
        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        omsExecuteBo.setOrderId(order.getId());

        // 构造OMS赠品对象，用于执行删除赠品操作
        OmsGiftBO omsGiftBO = new OmsGiftBO(OmsGiftHandlerType.DELETE_GIFT, order.getOrderNumber(), String.valueOf(orderItem.getId()), orderItem.getCommodity(), orderItem.getNumber());
        // 执行变更OMS赠品项目的逻辑
        doChangeOmsGiftItem(order, omsExecuteBo, omsGiftBO);

        // 调用服务删除赠品项目
        orderItemService.deleteGiftItem(orderItem);
        LOGGER.info("[deleteGiftItem] 删除赠品 订单号 {} 赠品ID {}", order.getOrderNumber(), orderItem.getId());
    }

    /**
     * 更新订单中的赠品信息
     *
     * @param order        订单对象，包含订单的详细信息
     * @param omsExecuteBo OMS执行业务对象，用于处理订单操作
     * @param omsGiftBO    OMS赠品业务对象，包含赠品的详细信息
     */
    private void doChangeOmsGiftItem(Order order, OmsExecuteBO omsExecuteBo, OmsGiftBO omsGiftBO) {
        LOGGER.info("[doChangeOmsGiftItem] 更新赠品 订单号 {}", order.getOrderNumber());
        OrderState orderState = order.orderState();
        // 只有已经配货的才需要同步到OMS
        if (!OrderState.prepared.equals(orderState)) {
            LOGGER.error("[oms更新赠品]订单状态不符合 订单号 {} 状态 {}", order.getOrderNumber(), orderState);
            return;
        }

        // 设置OMS执行对象中的赠品信息
        omsExecuteBo.setOmsGiftBo(omsGiftBO);

        // 执行OMS交易操作，处理赠品信息更新
        OmsExecuteResultBO omsExecuteResultBO = storeOrderGiftHandler.executeOmsTransaction(omsExecuteBo);

        // 获取OMS操作的响应结果
        BaseOmsResponse baseOmsResponse = omsExecuteResultBO.getBaseOmsResponse();

        // 检查OMS操作是否失败
        if (baseOmsResponse.isFail()) {
            // 构造错误信息，包括订单号和具体的错误消息
            String message = String.format("[oms更新赠品]更新订单赠品信息失败 订单号：%s，错误信息：%s 响应体:%s", order.getOrderNumber(), baseOmsResponse.getMessage(), JSON.toJSONString(baseOmsResponse));
            LOGGER.error(message);
            // 发送错误信息到飞书群组，通知相关人员
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.CUSTOMER_NOTICE);

            // 抛出异常，表示赠品信息更新失败
            throw new InstaException(OmsErrorCode.GiftUpdateFailException);
        }
    }
}
