package com.insta360.store.business.commodity.bo;

import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.insurance.model.ClimbServiceCommodity;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;

import java.util.List;
import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2023/4/13
 * @Description:
 */
public class CommodityPackBO {

    /**
     * 展示图
     */
    private Map<Integer, List<CommodityDisplay>> displayMap;

    /**
     * 套餐详情
     */
    private Map<Integer, CommodityInfo> commodityInfoMap;

    /**
     * 套餐价格
     */
    private Map<Integer, CommodityPrice> commodityPriceMap;

    /**
     * 套餐销售规则
     */
    private Map<Integer, CommodityTradeRule> commodityTradeRuleMap;

    /**
     * 套餐库存
     */
    private Map<Integer, Integer> commodityStockMap;

    /**
     * 套餐销售状态
     */
    private Map<Integer, CommoditySaleState> saleStateMap;

    /**
     * 套餐发货配置
     */
    private Map<Integer, CommodityDeliveryTimeConfig> commodityDeliveryTimeConfigMap;

    /**
     * 产品多语言
     */
    private Map<Integer, ProductInfo> productInfoMap;

    /**
     * 产品
     */
    private Map<Integer, Product> productMap;

    /**
     * 套餐推荐标签
     */
    private Map<Integer, List<CommodityTagBind>> tagBindMap;

    /**
     * 套餐报关信息
     */
    private Map<Integer, CommodityMeta> commodityMetaMap;

    /**
     * 套餐绑定增值服务
     */
    Map<Integer, List<ClimbServiceCommodity>> commodityBindClimeServiceMap;

    /**
     * 增值服务类型
     */
    Map<Integer, ClimbServiceCommodity> commodityServiceTypeMap;

    public Map<Integer, List<CommodityDisplay>> getDisplayMap() {
        return displayMap;
    }

    public void setDisplayMap(Map<Integer, List<CommodityDisplay>> displayMap) {
        this.displayMap = displayMap;
    }

    public Map<Integer, CommodityInfo> getCommodityInfoMap() {
        return commodityInfoMap;
    }

    public void setCommodityInfoMap(Map<Integer, CommodityInfo> commodityInfoMap) {
        this.commodityInfoMap = commodityInfoMap;
    }

    public Map<Integer, CommodityPrice> getCommodityPriceMap() {
        return commodityPriceMap;
    }

    public void setCommodityPriceMap(Map<Integer, CommodityPrice> commodityPriceMap) {
        this.commodityPriceMap = commodityPriceMap;
    }

    public Map<Integer, CommodityTradeRule> getCommodityTradeRuleMap() {
        return commodityTradeRuleMap;
    }

    public void setCommodityTradeRuleMap(Map<Integer, CommodityTradeRule> commodityTradeRuleMap) {
        this.commodityTradeRuleMap = commodityTradeRuleMap;
    }

    public Map<Integer, Integer> getCommodityStockMap() {
        return commodityStockMap;
    }

    public void setCommodityStockMap(Map<Integer, Integer> commodityStockMap) {
        this.commodityStockMap = commodityStockMap;
    }

    public Map<Integer, CommoditySaleState> getSaleStateMap() {
        return saleStateMap;
    }

    public void setSaleStateMap(Map<Integer, CommoditySaleState> saleStateMap) {
        this.saleStateMap = saleStateMap;
    }

    public Map<Integer, CommodityDeliveryTimeConfig> getCommodityDeliveryTimeConfigMap() {
        return commodityDeliveryTimeConfigMap;
    }

    public void setCommodityDeliveryTimeConfigMap(Map<Integer, CommodityDeliveryTimeConfig> commodityDeliveryTimeConfigMap) {
        this.commodityDeliveryTimeConfigMap = commodityDeliveryTimeConfigMap;
    }

    public Map<Integer, ProductInfo> getProductInfoMap() {
        return productInfoMap;
    }

    public void setProductInfoMap(Map<Integer, ProductInfo> productInfoMap) {
        this.productInfoMap = productInfoMap;
    }

    public Map<Integer, Product> getProductMap() {
        return productMap;
    }

    public void setProductMap(Map<Integer, Product> productMap) {
        this.productMap = productMap;
    }

    public Map<Integer, List<CommodityTagBind>> getTagBindMap() {
        return tagBindMap;
    }

    public void setTagBindMap(Map<Integer, List<CommodityTagBind>> tagBindMap) {
        this.tagBindMap = tagBindMap;
    }

    public Map<Integer, CommodityMeta> getCommodityMetaMap() {
        return commodityMetaMap;
    }

    public void setCommodityMetaMap(Map<Integer, CommodityMeta> commodityMetaMap) {
        this.commodityMetaMap = commodityMetaMap;
    }

    public Map<Integer, List<ClimbServiceCommodity>> getCommodityBindClimeServiceMap() {
        return commodityBindClimeServiceMap;
    }

    public void setCommodityBindClimeServiceMap(Map<Integer, List<ClimbServiceCommodity>> commodityBindClimeServiceMap) {
        this.commodityBindClimeServiceMap = commodityBindClimeServiceMap;
    }

    public Map<Integer, ClimbServiceCommodity> getCommodityServiceTypeMap() {
        return commodityServiceTypeMap;
    }

    public void setCommodityServiceTypeMap(Map<Integer, ClimbServiceCommodity> commodityServiceTypeMap) {
        this.commodityServiceTypeMap = commodityServiceTypeMap;
    }

    @Override
    public String toString() {
        return "CommodityPackBO{" +
                "displayMap=" + displayMap +
                ", commodityInfoMap=" + commodityInfoMap +
                ", commodityPriceMap=" + commodityPriceMap +
                ", commodityTradeRuleMap=" + commodityTradeRuleMap +
                ", commodityStockMap=" + commodityStockMap +
                ", saleStateMap=" + saleStateMap +
                ", commodityDeliveryTimeConfigMap=" + commodityDeliveryTimeConfigMap +
                ", productInfoMap=" + productInfoMap +
                ", productMap=" + productMap +
                ", tagBindMap=" + tagBindMap +
                ", commodityMetaMap=" + commodityMetaMap +
                ", commodityBindClimeServiceMap=" + commodityBindClimeServiceMap +
                ", commodityServiceTypeMap=" + commodityServiceTypeMap +
                '}';
    }
}
