package com.insta360.store.business.commodity.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface CommodityCodeDao extends BaseDao<CommodityCode> {

    /**
     * 批量保存料号
     *
     * @param commodityId
     * @param code
     * @param countryList
     */
    void batchSaveCodes(Integer commodityId, String code, @Param("countryList") List<String> countryList);

    /**
     * 批量更新料号
     *
     * @param commodityId
     * @param code
     * @param countryList
     */
    void batchUpdateCodes(Integer commodityId, String code, @Param("countryList") List<String> countryList);
}
