package com.insta360.store.business.reseller.exception;

import com.insta360.compass.core.exception.ErrorCode;

/**
 * @Author: wbt
 * @Date: 2020/10/20
 * @Description:
 */
public enum ResellerErrorCode implements ErrorCode {

    AlreadyAppliedException(30000),

    AlreadyBeResellerException(30001),

    AlreadyWithdrawException(30002),

    CurrencyNotChosenException(30003),

    IllegalWithdrawerException(30005),

    InvalidResellerException(30007),

    InvalidResellerTypeException(30008),

    RefundedResellerOrderException(30012),

    InvalidWithdrawStateException(30013),

    ResellerOrderNotFoundException(30013),

    OutOfBonusBalanceException(30014),

    WithdrawNotFoundException(30015),

    WithdrawUnavailableException(30016),

    ResellerUserInfoExistException(30017),

    ResellerUserInfoNotFoundException(30018),

    IllegalParamException(30019),

    ResellerCodeNotExistException(30020),

    WithdrawAlipayUsernameBlankException(30021,"提现支付宝账户的真实姓名为空"),

    WithdrawOrderNotExistsException(30022,"提现订单不存在"),

    /**
     * 当前场景提现渠道只支持'银行卡'/'银行卡（国内）'
     */
    OnlyBankWithdrawChannelException(*********, "当前场景提现渠道只支持'银行卡'/'银行卡（国内）'"),

    /**
     * 提现状态、提现渠道未选择
     */
    NoWithdrawStateOrChannelException(*********, "提现状态、提现渠道未选择"),

    /**
     * 查询条件中有邮箱未找到
     */
    EmailNotFoundInQueryException(*********, "查询条件中有邮箱未找到"),

    /**
     * 查询条件中有订单号未找到
     */
    OrderNumberNotFoundInQueryException(*********, "查询条件中有订单号未找到"),

    /**
     * 查询条件中有分销码未找到
     */
    PromoCodeNotFoundInQueryException(*********, "查询条件中有分销码未找到"),

     /**
     * 提现记录不存在
     */
    WithdrawRecordNotExistsException(*********, "提现记录不存在"),

    /**
     * 提现银行信息不存在
     */
    WithdrawBankInfoNotExistsException(*********, "提现银行信息不存在"),

    /**
     * ⽤⼾银⾏信息导出上限为500⾏
     */
    WithdrawBankInfoExportLimitException(*********, "⽤⼾银⾏信息导出上限为500⾏"),

    ;
    private Integer code;

    private String msg;

    ResellerErrorCode(Integer code) {
        this.code = code;
    }

    ResellerErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
