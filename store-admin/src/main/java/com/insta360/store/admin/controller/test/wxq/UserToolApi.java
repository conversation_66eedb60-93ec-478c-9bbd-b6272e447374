package com.insta360.store.admin.controller.test.wxq;

import com.insta360.compass.core.annotations.AvoidRepeatableCommit;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * tool/care
 *
 * @description:
 * @author: py
 * @create: 2023-12-01 10:30
 */
@RestController
public class UserToolApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserToolApi.class);

    @Autowired
    private UserAccountHelper userAccountHelper;

    /**
     * 根据邮箱查询
     *
     * @param email
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping("/admin/tool/user/getStoreAccountByEmail")
    public Response<Object> getStoreAccountByEmail(@RequestParam @NotBlank String email) {
        StoreAccount storeAccount = userAccountHelper.getStoreAccountByEmail(email);
        return Response.ok(storeAccount);
    }

    /**
     * 根据用户ID查询
     *
     * @param userId
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping("/admin/tool/user/getStoreAccountByUserId")
    public Response<Object> getStoreAccountByUserId(@RequestParam @NotBlank Integer userId) {
        StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(userId);
        return Response.ok(storeAccount);
    }

}
