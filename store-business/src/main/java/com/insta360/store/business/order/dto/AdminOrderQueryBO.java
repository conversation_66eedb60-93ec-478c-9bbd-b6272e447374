package com.insta360.store.business.order.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/21
 */
public class AdminOrderQueryBO implements Serializable {

    /**
     * 订单状态
     *
     * @see com.insta360.store.business.order.enums.OrderState
     */
    private Integer orderState;

    /**
     * 是否优先发货
     */
    private Boolean shipPriority;

    /**
     * 下单国家 （包含）
     */
    private List<String> countryList;

    /**
     * 下单国家 （不包含）
     */
    private List<String> noCountryList;

    /**
     * 下单时间 （开始时间）
     */
    private LocalDateTime fromTime;

    /**
     * 下单时间 （结束时间）
     */
    private LocalDateTime endTime;

    /**
     * 下单产品ID
     */
    private List<Integer> productIdList;

    /**
     * 售后单状态
     *
     * @see com.insta360.store.business.rma.enums.RmaState
     */
    private List<Integer> rmaStateList;

    /**
     * 支付渠道 （包含）
     *
     * @see com.insta360.store.business.meta.enums.PaymentChannel
     */
    private List<String> paymentChannelList;

    /**
     * 支付渠道 （不包含）
     */
    private List<String> noPaymentChannelList;

    /**
     * 售后类型
     *
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    private String rmaType;

    /**
     * 支付状态
     *
     * @see com.insta360.store.business.order.enums.OrderPaymentState
     */
    private Integer paymentState;


    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public Boolean getShipPriority() {
        return shipPriority;
    }

    public void setShipPriority(Boolean shipPriority) {
        this.shipPriority = shipPriority;
    }

    public List<String> getCountryList() {
        return countryList;
    }

    public void setCountryList(List<String> countryList) {
        this.countryList = countryList;
    }

    public List<String> getNoCountryList() {
        return noCountryList;
    }

    public void setNoCountryList(List<String> noCountryList) {
        this.noCountryList = noCountryList;
    }

    public LocalDateTime getFromTime() {
        return fromTime;
    }

    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Integer> productIdList) {
        this.productIdList = productIdList;
    }

    public List<Integer> getRmaStateList() {
        return rmaStateList;
    }

    public void setRmaStateList(List<Integer> rmaStateList) {
        this.rmaStateList = rmaStateList;
    }

    public List<String> getPaymentChannelList() {
        return paymentChannelList;
    }

    public void setPaymentChannelList(List<String> paymentChannelList) {
        this.paymentChannelList = paymentChannelList;
    }

    public List<String> getNoPaymentChannelList() {
        return noPaymentChannelList;
    }

    public void setNoPaymentChannelList(List<String> noPaymentChannelList) {
        this.noPaymentChannelList = noPaymentChannelList;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public Integer getPaymentState() {
        return paymentState;
    }

    public void setPaymentState(Integer paymentState) {
        this.paymentState = paymentState;
    }

    @Override
    public String toString() {
        return "AdminOrderQueryBO{" +
                "orderState=" + orderState +
                ", countryList=" + countryList +
                ", noCountryList=" + noCountryList +
                ", fromTime=" + fromTime +
                ", endTime=" + endTime +
                ", productIdList=" + productIdList +
                ", rmaStateList=" + rmaStateList +
                ", paymentChannelList=" + paymentChannelList +
                ", noPaymentChannelList=" + noPaymentChannelList +
                ", rmaType='" + rmaType + '\'' +
                ", paymentState=" + paymentState +
                '}';
    }
}
