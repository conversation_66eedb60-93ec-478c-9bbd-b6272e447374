package com.insta360.store.admin.controller.test.wxq;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.test.wxq.bo.CreateBundleProductDTO;
import com.insta360.store.admin.controller.test.wxq.bo.UpdateBundleProductDTO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.common.OkHttpUtils;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.prime.bo.PrimeCreateCommodityBO;
import com.insta360.store.business.prime.config.AmazonConfig;
import com.insta360.store.business.prime.dto.PrimeCommodityDTO;
import com.insta360.store.business.prime.enums.PrimeCommodityType;
import com.insta360.store.business.prime.enums.PrimeGraphqlOperation;
import com.insta360.store.business.prime.error.PrimeInstaErrorCode;
import com.insta360.store.business.prime.lib.handler.PrimeRequestHandler;
import com.insta360.store.business.prime.lib.request.LwaTokenRequest;
import com.insta360.store.business.prime.lib.response.BasePrimeResponse;
import com.insta360.store.business.prime.lib.response.LwaTokenResponse;
import com.insta360.store.business.prime.lib.response.ProductsResponse;
import com.insta360.store.business.prime.lib.response.ShopperBwPEligibilityResponse;
import com.insta360.store.business.prime.lib.variables.DeleteProductVariables;
import com.insta360.store.business.prime.lib.variables.ProductsVariables;
import com.insta360.store.business.prime.lib.variables.ShopperBwPEligibilityVariables;
import com.insta360.store.business.prime.service.PrimeCommodityService;
import com.insta360.store.business.prime.service.helper.PrimeCommodityHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * tool/prime
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6
 */
@RestController
public class PrimeToolApi extends BaseAdminApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrimeToolApi.class);

    @Autowired
    AmazonConfig amazonConfig;

    @Autowired
    private PrimeRequestHandler primeRequestHandler;

    @Autowired
    private PrimeCommodityHelper primeCommodityHelper;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private PrimeCommodityService primeCommodityService;

    @TraceLog(logPrefix = "测试LWA登录")
    @PostMapping("/admin/tool/prime/user/testLwaLogin")
    public Response<Object> testLwaLogin(@RequestParam String code, @RequestParam String codeVerifier, @RequestParam String redirectUri) {
        LwaTokenRequest request = new LwaTokenRequest(code, amazonConfig.getGrantType(), amazonConfig.getClientId(), amazonConfig.getClientSecret(), codeVerifier, redirectUri);
        OkHttpUtils.OkHttpResponse okHttpResponse = OkHttpUtils.postJson(amazonConfig.getLwaUrl(), JSON.toJSONString(request), null);
        LwaTokenResponse lwaTokenResponse = JSON.parseObject(okHttpResponse.getResponseBody(), LwaTokenResponse.class);
        return Response.ok(lwaTokenResponse);
    }

    @TraceLog(logPrefix= "测试ShopperBwPEligibility")
    @PostMapping("/admin/tool/prime/user/shopperBwPEligibility")
    public Response<Object> shopperBwPEligibility(@RequestParam String lwaToken) {
        BasePrimeResponse basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.ShopperBwPEligibility, new ShopperBwPEligibilityVariables(lwaToken));
        if (!basePrimeResponse.ok()) {
            LOGGER.error("LwaLogin失败，响应信息: {}", basePrimeResponse);
            throw new InstaException(PrimeInstaErrorCode.LWA_LOGIN_FAILED);
        }
        ShopperBwPEligibilityResponse shopperBwPEligibilityResponse = basePrimeResponse.parsePrimeResponse(ShopperBwPEligibilityResponse.class);
        ShopperBwPEligibilityResponse.ShopperBwPEligibility shopperBwPEligibility = shopperBwPEligibilityResponse.getShopperBwPEligibility();
        return Response.ok(shopperBwPEligibility);
    }

    /**
     * 删除所有Prime商品
     * <p>
     * 该方法会分页查询所有Prime商品，并逐一删除。
     * 执行流程：
     * 1. 查询第一页商品(最多100个)
     * 2. 逐一删除商品
     * 3. 如果有下一页，继续查询并删除
     * 4. 重复步骤2-3直到所有商品删除完毕
     * </p>
     *
     * @return
     */
    @TraceLog(logPrefix = "删除所有Prime商品")
    @DeleteMapping("/admin/tool/prime/deleteAll")
    public Response<Object> deleteAll() {
        LOGGER.info("开始执行删除所有Prime商品的操作");
        int totalDeleted = 0;
        int pageCount = 0;
        int successCount = 0;
        int failCount = 0;

        // 设置查询参数，每页最多100条记录
        ProductsVariables productsVariables = new ProductsVariables();
        productsVariables.setFirst(20);
        productsVariables.setAfter(null);

        LOGGER.info("查询第一页Prime商品数据");
        BasePrimeResponse basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.Products, productsVariables);
        ProductsResponse productsResponse = basePrimeResponse.parsePrimeResponse(ProductsResponse.class);
        ProductsResponse.Products products = productsResponse.getProducts();
        List<ProductsResponse.Edge> edges = products.getEdges();

        LOGGER.info("获取到第1页商品数据，共{}条记录", edges.size());
        pageCount++;

        // 处理第一页数据
        for (ProductsResponse.Edge edge : edges) {
            ProductsResponse.Node node = edge.getNode();
            ProductsResponse.ExternalId externalId = node.getExternalId();
            String value = externalId.getValue();
            String productId = node.getId();

            LOGGER.info("正在删除商品: ID={}, externalId={}", productId, value);
            DeleteProductVariables deleteProductVariables = new DeleteProductVariables();
            deleteProductVariables.setIdentifier(new DeleteProductVariables.Identifier(value));

            try {
                BasePrimeResponse basePrimeResponseDelete = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.DeleteProduct, deleteProductVariables);
                boolean success = basePrimeResponseDelete.ok();
                if (success) {
                    LOGGER.info("成功删除商品: ID={}, externalId={}", productId, value);
                    successCount++;
                } else {
                    LOGGER.warn("删除商品失败: ID={}, externalId={}, 错误信息: {}", productId, value, basePrimeResponseDelete.errorMessage());
                    failCount++;
                }
            } catch (Exception e) {
                LOGGER.error("删除商品时发生异常: ID={}, externalId={}", productId, value, e);
                failCount++;
            }
            totalDeleted++;
        }

        // 处理后续页面数据
        while (products.getPageInfo().getHasNextPage()) {
            LOGGER.info("存在下一页数据，继续查询第{}页", pageCount + 1);
            productsVariables.setAfter(products.getPageInfo().getEndCursor());

            try {
                basePrimeResponse = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.Products, productsVariables);
                productsResponse = basePrimeResponse.parsePrimeResponse(ProductsResponse.class);
                products = productsResponse.getProducts();
                edges = products.getEdges();

                pageCount++;
                LOGGER.info("获取到第{}页商品数据，共{}条记录", pageCount, edges.size());

                for (ProductsResponse.Edge edge : edges) {
                    ProductsResponse.Node node = edge.getNode();
                    ProductsResponse.ExternalId externalId = node.getExternalId();
                    String value = externalId.getValue();
                    String productId = node.getId();

                    LOGGER.info("正在删除商品: ID={}, externalId={}", productId, value);
                    DeleteProductVariables deleteProductVariables = new DeleteProductVariables();
                    deleteProductVariables.setIdentifier(new DeleteProductVariables.Identifier(value));

                    try {
                        BasePrimeResponse basePrimeResponseDelete = primeRequestHandler.graphqlReqeust(PrimeGraphqlOperation.DeleteProduct, deleteProductVariables);
                        boolean success = basePrimeResponseDelete.ok();
                        if (success) {
                            LOGGER.info("成功删除商品: ID={}, externalId={}", productId, value);
                            successCount++;
                        } else {
                            LOGGER.warn("删除商品失败: ID={}, externalId={}, 错误信息: {}", productId, value, basePrimeResponseDelete.errorMessage());
                            failCount++;
                        }
                    } catch (Exception e) {
                        LOGGER.error("删除商品时发生异常: ID={}, externalId={}", productId, value, e);
                        failCount++;
                    }
                    totalDeleted++;
                }
            } catch (Exception e) {
                LOGGER.error("查询第{}页商品数据时发生异常", pageCount, e);
                break;
            }
        }

        LOGGER.info("删除所有Prime商品操作完成，总共处理{}页，{}条商品，成功: {}，失败: {}",
                pageCount, totalDeleted, successCount, failCount);
        return Response.ok();
    }

    @TraceLog(logPrefix = "创建单个Prime商品")
    @PostMapping("/admin/tool/prime/createIndividualProduct")
    public Response<Object> createIndividualProduct(@RequestBody List<Long> commodityIds) {
        LOGGER.info("开始执行创建Individual类型Prime商品的操作");
        for (Long commodityId : commodityIds) {
            Commodity commodity = commodityService.getById(commodityId);
            PrimeCreateCommodityBO primeCreateCommodityBO = primeCommodityHelper.packPrimeCreateCommodityBO(commodity);
            LOGGER.debug("创建产品输入参数构建完成");
            PrimeCommodityDTO primeCommodityDTO = new PrimeCommodityDTO(commodityId, commodityId.toString(), true, null, primeCreateCommodityBO.getDetailUrl(), PrimeCommodityType.Individual);
            primeCommodityService.createPrimeCommodity(primeCommodityDTO);
        }
        return Response.ok();
    }

    @TraceLog(logPrefix = "创建bundle商品")
    @PostMapping("/admin/tool/prime/createBundleProduct")
    public Response<Object> createBundleProduct(@RequestBody List<CreateBundleProductDTO> createBundleProductDtoList) {
        LOGGER.info("开始执行创建Individual类型Prime商品的操作");
        for (CreateBundleProductDTO createBundleProductDto : createBundleProductDtoList) {
            Commodity commodity = commodityService.getById(createBundleProductDto.getCommodityId());
            PrimeCreateCommodityBO primeCreateCommodityBO = primeCommodityHelper.packPrimeCreateCommodityBO(commodity);
            LOGGER.debug("创建产品输入参数构建完成");
            List<Long> includeCommodityIds = createBundleProductDto.getIncludeCommodityIds();
            List<PrimeCommodityDTO.IncludeCommodity> includeCommodities = includeCommodityIds.stream().map(includeCommodityId -> new PrimeCommodityDTO.IncludeCommodity(includeCommodityId, 1)).collect(Collectors.toList());
            PrimeCommodityDTO primeCommodityDTO = new PrimeCommodityDTO(createBundleProductDto.getCommodityId(), null, false, includeCommodities, primeCreateCommodityBO.getDetailUrl(), PrimeCommodityType.Bundle);
            primeCommodityService.createPrimeCommodity(primeCommodityDTO);
        }
        return Response.ok();
    }

    @PostMapping("/admin/tool/prime/updateOfferPrime")
    public Response<Object> updateOfferPrime(@RequestBody UpdateBundleProductDTO updateBundleProductDTO) {
        LOGGER.info("开始执行更新OfferPrime的操作");
        List<Long> commodityIds = updateBundleProductDTO.getCommodityIds();
        for (Long commodityId : commodityIds) {
            PrimeCommodityDTO dto = new PrimeCommodityDTO(commodityId, commodityId.toString(), updateBundleProductDTO.getOfferPrime(), null, null, updateBundleProductDTO.getPrimeCommodityType());
            primeCommodityService.updatePrimeCommodity(dto);
        }
        return Response.ok();

    }
}


