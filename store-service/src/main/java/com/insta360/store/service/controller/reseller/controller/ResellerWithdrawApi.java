package com.insta360.store.service.controller.reseller.controller;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.security.annotation.Authorization;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.reseller.dto.ResellerWithdrawAccountDTO;
import com.insta360.store.business.reseller.dto.condition.WithdrawAccountInfo;
import com.insta360.store.business.reseller.enums.ResellerWithdrawAccountType;
import com.insta360.store.business.reseller.exception.ResellerErrorCode;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerWithdrawAccount;
import com.insta360.store.business.reseller.model.ResellerWithdrawRecord;
import com.insta360.store.business.reseller.service.ResellerWithdrawAccountService;
import com.insta360.store.business.reseller.service.ResellerWithdrawService;
import com.insta360.store.business.reseller.service.impl.helper.ResellerWithdrawHelper;
import com.insta360.store.service.common.authorization.ResellerChecker;
import com.insta360.store.service.controller.reseller.format.ResellerWithdrawPack;
import com.insta360.store.service.controller.reseller.vo.ResellerWithdrawRecordVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: hyc
 * @Date: 2019/2/28
 * @Description:
 */
@RestController
public class ResellerWithdrawApi extends BaseResellerApi {

    private static final int PAGE_SIZE_LIMIT = 200;

    @Autowired
    ResellerWithdrawAccountService resellerWithdrawAccountService;

    @Autowired
    ResellerWithdrawService resellerWithdrawService;

    @Autowired
    ResellerWithdrawPack resellerWithdrawPack;

    @Autowired
    ResellerWithdrawHelper resellerWithdrawHelper;

    /**
     * 申请提现
     *
     * @param resellerWithdrawAccountParam
     * @return
     */
    @Authorization(ResellerChecker.class)
    @AvoidRepeatableCommit(timeOut = 100)
    @PostMapping(path = "/store/reseller/withdraw/applyWithdraw", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> apply(@RequestBody ResellerWithdrawAccountDTO resellerWithdrawAccountParam) {
        // 入参基础校验
        if (Objects.isNull(resellerWithdrawAccountParam)
                || Objects.isNull(resellerWithdrawAccountParam.getWithdrawAccountId())
                || CollectionUtils.isEmpty(resellerWithdrawAccountParam.getOrderNumbers())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        ResellerWithdrawAccount withdrawAccount = resellerWithdrawAccountService.getById(resellerWithdrawAccountParam.getWithdrawAccountId());
        if (Objects.isNull(withdrawAccount)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 获取分销商信息
        Reseller reseller = getReseller();
        if (!withdrawAccount.isBelongTo(reseller)) {
            throw new InstaException(CommonErrorCode.PermissionDeniedException);
        }

        // 未设置提现币种
        if (reseller.useCurrency() == null) {
            throw new InstaException(ResellerErrorCode.CurrencyNotChosenException);
        }

        // 判断支付宝提现真实姓名参数是否为空
        if (withdrawAccount.isAlipay()) {
            if (StringUtil.isBlank(resellerWithdrawAccountParam.getUsername())) {
                throw new InstaException(ResellerErrorCode.WithdrawAlipayUsernameBlankException);
            }
        }

        resellerWithdrawHelper.withdrawApply(reseller, resellerWithdrawAccountParam, withdrawAccount);
        return Response.ok();
    }

    /**
     * 分页查询分销商提现记录信息
     *
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization(ResellerChecker.class)
    @GetMapping("/store/reseller/withdraw/listWithdrawPage")
    public Response<? extends Map> listWithdrawPage(@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber, @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        // 页大小限制
        if (Objects.nonNull(pageSize) && pageSize.intValue() > PAGE_SIZE_LIMIT) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 设置分页参数
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber);
        pageQuery.setPageSize(pageSize);

        Reseller reseller = getReseller();
        PageResult<ResellerWithdrawRecord> pageResult =  resellerWithdrawService.listWithdrawPageNew(reseller.getId(), pageQuery);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getList())) {
            return Response.ok();
        }

        List<ResellerWithdrawRecordVO> resellerWithdrawRecordVoList = resellerWithdrawPack.doPackResellerWithdrawVO(pageResult.getList());
        return Response.ok(pageResult.replaceList(resellerWithdrawRecordVoList));
    }

    /**
     * 创建分销提现账户
     *
     * @param withdrawAccountParam
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization(ResellerChecker.class)
    @PostMapping(path = "/store/reseller/withdraw/createWithdrawAccount", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> createAccount(@RequestBody ResellerWithdrawAccountDTO withdrawAccountParam) {
        String type = withdrawAccountParam.getType();
        JSONObject data = withdrawAccountParam.getData();

        WithdrawAccountInfo withdrawAccountInfo = new WithdrawAccountInfo();
        withdrawAccountInfo.setAccountType(ResellerWithdrawAccountType.parse(type));
        withdrawAccountInfo.setData(data);

        ResellerWithdrawAccount withdrawAccount = resellerWithdrawAccountService.create(getReseller(), withdrawAccountInfo);
        return Response.ok("withdrawAccount", withdrawAccount);
    }

    /**
     * 查询提现账户列表
     */
    @AvoidRepeatableCommit(timeOut = 100)
    @Authorization(ResellerChecker.class)
    @GetMapping("/store/reseller/withdraw/listWithdrawAccount")
    public Response<? extends Map> listAccount() {
        Reseller reseller = getReseller();
        return Response.ok("withdrawAccounts", resellerWithdrawAccountService.getByResellerAutoId(reseller.getId()));
    }

    /**
     * 修改提现账户
     *
     * @param withdrawAccountParam
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization(ResellerChecker.class)
    @PostMapping(path = "/store/reseller/withdraw/updateWithdrawAccount", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateAccount(@RequestBody ResellerWithdrawAccountDTO withdrawAccountParam) {
        Integer withdrawAccountId = withdrawAccountParam.getId();
        JSONObject data = withdrawAccountParam.getData();

        ResellerWithdrawAccount withdrawAccount = resellerWithdrawAccountService.getById(withdrawAccountId);
        if (withdrawAccount != null) {
            // 鉴权
            if (!withdrawAccount.isBelongTo(getReseller())) {
                throw new InstaException(ResellerErrorCode.IllegalWithdrawerException);
            }

            withdrawAccount.setData(data.toJSONString());
            resellerWithdrawAccountService.updateById(withdrawAccount);
            return Response.ok("withdraw_account", withdrawAccount);
        }
        return Response.ok();
    }

    /**
     * 删除提现账户
     *
     * @param resellerWithdrawAccountParam
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization(ResellerChecker.class)
    @PostMapping(path = "/store/reseller/withdraw/deleteWithdrawAccount", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> deleteAccount(@RequestBody ResellerWithdrawAccountDTO resellerWithdrawAccountParam) {
        ResellerWithdrawAccount withdrawAccount = resellerWithdrawAccountService.getById(resellerWithdrawAccountParam.getId());
        if (withdrawAccount != null) {
            // 鉴权
            if (!withdrawAccount.isBelongTo(getReseller())) {
                throw new InstaException(CommonErrorCode.PermissionDeniedException);
            }

            // 逻辑删除
            withdrawAccount.setEnable(false);
            resellerWithdrawAccountService.updateById(withdrawAccount);
        }
        return Response.ok();
    }
}
