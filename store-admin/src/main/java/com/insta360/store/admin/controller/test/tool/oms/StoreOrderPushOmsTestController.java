package com.insta360.store.admin.controller.test.tool.oms;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.controller.test.tool.oms.dto.PushOmsDTO;
import com.insta360.store.admin.controller.test.tool.oms.helper.HistoryOrderPushHelper;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.commodity.service.CommodityCustomsTaxRateService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.integration.wto.oms.service.helper.OmsPushPlatformRouterHelper;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/25
 */
@RestController
public class StoreOrderPushOmsTestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreOrderPushOmsTestController.class);

    @Autowired
    OrderService orderService;

    @Autowired
    ProductService productService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    HistoryOrderPushHelper historyOrderPushHelper;

    @Autowired
    OmsPushPlatformRouterHelper omsPushPlatformRouterHelper;

    @Resource(name = "searchSyncExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    CommodityCustomsTaxRateService commodityCustomsTaxRateService;


    /**
     * 订单推送OMS测试
     */
    @RequestMapping("/admin/tool/oms/orderPushOms")
    public Response<Object> orderPushOmsTest(@RequestBody List<String> orderNumberList) {
        if (CollectionUtils.isEmpty(orderNumberList)) {
            return Response.failed();
        }
        List<Order> orders = orderService.listByOrderNumber(orderNumberList);
        if (CollectionUtils.isEmpty(orders)) {
            return Response.failed();
        }
        omsPushPlatformRouterHelper.determinePlatformForOrder(orders);
        return Response.ok();
    }

    /**
     * 订单推送OMS测试
     */
    @GetMapping("/admin/test/oms/orderCompletePushOms")
    public Response<Object> orderPartyPushOms(@RequestParam String startTime, @RequestParam String endTime) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.in("state", Arrays.asList(OrderState.success.getCode(), OrderState.on_delivery.getCode()));
        qw.eq("is_repair", 0);
        qw.between("create_time", startTime, endTime);

        List<Order> orders = orderService.list(qw);
        if (CollectionUtils.isEmpty(orders)) {
            return Response.failed();
        }
        historyOrderPushHelper.pushOrderComplete(orders);
        return Response.ok();
    }

    /**
     * 退款订单推送OMS测试
     */
    @PostMapping("/admin/tool/oms/refundOrderPushOms")
    public Response<Object> refundOrderPushOmsTest(@RequestBody PushOmsDTO pushOmsDto) {
        if (Objects.isNull(pushOmsDto) || CollectionUtils.isEmpty(pushOmsDto.getOrderNumberList())) {
            return Response.failed();
        }
        List<Order> orders = orderService.listByOrderNumber(pushOmsDto.getOrderNumberList());
        if (CollectionUtils.isEmpty(orders)) {
            return Response.failed("未查询到相关订单");
        }

        // 订单ID列表
        List<Integer> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());

        List<RmaOrder> rmaOrderList = rmaOrderService.listByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(rmaOrderList)) {
            return Response.ok("未查询到相关售后单");
        }

        // 筛选出符合规则的‘售后类型’售后单
        rmaOrderList = rmaOrderList.stream().filter(rmaOrder -> RmaType.isRefundType(rmaOrder.rmaType())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(rmaOrderList)) {
            return Response.ok("过滤后未查询到符合规则的售后单");
        }

        historyOrderPushHelper.pushRefundOrder(rmaOrderList);

        return Response.ok();
    }

    @PostMapping("/admin/tool/oms/batchPushOrderPushOms")
    public Response<Object> batchPushOrderPushOmsTest(@RequestBody PushOmsDTO pushOmsDto) {
        if (Objects.isNull(pushOmsDto) || CollectionUtils.isEmpty(pushOmsDto.getOrderStateList()) || StringUtils.isEmpty(pushOmsDto.getStartTime()) || StringUtils.isBlank(pushOmsDto.getEndTime())) {
            return Response.failed("参数错误");
        }
        List<RmaOrder> rmaOrderList = rmaOrderService.listHistoryRmaOrder(pushOmsDto.getOrderStateList(), pushOmsDto.getStartTime(), pushOmsDto.getEndTime());
        if (CollectionUtils.isEmpty(rmaOrderList)) {
            return Response.ok("未查询到相关售后单");
        }

        historyOrderPushHelper.pushRefundOrder(rmaOrderList);
        return Response.ok();
    }

    /**
     * 删除美国仓库存自动同步指定套餐
     *
     * @param commodityIds
     * @return
     */
    @PostMapping("/admin/tool/deleteCustomsTaxSku")
    public Response<Object> deleteCustomsTaxSku(@RequestBody List<Integer> commodityIds) {
        return Response.ok(commodityCustomsTaxRateService.deleteByCommodityIds(commodityIds));
    }


    /**
     * 推送skuCode到OMS
     */
    @GetMapping("/admin/test/oms/pushSkuCodeToOms")
    public Response pushSkuCodeToOms() throws InterruptedException {
        List<Product> productList = productService.getProducts(Boolean.FALSE);
        if (CollectionUtils.isEmpty(productList)) {
            return Response.failed("未查询到产品列表");
        }

        List<Integer> productIdList = productList.stream().map(Product::getId).collect(Collectors.toList());
        List<Commodity> commodityList = commodityService.listByProductIds(productIdList);
        if (CollectionUtils.isEmpty(commodityList)) {
            return Response.failed("未查询到套餐列表");
        }

        List<Integer> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        List<CommodityCode> commodityCodeList = commodityCodeService.listAll(commodityIdList);
        if (CollectionUtils.isEmpty(commodityCodeList)) {
            return Response.failed("未查询到套餐skuCode列表");
        }

        List<String> skuCodes = commodityCodeList.stream().map(CommodityCode::getCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        List<List<String>> list = Lists.partition(skuCodes, 1800);

        CountDownLatch countDownLatch = new CountDownLatch(list.size());

        for (List<String> skuCodeList : list) {
            threadPoolTaskExecutor.execute(() -> {
                try {
                    LOGGER.info("[oms铺货关系初始化]任务线程名称:{} 开始执行...", Thread.currentThread().getName());
                    omsPushPlatformRouterHelper.skuCodeSync(skuCodeList);
                    LOGGER.info("[oms铺货关系初始化]任务线程名称:{}  结束执行...", Thread.currentThread().getName());
                } catch (Exception e) {
                    LOGGER.error(String.format("任务[%s] 执行失败, skuCodeList: %s", Thread.currentThread().getName(), JSON.toJSONString(skuCodeList)), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        countDownLatch.await();

        String message = String.format("已完成商城全量套餐料号推送oms,共计:%s", skuCodes.size());
        FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning);

        return Response.ok();
    }
}
