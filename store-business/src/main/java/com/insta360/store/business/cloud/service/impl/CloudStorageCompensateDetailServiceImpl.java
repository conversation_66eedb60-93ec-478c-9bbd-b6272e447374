package com.insta360.store.business.cloud.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.cloud.dao.CloudStorageCompensateDetailDao;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.model.CloudStorageCompensateDetail;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitDetail;
import com.insta360.store.business.cloud.service.CloudStorageCompensateDetailService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-06-03
 * @Description:
 */
@Service
public class CloudStorageCompensateDetailServiceImpl extends BaseServiceImpl<CloudStorageCompensateDetailDao, CloudStorageCompensateDetail> implements CloudStorageCompensateDetailService {

    @Autowired
    CloudStorageStoreBenefitDetailService cloudStorageStoreBenefitDetailService;

    @Override
    public CloudStorageCompensateDetail getDetailBySerialNumber(Integer userId, String serialNumber, BenefitType benefitType) {
        if (StringUtils.isBlank(serialNumber) || Objects.isNull(benefitType) || Objects.isNull(userId)) {
            return null;
        }
        QueryWrapper<CloudStorageCompensateDetail> qw = new QueryWrapper<>();
        qw.eq("user_id", userId);
        qw.eq("serial_number", serialNumber);
        qw.eq("type", benefitType.getType());
        return baseMapper.selectOne(qw);
    }

    @Override
    public CloudStorageCompensateDetail getDetailByType(Integer userId, BenefitType benefitType) {
        if (Objects.isNull(benefitType) || Objects.isNull(userId)) {
            return null;
        }
        QueryWrapper<CloudStorageCompensateDetail> qw = new QueryWrapper<>();
        qw.eq("user_id", userId);
        qw.eq("type", benefitType.getType());
        return baseMapper.selectOne(qw);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void bind(CloudStorageCompensateDetail compensateDetail, CloudStorageStoreBenefitDetail storeBenefitDetail) {
        if (Objects.isNull(compensateDetail) || Objects.isNull(storeBenefitDetail)) {
            return;
        }

        // 保存绑定关系
        this.save(compensateDetail);
        // 设置权益已使用
        cloudStorageStoreBenefitDetailService.updateById(storeBenefitDetail);
    }

    @Override
    public List<String> listBindCodeByUserId(Integer userId) {
        if (Objects.isNull(userId)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<CloudStorageCompensateDetail> qw = new QueryWrapper<>();
        qw.eq("user_id", userId);

        return Optional.ofNullable(baseMapper.selectList(qw))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .map(CloudStorageCompensateDetail::getGiftCode)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>(0));
    }
}
