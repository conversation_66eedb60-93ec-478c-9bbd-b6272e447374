package com.insta360.store.business.integration.wto.oms.lib.module.order;

/**
 * @Author: wkx
 * @Date: 2024/12/03
 * @Description:
 */
public class OrderDetail {

    /**
     * 明细行项目号
     */
    private String orderLineNo;

    /**
     * 行明细结算总额
     */
    private String actualAmount;

    /**
     * 明细总优惠
     */
    private String discountAmount;

    /**
     * 子项税费
     */
    private String taxPrice;

    public String getOrderLineNo() {
        return orderLineNo;
    }

    public void setOrderLineNo(String orderLineNo) {
        this.orderLineNo = orderLineNo;
    }

    public String getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(String actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(String discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getTaxPrice() {
        return taxPrice;
    }

    public void setTaxPrice(String taxPrice) {
        this.taxPrice = taxPrice;
    }

    @Override
    public String toString() {
        return "OrderDetail{" +
                "orderLineNo='" + orderLineNo + '\'' +
                ", actualAmount='" + actualAmount + '\'' +
                ", discountAmount='" + discountAmount + '\'' +
                ", taxPrice='" + taxPrice + '\'' +
                '}';
    }
}
