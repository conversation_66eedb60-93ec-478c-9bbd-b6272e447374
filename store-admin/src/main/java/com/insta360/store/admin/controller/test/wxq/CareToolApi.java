package com.insta360.store.admin.controller.test.wxq;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.annotations.AvoidRepeatableCommit;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.controller.test.wxq.dto.AddCareCardDeviceType;
import com.insta360.store.admin.controller.test.wxq.dto.AddSerialNumberRule;
import com.insta360.store.admin.controller.test.wxq.dto.AddSerialRule;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.insurance.model.*;
import com.insta360.store.business.insurance.service.*;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.model.StoreConfig;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDeliveryUniqueCode;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderDeliveryUniqueCodeService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.insurance.helper.InsuranceMessageSendHelper;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * tool/care
 *
 * @description:
 * @author: py
 * @create: 2023-12-01 10:30
 */
@RestController
public class CareToolApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(CareToolApi.class);

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    CareInsuranceService careInsuranceService;

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    ExtendInsuranceService extendInsuranceService;

    @Autowired
    CarePlusInsuranceService carePlusInsuranceService;

    @Autowired
    ServiceCommodityBindService serviceCommodityBindService;

    @Autowired
    ServiceCommodityRuleService serviceCommodityRuleService;

    @Autowired
    OrderDeliveryUniqueCodeService orderDeliveryUniqueCodeService;

    @Autowired
    CareActivationCardService careActivationCardService;

    @Autowired
    CareInsuranceActivationCardService careInsuranceActivationCardService;

    @Autowired
    CareCardDeviceTypeService careCardDeviceTypeService;

    @Autowired
    InsuranceMessageSendHelper insuranceMessageSendHelper;

    /**
     * 查询增值服务卡号信息
     *
     * @param cardNumber
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping("/admin/tool/temp/searchCareCard")
    public Response<Object> searchCareCard(@RequestParam @NotBlank String cardNumber) {
        CareActivationCard activationCard = careActivationCardService.getByCardNumber(StringUtils.trim(cardNumber));
        if (activationCard == null) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("卡号:%s 查不到，输错了吧兄弟", cardNumber), FeiShuGroupRobot.CareHelper);
            return Response.failed(String.format("卡号:%s 查不到，输错了吧兄弟", cardNumber));
        }
        FeiShuMessageUtil.storeGeneralMessage(String.format("卡号:%s 查询到卡密为[%s]", cardNumber, activationCard.getActivationCode()), FeiShuGroupRobot.CareHelper);
        return Response.ok(String.format("卡号:%s 查询到卡密为[%s]", cardNumber, activationCard.getActivationCode()));
    }

    /**
     * 无忧换修改绑定邮箱
     *
     * @param deviceSerial
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping("/admin/tool/care/updateEmail")
    public Response<Object> updateEmail(@RequestParam @NotBlank String deviceSerial, @RequestParam @NotBlank String oldEmail, @RequestParam @NotBlank String newEmail) {
        LOGGER.info("无忧换修改绑定邮箱 deviceSerial:{},oldEmail:{},newEmail:{}", deviceSerial, oldEmail, newEmail);
        CareInsuranceActivationCard careInsuranceActivationCard = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
        careInsuranceActivationCard.setEmail(newEmail);
        FeiShuMessageUtil.storeGeneralMessage(String.format("序列号:deviceSerial 邮箱修改为[%s]", newEmail), FeiShuGroupRobot.CareHelper);
        return Response.ok();
    }

    /**
     * 添加增值服务信息
     *
     * @param addCareCardDeviceType
     */
    @AvoidRepeatableCommit(timeOut = 1000L)
    @GetMapping("/admin/tool/temp/addCareCardDeviceType")
    public Response<Object> addCareCardDeviceType(@RequestBody AddCareCardDeviceType addCareCardDeviceType) {
        CareCardDeviceType careCardDeviceType = addCareCardDeviceType.buildCareCardDeviceType();
        careCardDeviceTypeService.save(careCardDeviceType);
        return Response.ok();
    }

    /**
     * 新品配置 增加配置增值服务序列号信息接口
     *
     * @return
     */
    @PostMapping(value = "/admin/tool/insurance/addNewProductSerialNumberRule")
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> addNewProductSerialNumberRule(@RequestBody @Validated AddSerialNumberRule addSerialNumberRule) {
        LOGGER.info("增加增值服务序列号信息:{}", JSONObject.toJSONString(addSerialNumberRule));

        Commodity commodity = commodityService.getById(addSerialNumberRule.getCommodityId());
        if (commodity == null) {
            throw new InstaException(-1, "套餐不存在");
        }

        String configValue = storeConfigService.getConfigValue(StoreConfigKey.print_set_ignore_commodity);
        List<Integer> commodityIds = JSON.parseArray(configValue, Integer.class);
        commodityIds.add(commodity.getId());
        List<Integer> addCommodityIds = commodityIds.stream().distinct().collect(Collectors.toList());
        StoreConfig storeConfig = new StoreConfig();
        storeConfig.setKey(StoreConfigKey.print_set_ignore_commodity.name());
        storeConfig.setValue(JSON.toJSONString(addCommodityIds));
        storeConfigService.updateByKey(storeConfig);

        ServiceType serviceType = addSerialNumberRule.getServiceType();
        if (serviceType.getServiceId() == 0) {
            throw new InstaException(-1, String.format("服务类型[%s] 没有serviceId不支持配置序列号规则", serviceType.getName()));
        }
        InsuranceServiceCommodityBind insuranceServiceCommodityBind = addSerialNumberRule.toInsuranceServiceCommodityBind();
        List<InsuranceServiceCommodityRule> insuranceServiceCommodityRules = addSerialNumberRule.toInsuranceServiceCommodityRules();
        InsuranceServiceCommodityBind serviceCommodityBind = serviceCommodityBindService.getByCommodityId(addSerialNumberRule.getCommodityId());
        if (serviceCommodityBind != null) {
            throw new InstaException(-1, String.format("套餐[%s]已存在该服务[%s]的 bind 配置", commodity.getName(), serviceType.getName()));
        }
        serviceCommodityBindService.save(insuranceServiceCommodityBind);
        for (InsuranceServiceCommodityRule insuranceServiceCommodityRule : insuranceServiceCommodityRules) {
            InsuranceServiceCommodityRule commodityIdSerialRule = serviceCommodityRuleService.getByCommodityIdSerialRule(insuranceServiceCommodityRule.getServiceCommodityId(), insuranceServiceCommodityRule.getSerialRule());
            if (commodityIdSerialRule != null) {
                throw new InstaException(-1, String.format("套餐[%s]已存在该服务[%s]的规则[%s]", commodity.getName(), serviceType.getName(), insuranceServiceCommodityRule.getSerialRule()));
            }
            serviceCommodityRuleService.saveBatch(insuranceServiceCommodityRules);
        }

        LOGGER.info("增加增值服务序列号信息:bind:{} rule:{}", JSONObject.toJSONString(insuranceServiceCommodityBind), JSONObject.toJSONString(insuranceServiceCommodityRules));

        return Response.ok();
    }

    /**
     * 增加序列号规则
     *
     * @return
     */
    @PostMapping(value = "/admin/tool/insurance/addSerialRule")
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> addSerialNumberRule(@RequestBody @Validated AddSerialRule addSerialRule) {
        LOGGER.info("增加增值服务序列号信息:{}", JSONObject.toJSONString(addSerialRule));

        Commodity commodity = commodityService.getById(addSerialRule.getCommodityId());
        if (commodity == null) {
            throw new InstaException(-1, "套餐不存在");
        }
        ServiceType serviceType = addSerialRule.getServiceType();
        if (serviceType.getServiceId() == 0) {
            throw new InstaException(-1, String.format("服务类型[%s] 没有serviceId不支持配置序列号规则", serviceType.getName()));
        }
        List<InsuranceServiceCommodityRule> insuranceServiceCommodityRules = addSerialRule.toInsuranceServiceCommodityRules();
        for (InsuranceServiceCommodityRule insuranceServiceCommodityRule : insuranceServiceCommodityRules) {
            InsuranceServiceCommodityRule commodityIdSerialRule = serviceCommodityRuleService.getByCommodityIdSerialRule(insuranceServiceCommodityRule.getServiceCommodityId(), insuranceServiceCommodityRule.getSerialRule());
            if (commodityIdSerialRule != null) {
                throw new InstaException(-1, String.format("套餐[%s]已存在该服务[%s]的规则[%s]", commodity.getName(), serviceType.getName(), insuranceServiceCommodityRule.getSerialRule()));
            }
            serviceCommodityRuleService.saveBatch(insuranceServiceCommodityRules);
        }

        LOGGER.info("增加增值服务序列号信息: rule:{}", JSONObject.toJSONString(insuranceServiceCommodityRules));

        return Response.ok();
    }

    /**
     * 获取增值服务信息
     *
     * @return
     */
    @PostMapping(value = "/admin/tool/insurance/carePlusInsurance")
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> carePlusInsurance(@RequestParam String serial, @RequestParam ServiceType serviceType) {
        switch (serviceType) {
            case care_plus:
                CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(serial);
                return Response.ok(carePlusInsurance);
            case care:
                CareInsurance bySerialCloud = careInsuranceService.getBySerialCloud(serial);
                return Response.ok(bySerialCloud);
            case extend:
                ExtendInsurance extendInsurance = extendInsuranceService.getBySerial(serial);
                return Response.ok(extendInsurance);
            default:
                return Response.failed();
        }

    }

    /**
     * 更新增值服务信息
     *
     * @return
     */
    @PostMapping(value = "/admin/tool/insurance/updateCarePlusInsurance")
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> updateCarePlusInsurance(@RequestBody CarePlusInsurance carePlusInsurance) {
        carePlusInsuranceService.updateById(carePlusInsurance);
        return Response.ok(carePlusInsurance);
    }

    /**
     * 更新增值服务信息
     *
     * @return
     */
    @PostMapping(value = "/admin/tool/insurance/updateExtendInsurance")
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> ExtendInsurance(@RequestBody ExtendInsurance extendInsurance) {
        extendInsuranceService.updateById(extendInsurance);
        return Response.ok(extendInsurance);
    }

    /**
     * 更新增值服务信息
     *
     * @return
     */
    @PostMapping(value = "/admin/tool/insurance/updateCareInsurance")
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> updateCareInsurance(@RequestBody CareInsurance careInsurance) {
        careInsuranceService.updateById(careInsurance);
        return Response.ok(careInsurance);
    }

    /**
     * 模拟管易发货保存序列号信息
     *
     * @return
     */
    @GetMapping(value = "/admin/tool/insurance/saveUniqueCode")
    public Response<Object> saveUniqueCode(@RequestParam Integer orderItemId, @RequestParam String uniqueCode) {
        LOGGER.info(String.format("模拟管易发货保存序列号信息开始...."));
        OrderItem orderItem = orderItemService.getById(orderItemId);
        Integer orderId = orderItem.getOrder();
        Order order = orderService.getById(orderId);

        CountryConfig countryConfig = countryConfigService.getByCountry(order.country());
        OrderDeliveryUniqueCode deliveryUniqueCode = new OrderDeliveryUniqueCode();
        deliveryUniqueCode.setOrderId(order.getId());
        deliveryUniqueCode.setOrderItem(orderItemId);
        deliveryUniqueCode.setUniqueCode(uniqueCode);
        deliveryUniqueCode.setCreateTime(LocalDateTime.now());
        deliveryUniqueCode.setUpdateTime(LocalDateTime.now());
        orderDeliveryUniqueCodeService.save(deliveryUniqueCode);
        LOGGER.info(String.format("准备发送延时消息...."));
        orderMessageSendHelper.sendInsuranceActivationDelayMessage(countryConfig, deliveryUniqueCode);
        return Response.ok();
    }

    /**
     * 手动激活并绑定增值服务
     *
     * @return
     */
    @GetMapping(value = "/admin/tool/insurance/reactivateTheBinding")
    public Response<Object> reactivateTheBinding(@RequestParam @NotBlank String orderNumber, @RequestParam @NotBlank String serial) {
        Order order = orderService.getByOrderNumber(orderNumber);
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        insuranceMessageSendHelper.sendOrderCreatedInsuranceBindMessage(order.getId(), serial);
        ThreadUtil.sleep(2, TimeUnit.SECONDS);
        insuranceMessageSendHelper.sendOrderPayedInsuranceActivationMessage(order, orderPayment.getPayTime());
        return Response.ok();
    }

}
