package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.commodity;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.meta.enums.OrderEndpoint;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import com.insta360.store.business.outgoing.rpc.store.job.*;
import com.insta360.store.business.product.model.Product;
import feign.RetryableException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2023/11/21
 * @Description:
 */
@Component
public class CommoditySaleStateCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommoditySaleStateCachePutHandler.class);

    @Autowired
    ProductCachePutService productCachePutService;

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Autowired
    HomeItemCachePutService homeItemCachePutService;

    @Autowired
    HomepageCachePutService homepageCachePutService;

    @Autowired
    CommodityCachePutService commodityCachePutService;

    @Autowired
    CategoryPageCachePutService categoryPageCachePutService;

    @Autowired
    NavigationBarCachePutService navigationBarCachePutService;

    @Autowired
    GraphicNavigationCachePutService graphicNavigationCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        List<Integer> commodityIds = cachePutKeyParameter.getCommodityIds();
        if (CollectionUtils.isEmpty(commodityIds)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。套餐id集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 如果没有套餐数据，则中断流程
        Collection<Commodity> commodities = commodityService.listByIds(commodityIds);
        if (CollectionUtils.isEmpty(commodities)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。启用套餐集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 如果没有产品数据，则中断流程
        Set<Integer> productIds = commodities.stream().map(Commodity::getProduct).collect(Collectors.toSet());
        Collection<Product> products = productService.listByIds(new ArrayList<>(productIds));
        if (CollectionUtils.isEmpty(products)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。产品集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}。", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(commodities, countDownLatch));
        cachePutThreadPool.execute(() -> this.task2(null, countDownLatch));
        cachePutThreadPool.execute(() -> this.task3(null, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新参数
        StoreCacheDataChangeEventBO storeCacheDataChangeEvent = new StoreCacheDataChangeEventBO();
        storeCacheDataChangeEvent.setProductEvents(this.parseProductEvent(products));
        return storeCacheDataChangeEvent;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.COMMODITY_SALE_STATE;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.PRODUCT_INFO, CacheableType.HOME_PAGE_KEY, CacheableType.HOME_ITEM_KEY, CacheableType.CATEGORY_PAGE,
                CacheableType.GRAPHIC_NAVIGATION, CacheableType.NAVIGATION_BAR_CATEGORY_KEY);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            List<Commodity> commodities = (List<Commodity>) param;
            for (Commodity commodity : commodities) {
                CacheConstant.COUNTIES.forEach(cacheCounties -> {
                    // 更新产品页
                    productCachePutService.getInfo(commodity.getProduct(), cacheCounties.getCountry(), cacheCounties.getLanguage());

                    // 更新套餐信息
                    commodityCachePutService.getInfo(commodity.getId(), cacheCounties.getCountry(), cacheCounties.getLanguage());

                    // 更新包装清单
                    commodityCachePutService.getDifferences(commodity.getProduct(), cacheCounties.getCountry(), cacheCounties.getLanguage());
                });
            }

            // 套餐维度和产品维度的推荐配件->产品交集
            List<Integer> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
            List<Integer> commodityProductIds = commodityRecommendationService.listByRecommendationCommodityIds(commodityIds);
            List<Integer> productIds = productRecommendationService.listByRecommendationCommodityIds(commodityIds);

            // 本身的产品id
            List<Integer> productIdList = commodities.stream().map(Commodity::getProduct).collect(Collectors.toList());
            productIdList.addAll(productIds);
            productIdList.addAll(commodityProductIds);
            List<Integer> productFinalIds = productIdList.stream().distinct().collect(Collectors.toList());

            for (Integer productId : productFinalIds) {
                CacheConstant.COUNTIES.forEach(cacheCounties -> {
                    // 更新推荐配件
                    commodityCachePutService.getCommodityRecommendationInfo(productId, cacheCounties.getCountry(), cacheCounties.getLanguage());
                });
            }

            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
        try {
            CacheConstant.COUNTIES.forEach(cacheCounties -> {
                // 更新首页
                homepageCachePutService.listHomepage(cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新导航栏
                navigationBarCachePutService.listNavigationBarCategoryInfos(cacheCounties.getCountry(), cacheCounties.getLanguage());

                // 更新全部类目
                categoryPageCachePutService.listAllCategory(cacheCounties.getCountry(), cacheCounties.getLanguage());
            });
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务2完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task2}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {
        try {
            CacheConstant.COUNTIES.forEach(cacheCounties -> {
                // 更新图文导航（PC端）
                graphicNavigationCachePutService.listGraphicNavigation(cacheCounties.getCountry(), cacheCounties.getLanguage(), OrderEndpoint.pc.getCode());

                // 更新图文导航（MOBILE端）
                graphicNavigationCachePutService.listGraphicNavigation(cacheCounties.getCountry(), cacheCounties.getLanguage(), OrderEndpoint.mobile.getCode());
            });

            // 更新类目页
            List<HomepageItemMain> homepageItemMains = homepageItemMainService.listEnableHomeItem();
            homepageItemMains.forEach(homepageItemMain ->
                    CacheConstant.COUNTIES.forEach(cacheCounties ->
                            homeItemCachePutService.listHomeItemInfoByHomeItemType(homepageItemMain.getId(),
                                    cacheCounties.getCountry(), cacheCounties.getLanguage())));
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务3完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task3}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }
}
