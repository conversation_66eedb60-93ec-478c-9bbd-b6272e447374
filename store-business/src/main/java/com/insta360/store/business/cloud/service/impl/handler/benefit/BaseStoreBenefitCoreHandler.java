package com.insta360.store.business.cloud.service.impl.handler.benefit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.cloud.bo.StoreCloudBusinessTypeResultBO;
import com.insta360.store.business.cloud.config.CloudStorageConfig;
import com.insta360.store.business.cloud.constant.StoreBenefitConstant;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.exception.CloudStorageBenefitErrorCode;
import com.insta360.store.business.cloud.model.CloudStorageInsuranceBenefitBind;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitChange;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitDetail;
import com.insta360.store.business.cloud.service.*;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitContext;
import com.insta360.store.business.cloud.service.impl.helper.StoreBenefitEmailHelper;
import com.insta360.store.business.common.constants.RedisKeyConstant;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.discount.service.DiscountWritService;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.insurance.config.InsuranceCommonConfiguration;
import com.insta360.store.business.insurance.service.impl.fatory.InsuranceFactory;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @Description 商城权益核心处理抽象类
 * @Date 2024/5/14
 */
public abstract class BaseStoreBenefitCoreHandler implements StoreBenefitSubscribeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseStoreBenefitCoreHandler.class);

    /**
     * 用户商城权益
     */
    protected CloudStorageStoreBenefit storeBenefit;

    /**
     * 用户权益最近一次变更记录
     */
    protected CloudStorageStoreBenefitChange benefitLastChange;

    /**
     * 订阅折扣权益明细
     */
    protected CloudStorageStoreBenefitDetail subscribeDiscountBenefitDetail;

    /**
     * care权益明细
     */
    protected CloudStorageStoreBenefitDetail careBenefitDetail;

    /**
     * 延保权益明细
     */
    protected CloudStorageStoreBenefitDetail extendBenefitDetail;

    @Autowired
    CloudStorageStoreBenefitDetailService cloudStorageStoreBenefitDetailService;

    @Autowired
    CloudStorageInsuranceBenefitBindService cloudStorageInsuranceBenefitBindService;


    @Autowired
    StoreBenefitBusinessService storeBenefitBusinessService;

    @Autowired
    InsuranceFactory insuranceFactory;

    @Autowired
    InsuranceCommonConfiguration insuranceCommonConfiguration;

    @Autowired
    StoreBenefitEmailHelper storeBenefitEmailHelper;

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    CloudStorageConfig cloudStorageConfig;

    @Autowired
    UserAccountHelper userAccountHelper;

    @Autowired
    DiscountWritService discountWritService;

    @Autowired
    CloudStorageCompensateDetailService cloudStorageCompensateDetailService;

    @Autowired
    CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    // 策略模式注册处理器
    private Map<ServiceScenesType, Consumer<StoreBenefitContext>> sceneHandlers = new HashMap<>();

    // 注册对应策略
    public BaseStoreBenefitCoreHandler() {
        sceneHandlers.put(ServiceScenesType.PURCHASE, this::purchaseSceneHandle);
        sceneHandlers.put(ServiceScenesType.RENEW, this::renewSceneHandle);
        sceneHandlers.put(ServiceScenesType.UPGRADE, this::upgradeSceneHandle);
        sceneHandlers.put(ServiceScenesType.DOWNGRADE, this::downgradeSceneHandle);
        sceneHandlers.put(ServiceScenesType.REFUND, this::refundSceneHandle);
        sceneHandlers.put(ServiceScenesType.EXPIRED, this::expiredSceneHandle);
    }

//    @Transactional(rollbackFor = RuntimeException.class)
//    @Override
//    public void handle(StoreBenefitContext storeBenefitContext) {
//        // 基础校验
//        this.baseCheck(storeBenefitContext);
//        // 初始化
//        this.init(storeBenefitContext);
//        // 获取当前权益变更节点下处于哪种场景
//        StoreCloudBusinessTypeResultBO typeMappingResult = storeBenefitContext.getTypeMappingResult();
//        // 变更场景类型
//        ServiceScenesType serviceScenesType = typeMappingResult.getServiceScenesType();
//        switch (serviceScenesType) {
//            case PURCHASE:
//                this.purchaseSceneHandle(storeBenefitContext);
//                break;
//            case RENEW:
//                this.renewSceneHandle(storeBenefitContext);
//                break;
//            case UPGRADE:
//                this.upgradeSceneHandle(storeBenefitContext);
//                break;
//            case DOWNGRADE:
//                this.downgradeSceneHandle(storeBenefitContext);
//                break;
//            case REFUND:
//                this.refundSceneHandle(storeBenefitContext);
//                break;
//            case EXPIRED:
//                this.expiredSceneHandle(storeBenefitContext);
//                break;
//            case PARTIAL_REFUND:
//                // 该场景无需额外处理。
//                break;
//            default:
//                throw new InstaException(CloudStorageBenefitErrorCode.IllegalServiceScenesTypeException);
//        }
//
//        // 缓存处理
//        this.storeBenefitCacheHandle(serviceScenesType);
//    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void handle(StoreBenefitContext storeBenefitContext) {
        // 基础校验
        this.baseCheck(storeBenefitContext);
        // 初始化
        this.init(storeBenefitContext);
        // 获取当前权益变更节点下处于哪种场景
        StoreCloudBusinessTypeResultBO typeMappingResult = storeBenefitContext.getTypeMappingResult();
        // 变更场景类型
        ServiceScenesType serviceScenesType = typeMappingResult.getServiceScenesType();

        // 策略模式执行处理器
        Optional.ofNullable(sceneHandlers.get(serviceScenesType)).ifPresent(handler -> {
            // 执行策略，处理权益下发
            handler.accept(storeBenefitContext);
            // 后置处理
            this.doPostProcess(serviceScenesType);
        });
    }

    /**
     * 初始化，根据传入的权益变更记录上下文，来更新或者插入用户权益信息。
     * 如果上下文中标识为更新，则更新权益主记录信息；否则，插入新的权益主记录信息。
     * 同时，会根据权益类型加载相关的详情信息。
     *
     * @param storeBenefitContext 包含店云服务业务类型映射结果信息和更新标记的上下文对象。
     */
    protected void init(StoreBenefitContext storeBenefitContext) {
        // 如果标记为更新，则进行更新操作
        if (storeBenefitContext.getUpdateMark()) {
            // 更新用户权益主记录和最后一次权益变更记录
            storeBenefitBusinessService.updateStoreBenefit(storeBenefitContext.getNewStoreBenefit(), storeBenefitContext.getLastBenefitChangeRecord());
            this.storeBenefit = storeBenefitContext.getNewStoreBenefit();
            this.benefitLastChange = storeBenefitContext.getLastBenefitChangeRecord();
            // 加载三种不同类型的商城特殊权益
            this.subscribeDiscountBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.ACCESSORIES_DISCOUNT.getType());
            this.careBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.CARE.getType());
            this.extendBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.EXTEND.getType());
        } else {
            // 如果标记为插入，则进行插入操作
            storeBenefitBusinessService.insertStoreBenefit(storeBenefitContext.getNewStoreBenefit(), storeBenefitContext.getLastBenefitChangeRecord());
            this.storeBenefit = storeBenefitContext.getNewStoreBenefit();
            this.benefitLastChange = storeBenefitContext.getLastBenefitChangeRecord();
        }
    }

    /**
     * 购买场景处理
     *
     * @param storeBenefitContext
     */
    abstract void purchaseSceneHandle(StoreBenefitContext storeBenefitContext);

    /**
     * 续费场景处理
     *
     * @param storeBenefitContext
     */
    abstract void renewSceneHandle(StoreBenefitContext storeBenefitContext);

    /**
     * 升级场景处理
     *
     * @param storeBenefitContext
     */
    abstract void upgradeSceneHandle(StoreBenefitContext storeBenefitContext);

    /**
     * 降级场景处理
     *
     * @param storeBenefitContext
     */
    abstract void downgradeSceneHandle(StoreBenefitContext storeBenefitContext);

    /**
     * 退款场景处理
     *
     * @param storeBenefitContext
     */
    abstract void refundSceneHandle(StoreBenefitContext storeBenefitContext);

    /**
     * 过期场景处理
     *
     * @param storeBenefitContext
     */
    abstract void expiredSceneHandle(StoreBenefitContext storeBenefitContext);

    /**
     * care上线地区校验
     *
     * @param region
     * @return
     */
    protected boolean careOnlineRegionCheck(String region) {
        boolean contains = insuranceCommonConfiguration.getCareOnlineRegions().contains(region);
        if (!contains) {
            LOGGER.info("[云存储权益变更通知]用户：[{}] 平台来源：[{}] 订阅类型：[{}] 购买地区：[{}] 暂不支持Care", storeBenefit.getUserId(), storeBenefit.getPlatformSource(), storeBenefit.getSubscribeType(), region);
        }
        return contains;
    }

    /**
     * 延保上线地区校验
     *
     * @param region
     * @return
     */
    protected boolean extendOnlineRegionCheck(String region) {
        boolean contains = insuranceCommonConfiguration.getExtendOnlineRegions().contains(region);
        if (!contains) {
            LOGGER.info("[云存储权益变更通知]用户：[{}] 平台来源：[{}] 订阅类型：[{}] 购买地区：[{}] 暂不支持延保", storeBenefit.getUserId(), storeBenefit.getPlatformSource(), storeBenefit.getSubscribeType(), region);
        }
        return contains;
    }

    /**
     * 作废care或扣减延保质保时长
     * 该方法会根据传入的car、延保权益绑定信息，利用工厂模式获取相应的保险处理器，并通过处理器取消相应的云服务。
     *
     * @param insuranceBenefitBind car、延保权益绑定信息，包含保险类型和序列号。
     */
    protected void insuranceInvalid(CloudStorageInsuranceBenefitBind insuranceBenefitBind) {
        // 通过保险类型获取对应的保险处理器
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(insuranceBenefitBind.getInsuranceType());
        // 使用保险处理器取消相应的云服务
        insuranceHandler.cloudCancelService(insuranceBenefitBind.getSerialNumber());
    }

    /**
     * 发送权益发放邮件
     */
    protected void sendBenefitEmail() {
        // 邮件发送
        storeBenefitEmailHelper.sendBenefitEmail(storeBenefit);
    }

    /**
     * 构建订阅折扣权益
     *
     * @return
     */
    protected CloudStorageStoreBenefitDetail buildSubscribeDiscountBenefitDetail() {
        CloudStorageStoreBenefitDetail subscribeDiscountBenefitDetail = new CloudStorageStoreBenefitDetail();
        subscribeDiscountBenefitDetail.setBenefitId(storeBenefit.getId());
        subscribeDiscountBenefitDetail.setBenefitType(BenefitType.ACCESSORIES_DISCOUNT.getType());
        subscribeDiscountBenefitDetail.setUsedQuota(0);
        subscribeDiscountBenefitDetail.setFixedQuota(StoreBenefitConstant.DISCOUNT_INIT_QUOTA);
        subscribeDiscountBenefitDetail.setRemainderQuota(StoreBenefitConstant.DISCOUNT_INIT_QUOTA);
        return subscribeDiscountBenefitDetail;
    }

    /**
     * 构建care权益
     *
     * @return
     */
    protected CloudStorageStoreBenefitDetail buildCareBenefitDetail() {
        CloudStorageStoreBenefitDetail careBenefitDetail = new CloudStorageStoreBenefitDetail();
        careBenefitDetail.setBenefitId(storeBenefit.getId());
        careBenefitDetail.setBenefitType(BenefitType.CARE.getType());
        careBenefitDetail.setUsedQuota(0);
        careBenefitDetail.setFixedQuota(StoreBenefitConstant.CARE_OR_EXTEND_INIT_QUOTA);
        careBenefitDetail.setRemainderQuota(StoreBenefitConstant.CARE_OR_EXTEND_INIT_QUOTA);
        return careBenefitDetail;
    }

    /**
     * 构建延保权益
     */
    protected CloudStorageStoreBenefitDetail buildExtendBenefitDetail() {
        CloudStorageStoreBenefitDetail extendBenefitDetail = new CloudStorageStoreBenefitDetail();
        extendBenefitDetail.setBenefitId(storeBenefit.getId());
        extendBenefitDetail.setBenefitType(BenefitType.EXTEND.getType());
        extendBenefitDetail.setUsedQuota(0);
        extendBenefitDetail.setFixedQuota(StoreBenefitConstant.CARE_OR_EXTEND_INIT_QUOTA);
        extendBenefitDetail.setRemainderQuota(StoreBenefitConstant.CARE_OR_EXTEND_INIT_QUOTA);
        return extendBenefitDetail;
    }

    /**
     * 全部权益设置为过期
     */
    protected void stopAllBenefit() {
        // 配件折扣权益
        this.stopOldBenefitDetail(subscribeDiscountBenefitDetail);
        // 延保权益
        this.stopOldBenefitDetail(extendBenefitDetail);
        // care权益
        this.stopOldBenefitDetail(careBenefitDetail);
    }

    /**
     * 停止旧的附加权益
     *
     * @param benefitDetail
     */
    protected void stopOldBenefitDetail(CloudStorageStoreBenefitDetail benefitDetail) {
        if (Objects.isNull(benefitDetail) || benefitDetail.getExpired()) {
            return;
        }
        benefitDetail.setExpired(true);
        cloudStorageStoreBenefitDetailService.updateById(benefitDetail);
    }

    /**
     * 禁用用户所有的代金券
     *
     * @param userId
     */
    protected void disableGiftCard(Integer userId) {
        List<String> codes = cloudStorageCompensateDetailService.listBindCodeByUserId(userId);
        if (CollectionUtils.isNotEmpty(codes)) {
            codes.forEach(code -> giftCardService.disableGiftCard(code));
        }
    }

    /**
     * 进行基础检查。
     * 该方法的目的是在执行特定业务逻辑之前，对传入的上下文对象进行基础的检查，以确保后续操作的正确性和合法性。
     *
     * @param storeBenefitContext 存储优惠上下文对象，包含与存储优惠相关的所有信息。
     */
    private void baseCheck(StoreBenefitContext storeBenefitContext) {
        // 1、通过调用regionCheck方法，对存储优惠上下文中的区域信息进行校验，以确保区域信息的正确性。
        this.regionCheck(storeBenefitContext);
    }

    /**
     * 校验区域一致性。
     * 在更新存储权益时，确保新旧权益的区域信息一致，防止区域信息错误地被修改。
     * 特别是在购买场景下，区域信息的准确性至关重要。
     *
     * @param storeBenefitContext 存储权益上下文，包含新旧存储权益信息及场景类型等。
     * @throws InstaException 如果新权益不存在、原始权益记录不存在或平台来源不一致，则抛出异常。
     */
    private void regionCheck(StoreBenefitContext storeBenefitContext) {
        // 获取旧的存储权益信息
        CloudStorageStoreBenefit oldStoreBenefit = storeBenefitContext.getOldStoreBenefit();
        // 新的云服务商城权益
        CloudStorageStoreBenefit newStoreBenefit = storeBenefitContext.getNewStoreBenefit();
        // 检查新权益是否存在，如果不存在，则抛出异常
        if (Objects.isNull(newStoreBenefit)) {
            throw new InstaException(CloudStorageBenefitErrorCode.BenefitBuildException);
        }

        // 获取场景类型
        ServiceScenesType serviceScenesType = storeBenefitContext.getTypeMappingResult().getServiceScenesType();
        // 如果场景类型是'购买'、'过期'场景，则直接返回，不进行后续校验
        if (ServiceScenesType.PURCHASE.equals(serviceScenesType) || ServiceScenesType.EXPIRED.equals(serviceScenesType)) {
            return;
        }

        // 如果旧权益记录不存在，则抛出异常
        if (Objects.isNull(oldStoreBenefit)) {
            throw new InstaException(CloudStorageBenefitErrorCode.NotExistOriginalBenefitRecordException);
        }

        // 非'购买'场景则检查新旧权益的平台来源是否一致，如果不一致，则抛出异常
        if (StringUtils.isBlank(oldStoreBenefit.getPlatformSource())
                || StringUtils.isBlank(newStoreBenefit.getPlatformSource())
                || !oldStoreBenefit.getPlatformSource().trim().equalsIgnoreCase(newStoreBenefit.getPlatformSource().trim())) {
            throw new InstaException(CloudStorageBenefitErrorCode.PlatformSourceInconsistentException);
        }
    }

    /**
     * 后置处理
     * 该方法的目的是在特定业务逻辑执行完毕后，进行后续处理，包括缓存处理、特殊权益发送等操作。
     *
     * @param serviceScenesType 场景类型，表示云服务的业务场景。
     */
    private void doPostProcess(ServiceScenesType serviceScenesType) {
        // 缓存处理
        this.storeBenefitCacheHandle(serviceScenesType);
    }

    /**
     * 下一台相机优惠权益处理
     */
//    protected void handleNextCameraDiscountBenefit() {
//        // 下一台相机优惠权益预处理标记
//        Boolean nextOneCameraDiscountMark = storeBenefit.getNextOneCameraDiscount();
//        if (!Boolean.TRUE.equals(nextOneCameraDiscountMark)) {
//            return;
//        }
//
//        BenefitCapacityType capacityType = storeBenefit.parseCapacityType();
//        if (!BenefitCapacityType.TWO_T.equals(capacityType)) {
//            return;
//        }
//
//        CloudStorageCompensateDetail compensateDetail = cloudStorageCompensateDetailService.getDetailByType(storeBenefit.getUserId(), BenefitType.NEXT_ONE_CAMERA_DISCOUNT);
//        if (Objects.nonNull(compensateDetail)) {
//            return;
//        }
//
//        // 订阅期数
//        Integer periodNumber = storeBenefit.getPeriodNumber();
//        if (Objects.isNull(periodNumber)) {
//            return;
//        }
//
//        // 根据订阅类型判断期数是否满足要求
//        SkuSubscribeType skuSubscribeType = storeBenefit.parseSkuSubscribeType();
//        switch (skuSubscribeType) {
//            case YEARLY:
//                if (periodNumber < 2) {
//                    return;
//                }
//                break;
//            case MONTHLY:
//                if (periodNumber < 13) {
//                    return;
//                }
//                break;
//            default:
//                return;
//        }
//
//        String email = null;
//        try {
//            // 获取用户信息
//            StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(storeBenefit.getUserId());
//            if (Objects.nonNull(storeAccount)) {
//                email = storeAccount.getUsername();
//            }
//        } catch (Exception e) {
//            LOGGER.error(String.format("[云存储权益变更通知]用户[%s]发放‘下一台相机优惠’代金券异常,获取用户信息失败. ", storeBenefit.getUserId()), e);
//        }
//
//        // 发放代金券
//        String giftCardCode = this.issueGiftCard(email);
//        if (StringUtils.isBlank(giftCardCode)) {
//            FeiShuMessageUtil.storeGeneralMessage("用户[%s]发放‘下一台相机优惠’代金券失败,请人工处理。", FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.CYJ, FeiShuAtUser.LQ);
//            return;
//        }
//
//        CloudStorageCompensateDetail cloudStorageCompensateDetail = new CloudStorageCompensateDetail(storeBenefit.getUserId(), giftCardCode, BenefitType.NEXT_ONE_CAMERA_DISCOUNT);
//        cloudStorageCompensateDetailService.save(cloudStorageCompensateDetail);
//
//        CloudStorageStoreBenefit updateBenefit = new CloudStorageStoreBenefit();
//        updateBenefit.setId(storeBenefit.getId());
//        updateBenefit.setNextOneCameraDiscount(Boolean.FALSE);
//        cloudStorageStoreBenefitService.updateById(updateBenefit);
//    }

    /**
     * 发放代金券
     *
     * @return
     */
//    private String issueGiftCard(String email) {
//        // 封装请求参数
//        BatchCreateDiscountCommonAO batchCreateGiftCardAo = new BatchCreateDiscountCommonAO();
//        batchCreateGiftCardAo.setTemplateCode(cloudStorageConfig.getNextOneCameraTemplateCode());
//        batchCreateGiftCardAo.setBindEmail(email);
//        batchCreateGiftCardAo.setRemark("cloud_gift");
//        batchCreateGiftCardAo.setDiscountModel(DiscountModel.GIFT_CODE.code);
//        batchCreateGiftCardAo.setPlatformSource(PlatformSourceType.TEMPLATE.code);
//        batchCreateGiftCardAo.setEffectDelayDays(cloudStorageConfig.getEffectDelayDays());
//
//        BatchCreateCommonDiscountRO createCommonDiscountRo = null;
//        try {
//            createCommonDiscountRo = discountWritService.batchCreate(batchCreateGiftCardAo);
//        } catch (Exception e) {
//            LOGGER.error(String.format("[云存储权益变更通知]用户[%s]发放‘下一台相机优惠’代金券异常. ", email), e);
//        }
//
//        String giftCardCode = null;
//        if (Objects.nonNull(createCommonDiscountRo) && CollectionUtils.isNotEmpty(createCommonDiscountRo.getCodeList())) {
//            giftCardCode = createCommonDiscountRo.getCodeList().stream().findFirst().get();
//        }
//        return giftCardCode;
//    }

    /**
     * 根据服务场景类型存储权益信息到缓存中。
     * 该方法主要用于处理权益信息的缓存更新或删除，基于服务场景类型的不同条件。
     *
     * @param serviceScenesType 权益服务场景类型，用于判断缓存的处理方式。
     */
    private void storeBenefitCacheHandle(ServiceScenesType serviceScenesType) {
        // 如果服务场景类型为空，则直接返回，不进行后续处理。
        if (Objects.isNull(serviceScenesType)) {
            return;
        }

        // 构建权益信息的Redis缓存键名，基于用户ID。
        // 缓存key
        String redisKey = RedisKeyConstant.STORE_CLOUD_SUBSCRIBE_USER_KEY + storeBenefit.getUserId();

        try {
            // 如果服务场景类型为退款或过期，则删除已有的权益缓存信息。
            // 如果权益变更场景时'退款'、'过期'时则删除掉该用户已存在的权益缓存信息
            if (Lists.newArrayList(ServiceScenesType.REFUND, ServiceScenesType.EXPIRED).contains(serviceScenesType)) {
                // 判断缓存键是否存在，如果存在，则删除该键。
                if (RedisTemplateUtil.hashKey(redisKey)) {
                    RedisTemplateUtil.delKey(redisKey);
                }
                return;
            }

            // 计算缓存的过期时间，基于权益的过期时间与当前时间的差值。
            long cacheExpirationTime = storeBenefit.getExpirationTime() - System.currentTimeMillis();
            // 将权益信息序列化为JSON字符串，并设置缓存键值对，缓存过期时间为计算得到的过期时间。
            RedisTemplateUtil.setKeyValue(redisKey, JSON.toJSONString(storeBenefit), cacheExpirationTime, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            LOGGER.error(String.format("[商城云服务订阅]云存储商城权益变更场景业务处理,更新缓存失败. redisKey:{%s}, newStoreBenefit:%s", redisKey, JSON.toJSONString(storeBenefit)), e);
        }
    }
}