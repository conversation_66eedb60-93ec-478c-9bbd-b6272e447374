package com.insta360.store.business.rma.constant;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaLanguage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2021/07/23
 * @Description:
 */
public class RmaConstant {

    /**
     * 售后原因邮件队列名称
     */
    public static final String RMA_REASON_QUEUE = "rma_reason_email_queue";

    /**
     * 仅退款-协商选项按钮多语言文案Map
     */
    public static final Map<String, Map<String, String>> REFUND_NEGOTIATION_OPTIONS_MAP = ImmutableMap.<String, Map<String, String>>builder()
            .put("continue_with_refund", ImmutableMap.<String, String>builder()
                    .put(InstaLanguage.zh_CN.name(), "继续退款")
                    .put(InstaLanguage.zh_TW.name(), "繼續退款")
                    .put(InstaLanguage.de_DE.name(), "Kommentar muss ausgefüllt sein")
                    .put(InstaLanguage.es_ES.name(), "Continuar con la devolución")
                    .put(InstaLanguage.fr_FR.name(), "Poursuivre le remboursement")
                    .put(InstaLanguage.it_IT.name(), "Continua con il rimborso")
                    .put(InstaLanguage.ja_JP.name(), "払い戻しを続ける")
                    .put(InstaLanguage.ko_KR.name(), "환불 계속하기")
                    .put(InstaLanguage.en_US.name(), "Continue with Refund")
                    .build()
            )
            .put("discuss_shipping_options", ImmutableMap.<String, String>builder()
                    .put(InstaLanguage.zh_CN.name(), "协商发货安排")
                    .put(InstaLanguage.zh_TW.name(), "協商發貨安排")
                    .put(InstaLanguage.de_DE.name(), "Versandoptionen besprechen")
                    .put(InstaLanguage.es_ES.name(), "Comprobar opciones de envío")
                    .put(InstaLanguage.fr_FR.name(), "Discuter des options d'expédition")
                    .put(InstaLanguage.it_IT.name(), "Rivedi le opzioni di spedizione")
                    .put(InstaLanguage.ja_JP.name(), "配送方法について相談する")
                    .put(InstaLanguage.ko_KR.name(), "배송 옵션 조율")
                    .put(InstaLanguage.en_US.name(), "Discuss Shipping Options")
                    .build()
            )
            .build();

    /**
     * 欧盟成员国/地区二字码集合
     */
    public static final List<String> EU_CODES = Lists.newArrayList("AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE");

    /**
     * 业务流程主责归因code
     */
    public static final Integer RMA_BUSINESS_PROCESS_CODE = 3;

    /**
     * 用户售后申请原因-其他原因
     */
    public static final String RMA_OTHER_REASON = "other_reasons";

    /**
     * 云服务升级失败-客服确认原因
     */
    public static final String RMA_CLOUD_UPGRADE_FAIL = "cloud_upgrade_fail";

    /**
     * 云服务升级成功-客服确认原因
     */
    public static final String RMA_CLOUD_UPGRADE_SUCCESS = "cloud_upgrade_success";

    /**
     * zendesk客服系统邮箱地址
     */
    public static final String RMA_ZENDESK_EMAIL = "<EMAIL>";

    /**
     * 退换货：reason -> 用户选择的文案
     */
    public static final Map<String, String> RETURN_REASON_MAPPINGS = new HashMap<>();

    static {
        RETURN_REASON_MAPPINGS.put("mistaken_order", "I picked the wrong product or bundle.");
        RETURN_REASON_MAPPINGS.put("incompatible", "The product is not compatible with my device.");
        RETURN_REASON_MAPPINGS.put("arrival_delay", "The order didn't arrive in time.");
        RETURN_REASON_MAPPINGS.put("unable_to_rcevive", "I can't receive the order (I can't pick up at the shipping address/I can't contact the shipping carrier).");
        RETURN_REASON_MAPPINGS.put("damaged_item", "The package/product is damaged.");
        RETURN_REASON_MAPPINGS.put("package_lost", "The package is lost.");
        RETURN_REASON_MAPPINGS.put("missing_items", "Item(s) missing in the package.");
        RETURN_REASON_MAPPINGS.put("wrong_item", "Wrong item(s) sent.");
        RETURN_REASON_MAPPINGS.put("quality_defective", "Quality issue/defect");
        RETURN_REASON_MAPPINGS.put("quality_unsatisfaction", "I'm unsatisfied with the quality/performance.");
        RETURN_REASON_MAPPINGS.put("other_reasons", "other reasons.");
    }
}
