package com.insta360.store.business.trade.support.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.trade.service.impl.helper.bind_service.enums.CustomImageType;
import com.insta360.store.business.trade.support.CustomImageInterface;
import com.insta360.store.business.trade.support.handler.FmgCustomImageHandler;
import com.insta360.store.business.trade.support.handler.Go2CustomImageHandler;
import com.insta360.store.business.trade.support.handler.Go3CustomImageHandler;
import com.insta360.store.business.trade.support.handler.Go3SCustomImageHandler;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/3
 */
@Component
public class CustomImageHandlerFactory {

    /**
     * 获取定制贴处理器
     *
     * @param serviceType 服务类型
     * @return {@link CustomImageInterface}
     */
    public CustomImageInterface getCustomImageHandler(CustomImageType serviceType) {
        switch (serviceType) {
            case customed_shell:
                return ApplicationContextHolder.getApplicationContext().getBean(Go2CustomImageHandler.class);
            case fmg_customed_shell:
                return ApplicationContextHolder.getApplicationContext().getBean(FmgCustomImageHandler.class);
            case go3_customed_shell:
                return ApplicationContextHolder.getApplicationContext().getBean(Go3CustomImageHandler.class);
            case go3s_customed_shell:
                return ApplicationContextHolder.getApplicationContext().getBean(Go3SCustomImageHandler.class);
            default:
                throw new InstaException(CommonErrorCode.InvalidParameter, "服务类型无法获取定制贴处理器");
        }
    }
}
