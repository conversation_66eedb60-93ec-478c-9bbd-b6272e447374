package com.insta360.store.business.discount.service.impl.helper.other;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.discount.dto.ao.BatchCreateDiscountCommonAO;
import com.insta360.store.business.discount.dto.ro.BatchCreateCommonDiscountRO;
import com.insta360.store.business.discount.enums.DiscountModel;
import com.insta360.store.business.discount.enums.PlatformSourceType;
import com.insta360.store.business.discount.exception.DiscountErrorCode;
import com.insta360.store.business.discount.exception.StoreDiscountErrorCode;
import com.insta360.store.business.discount.model.GiftCardTemplate;
import com.insta360.store.business.discount.service.DiscountWritService;
import com.insta360.store.business.discount.service.GiftCardTemplateService;
import com.insta360.store.business.discount.service.impl.helper.monitor.GiftCardTemplateMonitorHelper;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.user.email.BaseUserEmail;
import com.insta360.store.business.user.email.UserEmailFactory;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.model.EmailSubscribeActivityPage;
import com.insta360.store.business.user.service.EmailSubscribeActivityPageService;
import com.insta360.store.business.user.service.EmailSubscribeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2020/08/15
 * @Description:
 */
@Component
public class EmailSubscribeGiftCardHelper {

    @Autowired
    GiftCardTemplateService giftCardTemplateService;

    @Autowired
    DiscountWritService discountWritService;

    @Autowired
    UserEmailFactory userEmailFactory;

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    EmailSubscribeActivityPageService emailSubscribeActivityPageService;

    @Autowired
    GiftCardTemplateMonitorHelper giftCardTemplateMonitorHelper;

    /**
     * 执行
     *
     * @param emailSubscribe
     * @param beginTime
     * @param clsEmail
     */
    public void doExecute(EmailSubscribe emailSubscribe, LocalDateTime beginTime, Class<? extends BaseUserEmail> clsEmail, EmailSubscribeActivityPage emailSubscribeActivityPage) {
        // 发放代金券
        if (emailSubscribeActivityPage.getSendGiftCode()) {
            emailSubscribe.setGiftCard(getGiftCardCode(emailSubscribe, beginTime, emailSubscribeActivityPage.getBindEmail()));
        }

        // 通知用户订阅成功
        this.sendEmail(emailSubscribe, clsEmail, emailSubscribeActivityPage);

        // 更新发送规则
        emailSubscribe.setSendEmailRule(true);
        emailSubscribe.setUpdateTime(LocalDateTime.now());
        emailSubscribeService.updateById(emailSubscribe);
    }

    /**
     * 获取代金券
     *
     * @param emailSubscribe
     * @param beginTime
     * @param bindEmail
     * @return
     */
    private String getGiftCardCode(EmailSubscribe emailSubscribe, LocalDateTime beginTime, Boolean bindEmail) {
        // 代金券模版
        GiftCardTemplate giftCardTemplate = getGiftCardTemplate(emailSubscribe.getActivityPage());
        if (!giftCardTemplate.effectTimeCheck()) {
            // 飞书告警
            giftCardTemplateMonitorHelper.doPackEmailSubscribeFailText(giftCardTemplate.getTemplateCode(), emailSubscribe.getEmail(), emailSubscribe.getActivityPage(), giftCardTemplate.getRemark());
            return null;
        }
        // 构建代金券生成请求参数
        BatchCreateDiscountCommonAO batchCreateGiftCardAo = new BatchCreateDiscountCommonAO();
        batchCreateGiftCardAo.setTemplateCode(giftCardTemplate.getTemplateCode());
        batchCreateGiftCardAo.setDiscountModel(DiscountModel.GIFT_CODE.code);
        batchCreateGiftCardAo.setPlatformSource(PlatformSourceType.TEMPLATE.code);
        batchCreateGiftCardAo.setFromTime(beginTime);
        // 是否生成绑定邮箱的代金券
        if (bindEmail) {
            batchCreateGiftCardAo.setBindEmail(emailSubscribe.getEmail());
        }

        String giftCardCode = null;
        BatchCreateCommonDiscountRO resp = discountWritService.batchCreate(batchCreateGiftCardAo);
        if (Objects.nonNull(resp)) {
            giftCardCode = resp.getCodeList().stream().findFirst().get();
        }
        return giftCardCode;
    }

    /**
     * 获取代金券模版
     *
     * @param activityName
     * @return
     */
    private GiftCardTemplate getGiftCardTemplate(String activityName) {
        // 根据活动名称拿到活动配置信息
        EmailSubscribeActivityPage activityConfig = emailSubscribeActivityPageService.getByActivityName(activityName);
        if (Objects.isNull(activityConfig)) {
            FeiShuMessageUtil.storeGeneralMessage("邮件订阅活动配置信息缺失。活动名称：" + activityName, FeiShuGroupRobot.MainNotice, FeiShuAtUser.PY);
            throw new InstaException(StoreDiscountErrorCode.ACTIVE_CONFIG_MISSING_EXCEPTION);
        }
        // 校验代金券模版
        String giftTemplateCode = activityConfig.getGiftTemplateCode();
        GiftCardTemplate giftCardTemplate = giftCardTemplateService.getByCode(giftTemplateCode);
        if (StringUtils.isBlank(giftTemplateCode) || Objects.isNull(giftCardTemplate)) {
            FeiShuMessageUtil.storeGeneralMessage("活动对应的邮件订阅代金券模版未找到或已失效。code：" + giftTemplateCode + "，活动名称：" + activityName, FeiShuGroupRobot.MainNotice, FeiShuAtUser.PY);
            throw new InstaException(DiscountErrorCode.InvalidGiftCardTemplateException);
        }
        return giftCardTemplate;
    }

    /**
     * 邮件发送
     *
     * @param emailSubscribe
     * @param clsEmail
     */
    public void sendEmail(EmailSubscribe emailSubscribe, Class<? extends BaseUserEmail> clsEmail, EmailSubscribeActivityPage emailSubscribeActivityPage) {
        BaseUserEmail email = userEmailFactory.getEmail(emailSubscribe, clsEmail, emailSubscribeActivityPage);
        email.doSend(emailSubscribe.getEmail());
    }
}
