package com.insta360.store.admin.controller.reseller.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.reseller.vo.*;
import com.insta360.store.business.discount.enums.old.DiscountType;
import com.insta360.store.business.discount.model.Coupon;
import com.insta360.store.business.discount.service.CouponService;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.reseller.common.ResellerConstant;
import com.insta360.store.business.reseller.dto.ResellerAccountDTO;
import com.insta360.store.business.reseller.enums.PromoLinkType;
import com.insta360.store.business.reseller.enums.ResellerClauseState;
import com.insta360.store.business.reseller.enums.ResellerType;
import com.insta360.store.business.reseller.exception.ResellerErrorCode;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerCommissionRate;
import com.insta360.store.business.reseller.model.ResellerInfo;
import com.insta360.store.business.reseller.model.ResellerPromoLink;
import com.insta360.store.business.reseller.service.*;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019-07-17
 * @Description: 分销商管理/分销商查询
 */
@RestController
@Validated
@PermissionResource(code = "resellerAccount", desc = "分销商用户管理")
public class AdResellerAccountApi extends BaseAdminApi {

    @Autowired
    ResellerService resellerService;

    @Autowired
    ResellerInfoService resellerInfoService;

    @Autowired
    ResellerPromoLinkService promoLinkService;

    @Autowired
    ResellerCommissionRateService commissionRateService;

    @Autowired
    ResellerProfitService profitService;

    @Autowired
    CouponService couponService;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    UserAccountHelper userAccountHelper;

    /**
     * 分销商查询列表
     *
     * @param pageNumber
     * @param pageSize
     */
    @LogAttr(desc = "分销商查询列表", logType = LogType.query)
    @Permission(code = "store.reseller.resellerAccount.listResellers", desc = "分销商查询列表")
    @GetMapping("/admin/reseller/listResellers")
    public Response<? extends Map> listResellers(@RequestParam(required = false, value = "page_number") Integer pageNumber,
                                                 @RequestParam(required = false, value = "page_size") Integer pageSize) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber, 1);
        pageQuery.setPageSize(pageSize, 20);

        PageResult<Reseller> pageResult = resellerService.getPage(pageQuery);

        List<AdResellerVO> resellerVOS = pageResult
                .getList()
                .stream()
                .map(reseller -> {
                    AdResellerVO resellerVO = new AdResellerVO(reseller);
                    StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(reseller.getUserId());
                    resellerVO.setAccountRef(storeAccount);
                    return resellerVO;
                }).collect(Collectors.toList());

        return Response.ok(pageResult.replaceList(resellerVOS));
    }

    /**
     * 查找分销商
     *
     * @param username
     * @param resellerCode
     */
    @LogAttr(desc = "查找分销商", logType = LogType.query)
    @Permission(code = "store.reseller.resellerAccount.findReseller", desc = "查找分销商")
    @GetMapping("/admin/reseller/findReseller")
    public Response<? extends Map> findReseller(@RequestParam(required = false, value = "username") String username,
                                                @RequestParam(required = false, value = "reseller_code") String resellerCode) {
        List<Reseller> resellerList = null;
        if (StringUtil.isNotBlank(username)) {
            resellerList = resellerService.searchByEmail(username);
        } else if (StringUtil.isNotBlank(resellerCode)) {
            resellerList = resellerService.searchByPromoCode(resellerCode);
        }

        if (resellerList != null) {
            List<AdResellerVO> resellerVOS = resellerList.stream().map(reseller -> {
                AdResellerVO resellerVO = new AdResellerVO(reseller);
                if (ResellerType.individual.name().equals(reseller.getType())) {
                    StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(reseller.getUserId());
                    resellerVO.setAccountRef(storeAccount);
                }
                return resellerVO;
            }).collect(Collectors.toList());

            return Response.ok("resellers", resellerVOS);
        }

        return Response.ok();
    }

    /**
     * 查询分销商基础信息
     *
     * @param reseller
     */
    @LogAttr(desc = "查询分销商基础信息", logType = LogType.query)
    @Permission(code = "store.reseller.resellerAccount.getResellerDetail", desc = "查询分销商基础信息")
    @GetMapping("/admin/reseller/getResellerDetail")
    public Response<AdResellerDetailVO> getResellerDetail(@RequestParam(required = false, value = "reseller") Integer reseller,
                                                          @RequestParam(required = false, value = "userId") Integer userId) {
        AdResellerDetailVO detailVO = new AdResellerDetailVO();

        Reseller needReseller = resellerService.getByUserId(userId);

        if (Objects.isNull(needReseller)) {
            return Response.failed(ResellerErrorCode.InvalidResellerException);
        }

        AdResellerVO resellerVO = new AdResellerVO(needReseller);

        // 收益率
        List<ResellerPromoLink> promoLinks = promoLinkService.getByType(PromoLinkType.product);

        Reseller finalNeedReseller = needReseller;
        List<AdResellerPromoLinkVO> promoLinkVOS = promoLinks.stream().map(p -> {
            AdResellerPromoLinkVO promoLinkVO = new AdResellerPromoLinkVO(p);

            ResellerCommissionRate resellerCommissionRate = commissionRateService.getResellerCommissionRateByResellerId(finalNeedReseller.getId(), p.getRelatedProduct());
            // 分销商专属的分销佣金,若分销商未同意条款，则返佣比例为0
            if (Objects.nonNull(resellerCommissionRate) && finalNeedReseller.getClauseState() == ResellerClauseState.REFUSAL_STATE.code) {
                resellerCommissionRate.setCommissionRate(0d);
            }

            // 分销计算时用的佣金率
            Double commissionRate = commissionRateService.getRate(finalNeedReseller, p.getRelatedProduct());
            promoLinkVO.setCommissionRate(commissionRate);

            promoLinkVO.setResellerSpecificCommissionRate(resellerCommissionRate);
            return promoLinkVO;
        }).collect(Collectors.toList());

        detailVO.setProducts(promoLinkVOS);

        // 收益
        if (needReseller.useCurrency() != null) {
            Price available = profitService.getAvailableCommission(needReseller);
            Price gained = profitService.getGainedCommission(needReseller);
            Price total = profitService.getTotalCommission(needReseller);

            AdResellerIncomeVO incomeVO = new AdResellerIncomeVO();
            incomeVO.setAvailable(available);
            incomeVO.setGained(gained);
            incomeVO.setTotal(total);
            detailVO.setIncome(incomeVO);
        }

        // 封装用户账户信息
        StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(needReseller.getUserId());
        resellerVO.setAccountRef(storeAccount);
        resellerVO.setClauseVo(buildResellerClause(needReseller));

        // 封装分销商明细信息
        ResellerInfo resellerInfo = resellerInfoService.getByResellerAutoId(needReseller.getId());
        resellerVO.setResellerInfo(resellerInfo);

        detailVO.setReseller(resellerVO);

        return Response.ok(detailVO);
    }

    /**
     * 绑定优惠券
     *
     * @param resellerAccountParam
     * @return
     */
    @LogAttr(desc = "绑定优惠券")
    @Permission(code = "store.reseller.resellerAccount.bindCoupon", desc = "绑定优惠券")
    @PostMapping(path = "/admin/reseller/bindCoupon", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> bindCoupon(@RequestBody ResellerAccountDTO resellerAccountParam) {
        Reseller reseller = resellerService.getByUserId(resellerAccountParam.getUserId());
        if (reseller == null) {
            throw new InstaException(ResellerErrorCode.InvalidResellerException);
        }
        String couponCode = resellerAccountParam.getCoupon();
        if (StringUtils.isBlank(couponCode)) {
            couponCode = "";
        } else {
            Coupon coupon = couponService.getByCode(couponCode);
            if (coupon == null) {
                return Response.failed("请输入正确的优惠码！");
            }

            DiscountType discountType = coupon.discountType();
            if (DiscountType.isOrderDiscount(discountType)) {
                return Response.failed("暂不支持绑定[订单维度]优惠码！");
            }

            couponCode = coupon.getCouponCode();
        }

        reseller.setRelatedCoupon(couponCode);
        return Response.ok(resellerService.updateResellerById(reseller));
    }

    /**
     * 查找优惠券相关分销商
     *
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @LogAttr(desc = "查找优惠券相关分销商", logType = LogType.query)
    @Permission(code = "store.reseller.resellerAccount.listCouponRelatedReseller", desc = "查找优惠券相关分销商")
    @GetMapping("/admin/reseller/listCouponRelatedReseller")
    public Response<? extends Map> listCouponRelatedReseller(@RequestParam(required = false, value = "page_number") Integer pageNumber,
                                                             @RequestParam(required = false, value = "page_size") Integer pageSize) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber, 1);
        pageQuery.setPageSize(pageSize, 10);
        PageResult<Reseller> resellerPageResult = resellerService.getCouponRelatedResellers(pageQuery);

        List<AdResellerVO> resellerVOS = resellerPageResult
                .getList()
                .stream()
                .map(r -> {
                    AdResellerVO resellerVO = new AdResellerVO(r);
                    Coupon coupon = couponService.getByCode(r.getRelatedCoupon());
                    resellerVO.setCouponFromTime(coupon.getEffectTime());
                    resellerVO.setCouponEndTime(coupon.getInvalidTime());
                    return resellerVO;
                }).collect(Collectors.toList());

        return Response.ok("resellers", resellerPageResult.replaceList(resellerVOS));
    }

    /**
     * 构建分销条款信息
     *
     * @param needReseller
     * @return
     */
    private AdResellerClauseVo buildResellerClause(Reseller needReseller) {
        AdResellerClauseVo clauseVo = new AdResellerClauseVo();
        clauseVo.setClauseState(needReseller.getClauseState());
        String versionStr = storeConfigService.getConfigValue(StoreConfigKey.reseller_clause_version);
        if (needReseller.getVersion() < Integer.parseInt(versionStr) || needReseller.getClauseState() == ResellerClauseState.DEFAULT_STATE.code) {
            clauseVo.setWarnText(ResellerConstant.defaultClauseStateWarnText);
        } else {
            if (needReseller.getClauseState() == ResellerClauseState.CONSENT_STATE.code) {
                clauseVo.setWarnText(String.format(ResellerConstant.consentClauseStateWarnText, TimeUtil.parseChinaLocalDateTime(needReseller.getClauseConsentTime()).format(ResellerConstant.df)));
            }
            if (needReseller.getClauseState() == ResellerClauseState.REFUSAL_STATE.code) {
                clauseVo.setWarnText(String.format(ResellerConstant.refusalClauseStateWarnText, TimeUtil.parseChinaLocalDateTime(needReseller.getClauseRefusalTime()).format(ResellerConstant.df)));
            }
        }

        return clauseVo;
    }
}
