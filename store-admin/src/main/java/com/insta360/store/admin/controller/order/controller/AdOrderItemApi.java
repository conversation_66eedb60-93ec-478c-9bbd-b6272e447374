package com.insta360.store.admin.controller.order.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.business.order.dto.OrderItemDTO;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.impl.helper.OrderGiftHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2021/10/11
 * @Description:
 */
@RestController
@PermissionResource(code = "orderItem", desc = "订单子项配置")
public class AdOrderItemApi extends BaseAdminApi {

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderGiftHelper orderGiftHelper;

    /**
     * 删除订单子项赠品
     *
     * @param orderItemParam
     * @return
     */
    @LogAttr(desc = "删除订单子项赠品")
    @Permission(code = "store.order.orderItem.delOrderItemGift", desc = "删除订单子项赠品")
    @PostMapping("/admin/order/item/delOrderItemGift")
    public Response<? extends Map> delOrderItemGift(@RequestBody OrderItemDTO orderItemParam) {
        // 订单子项要真实有效 && 该item必须为赠品
        OrderItem orderItem = orderItemService.getById(orderItemParam.getOrderItemId());
        if (orderItem == null || !orderItem.getIsGift()) {
            throw new InstaException(OrderErrorCode.OrderItemNotFoundException);
        }

        // 删除赠品
        orderGiftHelper.deleteGiftItem(orderItem);
        return Response.ok();
    }
}
