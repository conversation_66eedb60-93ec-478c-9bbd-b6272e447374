package com.insta360.store.business.commodity.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
@TableName("product_commodity")
public class Commodity extends BaseModel<Commodity> {

    /**
     * 非套餐id 默认值
     */
    public static final Integer NOT_COMMODITY_ID = -1;

    private static final long serialVersionUID = 1L;

    // VB预售产品
    public static final int VB_PRESALE_ID = 3196;

    public static final int TITAN_BOOKING_ID = 631;
    // titan尾款
    public static final int TITAN_REST_ID = 633;

    /**
     * 云服务赠送套餐
     */
    public static final int CLOUD_GIFT_ID = 3197;

    // onex房产套餐，单独产品
    public static final int ONEX_REAL_ESTATE_KIT_ID_NEW = 721;
    public static final int ONEX_REAL_ESTATE_KIT_ID_NEW_X = 722;
    public static final int ONEX_REAL_ESTATE_KIT_ID_NEW_STANDALONE = 723;


    // Go所有定制套餐
    public static final List<Integer> GO_ENGRAVING = Arrays.asList(919, 1312);

    // pro2所有套餐
    public static List<Integer> PRO2_ALL = Arrays.asList(361, 362, 363, 364, 365, 366);

    // titan所有套餐
    public static List<Integer> TITAN_ALL = Arrays.asList(891, 892, 930, 931, 938, 939);

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 关联的产品ID
     */
    private Integer product;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 是否启用（0：不启用；1：启用）
     */
    private Boolean enabled;

    /**
     * 排序
     */
    @JSONField(name = "order_index")
    private Integer orderIndex;

    /**
     * 内容描述
     */
    private String contents;

    /**
     * 可拆分的其它的套餐
     */
    @JSONField(name = "include_commodities")
    private String includeCommodities;


    /**
     * 是否作用于后台新增赠品列表（0：作用于；1：不作用于）
     */
    @JSONField(name = "can_be_gift")
    private Boolean canBeGift;

    /**
     * 是否是正常销售的套餐（0：不是；1：是）
     */
    @JSONField(name = "normal_sale")
    private Boolean normalSale;

    /**
     * 是否是绑定服务套餐（0：不是；1：是）
     */
    @JSONField(name = "bind_service")
    private Boolean bindService;

    /**
     * 是否是新品套餐（0：不是；1：是）
     */
    private Boolean newCommodity;

    /**
     * 是否是报关（0：不是；1：是）
     */
    private Boolean customsDeclaration;

    /**
     * 是否标准套餐（0：不是；1：是）
     */
    private Boolean standard;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public String getIncludeCommodities() {
        return includeCommodities;
    }

    public void setIncludeCommodities(String includeCommodities) {
        this.includeCommodities = includeCommodities;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getCanBeGift() {
        return canBeGift;
    }

    public void setCanBeGift(Boolean canBeGift) {
        this.canBeGift = canBeGift;
    }

    public Boolean getNormalSale() {
        return normalSale;
    }

    public void setNormalSale(Boolean normalSale) {
        this.normalSale = normalSale;
    }

    public Boolean getBindService() {
        return bindService;
    }

    public void setBindService(Boolean bindService) {
        this.bindService = bindService;
    }

    public Boolean getNewCommodity() {
        return newCommodity;
    }

    public void setNewCommodity(Boolean newCommodity) {
        this.newCommodity = newCommodity;
    }

    public Boolean getCustomsDeclaration() {
        return customsDeclaration;
    }

    public void setCustomsDeclaration(Boolean customsDeclaration) {
        this.customsDeclaration = customsDeclaration;
    }

    public Boolean getStandard() {
        return standard;
    }

    public void setStandard(Boolean standard) {
        this.standard = standard;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "Commodity{" +
                "id=" + id +
                ", product=" + product +
                ", name='" + name + '\'' +
                ", enabled=" + enabled +
                ", orderIndex=" + orderIndex +
                ", contents='" + contents + '\'' +
                ", includeCommodities='" + includeCommodities + '\'' +
                ", canBeGift=" + canBeGift +
                ", normalSale=" + normalSale +
                ", bindService=" + bindService +
                ", newCommodity=" + newCommodity +
                ", customsDeclaration=" + customsDeclaration +
                ", standard=" + standard +
                ", createTime=" + createTime +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Commodity commodity = (Commodity) o;
        return Objects.equals(id, commodity.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
