package com.insta360.store.service.controller.meta.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.meta.enums.NavigationBarTypeEnum;
import com.insta360.store.business.meta.enums.NavigationSubsetTypeEnum;
import com.insta360.store.business.meta.model.*;
import com.insta360.store.business.meta.service.impl.helper.MetaBatchHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.service.controller.meta.vo.*;
import com.insta360.store.service.controller.product.format.CommodityPack;
import com.insta360.store.service.controller.product.vo.CommodityTagInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/6/1
 * @Description:
 */
@Component
public class NavigationBarCategoryPack {

    private final static Logger LOGGER = LoggerFactory.getLogger(NavigationBarCategoryPack.class);

    @Autowired
    CommodityPack commodityPack;

    @Autowired
    MetaBatchHelper metaBatchHelper;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    ProductBatchHelper productBatchHelper;

    /**
     * 导航栏数据封装
     *
     * @param country
     * @param language
     * @return
     */
    public NavigationBarCategoryInfoVO doPackCacheNavigationBarCategoryInfo(InstaCountry country, InstaLanguage language) {
        LOGGER.info("start...");
        long startTime = System.currentTimeMillis();

        // 去除重复套餐
        Set<Integer> commodityIdsSet = new HashSet<>();
        Map<Integer, NavigationBarCategoryInside> categoryInsideMap = metaBatchHelper.categoryNavigationBarMap();
        if (MapUtils.isEmpty(categoryInsideMap)) {
            return new NavigationBarCategoryInfoVO();
        }

        // list to map
        ArrayList<Integer> navigationIds = new ArrayList<>(categoryInsideMap.keySet());
        Map<Integer, NavigationBarCategoryInfo> categoryInfoMap = metaBatchHelper.navigationBarCategoryInfoMap(navigationIds, country, language);
        Map<Integer, List<NavigationBarCategorySubsetInside>> subsetMap = metaBatchHelper.navigationSubsetMapByInsideIds(navigationIds);
        Map<Integer, List<NavigationBarBannerMain>> bannerMainMap = metaBatchHelper.bannerMainMapByInsideIds(navigationIds);

        List<NavigationBarCategoryVO> categorySuperVOS = categoryInsideMap.values().stream().map(categoryInside -> {
            NavigationBarCategoryVO categorySuperVO = new NavigationBarCategoryVO(categoryInside);
            NavigationBarCategoryInfo categoryInfo = categoryInfoMap.get(categoryInside.getId());
            if (categoryInfo == null) {
                return null;
            }
            categorySuperVO.setCategoryName(categoryInfo.getCategoryName());
            // 封装banner数据
            if (NavigationBarTypeEnum.isBannerType(categoryInside.getType())) {
                return packBannerMains(categorySuperVO, categoryInside.getId(), bannerMainMap, country, language);
            }

            // 封装配件new数据
            if (NavigationBarTypeEnum.accessoryList().contains(categoryInside.getType())) {
                return packAccessoryNew(categorySuperVO, categoryInside.getId(), subsetMap, commodityIdsSet, country, language);
            }

            // 封装二级类目
            categorySuperVO.setCategorySubsets(packNavigationBarCategorySubset(categoryInside.getId(), subsetMap, commodityIdsSet, country, language));
            return categorySuperVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        long dbTime = System.currentTimeMillis();

        LOGGER.info("db consume time 。。。。" + (dbTime - startTime));

        // 导航栏在该地区语言没有可用套餐数据
        if (CollectionUtils.isEmpty(commodityIdsSet)) {
            NavigationBarCategoryInfoVO categoryInfoVO = new NavigationBarCategoryInfoVO();
            categoryInfoVO.setNavigationBarInfos(new ArrayList<>(0));
            categoryInfoVO.setCommodityInfos(new ArrayList<>(0));
            return categoryInfoVO;
        }

        // 套餐规则校验 & 套餐信息分类集合列表
        Map<Integer, Commodity> commodityMap = commodityBatchHelper.commodityMapCommodityIds(new ArrayList<>(filterCommodity(commodityIdsSet, country)));
        List<NavigationBarCommodityInfoVO> commodityInfos = doPackCommodityInfo(commodityMap.values(), language, country);

        LOGGER.info("pack consume time 。。。。" + (System.currentTimeMillis() - startTime));

        // 一级类目排序
        categorySuperVOS.sort((n1, n2) -> n2.getOrderIndex() - n1.getOrderIndex());

        NavigationBarCategoryInfoVO categoryInfoVO = new NavigationBarCategoryInfoVO();
        categoryInfoVO.setNavigationBarInfos(categorySuperVOS);
        categoryInfoVO.setCommodityInfos(commodityInfos);
        return categoryInfoVO;
    }

    /**
     * 封装配件导航栏的数据
     *
     * @param categorySuperVO
     * @param categoryInsideId
     * @param subsetMap
     * @param commodityIdsSet
     * @param country
     * @param language
     * @return
     */
    private NavigationBarCategoryVO packAccessoryNew(NavigationBarCategoryVO categorySuperVO, Integer categoryInsideId,
                                                     Map<Integer, List<NavigationBarCategorySubsetInside>> subsetMap,
                                                     Set<Integer> commodityIdsSet, InstaCountry country, InstaLanguage language) {
        List<NavigationBarCategorySubsetInside> categorySubsetInsides = subsetMap.get(categoryInsideId);
        if (CollectionUtils.isEmpty(categorySubsetInsides)) {
            return categorySuperVO;
        }
        // 二级导航排序
        List<NavigationBarCategorySubsetInside> insides = categorySubsetInsides.stream()
                .sorted(Comparator.comparing(NavigationBarCategorySubsetInside::getOrderIndex).reversed()).collect(Collectors.toList());

        List<Integer> subsetIds = insides.stream().map(NavigationBarCategorySubsetInside::getId).collect(Collectors.toList());
        Map<Integer, NavigationBarCategorySubsetInfo> subsetInfoMap = metaBatchHelper.subsetInfoMapBySubsetInsideIds(subsetIds, country, language);
        Map<Integer, List<NavigationBarCategoryThird>> thirdMap = metaBatchHelper.thirdMapBySubsetInsideIds(subsetIds);

        // 选择新品标签
        Map<Integer, CommodityTagGroup> newCommodityTagGroupMap = commodityBatchHelper.newTagGroupMapTagIds();
        List<Integer> newTagIds = new ArrayList<>(newCommodityTagGroupMap.keySet());

        // 二级导航
        categorySuperVO.setSubsetList(insides.stream().map(subsetInside -> {
            Integer subsetInsideId = subsetInside.getId();
            List<NavigationBarCategoryThird> thirdList = thirdMap.get(subsetInsideId);

            NavigationBarCategorySubsetVO navigationBarCategorySubsetVo = new NavigationBarCategorySubsetVO(subsetInside);
            NavigationBarCategorySubsetInfo subsetInfo = subsetInfoMap.get(subsetInsideId);
            if (Objects.isNull(subsetInfo)) {
                return null;
            }
            navigationBarCategorySubsetVo.setCategorySubsetName(subsetInfo.getSubsetName());

            if (CollectionUtils.isEmpty(thirdList)) {
                return navigationBarCategorySubsetVo;
            }

            // 套餐类型特殊校验
            List<Integer> commodityIds = thirdList.stream().map(NavigationBarCategoryThird::getCommodityId).filter(Objects::nonNull).collect(Collectors.toList());
            if (NavigationSubsetTypeEnum.COMMODITY.getType().equals(subsetInside.getType())) {
                // 过滤套餐禁用、未配置价格、产品下架等情况
                Set<Integer> commoditySet = filterCommodity(new HashSet<>(commodityIds), country);
                thirdList = thirdList.stream().filter(navigationBarCategoryThird -> commoditySet.contains(navigationBarCategoryThird.getCommodityId())).collect(Collectors.toList());
                commodityIdsSet.addAll(commoditySet);
            }

            // 三级导航排序
            List<NavigationBarCategoryThird> thirdSortList = thirdList.stream()
                    .sorted(Comparator.comparing(NavigationBarCategoryThird::getOrderIndex).reversed()).collect(Collectors.toList());

            List<Integer> thirdIds = thirdSortList.stream().map(NavigationBarCategoryThird::getId).collect(Collectors.toList());
            Map<Integer, NavigationBarCategoryThirdInfo> thirdInfoMap = metaBatchHelper.thirdInfoMapByThirdIds(thirdIds, country, language);
            // 三级导航
            List<NavigationBarCategoryThirdVO> thirdVoList = thirdSortList.stream().map(navigationBarCategoryThird -> {
                NavigationBarCategoryThirdInfo navigationBarCategoryThirdInfo = thirdInfoMap.get(navigationBarCategoryThird.getId());
                if (Objects.isNull(navigationBarCategoryThirdInfo)) {
                    return null;
                }
                NavigationBarCategoryThirdVO navigationBarCategoryThirdVo = new NavigationBarCategoryThirdVO(navigationBarCategoryThird);
                navigationBarCategoryThirdVo.setThirdName(navigationBarCategoryThirdInfo.getThirdName());
                navigationBarCategoryThirdVo.setThirdInsideName(navigationBarCategoryThird.getThirdInsideName());
                return navigationBarCategoryThirdVo;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            // 三级导航vo判断
            if (CollectionUtils.isEmpty(thirdVoList)) {
                return navigationBarCategorySubsetVo;
            }

            // 新品标签绑定
            if (NavigationSubsetTypeEnum.COMMODITY.getType().equals(subsetInside.getType())) {
                Map<Integer, List<CommodityTagBind>> tagBindMap = commodityBatchHelper.commodityTagBindMapCommodityIds(commodityIds);
                thirdVoList.forEach(thirdVo -> {
                    thirdVo.setNewTag(packCommodityTagInfo(thirdVo.getCommodityId(), tagBindMap, newTagIds));
                });
            }

            navigationBarCategorySubsetVo.setThirdList(thirdVoList);
            return navigationBarCategorySubsetVo;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
        return categorySuperVO;
    }

    /**
     * 封装活动banner
     *
     * @param categorySuperVO
     * @param categoryInsideId
     * @param country
     * @return
     */
    private NavigationBarCategoryVO packBannerMains(NavigationBarCategoryVO categorySuperVO, Integer categoryInsideId,
                                                    Map<Integer, List<NavigationBarBannerMain>> bannerMainMap, InstaCountry country, InstaLanguage language) {
        List<NavigationBarBannerMain> bannerMains = bannerMainMap.get(categoryInsideId);
        if (CollectionUtils.isEmpty(bannerMains)) {
            return categorySuperVO;
        }
        List<Integer> bannerMainIds = bannerMains.stream().map(NavigationBarBannerMain::getId).collect(Collectors.toList());
        Map<Integer, List<NavigationBarBannerSubset>> bannerSubsetMap = metaBatchHelper.subsetMapByBannerMainIds(bannerMainIds);
        // banner二级
        categorySuperVO.setBannerMains(bannerMains.stream().map(bannerMain -> {
            List<NavigationBarBannerSubset> bannerSubsets = bannerSubsetMap.get(bannerMain.getId());
            if (CollectionUtils.isEmpty(bannerSubsets)) {
                return null;
            }
            List<Integer> bannerSubsetIds = bannerSubsets.stream().map(NavigationBarBannerSubset::getId).collect(Collectors.toList());
            Map<Integer, NavigationBarBannerInfo> bannerInfoMap = metaBatchHelper.bannerInfoMapBySubsetIds(bannerSubsetIds, country, language);
            List<NavigationBarBannerSubsetVO> bannerSubsetVos = bannerSubsets.stream().map(bannerSubset -> {
                NavigationBarBannerInfo bannerInfo = bannerInfoMap.get(bannerSubset.getId());
                if (bannerInfo == null) {
                    return null;
                }
                return new NavigationBarBannerSubsetVO(bannerSubset);
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bannerSubsetVos)) {
                return null;
            }
            NavigationBarBannerMainVO bannerMainVO = new NavigationBarBannerMainVO(bannerMain);
            bannerMainVO.setBannerSubsets(bannerSubsetVos);
            return bannerMainVO;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
        return categorySuperVO;
    }

    /**
     * 封装二级类目&套餐
     *
     * @param categoryInsideId
     * @param subsetMap
     * @param commodityIdsSet
     * @param country
     * @param language
     * @return
     */
    private List<NavigationBarCategorySubsetInfoVO> packNavigationBarCategorySubset(Integer categoryInsideId, Map<Integer, List<NavigationBarCategorySubsetInside>> subsetMap,
                                                                                    Set<Integer> commodityIdsSet, InstaCountry country, InstaLanguage language) {
        List<NavigationBarCategorySubsetInside> categorySubsetInsides = subsetMap.get(categoryInsideId);
        // empty return
        if (CollectionUtils.isEmpty(categorySubsetInsides)) {
            return new ArrayList<>(0);
        }
        List<Integer> subsetInsideIds = categorySubsetInsides.stream().map(NavigationBarCategorySubsetInside::getId).collect(Collectors.toList());
        Map<Integer, NavigationBarCategorySubsetInfo> categorySubsetInfoMap = metaBatchHelper.subsetInfoMapBySubsetInsideIds(subsetInsideIds, country, language);
        Map<Integer, List<NavigationBarCategoryCommodity>> categoryCommodityMap = metaBatchHelper.commodityMapBySubsetInsideIds(subsetInsideIds);

        // 二级类目排序
        categorySubsetInsides.sort((s1, s2) -> s2.getOrderIndex() - s1.getOrderIndex());

        return categorySubsetInsides.stream().map(categorySubsetInside -> {
            NavigationBarCategorySubsetInfoVO subsetInfoVO = new NavigationBarCategorySubsetInfoVO(categorySubsetInside);
            NavigationBarCategorySubsetInfo subsetInfo = categorySubsetInfoMap.get(categorySubsetInside.getId());
            if (subsetInfo == null) {
                return null;
            }
            subsetInfoVO.setCategorySubsetName(subsetInfo.getSubsetName());
            // 封装导航栏套餐
            List<NavigationBarCategoryCommodity> categoryCommodities = categoryCommodityMap.get(categorySubsetInside.getId());

            if (CollectionUtils.isEmpty(categoryCommodities)) {
                subsetInfoVO.setNavigationBarCommodityInfos(new ArrayList<>());
                return subsetInfoVO;
            }
            // 套餐排序
            categoryCommodities.sort((s1, s2) -> s2.getOrderIndex() - s1.getOrderIndex());

            subsetInfoVO.setNavigationBarCommodityInfos(categoryCommodities.stream().filter(NavigationBarCategoryCommodity::getEnabled).map(categoryCommodity -> {
                NavigationBarCategoryCommodityVO categoryCommodityVO = new NavigationBarCategoryCommodityVO(categoryCommodity);
                commodityIdsSet.add(categoryCommodity.getCommodityId());
                return categoryCommodityVO;
            }).collect(Collectors.toList()));
            return subsetInfoVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 过滤禁用的套餐和下架的套餐
     *
     * @param commodityIds
     * @param country
     * @return
     */
    private Set<Integer> filterCommodity(Set<Integer> commodityIds, InstaCountry country) {
        List<Integer> commodityIdList = new ArrayList<>(commodityIds);
        Map<Integer, Commodity> commodityMap = commodityBatchHelper.commodityMapCommodityIds(commodityIdList);
        // 防止套餐全部禁用
        if (MapUtils.isEmpty(commodityMap)) {
            return new HashSet<>();
        }
        List<Integer> productIds = commodityMap.values().stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());
        Map<Integer, Product> productMap = productBatchHelper.productMapProductIds(productIds);
        Map<Integer, CommoditySaleState> saleStateMap = commodityBatchHelper.saleStateMapCommodityIds(commodityIdList, country);
        return commodityIds
                .stream()
                .filter(commodityId -> {
                    // 套餐禁用不展示
                    Commodity commodity = commodityMap.get(commodityId);
                    if (commodity == null) {
                        return false;
                    }

                    // 产品禁用不展示
                    Product product = productMap.get(commodity.getProduct());
                    if (product == null) {
                        return false;
                    }

                    // 下架套餐不展示
                    CommoditySaleState saleState = saleStateMap.get(commodityId);
                    return saleState != null && !SaleState.remove.equals(SaleState.parse(saleState.getSaleState()));
                }).collect(Collectors.toSet());
    }

    /**
     * 封装二级类目下的产品配置信息
     *
     * @param commodities
     * @param language
     * @param country
     * @return
     */
    private List<NavigationBarCommodityInfoVO> doPackCommodityInfo(Collection<Commodity> commodities, InstaLanguage language, InstaCountry country) {
        return commodityPack.noCheckBatchPack(commodities, getCommoditySetting(language, country))
                .stream().map(commodityVo -> new NavigationBarCommodityInfoVO().parse(commodityVo)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 套餐信息封装配置
     *
     * @return
     */
    private CommodityPack.PackSetting getCommoditySetting(InstaLanguage language, InstaCountry country) {
        CommodityPack.PackSetting setting = new CommodityPack.PackSetting();
        setting.setLanguage(language);
        setting.setCountry(country);

        setting.setWithDisplay(true);
        setting.setWithStock(true);
        setting.setWithSaleState(true);
        setting.setWithPrice(true);

        setting.setWithProductUrlKey(true);
        setting.setWithProductName(true);
        setting.setWithProductId(true);

        setting.setWithCommodityInfo(true);
        return setting;
    }

    /**
     * 封装新品标签信息
     *
     * @param commodityId
     * @param tagBindMap
     * @param newTagIds
     * @return
     */
    private Boolean packCommodityTagInfo(Integer commodityId, Map<Integer, List<CommodityTagBind>> tagBindMap,
                                         List<Integer> newTagIds) {
        // 没有绑定tag 则不进行展示
        List<CommodityTagBind> commodityTagBinds = tagBindMap.get(commodityId);
        if (CollectionUtils.isEmpty(commodityTagBinds)) {
            return false;
        }

        // 只选择新品标签
        List<CommodityTagBind> newCommodityTagBinds = commodityTagBinds.stream()
                .filter(commodityTagBind -> newTagIds.contains(commodityTagBind.getTagId()))
                .collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(newCommodityTagBinds);
    }
}
