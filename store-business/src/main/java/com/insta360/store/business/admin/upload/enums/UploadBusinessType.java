package com.insta360.store.business.admin.upload.enums;


import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 上传数据业务类型
 * @Date 2023/10/7
 */
public enum UploadBusinessType {

    /**
     * 产品基础信息
     */
    product_base_info(1, "产品基础信息", Lists.newArrayList("*产品内部显示名称", "*商品购买链接后缀", "*产品类目", "*是否支持佣金", "*美国税号类别"), UploadModuleType.PRODUCT_MODULE),

    /**
     * 产品多语言信息
     */
    product_multi_language(2, "产品多语言信息", Lists.newArrayList("*配置内容", "*产品ID", "简体中文", "繁体中文", "英文", "日语", "德语", "韩语", "西班牙语", "意大利语", "法语"), UploadModuleType.PRODUCT_MODULE),

    /**
     * 产品包装清单
     */
    product_pack_list(3, "产品包装清单", Lists.newArrayList("*配置内容", "*类型", "*产品ID", "*序号（连续）", "简体中文", "繁体中文", "英文", "日语", "德语", "韩语", "西班牙语", "意大利语", "法语"), UploadModuleType.PRODUCT_MODULE),

    /**
     * 套餐基础信息
     */
    commodity_base_info(4, "套餐基础信息", Lists.newArrayList("*产品ID", "*套餐内部显示名称", "*是否正常销售", "*是否可做赠品", "*是否为增值服务", "*是否需要报关", "套餐顺序", "*销售库存", "*购买限制", "*预计发货天数"), UploadModuleType.COMMODITY_MODULE),

    /**
     * 套餐多语言信息
     */
    commodity_multi_language(5, "套餐多语言信息", Lists.newArrayList("*配置内容", "*套餐ID", "简体中文", "繁体中文", "英文", "日语", "德语", "韩语", "西班牙语", "意大利语", "法语"), UploadModuleType.COMMODITY_MODULE),

    /**
     * 现价
     */
    current_price(6, "现价比对", Lists.newArrayList(), UploadModuleType.PRICE_MODULE),

    /**
     * 原价
     */
    original_price(7, "原价比对", Lists.newArrayList(), UploadModuleType.PRICE_MODULE),

    /**
     * 新品原价
     */
    new_product_original_price(8, "新品原价比对", Lists.newArrayList(), UploadModuleType.PRICE_MODULE),

    /**
     * 物流报价
     */
    logistics_quotation(9, "物流报价", Lists.newArrayList("地区二字码", "物流渠道", "物流商", "包装类型", "重量低区间", "重量高区间", "燃油附加费基础费用", "初步运费"), UploadModuleType.LOGISTICS_QUOTATION),

    /**
     * 新品物料
     */
    new_product_materials(10, "新品物料", Lists.newArrayList("维修产品Id", "内部套餐名", "简体中文套餐名", "简中套餐描述", "英文套餐名称", "英文套餐描述", "日文套餐名称", "日文套餐描述"), UploadModuleType.COMMODITY_MODULE),

    /**
     * 实体卡绑定
     */
    care_card_bind(11, "实体卡绑定", Lists.newArrayList("相机序列号", "客户姓名", "手机号", "国家地区", "邮箱", "care虚拟码"), UploadModuleType.INSURANCE_MODULE),

    ;

    private final Integer type;

    private final String value;

    private final List<String> title;

    private final UploadModuleType moduleType;

    UploadBusinessType(Integer type, String value, List<String> title, UploadModuleType moduleType) {
        this.type = type;
        this.value = value;
        this.title = title;
        this.moduleType = moduleType;
    }

    /**
     * 枚举匹配
     *
     * @param type
     * @return
     */
    public static UploadBusinessType matchType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }

        for (UploadBusinessType uploadBusinessType : UploadBusinessType.values()) {
            if (uploadBusinessType.type.equals(type)) {
                return uploadBusinessType;
            }
        }

        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    public List<String> getTitle() {
        return title;
    }

    public UploadModuleType getModuleType() {
        return moduleType;
    }
}
