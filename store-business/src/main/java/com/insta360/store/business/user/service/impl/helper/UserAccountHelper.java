package com.insta360.store.business.user.service.impl.helper;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.rpc.user.dto.UserAccount;
import com.insta360.store.business.outgoing.rpc.user.service.UserAccountService;
import com.insta360.store.business.user.model.StoreAccount;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/27
 */
@Component
public class UserAccountHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAccountHelper.class);

    @Autowired
    UserAccountService userAccountService;

    /**
     * 获取注册用户ID
     *
     * @param email
     * @return
     */
    public Integer getInstaAccountId(String email) {
        StoreAccount storeAccount = null;
        try {
            storeAccount = this.getStoreAccountByEmail(email.trim());
        } catch (Exception e) {
            LOGGER.error(String.format("user rpc调用失败。email: {%s}", email), e);
        }
        return Objects.nonNull(storeAccount) ? storeAccount.getInstaAccount() : null;
    }

    /**
     * 获取商城StoreAccount对象（构造user账户&商城映射）
     *
     * @param email
     * @return
     */
    public StoreAccount getStoreAccountByEmail(String email) {
        try {
            LOGGER.info("user rpc调用 request email {}", email);
            Response<UserAccount> userAccountResponse = userAccountService.getByUsername(email);
            LOGGER.info("user rpc调用 response {}", JSONObject.toJSONString(userAccountResponse));
            // 兼容访客/查不到场景，外层判空处理
            if (Objects.isNull(userAccountResponse) || Objects.isNull(userAccountResponse.getData())) {
                return null;
            }
            UserAccount userAccount = userAccountResponse.getData();
            LOGGER.info("user rpc调用 userAccount {}", userAccount);
            StoreAccount storeAccount = new StoreAccount();
            storeAccount.setInstaAccount(userAccount.getId());
            storeAccount.setUsername(userAccount.getUsername());
            storeAccount.setCountry(userAccount.getCountry());
            storeAccount.setLanguage(userAccount.getLanguage());
            storeAccount.setCreateTime(userAccount.getCreateTime());
            return storeAccount;
        } catch (Exception e) {
            String errorMsg = String.format("rpc获取用户信息失败。原因{%s}", e.getMessage());
            LOGGER.error(errorMsg, e);
            FeiShuMessageUtil.storeGeneralMessage(errorMsg, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw e;
        }
    }

    /**
     * 获取商城StoreAccount对象（构造user账户&商城映射）
     *
     * @param userId
     * @return
     */
    public StoreAccount getStoreAccountByUserId(Integer userId) {
        try {
            LOGGER.info("user rpc调用 request userId {}", userId);
            Response<UserAccount> userAccountResponse = userAccountService.getProfileByUserId(userId);
            LOGGER.info("user rpc调用 response {}", JSONObject.toJSONString(userAccountResponse));
            // 兼容访客/查不到场景，外层判空处理
            if (Objects.isNull(userAccountResponse) || Objects.isNull(userAccountResponse.getData())) {
                return null;
            }
            UserAccount userAccount = userAccountResponse.getData();
            LOGGER.info("user rpc调用 userAccount {}", userAccount);
            StoreAccount storeAccount = new StoreAccount();
            storeAccount.setInstaAccount(userAccount.getId());
            storeAccount.setUsername(userAccount.getUsername());
            storeAccount.setCountry(userAccount.getCountry());
            storeAccount.setLanguage(userAccount.getLanguage());
            storeAccount.setCreateTime(userAccount.getCreateTime());
            return storeAccount;
        } catch (Exception e) {
            String errorMsg = String.format("rpc获取用户信息失败。原因{%s}", e.getMessage());
            LOGGER.error(errorMsg, e);
            FeiShuMessageUtil.storeGeneralMessage(errorMsg, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw e;
        }
    }
}