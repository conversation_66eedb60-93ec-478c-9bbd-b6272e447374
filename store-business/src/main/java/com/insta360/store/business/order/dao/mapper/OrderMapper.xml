<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.order.dao.OrderDao">

    <select id="listByCommodityIds" parameterType="com.insta360.store.business.order.bo.RepeatOrderQueryBO"
            resultType="com.insta360.store.business.commodity.bo.CommodityStockNoticeBO">
        select o.id as order_id, o.order_number, o.area, o.state, oi.commodity as commodity_id, oi.number
        from `order` o
        join order_item oi on oi.`order` = o.id
        <where>
            <if test="commodityIds != null and commodityIds.size() > 0">
                AND oi.commodity IN
                <foreach collection="commodityIds" item="commodityId" open="(" close=")" separator=",">
                    #{commodityId}
                </foreach>
            </if>
            <if test="fromTime != null and endTime != null">
                AND o.create_time BETWEEN #{fromTime} AND #{endTime}
            </if>
            <if test="orderStates != null and orderStates.size() > 0">
                AND o.state IN
                <foreach collection="orderStates" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listByPhoneOrAddress" parameterType="com.insta360.store.business.order.bo.RepeatOrderQueryBO"
            resultType="com.insta360.store.business.order.model.Order">
        SELECT
        o.*
        FROM
        `order` AS o
        LEFT JOIN
        order_delivery AS od ON od.`order` = o.id
        <where>
            <if test="condition.phone != null">
                AND od.phone = #{condition.phone}
            </if>
            <if test="condition.phoneCode != null">
                AND od.phone_code = #{condition.phoneCode}
            </if>
            <if test="condition.country != null">
                AND od.country = #{condition.country}
            </if>
            <if test="condition.province != null">
                AND od.province = #{condition.province}
            </if>
            <if test="condition.city != null">
                AND od.city = #{condition.city}
            </if>
            <if test="condition.address != null">
                AND od.address = #{condition.address}
            </if>
            <if test="condition.subAddress != null">
                AND od.sub_address = #{condition.subAddress}
            </if>
            <if test="condition.orderStates != null and condition.orderStates.size() > 0">
                AND o.state IN
                <foreach collection="condition.orderStates" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
            <if test="condition.fromTime != null and condition.endTime != null">
                AND o.create_time BETWEEN #{condition.fromTime} and #{condition.endTime}
            </if>
        </where>
    </select>

    <select id="listCustomOrders" resultType="com.insta360.store.business.order.model.Order">
        SELECT
        o.*
        FROM
        `order` AS o
        JOIN
        engraving_image AS ei ON ei.order_number = o.order_number
        WHERE
        1=1
        <if test="from_time != null and end_time != null">
            AND o.create_time BETWEEN #{from_time} and #{end_time}
        </if>

        <if test="order_numbers != null and order_numbers.size() > 0">
            AND o.order_number IN
            <foreach collection="order_numbers" item="order_number" open="(" close=")" separator=",">
                #{order_number}
            </foreach>
        </if>

        <if test="order_states != null and order_states.size() > 0">
            AND o.state IN
            <foreach collection="order_states" item="state" open="(" close=")" separator=",">
                #{state}
            </foreach>
        </if>

        AND ei.custom_type = #{customType}
    </select>

    <!-- Reuse the conditions for count and pagination queries -->
    <select id="countCustomExportOrders" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        `order` AS o
        JOIN
        engraving_image AS ei ON ei.order_number = o.order_number
        <where>
            <if test="from_time != null and end_time != null">
                o.create_time BETWEEN #{from_time} AND #{end_time}
            </if>
            <if test="order_numbers != null and order_numbers.size() > 0">
                AND o.order_number IN
                <foreach collection="order_numbers" item="order_number" open="(" close=")" separator=",">
                    #{order_number}
                </foreach>
            </if>
            <if test="order_states != null and order_states.size() > 0">
                AND o.state IN
                <foreach collection="order_states" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
            AND ei.custom_type = #{customType}
        </where>
    </select>

    <select id="pageCustomOrders" resultType="com.insta360.store.business.order.model.Order">
        SELECT
        o.*
        FROM
        `order` AS o
        JOIN
        engraving_image AS ei ON ei.order_number = o.order_number
        WHERE
        ei.custom_type = #{customType}
        <if test="from_time != null and end_time != null">
            AND o.create_time BETWEEN #{from_time} AND #{end_time}
        </if>
        <if test="order_numbers != null and order_numbers.size() > 0">
            AND o.order_number IN
            <foreach collection="order_numbers" item="order_number" open="(" close=")" separator=",">
                #{order_number}
            </foreach>
        </if>
        <if test="order_states != null and order_states.size() > 0">
            AND o.state IN
            <foreach collection="order_states" item="state" open="(" close=")" separator=",">
                #{state}
            </foreach>
        </if>
    </select>

    <select id="queryByComplexCondition"
            parameterType="com.insta360.store.business.admin.order.query.bo.AdminOrderQueryCondition"
            resultType="com.insta360.store.business.order.model.Order">
        SELECT
        o.*
        FROM
        `order` AS o
        JOIN
        order_item AS oi ON oi.`order` = o.id
        JOIN
        order_payment AS op ON op.`order` = o.id
        <if test="condition.containsLink != null and condition.containsLink == true">
           JOIN reseller_order as ro ON ro.order_number = o.order_number
        </if>
        <if test="condition.joinUtmSource != null and condition.joinUtmSource == true">
           LEFT JOIN reseller_utm_source as rus ON rus.order_number = o.order_number
        </if>
        WHERE
        -- 订单状态
        o.state IN
        <foreach collection="condition.states" item="state" open="(" close=")" separator=",">
            #{state}
        </foreach>

        -- 产品列表
        <if test="condition.productIds != null and condition.productIds.size() > 0">
            AND oi.product IN
            <foreach collection="condition.productIds" item="productId" open="(" close=")" separator=",">
                #{productId}
            </foreach>
        </if>

        -- 套餐列表
        <if test="condition.commodityIds != null and condition.commodityIds.size() > 0">
            AND oi.commodity IN
            <foreach collection="condition.commodityIds" item="commodityId" open="(" close=")" separator=",">
                #{commodityId}
            </foreach>
        </if>

        -- 地区列表
        <if test="condition.countryList != null and condition.countryList.size() > 0">
            AND o.area IN
            <foreach collection="condition.countryList" item="country" open="(" close=")" separator=",">
                #{country}
            </foreach>
        </if>

        -- 下单终端
        <if test="condition.endpoint != null">
            AND o.endpoint = #{condition.endpoint}
        </if>

        -- 分销码
        <if test="condition.promoCode != null">
            AND o.promo_code = #{condition.promoCode}
        </if>

        -- 优惠码
        <if test="condition.couponCode != null">
            AND o.coupon_code = #{condition.couponCode}
        </if>

        -- 支付渠道
        <if test="condition.paymentChannelList != null and condition.paymentChannelList.size() > 0">
            AND op.channel IN
            <foreach collection="condition.paymentChannelList" item="channel" open="(" close=")" separator=",">
                #{channel}
            </foreach>
        </if>

        -- 时间范围
        <if test="condition.fromTime != null and condition.endTime != null">
            AND o.create_time between #{condition.fromTime} and #{condition.endTime}
        </if>

        -- 关联utm_source表
        <if test="condition.joinUtmSource != null and condition.joinUtmSource == true">
            <if test='condition.utmSourceInclude != null and condition.utmSourceInclude == true'>
                AND rus.utm_source IN
                <foreach collection="condition.utmSourceList" item="utmSource" open="(" close=")" separator=",">
                    #{utmSource}
                </foreach>
            </if>

            <if test='condition.utmSourceInclude != null and condition.utmSourceInclude == false'>
                AND (rus.utm_source NOT IN
                <foreach collection="condition.utmSourceList" item="utmSource" open="(" close=")" separator=",">
                    #{utmSource}
                </foreach>
                OR rus.utm_source is null)
            </if>
        </if>

        -- 排除维修订单
        AND o.is_repair = false
        GROUP BY o.id
        ORDER BY o.id DESC
    </select>

    <!-- 根据多条件查询订单ID集合 -->
    <select id="listOrderIdByComplexConditions" resultType="java.lang.Integer"
            parameterType="com.insta360.store.business.order.dto.AdminOrderQueryBO">
        select o1.id from `order` o1
        join order_item o2 on o1.id = o2.`order`
        join order_payment o3 on o1.id = o3.`order`
        left join rma_order o4 on o1.id = o4.order_id
        <where>
            <if test="shipPriority != null">
                and ship_priority = #{shipPriority}
            </if>
            <if test="orderState != null">
                and o1.state = #{orderState}
            </if>
            <if test="countryList != null">
                and o1.area in
                <foreach collection="countryList" item="country" open="(" close=")" separator=",">
                    #{country}
                </foreach>
            </if>
            <if test="noCountryList != null">
                and o1.area not in
                <foreach collection="noCountryList" item="country" open="(" close=")" separator=",">
                    #{country}
                </foreach>
            </if>
            <if test="fromTime != null and endTime != null">
                and o3.pay_time between #{fromTime} and #{endTime}
            </if>
            <if test="productIdList != null">
                and o2.product in
                <foreach collection="productIdList" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="rmaStateList != null">
                and o4.state in
                <foreach collection="rmaStateList" item="rmaState" open="(" close=")" separator=",">
                    #{rmaState}
                </foreach>
            </if>
            <if test="rmaType != null">
                and o4.rma_type = #{rmaType}
            </if>
            <if test="paymentChannelList != null">
                and o3.channel in
                <foreach collection="paymentChannelList" item="channel" open="(" close=")" separator=",">
                    #{channel}
                </foreach>
            </if>
            <if test="noPaymentChannelList != null">
                and o3.channel not in
                <foreach collection="noPaymentChannelList" item="channel" open="(" close=")" separator=",">
                    #{channel}
                </foreach>
            </if>
            <if test="paymentState != null">
                and o3.state = #{paymentState}
            </if>
                and o1.is_repair = false
        </where>
        GROUP BY o1.id
        order by o1.ship_priority desc,o1.id desc
    </select>

    <!-- 根据订单ID分页查询 -->
    <select id="listOrderByPage" resultType="com.insta360.store.business.order.model.Order">
        select * from `order`
        <where>
            <if test="orderIdList != null">
                and id in
                <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
        order by ship_priority desc,id desc
    </select>

    <!-- 根据订单ID批量更新订单状态 -->
    <update id="batchUpdateOrderState" >
        update `order`
            <set>
                <if test="orderState != null">
                    state = #{orderState}
                </if>
            </set>
        where id in
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </update>

    <select id="queryByTradeCodeAndUtmSource" resultType="com.insta360.store.business.order.model.Order">
        SELECT
        o.*
        FROM
        `order` AS o
        <if test='condition.containsLink != null and condition.containsLink'>
            JOIN reseller_order as ro on o.order_number = ro.order_number
        </if>
        <if test="utmSourceList != null and utmSourceList.size() != 0">
           LEFT JOIN reseller_utm_source as rus ON rus.order_number = o.order_number
        </if>
        where 1 = 1
            <if test="tradeCode != null and tradeCode != '' ">
                AND (o.promo_code = #{tradeCode} or o.coupon_code = #{tradeCode} or o.gift_card_code = #{tradeCode})
            </if>

            -- 关联utm_source表
            <if test="condition.joinUtmSource != null and condition.joinUtmSource == true">
                <if test='condition.utmSourceInclude != null and condition.utmSourceInclude == true'>
                    AND rus.utm_source IN
                    <foreach collection="condition.utmSourceList" item="utmSource" open="(" close=")" separator=",">
                        #{utmSource}
                    </foreach>
                </if>

                <if test='condition.utmSourceInclude != null and condition.utmSourceInclude == false'>
                    AND (rus.utm_source NOT IN
                    <foreach collection="condition.utmSourceList" item="utmSource" open="(" close=")" separator=",">
                        #{utmSource}
                    </foreach>
                    OR rus.utm_source is null)
                </if>
            </if>
        order by o.create_time desc
    </select>
</mapper>
