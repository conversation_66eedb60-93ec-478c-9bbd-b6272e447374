package com.insta360.store.business.order.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.enums.OrderPushBlockReasonTypeEnum;
import com.insta360.store.business.order.bo.NccOrderCheckBO;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.constant.OrderAutoPushConstantPool;
import com.insta360.store.business.order.service.impl.helper.OrderPushAutoHelper;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductCategorySubset;
import com.insta360.store.business.product.service.ProductCategorySubsetService;
import com.insta360.store.business.product.service.ProductService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 针对台湾地区NCC新规新增推单过滤条件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/19
 */
@Component
public class TwNccOrderCheckChain extends BaseOrderPushAutoCheckChain {

    public static final Logger LOGGER = LoggerFactory.getLogger(TwNccOrderCheckChain.class);

    @Autowired
    ProductService productService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    OrderPushAutoHelper orderPushAutoHelper;

    @Autowired
    ProductCategorySubsetService productCategorySubsetService;

    /**
     * 执行订单自动推送条件检查
     * <p>
     * 逻辑说明：
     * 1. 获取订单项关联的商品和产品信息
     * 2. 构建商品分类子集映射表
     * 3. 生成检查业务对象列表（NccOrderCheckBO）
     * 4. 统计三类合规数量之和
     * 5. 判断总数量是否小于系统限制阈值
     *
     * @param order 待检查的订单对象
     * @return true: 满足自动推送条件（总数量未达限制）; false: 不满足条件
     */
    @Override
    public boolean doCheck(Order order) {
        LOGGER.info("[台湾NCC新规]开始执行新增推单过滤条件检查 orderNumber:{}", order.getOrderNumber());
        InstaCountry country = InstaCountry.parse(order.getArea());
        if (!InstaCountry.TW.equals(country)) {
            LOGGER.info("[台湾NCC新规]订单地区非台湾，跳过检查 orderNumber:{}", order.getOrderNumber());
            return true;
        }

        // 步骤一：数据准备阶段
        // 获取订单项对应的商品基本信息
        List<OrderItem> orderItems = orderItemService.listOrderItemsByOrderId(order.getId());
        List<Integer> commodityIds = orderItems.stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        Map<Integer, Commodity> commodityMap = commodityService.listCommodities(commodityIds).stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));

        // 获取商品对应的产品信息
        List<Integer> productIds = commodityMap.values().stream().map(Commodity::getProduct).collect(Collectors.toList());
        Collection<Product> products = productService.listByIds(productIds);
        Map<Integer, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        List<String> categoryKeyList = products.stream().map(Product::getCategoryKey).collect(Collectors.toList());
        List<ProductCategorySubset> productCategorySubsetList = productCategorySubsetService.listByCategorySubsetKeys(categoryKeyList);

        // 末级类目与一级类目的映射
        Map<String, String> categoryMap = productCategorySubsetList.stream().collect(Collectors.toMap(ProductCategorySubset::getCategorySubsetKey, ProductCategorySubset::getCategoryMainKey));

        // 步骤二：业务对象构建阶段
        List<NccOrderCheckBO> checkBoList = orderItems.stream().map(oi -> createCheckBO(oi, commodityMap, productMap, categoryMap)).filter(Objects::nonNull).collect(Collectors.toList());
        LOGGER.info("[台湾NCC新规]新增推单过滤条件检查结果 orderNumber:{}, checkBoList:{}", order.getOrderNumber(), JSON.toJSONString(checkBoList));

        // 步骤三：合规数量统计阶段
        // 统计三类合规条件的数量总和
        int accessoriesCount = calculateAccessoriesCount(checkBoList);
        int subsetCategoryCount = calculateSubsetCategoryCount(checkBoList);
        int mainCategoryCount = calculateMainCategoryCount(checkBoList);
        LOGGER.info("[台湾NCC新规]新增推单过滤条件统计结果 accessoriesCount:{}, subsetCategoryCount:{}, mainCategoryCount:{}", accessoriesCount, subsetCategoryCount, mainCategoryCount);

        // 判断总数量是否小于系统限制（当前限制值见常量池配置）
        boolean isAutoPush = (accessoriesCount + subsetCategoryCount + mainCategoryCount) <= OrderAutoPushConstantPool.NCC_NUMBER_LIMIT;
        LOGGER.info("[台湾NCC新规]新增推单过滤条件检查结果 orderNumber:{}, isAutoPush:{}", order.getOrderNumber(), isAutoPush);
        if (isAutoPush) {
            return true;
        }
        orderPushAutoHelper.addInfoAndLog(OrderPushBlockReasonTypeEnum.NCC_ORDER, null, order.getOrderNumber(), order.getId());
        return false;
    }

    /**
     * 创建单个检查业务对象
     *
     * @param orderItem
     * @param commodityMap
     * @param productMap
     * @param categoryMap
     * @return
     */
    private NccOrderCheckBO createCheckBO(OrderItem orderItem, Map<Integer, Commodity> commodityMap, Map<Integer, Product> productMap, Map<String, String> categoryMap) {
        NccOrderCheckBO bo = new NccOrderCheckBO();
        bo.setCommodityId(orderItem.getCommodity());
        bo.setProductId(orderItem.getProduct());
        bo.setNum(orderItem.getNumber());

        // 通过商品→产品的级联查询获取分类信息
        Product product = Optional.ofNullable(commodityMap.get(orderItem.getCommodity())).map(Commodity::getProduct).map(productMap::get).orElse(null);
        if (product == null) {
            return null;
        }

        String subsetCategory = product.getCategoryKey();
        bo.setCategorySubset(subsetCategory);

        String mainCategory = Optional.ofNullable(categoryMap).map(category -> category.get(subsetCategory)).orElse(subsetCategory);
        bo.setCategoryMain(mainCategory);
        LOGGER.info("[台湾NCC新规]新增推单过滤条件创建业务对象 commodityId:{}, bo:{}", orderItem.getCommodity(), JSON.toJSONString(bo));
        return bo;
    }

    /**
     * 统计配件合规数量（根据预设商品ID列表）
     *
     * @param checkBOList
     * @return int
     */
    private int calculateAccessoriesCount(List<NccOrderCheckBO> checkBOList) {
        return checkBOList.stream().filter(bo -> OrderAutoPushConstantPool.NCC_COMMODITY_ID.contains(bo.getCommodityId())).mapToInt(NccOrderCheckBO::getNum).sum();
    }

    /**
     * 统计子类合规数量（根据预设子类列表）
     *
     * @param checkBOList
     * @return int
     */
    private int calculateSubsetCategoryCount(List<NccOrderCheckBO> checkBOList) {
        return checkBOList.stream().filter(bo -> OrderAutoPushConstantPool.NCC_CATEGORY.contains(bo.getCategorySubset())).mapToInt(NccOrderCheckBO::getNum).sum();
    }

    /**
     * 统计主类合规数量（手持稳定器主类）
     *
     * @param checkBOList
     * @return int
     */
    private int calculateMainCategoryCount(List<NccOrderCheckBO> checkBOList) {
        return checkBOList.stream().filter(bo -> ProductCategoryMainType.CM_HAND_STABILIZER.name().equals(bo.getCategoryMain())).mapToInt(NccOrderCheckBO::getNum).sum();
    }
}
