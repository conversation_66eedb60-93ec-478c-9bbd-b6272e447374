<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

<!--    <localRepository>/home/<USER>/repo/maven/m2</localRepository>-->
    <pluginGroups>
    </pluginGroups>
    <servers>
        <server>
            <id>insta-repository-public</id>
            <username>backend</username>
            <password>Kokiakiko123</password>
        </server>
        <server>
            <id>insta-repository-release</id>
            <username>backend</username>
            <password>Kokiakiko123</password>
        </server>
        <server>
            <id>insta-repository-snapshots</id>
            <username>backend</username>
            <password>Kokiakiko123</password>
        </server>
    </servers>
    <mirrors>
        <mirror>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
    </mirrors>
</settings>
