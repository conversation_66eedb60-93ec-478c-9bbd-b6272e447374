package com.insta360.store.service.controller.review.vo;

import com.insta360.compass.core.util.BeanUtil;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2022/07/05
 * @Description:
 */
public class ReviewVO implements Serializable {

    /**
     * 评论编号
     */
    private String reviewNumber;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 评论标题
     */
    private String reviewTitle;

    /**
     * 评论内容
     */
    private String reviewContent;

    /**
     * 评论回复
     */
    private String reviewReply;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 评分
     */
    private Integer reviewRate;

    /**
     * 点赞数量
     */
    private Integer likeCount;

    /**
     * 是否置顶
     */
    private Boolean topTag;

    /**
     * 评论状态
     */
    private Integer reviewState;

    /**
     * 评论时间
     */
    private LocalDateTime reviewTime;

    /**
     * 评论资源列表
     */
    private List<ReviewResourceVO> reviewResourceList;

    public ReviewVO() {
    }

    public ReviewVO(ReviewBO reviewBo) {
        if (reviewBo != null) {
            BeanUtil.copyProperties(reviewBo, this);
        }
    }

    public String getReviewNumber() {
        return reviewNumber;
    }

    public void setReviewNumber(String reviewNumber) {
        this.reviewNumber = reviewNumber;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getReviewTitle() {
        return reviewTitle;
    }

    public void setReviewTitle(String reviewTitle) {
        this.reviewTitle = reviewTitle;
    }

    public String getReviewContent() {
        return reviewContent;
    }

    public void setReviewContent(String reviewContent) {
        this.reviewContent = reviewContent;
    }

    public String getReviewReply() {
        return reviewReply;
    }

    public void setReviewReply(String reviewReply) {
        this.reviewReply = reviewReply;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Integer getReviewRate() {
        return reviewRate;
    }

    public void setReviewRate(Integer reviewRate) {
        this.reviewRate = reviewRate;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Boolean getTopTag() {
        return topTag;
    }

    public void setTopTag(Boolean topTag) {
        this.topTag = topTag;
    }

    public Integer getReviewState() {
        return reviewState;
    }

    public void setReviewState(Integer reviewState) {
        this.reviewState = reviewState;
    }

    public LocalDateTime getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }

    public List<ReviewResourceVO> getReviewResourceList() {
        return reviewResourceList;
    }

    public void setReviewResourceList(List<ReviewResourceVO> reviewResourceList) {
        this.reviewResourceList = reviewResourceList;
    }

    @Override
    public String toString() {
        return "ReviewVO{" +
                ", reviewNumber='" + reviewNumber + '\'' +
                ", nickName='" + nickName + '\'' +
                ", reviewTitle='" + reviewTitle + '\'' +
                ", reviewContent='" + reviewContent + '\'' +
                ", reviewReply='" + reviewReply + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", reviewRate=" + reviewRate +
                ", likeCount=" + likeCount +
                ", topTag=" + topTag +
                ", reviewState=" + reviewState +
                ", reviewTime=" + reviewTime +
                ", reviewResourceList=" + reviewResourceList +
                '}';
    }

}
