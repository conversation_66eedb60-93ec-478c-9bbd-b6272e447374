package com.insta360.store.business.payment.service.impl.handler;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.ThreadUtil;
import com.insta360.store.business.cloud.constant.CloudSubscribeTextConstant;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.configuration.utils.ShaUtil;
import com.insta360.store.business.exception.RetryHandlerException;
import com.insta360.store.business.forter.enums.ForterSyncOrderStatusType;
import com.insta360.store.business.integration.checkout.lib.response.CreateGetCkoOrderDetailResponse;
import com.insta360.store.business.integration.checkout.service.CkoPaymentSyncService;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.outgoing.mq.check.bo.PaymentParamCheckBO;
import com.insta360.store.business.outgoing.mq.check.helper.DoubleCheckSendHelper;
import com.insta360.store.business.payment.bo.*;
import com.insta360.store.business.payment.constants.CkoPaymentConstant;
import com.insta360.store.business.payment.context.PaymentResultDataContext;
import com.insta360.store.business.payment.enums.PaymentReconciliationStatus;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.enums.forter.claims.ForterClaimsStatus;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.checkout.CheckoutConfig;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentExemption;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentRequestEnum;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentStatusEnum;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentTypeEnum;
import com.insta360.store.business.payment.lib.checkout.model.*;
import com.insta360.store.business.payment.lib.checkout.request.BaseCkoPaymentRequest;
import com.insta360.store.business.payment.lib.checkout.response.CreateCkoCreatePaymentResponse;
import com.insta360.store.business.payment.lib.checkout.response.CreateCkoPaymentNotifyResponse;
import com.insta360.store.business.payment.service.impl.helper.CkoPaymentHelper;
import com.insta360.store.business.trade.enums.CreditCardPaymentActionEnum;
import com.insta360.store.business.trade.exception.TradeErrorCode;
import com.insta360.store.business.trade.model.CreditCardPayChannel;
import com.insta360.store.business.trade.model.CreditCardPaymentAuthResult;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.service.CreditCardPayChannelService;
import com.insta360.store.business.user.model.StoreAccount;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2021/11/15
 * @Description:
 */
public abstract class BaseCkoPaymentHandler extends BasePaymentHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseCkoPaymentHandler.class);

    /**
     * CKO默认延迟对账时间（checkout的延迟回传一般出现在3DS交易情况下，给上5分钟）
     */
    private static final Long CKO_DEFALUT_DELAY_TIME = 300000L;

    @Autowired
    CkoPaymentSyncService ckoPaymentSyncService;

    @Autowired
    CreditCardPayChannelService creditCardPayChannelService;

    @Autowired
    DoubleCheckSendHelper doubleCheckSendHelper;

    @Autowired
    CkoPaymentHelper ckoPaymentHelper;

    @Override
    protected <T> T _payOrder(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        return (T) preHandlePaymentResult(getPaymentRequest(paymentInfo, paymentExtra), paymentExtra);
    }

    /**
     * 前置请求支付结果处理
     *
     * @param request
     * @param paymentExtra
     */
    protected <T> T preHandlePaymentResult(BaseCkoPaymentRequest request, PaymentExtra paymentExtra) {
        CreateCkoCreatePaymentResponse paymentResponse = this.parsePaymentResponseWithKey(request);
        String paymentResult = doPreHandlerPaymentResult(request, paymentResponse);
        if (StringUtil.isNotBlank(paymentResult)) {
            return (T) paymentResult;
        }

        // 信用卡订单支付的额外信息（走异步），3D订单等待回传再记录
        ThreadUtil.execute(() -> this.savePaymentInfo(paymentResponse));

        // 返回订单号，供页面手动redirect
        return (T) request.getReference();
    }

    /**
     * 处理前置支付结果
     *
     * @param request
     * @param paymentResponse
     * @return
     */
    protected String doPreHandlerPaymentResult(BaseCkoPaymentRequest request, CreateCkoCreatePaymentResponse paymentResponse) {
        // 发送交易校验监控事件
        OrderPaymentCheckBO orderPaymentCheckParam = new OrderPaymentCheckBO();
        orderPaymentCheckParam.setDelayTime(this.getTradeCheckDelayTime());
        orderPaymentCheckParam.setPayId(paymentResponse.getId());
        orderPaymentCheckParam.setOrderNumber(request.getReference());
        orderPaymentCheckParam.setPaymentChannel(this.getPaymentChannel());
        orderMessageSendHelper.sendOrderPaymentCheckMonitorMessage(orderPaymentCheckParam);

        // 3ds支付
        String payUrl = this.isThreeDS(paymentResponse);
        if (payUrl != null) {
            LOGGER.info("Is 3d or apm payment request order. pay_url:" + payUrl + ", requestParam:" + request);
            return payUrl;
        }

        // 处理支付结果（checkout订单依赖前置支付结果进行订单状态的转换）
        this.processOrderPayed(paymentResponse.getStatus(), paymentResponse.getResponseCode(), paymentResponse.getResponseSummary(),
                paymentResponse.getReference(), paymentResponse.getId(), PaymentTradeSecurityType.HAVE_NOT_CAPTURE);

        // 金额对账校验
        try {
            Integer requestAmount = request.getAmount();
            Integer authorizedAmount = paymentResponse.getAmount();
            if (requestAmount.compareTo(authorizedAmount) != 0) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[发起交易时支付对账]checkout 支付结果响应金额与订单金额不一致。result:{%s}", paymentResponse),
                        FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        } catch (Exception e) {
            LOGGER.error("[发起交易时支付对账]cko 金额比对失败。error message:{}", e.getMessage());
            FeiShuMessageUtil.storeGeneralMessage(String.format("[发起交易时支付对账]cko 金额比对失败。paymentResult:{%s}，error message:{%s}",
                    paymentResponse, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        return null;
    }

    /**
     * 处理请求结果(幂等)
     *
     * @param request
     * @return
     */
    protected CreateCkoCreatePaymentResponse parsePaymentResponseWithKey(BaseCkoPaymentRequest request) {
        String result = request.executePostWithKey(this.getCheckoutConfigInfo());
        LOGGER.info("Credit card payment response info : " + result + "，order_number:" + request.getReference());
        return CreateCkoCreatePaymentResponse.parse(result);
    }

    /**
     * 处理请求结果
     *
     * @param request
     * @return
     */
    protected CreateCkoCreatePaymentResponse parsePaymentResponse(BaseCkoPaymentRequest request) {
        String result = request.executePost(this.getCheckoutConfigInfo());
        LOGGER.info("Credit card payment response info : " + result + "，order_number:" + request.getReference());
        return CreateCkoCreatePaymentResponse.parse(result);
    }

    /**
     * 判断是否3DS支付
     *
     * @param paymentResponse
     * @return
     */
    protected String isThreeDS(CreateCkoCreatePaymentResponse paymentResponse) {
        // 状态返回值判断 && 3D/本土支付 验证链接判断
        if (CkoPaymentStatusEnum.PENDING.getName().equals(paymentResponse.getStatus()) && paymentResponse.getLinks() != null && paymentResponse.getLinks().getRedirect() != null) {
            return paymentResponse.getLinks().getRedirect().getHref();
        }
        return null;
    }

    /**
     * 获取支付请求参数
     *
     * @param paymentInfo
     * @param paymentExtra
     * @return
     */
    protected BaseCkoPaymentRequest getPaymentRequest(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        return buildPaymentRequest(paymentInfo, paymentExtra);
    }

    /**
     * 获取CKO支付配置信息
     *
     * @return
     */
    abstract protected CheckoutConfig getCheckoutConfigInfo();

    /**
     * 构造支付请求参数（异步通知接口配置在cko的后台notification）
     *
     * @param paymentInfo
     * @param paymentExtra
     * @return
     */
    protected BaseCkoPaymentRequest buildPaymentRequest(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        BaseCkoPaymentRequest request = this.newPaymentRequest();
        paymentExtra.setOrder(order);
        request.setCapture(true);
        request.setAmount(getOrderAmount(paymentInfo));
        request.setDescription("insta360");
        request.setReference(paymentInfo.orderNumber);
        request.setCurrency(paymentInfo.currency.name());
        request.setPaymentIp(paymentExtra.getClientIP());
        request.setCustomer(getCustomerInfo(paymentInfo));
        request.setShipping(getShippingInfo());
        request.setSource(getSourceInfo(paymentExtra));
        request.setPaymentType(CkoPaymentRequestEnum.REGULAR.getName());
        request.setThreeDomainSecure(getThreeDomainSecureInfo(paymentExtra));
        request.setSuccessUrl(getReturnUrl());
        request.setFailureUrl(getReturnUrl());
        request.setProcessingChannelId(getProcessingChannel(paymentExtra.getPaymentChannelId()));
        request.setMetadata(getMetadata());
        return request;
    }

    /**
     * 获取 Payment Request
     *
     * @return
     */
    abstract protected BaseCkoPaymentRequest newPaymentRequest();

    /**
     * 是否需要进行交易对账
     *
     * @return
     */
    abstract protected Boolean isNeedPaymentCheck();

    /**
     * 获取解密后的卡号
     *
     * @param cardNumberSign 加密后的卡号
     * @return
     */
    protected String getCreditCardNumber(String cardNumberSign) {
        throw new IllegalArgumentException();
    }

    /**
     * 获取解密后的卡cvv
     *
     * @param cardCvvSign 加密后的卡cvv
     * @return
     */
    protected String getCreditCardCvv(String cardCvvSign) {
        throw new IllegalArgumentException();
    }

    /**
     * 处理支付结果
     *
     * @param paymentResult
     * @param handleResult
     */
    @Override
    public void _handlePaymentResult(Object paymentResult, PaymentHandleResult handleResult) {
        CreateCkoPaymentNotifyResponse notifyResponse = (CreateCkoPaymentNotifyResponse) paymentResult;
        Data responseData = notifyResponse.getData();
        if (responseData == null) {
            return;
        }

        // 商城订单号
        String orderNumber = responseData.getReference();

        // 支持的回传类型
        CkoPaymentTypeEnum paymentType = CkoPaymentTypeEnum.parse(notifyResponse.getType());
        if (paymentType == null) {
            return;
        }

        // 拒付类型走拒付通道
        if (paymentType.getDispute()) {
            // 记录拒付订单号
            orderNumber = responseData.getPaymentReference();

            // 设置为拒付
            orderPaymentService.setPaymentChargeBack(orderNumber, getPaymentChannel());
            this.ckoOrderChargeBackSyncForter(notifyResponse);
        }

        switch (paymentType) {
            case PAYMENT_CAPTURED:
                // 支付结果处理（支付交易捕获）
                this.ckoOrderCaptureProcessor(orderNumber, responseData.getId(), responseData.getResponseCode(), responseData.getResponseSummary());
                break;
            case PAYMENT_EXPIRED:
                // 支付超时订阅通知（未经过3D校验）
                this.ckoOrderPaymentExpiredSyncForter(orderNumber);
                break;
            case CARD_VERIFIED:
                this.ckoCardVerifiedProcessor(responseData);
                break;
            case PAYMENT_CAPTURE_DECLINED:
                this.ckoCaptureDeclinedProcessor(responseData);
                break;
            default:
                break;
        }

        // 记录支付信息
        PaymentResultDataContext paymentResultContext = getPaymentResultContext();
        paymentResultContext.setOrderNumber(orderNumber);
        paymentResultContext.setResultData(JSONObject.toJSONString(notifyResponse));
        paymentResultContext.setPaymentChannel(this.getPaymentChannel());
    }

    /**
     * capture失败处理
     *
     * @param responseData
     */
    protected void ckoCaptureDeclinedProcessor(Data responseData) {
        String captureDeclineMsg = String.format(CkoPaymentConstant.CAPTURE_DECLINE_MSG, responseData.getReference(),
                responseData.getId(), responseData.getResponseCode(), responseData.getResponseSummary());
        FeiShuMessageUtil.storeGeneralMessage(captureDeclineMsg, FeiShuGroupRobot.PaymentInfo, FeiShuAtUser.TW,
                FeiShuAtUser.ZXJ, FeiShuAtUser.LCX, FeiShuAtUser.CYJ);
    }

    /**
     * 卡校验事件处理
     *
     * @param responseData
     */
    protected void ckoCardVerifiedProcessor(Data responseData) {
    }

    @Override
    public Long getTradeCheckDelayTime() {
        return CKO_DEFALUT_DELAY_TIME;
    }

    @Override
    public void handlePaymentResultCheck(OrderPaymentCheckBO orderPaymentCheckParam) {
        LOGGER.info("触发cko交易核对流程。参数:{}", orderPaymentCheckParam);
        String orderNumber = orderPaymentCheckParam.getOrderNumber();
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            LOGGER.error("触发钱海交易核对异常。订单不存在");
            return;
        }

        String payId = orderPaymentCheckParam.getPayId();
        if (StringUtil.isBlank(payId)) {
            LOGGER.error("触发CKO交易核对异常。支付id不存在。订单号:{}", orderNumber);
            return;
        }

        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (!OrderPaymentState.PAYMENT_PENDING.equals(orderPayment.paymentState())) {
            LOGGER.info("触发CKO交易核对异常。交易状态不是【交易中】，不予进行交易核对。");
            return;
        }

        CreateGetCkoOrderDetailResponse paymentDetailResponse;
        try {
            paymentDetailResponse = ckoPaymentSyncService.getCkoOrderStateResponse(getCheckoutConfigInfo(), orderNumber, payId);
        } catch (Exception e) {
            LOGGER.error(String.format("触发CKO交易核对异常。原因：{%s}。订单号：{%s}。payId：{%s}", e.getMessage(), orderNumber, payId), e);
            // 用于外层捕获请求超时exception
            throw e;
        }

        LOGGER.info("cko 交易对账响应：{}", paymentDetailResponse.toString());
        ckoPaymentHelper.handleCkoPaymentResultCheck(paymentDetailResponse, orderPayment, this.getPaymentChannel());
    }

    /**
     * 支付结果校验处理
     *
     * @param paymentDetailResponse
     * @param orderPayment
     */
    public void doHandlePaymentResultCheck(CreateGetCkoOrderDetailResponse paymentDetailResponse, OrderPayment orderPayment) {
        String payId = paymentDetailResponse.getId();
        String orderNumber = paymentDetailResponse.getReference();
        String paymentStatus = paymentDetailResponse.getStatus();
        String responseCode = CkoPaymentConstant.DEFAULT_RESPONSE_CODE;
        String responseSummary = CkoPaymentConstant.DEFAULT_RESPONSE_SUMMARY;

        // 非已捕获异常不做交易结果处理
        if (!CkoPaymentStatusEnum.CAPTURED.getName().equals(paymentStatus)) {
            return;
        }
        try {
            // 交易金额对比
            Integer captureAmount = paymentDetailResponse.getAmount();
            float amount = orderPaymentCounter.count(orderPayment);
            Integer payAmount = CkoPaymentConstant.ORIGIN_CURRENCY.contains(orderPayment.currency()) ? (int) amount : Math.round(amount * 100);
            if (payAmount.compareTo(captureAmount) != 0) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("【交易中对账】checkout 交易对账 支付结果响应金额与订单金额不一致。result:{%s}", paymentDetailResponse),
                        FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        } catch (Exception e) {
            LOGGER.error("[发起交易时支付对账]cko 金额比对失败。error message:{}", e.getMessage());
            FeiShuMessageUtil.storeGeneralMessage(String.format("【交易中对账】cko 金额比对失败。paymentResult:{%s}，error message:{%s}",
                    paymentDetailResponse, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }

        // 处理支付结果（checkout3D订单依赖后置支付结果进行订单状态的转换）
        this.processOrderPayed(paymentStatus, responseCode, responseSummary, orderNumber, payId, PaymentTradeSecurityType.SECURITY);

        // 交易信息的记录
        CreateCkoCreatePaymentResponse paymentResponse = new CreateCkoCreatePaymentResponse();
        paymentResponse.setStatus(paymentStatus);
        paymentResponse.setReference(orderNumber);
        paymentResponse.setAuthType(paymentStatus + "（交易对账）");
        paymentResponse.setResponseCode(responseCode);
        paymentResponse.setResponseSummary(responseSummary);
        paymentResponse.setId(paymentDetailResponse.getId());
        this.savePaymentInfo(paymentResponse);

        // 保存capture结果
        this.saveCaptureActionResult(orderNumber, responseCode, responseSummary, CreditCardPaymentActionEnum.CAPTURE_RESULT.getAuthType() + "（交易对账）");
    }

    /**
     * 3D订单 capture 流程
     *
     * @param paymentResult
     */
    public <T> T threeDomainSecurePayment(Object paymentResult) {
        ThreeDomainSecureBO threeDomainSecure = (ThreeDomainSecureBO) paymentResult;
        LOGGER.info("3d payment result:{}, 3d payment result BO:{}", paymentResult, threeDomainSecure);

        // 获取3D订单支付结果
        CreateGetCkoOrderDetailResponse ckoOrderStateResponse = ckoPaymentSyncService.getCkoOrderStateResponse(getCheckoutConfigInfo(), threeDomainSecure.getOrderNumber(), threeDomainSecure.getSid());

        if (Objects.isNull(ckoOrderStateResponse)) {
            Order order = orderService.getByOrderNumber(threeDomainSecure.getOrderNumber());
            if (Objects.isNull(order) || OrderState.afterPayed(order.orderState())) {
                LOGGER.info(String.format("3d payment order info {%s}", order));
                return null;
            }
            // 发送3ds cko get detail失败通知
            FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.CKO_GET_DETAIL_FAIL_TEXT, threeDomainSecure.getOrderNumber()),
                    FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.ZM);
            return null;
        }

        OrderPayment orderPayment = orderPaymentService.getByOrderNumber(threeDomainSecure.getOrderNumber());
        if (orderPayment == null) {
            LOGGER.error("cko 3ds capture fail. message: order payment not found. paymentResult:{}", paymentResult);
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        // 交易金额
        try {
            Integer captureAmount = ckoOrderStateResponse.getAmount();
            float amount = orderPaymentCounter.count(orderPayment);
            Integer payAmount = CkoPaymentConstant.ORIGIN_CURRENCY.contains(orderPayment.currency()) ? (int) amount : Math.round(amount * 100);
            if (payAmount.compareTo(captureAmount) != 0) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[发起交易时支付对账]checkout 3ds 支付结果响应金额与订单金额不一致。result:{%s}", ckoOrderStateResponse),
                        FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        } catch (Exception e) {
            LOGGER.error("[发起交易时支付对账]cko 金额比对失败。error message:{}", e.getMessage());
            FeiShuMessageUtil.storeGeneralMessage(String.format("[发起交易时支付对账]cko 金额比对失败。paymentResult:{%s}，error message:{%s}",
                    ckoOrderStateResponse, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }

        // 信用卡订单支付的额外信息
        String authType = null;
        String responseCode = null;
        String responseSummary = null;

        // 交易类型
        String paymentStatus = ckoOrderStateResponse.getStatus();
        if (CkoPaymentStatusEnum.isAuthSuccess(paymentStatus)) {
            LOGGER.info("3d check it success.");
            authType = paymentStatus;
            responseCode = CkoPaymentConstant.DEFAULT_RESPONSE_CODE;
            responseSummary = CkoPaymentConstant.DEFAULT_RESPONSE_SUMMARY;
        } else {
            List<Actions> actions = ckoOrderStateResponse.getActions();
            LOGGER.info("3d check it failure. actions:{}", actions);
            if (CollectionUtils.isNotEmpty(actions)) {
                // 如果是其它的类型，则关注第一个Authorization即可
                for (Actions action : actions) {
                    if ("Authorization".equals(action.getType())) {
                        authType = action.getType();
                        responseCode = action.getResponseCode();
                        responseSummary = action.getResponseSummary();
                        break;
                    }
                }
            } else {
                String errorStr = "fail to pass 3ds check";
                authType = errorStr;
                responseSummary = errorStr;
            }
        }

        // 处理支付结果（checkout3D订单依赖后置支付结果进行订单状态的转换）
        this.processOrderPayed(paymentStatus, responseCode, responseSummary, threeDomainSecure.getOrderNumber(),
                ckoOrderStateResponse.getId(), PaymentTradeSecurityType.SECURITY);

        // 记录支付信息
        PaymentResultDataContext paymentResultContext = getPaymentResultContext();
        paymentResultContext.setOrderNumber(threeDomainSecure.getOrderNumber());
        paymentResultContext.setResultData(JSONObject.toJSONString(ckoOrderStateResponse));
        paymentResultContext.setPaymentChannel(this.getPaymentChannel());

        // 交易信息的记录
        CreateCkoCreatePaymentResponse paymentResponse = new CreateCkoCreatePaymentResponse();
        paymentResponse.setStatus(paymentStatus);
        paymentResponse.setAuthType(authType);
        paymentResponse.setResponseCode(responseCode);
        paymentResponse.setResponseSummary(responseSummary);
        paymentResponse.setId(ckoOrderStateResponse.getId());
        paymentResponse.setReference(threeDomainSecure.getOrderNumber());
        this.savePaymentInfo(paymentResponse);
        return null;
    }

    @Override
    public Boolean isSignatureValid(Object signObject) {
        PaymentSignatureValidBO paymentSignatureValid = (PaymentSignatureValidBO) signObject;
        HttpServletRequest request = paymentSignatureValid.getRequest();
        String ckoSignValue = paymentSignatureValid.getCkoSignValue();

        // 优先验证 authorization 信息
        CheckoutConfig checkoutConfigInfo = getCheckoutConfigInfo();
        String authorization = request.getHeader(CkoPaymentConstant.CKO_AUTHORIZATION_HEADER);
        if (!checkoutConfigInfo.getAuthorizationHeader().equals(authorization)) {
            LOGGER.info("checkout支付结果回传authorization验证失败。authorization：{}", authorization);
            return false;
        }
        return ShaUtil.getSignVale(ckoSignValue, checkoutConfigInfo.getSignatureKey()).equalsIgnoreCase(request.getHeader(CkoPaymentConstant.CKO_SIGNATURE_HEADER));
    }

    /**
     * Capture回传处理
     *
     * @param orderNumber
     * @param payId
     * @param responseCode
     * @param responseSummary
     */
    protected void ckoOrderCaptureProcessor(String orderNumber, String payId, String responseCode, String responseSummary) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            return;
        }

        // 给支付打上标签，收到capture后，修改为安全的交易（已经修改过不用重新修改）
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        PaymentTradeSecurityType securityType = PaymentTradeSecurityType.SECURITY;
        if (!securityType.equals(orderPayment.tradeSecurityType())) {
            orderPayment.setTradeSecurity(securityType.getCode());
            orderPaymentService.updateById(orderPayment);
        }

        // 兜底支付成功状态未改变场景
        if (!OrderPaymentState.PAYED.equals(orderPayment.paymentState())) {
            DoubleCheckBO doubleCheckBo = new DoubleCheckBO();
            doubleCheckBo.setBusinessId(order.getId());
            doubleCheckBo.setCheckType(DoubleCheckEnum.PaymentCkoCaptureCheck);
            PaymentParamCheckBO paymentParamCheckBo = new PaymentParamCheckBO();
            paymentParamCheckBo.setPayId(payId);
            paymentParamCheckBo.setPaymentChannel(getPaymentChannel());
            doubleCheckBo.setPaymentParamCheckBo(paymentParamCheckBo);
            doubleCheckSendHelper.sendDoubleCheckMessage(doubleCheckBo);
        }

        // 保存capture结果
        this.saveCaptureActionResult(orderNumber, responseCode, responseSummary, CreditCardPaymentActionEnum.CAPTURE_RESULT.getAuthType());
    }

    /**
     * 保存cko capture结果
     *
     * @param orderNumber
     * @param responseCode
     * @param responseSummary
     * @param authType
     */
    private void saveCaptureActionResult(String orderNumber, String responseCode, String responseSummary, String authType) {
        // 节点信息保存
        CreditCardPaymentAuthResult creditCardPaymentAuthResult = new CreditCardPaymentAuthResult();
        creditCardPaymentAuthResult.setOrderNumber(orderNumber);
        creditCardPaymentAuthResult.setActionType(CreditCardPaymentActionEnum.CAPTURE_RESULT.getActionName());
        creditCardPaymentAuthResult.setPayMethod(StorePaymentMethodEnum.CKO_PAYMENT.getName());
        creditCardPaymentAuthResult.setAuthType(authType);
        creditCardPaymentAuthResult.setAuthCode(responseCode);
        creditCardPaymentAuthResult.setAuthText(responseSummary);
        creditCardPaymentAuthResult.setCreateTime(LocalDateTime.now());
        creditCardPaymentAuthResult.setUpdateTime(LocalDateTime.now());
        creditCardPaymentAuthResultService.save(creditCardPaymentAuthResult);
    }

    @Override
    public PaymentReconciliationCheckResultBO handleTransactionReconciliation(PaymentReconciliationCheckBO paymentReconciliationCheckParam) {
        Order order = paymentReconciliationCheckParam.getOrder();
        if (Objects.isNull(order)) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败,订单不存在。param:{}", paymentReconciliationCheckParam);
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        OrderPayment orderPayment = paymentReconciliationCheckParam.getOrderPayment();
        if (Objects.isNull(orderPayment)) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败,订单支付信息不存在。param:{}", paymentReconciliationCheckParam);
            throw new InstaException(OrderErrorCode.OrderPaymentNotFoundException);
        }

        // 交易号
        String paymentId = orderPayment.getChannelPaymentId();
        if (StringUtil.isBlank(paymentId)) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败。交易号不存在。param:{}", paymentReconciliationCheckParam);
            throw new InstaException(PaymentErrorCode.TransactionIdDoesNotExistException);
        }

        CreateGetCkoOrderDetailResponse ckoPayOrderDetailResponse = ckoPaymentSyncService.getCkoOrderStateResponse(getCheckoutConfigInfo(), order.getOrderNumber(), paymentId);
        if (Objects.isNull(ckoPayOrderDetailResponse) || Objects.isNull(ckoPayOrderDetailResponse.getId())) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败。响应内容/交易号不存在。param:{}", ckoPayOrderDetailResponse);
            throw new RetryHandlerException("缺少响应数据");
        }

        // 金额回传校验
        Integer responseAmount = ckoPayOrderDetailResponse.getAmount();
        if (Objects.isNull(responseAmount)) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败。Amount不存在。param:{}", ckoPayOrderDetailResponse);
            throw new RetryHandlerException("缺少响应数据");
        }

        // 交易状态
        if (!CkoPaymentStatusEnum.isHandleTransactionReconciliation(ckoPayOrderDetailResponse.getStatus())) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败。非处理交易对账状态。param:{}", ckoPayOrderDetailResponse);
            throw new InstaException(PaymentErrorCode.TransactionReconciliationCheckFailedException);
        }

        return doHandleTransactionReconciliation(order.getOrderNumber(), orderPayment, ckoPayOrderDetailResponse);
    }

    /**
     * 交易对账处理
     *
     * @param orderNumber
     * @param orderPayment
     * @param ckoPayOrderDetailResponse
     * @return
     */
    private PaymentReconciliationCheckResultBO doHandleTransactionReconciliation(String orderNumber, OrderPayment orderPayment, CreateGetCkoOrderDetailResponse ckoPayOrderDetailResponse) {
        // 对账payId校验
        PaymentReconciliationStatus reconciliationStatus = PaymentReconciliationStatus.RECONCILED;
        String responsePayId = ckoPayOrderDetailResponse.getId();
        if (!orderPayment.getChannelPaymentId().equals(responsePayId)) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败。responsePayId和商城payId不一致。param:{}", responsePayId);
            FeiShuMessageUtil.storeGeneralMessage(String.format("CKO 延迟交易对账失败。responsePayId和商城payId不一致。订单号{%s}", orderNumber), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            reconciliationStatus = PaymentReconciliationStatus.ERROR;
        }

        // 交易货币校验
        if (!orderPayment.getCurrency().equals(ckoPayOrderDetailResponse.getCurrency())) {
            LOGGER.error("[支付后交易对账]CKO 延迟交易对账失败。交易货币不一致。param:{}", responsePayId);
            FeiShuMessageUtil.storeGeneralMessage(String.format("CKO 延迟交易对账失败。交易货币不一致。订单号{%s}", orderNumber), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            reconciliationStatus = PaymentReconciliationStatus.ERROR;
        }

        // 金额校验
        float payAmount = orderPaymentCounter.count(orderPayment);
        int authAmount = CkoPaymentConstant.ORIGIN_CURRENCY.contains(orderPayment.currency()) ? (int) payAmount : Math.round(payAmount * 100);
        BigDecimal ckoAuthAmount = new BigDecimal(String.valueOf(ckoPayOrderDetailResponse.getAmount()));
        BigDecimal orderPayAmount = new BigDecimal(String.valueOf(authAmount));
        if (orderPayAmount.compareTo(ckoAuthAmount) != 0) {
            LOGGER.error("[支付后交易对账]CKO 交易对账 支付结果响应金额与订单金额不一致。result:{}", ckoPayOrderDetailResponse);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[支付后交易对账] CKO 交易对账 支付结果响应金额与订单金额不一致。订单号{%s}", orderNumber),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            reconciliationStatus = PaymentReconciliationStatus.ERROR;
        }

        // 构建交易对账结果对象
        PaymentReconciliationCheckResultBO paymentReconciliationCheckResult = new PaymentReconciliationCheckResultBO();
        paymentReconciliationCheckResult.setPaymentAmount(orderPayAmount);
        paymentReconciliationCheckResult.setPaymentCurrency(orderPayment.getCurrency());
        paymentReconciliationCheckResult.setReconciliationStatus(reconciliationStatus.getCode());
        paymentReconciliationCheckResult.setActualPaymentAmount(ckoAuthAmount);
        paymentReconciliationCheckResult.setActualPaymentCurrency(ckoPayOrderDetailResponse.getCurrency());
        return paymentReconciliationCheckResult;
    }

    /**
     * 获取该比订单的支付价格。日韩价格不变，其它货币价格使用"分"进行结算。
     *
     * @param paymentInfo
     * @return
     */
    private Integer getOrderAmount(PaymentInfo paymentInfo) {
        if (CkoPaymentConstant.ORIGIN_CURRENCY.contains(paymentInfo.currency)) {
            return paymentInfo.amount.intValue();
        }
        return Math.round(paymentInfo.amount * 100);
    }

    /**
     * 信用卡信息
     *
     * @param paymentExtra
     * @return
     */
    protected Source getSourceInfo(PaymentExtra paymentExtra) {
        Source source = new Source();
        source.setNumber(this.getCreditCardNumber(paymentExtra.getCardNumber()));
        source.setCvv(this.getCreditCardCvv(paymentExtra.getCvv()));
        source.setBillingAddress(this.getBillingAddress());
        source.setType(paymentExtra.getCkoPaymentMethod());
        source.setExpiryMonth(paymentExtra.getCardMonth());
        source.setExpiryYear(paymentExtra.getCardYear());
        source.setPhone(this.getPhoneInfo());
        source.setName(paymentExtra.getName());
        return source;
    }

    /**
     * 信用卡账单地址
     *
     * @return
     */
    protected BillingAddress getBillingAddress() {
        BillingAddress ckoBillingAddress = new BillingAddress();
        BeanUtil.copyProperties(this.getBillingAddressInfo(), ckoBillingAddress);
        return ckoBillingAddress;
    }

    /**
     * 客户信息
     *
     * @param paymentInfo
     * @return
     */
    private Customer getCustomerInfo(PaymentInfo paymentInfo) {
        Customer customer = new Customer();
        customer.setEmail(paymentInfo.email);
        customer.setName(paymentInfo.firstName + paymentInfo.lastName);
        // 走rpc获取邮箱真实账户
        try {
            StoreAccount storeAccount = userAccountHelper.getStoreAccountByEmail(paymentInfo.email);
            if (storeAccount != null) {
                customer.setId(String.valueOf(storeAccount.getInstaAccount()));
            }
        } catch (Exception e) {
            LOGGER.error(String.format("rpc获取用户信息失败。原因{%s}", e.getMessage()), e);
        }
        return customer;
    }

    /**
     * 收货地址
     *
     * @return
     */
    private Shipping getShippingInfo() {
        Shipping shipping = new Shipping();
        shipping.setAddress(getAddressInfo());
        shipping.setPhone(getPhoneInfo());
        return shipping;
    }

    /**
     * 收货地址
     *
     * @return
     */
    private Address getBillingAddressInfo() {
        Address address = new Address();
        if (billingAddress == null) {
            LOGGER.info("order delivery is null");
            return address;
        }
        address.setAddressLine1(billingAddress.getAddress());
        address.setAddressLine2(billingAddress.getSubAddress());
        address.setCity(billingAddress.getCity());
        address.setCountry(billingAddress.getCountryCode());
        address.setZip(billingAddress.getZipCode());
        address.setState(billingAddress.getProvince());
        return address;
    }

    /**
     * 收货地址
     *
     * @return
     */
    private Address getAddressInfo() {
        Address address = new Address();
        if (delivery == null) {
            LOGGER.info("order delivery is null");
            return address;
        }
        address.setAddressLine1(delivery.getAddress());
        address.setAddressLine2(delivery.getSubAddress());
        address.setCity(delivery.getCity());
        address.setCountry(delivery.getCountryCode());
        address.setZip(delivery.getZipCode());
        address.setState(delivery.getProvince());
        return address;
    }

    /**
     * 手机信息
     *
     * @return
     */
    private Phone getPhoneInfo() {
        return new Phone();
    }

    /**
     * 3ds 信息
     *
     * @param paymentExtra
     * @return
     */
    protected ThreeDomainSecure getThreeDomainSecureInfo(PaymentExtra paymentExtra) {
        ThreeDomainSecure threeDomainSecure = new ThreeDomainSecure();
        // 需要走3d
        threeDomainSecure.setEnabled(paymentExtra.getThreeDomainTrade());

        // 3d豁免
        if (paymentExtra.getExemption() != null && paymentExtra.getExemption()) {
            threeDomainSecure.setEnabled(false);
            threeDomainSecure.setExemption(CkoPaymentExemption.TRANSACTION_RISK_ASSESSMENT.getName());
        }
        return threeDomainSecure;
    }

    /**
     * 获取支付通道标识
     *
     * @param paymentChannelId
     * @return
     */
    protected String getProcessingChannel(Integer paymentChannelId) {
        CreditCardPayChannel creditCardPayChannel = creditCardPayChannelService.getById(paymentChannelId);
        if (creditCardPayChannel == null || StringUtil.isBlank(creditCardPayChannel.getProcessingId())) {
            throw new InstaException(TradeErrorCode.CreditCardPayChannelNotFoundException);
        }
        return creditCardPayChannel.getProcessingId();
    }

    /**
     * 商家自定义数据
     *
     * @return
     */
    private Metadata getMetadata() {
        Metadata metadata = new Metadata();
        metadata.setPayChannel(getPaymentChannel().name());
        return metadata;
    }

    /**
     * 支付结果处理
     *
     * @param paymentStatus
     * @param responseCode
     * @param responseSummary
     * @param orderNumber
     * @param payId
     */
    private void processOrderPayed(String paymentStatus, String responseCode, String responseSummary, String orderNumber,
                                   String payId, PaymentTradeSecurityType paymentTradeSecurityType) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("支付结果处理失败，订单找不到。支付渠道[%s]", this.getPaymentChannel()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 3Ds 订单豁免失败（checkout 技术反馈，该 responseCode 往往与 Pending 一起返回，但是理论上也会出现与 Declined 一起返回的情况）
        if (CkoPaymentConstant.THREE_DS_EXEMPTION_FAIL.equals(responseCode)) {
            // 3Ds 订单豁免失败同步 forter
            this.ckoOrderPaymentExemptionFailSyncForter(order, payId, responseCode, responseSummary);
            FeiShuMessageUtil.storeGeneralMessage("checkout 触发 20154 回传。ResponseCode:" + responseCode + "，ResponseSummary:" + responseSummary, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 返回了 20154 随之会一并返回3DS跳转链接，不参与支付结果的处理，需提前return
            return;
        }

        // Pending状态下的订单不参与支付结果的处理
        CkoPaymentStatusEnum ckoPaymentStatus = CkoPaymentStatusEnum.parseName(paymentStatus);
        if (CkoPaymentStatusEnum.PENDING.equals(ckoPaymentStatus)) {
            return;
        }

        // 支付成功
        boolean success = CkoPaymentStatusEnum.isAuthSuccess(paymentStatus) && CkoPaymentConstant.SUCCESS_CODE.equals(responseCode);
        if (success) {
            // 设置为支付成功
            orderPaymentService.setPaymentSuccess(order.getOrderNumber(), getPaymentChannel(), payId, paymentTradeSecurityType);
        }
        // 支付失败
        else {
            // 设置为支付失败
            orderPaymentService.setPaymentFailure(order.getOrderNumber());
        }

        // 支付结果同步forter
        this.ckoOrderPayedSyncForter(order, payId, responseCode, responseSummary, success);
    }

    /**
     * 3Ds 订单豁免失败同步forter
     *
     * @param order
     * @param payId
     * @param responseCode
     * @param responseSummary
     */
    private void ckoOrderPaymentExemptionFailSyncForter(Order order, String payId, String responseCode, String responseSummary) {
        OrderSyncForterBO orderSyncForter = new OrderSyncForterBO();
        orderSyncForter.setOrder(order);
        orderSyncForter.setPayId(payId);
        orderSyncForter.setResponseCode(responseCode);
        orderSyncForter.setResponseSummary(responseSummary);
        orderSyncForter.setPaymentChannel(this.getPaymentChannel());
        orderSyncForter.setForterSyncOrderStatusType(ForterSyncOrderStatusType.ORDER_EXEMPTION_FAIL);
        orderMessageSendHelper.sendOrderPayedSyncForterMessage(orderSyncForter);
    }

    /**
     * checkout支付订单同步forter
     *
     * @param order
     * @param payId
     * @param responseCode
     * @param responseSummary
     * @param isPaySuccess
     */
    private void ckoOrderPayedSyncForter(Order order, String payId, String responseCode, String responseSummary, Boolean isPaySuccess) {
        // 兼容订单支付成功或失败
        ForterSyncOrderStatusType orderStatusType = isPaySuccess ? ForterSyncOrderStatusType.ORDER_PAYED : ForterSyncOrderStatusType.ORDER_PAY_FAILED;

        // 同步forter
        OrderSyncForterBO orderSyncForter = new OrderSyncForterBO();
        orderSyncForter.setOrder(order);
        orderSyncForter.setPayId(payId);
        orderSyncForter.setResponseCode(responseCode);
        orderSyncForter.setResponseSummary(responseSummary);
        orderSyncForter.setPaymentChannel(this.getPaymentChannel());
        orderSyncForter.setForterSyncOrderStatusType(orderStatusType);
        orderMessageSendHelper.sendOrderPayedSyncForterMessage(orderSyncForter);
    }

    /**
     * checkout拒付订单同步forter
     *
     * @param notifyResponse
     */
    private void ckoOrderChargeBackSyncForter(CreateCkoPaymentNotifyResponse notifyResponse) {
        Data responseData = notifyResponse.getData();

        Order order = orderService.getByOrderNumber(responseData.getPaymentReference());

        OrderChargeBackBO orderChargeBack = new OrderChargeBackBO();
        orderChargeBack.setAmount(CkoPaymentConstant.ORIGIN_CURRENCY.contains(Currency.parse(responseData.getCurrency())) ? responseData.getAmount().floatValue() : responseData.getAmount() / 100f);
        orderChargeBack.setCurrency(responseData.getCurrency());
        orderChargeBack.setPaymentChannelType(StorePaymentMethodEnum.CKO_PAYMENT);
        orderChargeBack.setOrderNumber(responseData.getPaymentReference());
        orderChargeBack.setCardType(responseData.getPaymentMethod());
        orderChargeBack.setNoticeType(notifyResponse.getType());
        orderChargeBack.setPushDate(notifyResponse.getCreatedOn());
        orderChargeBack.setPushDetail(responseData.getCategory());
        orderChargeBack.setClaimsStatus(ForterClaimsStatus.ckoParse(notifyResponse.getType()));
        orderChargeBack.setReason(responseData.getCategory());
        orderChargeBack.setReasonCode(responseData.getReasonCode());

        // 同步forter
        OrderSyncForterBO orderSyncForter = new OrderSyncForterBO();
        orderSyncForter.setOrder(order);
        orderSyncForter.setOrderChargeBack(orderChargeBack);
        orderSyncForter.setPaymentChannel(this.getPaymentChannel());
        orderSyncForter.setForterSyncOrderStatusType(ForterSyncOrderStatusType.ORDER_CHARGE_BACK);
        orderMessageSendHelper.sendOrderPayedSyncForterMessage(orderSyncForter);
    }

    /**
     * checkout支付超时同步forter
     *
     * @param orderNumber
     */
    private void ckoOrderPaymentExpiredSyncForter(String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 同步forter
        OrderSyncForterBO orderSyncForter = new OrderSyncForterBO();
        orderSyncForter.setOrder(order);
        orderSyncForter.setPaymentChannel(this.getPaymentChannel());
        orderSyncForter.setForterSyncOrderStatusType(ForterSyncOrderStatusType.ORDER_PAYMENT_EXPIRED);
        orderMessageSendHelper.sendOrderPayedSyncForterMessage(orderSyncForter);
    }

    /**
     * 保存信用卡订单支付信息（卡bin国 & AuthInfo）
     *
     * @param paymentInfo
     */
    @Override
    protected <T> void savePaymentInfo(T paymentInfo) {
        CreateCkoCreatePaymentResponse paymentResponse = (CreateCkoCreatePaymentResponse) paymentInfo;
        // 支付信息记录
        try {
            CreateGetCkoOrderDetailResponse ckoOrderStateResponse = ckoPaymentSyncService.getCkoOrderStateResponse(getCheckoutConfigInfo(), paymentResponse.getReference(), paymentResponse.getId());
            Source source = ckoOrderStateResponse.getSource();
            if (source == null) {
                return;
            }

            CreditCardPaymentInfo creditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(paymentResponse.getReference());
            if (creditCardPaymentInfo == null) {
                LOGGER.error("checkout支付结果数据记录失败。原始数据记录未找到。订单号：{}", paymentResponse.getReference());
                return;
            }

            // 支付结果
            boolean success = CkoPaymentStatusEnum.isAuthSuccess(paymentResponse.getStatus()) && CkoPaymentConstant.SUCCESS_CODE.equals(paymentResponse.getResponseCode());
            if (success) {
                creditCardPaymentInfo.setPaymentResult(true);
                creditCardPaymentInfo.setOrderPayTime(LocalDateTime.now());
            }

            // 卡Bin信息
            creditCardPaymentInfo.setCardBin(source.getIssuerCountry());
            creditCardPaymentInfo.setCreditCardType(source.getScheme());

            // 3d eci info
            creditCardPaymentInfo.setPaymentEci(ckoOrderStateResponse.getEci());

            // auth info
            creditCardPaymentInfo.setPayMethod(StorePaymentMethodEnum.CKO_PAYMENT.getName());
            creditCardPaymentInfo.setPaymentAuthCode(paymentResponse.getResponseCode());
            creditCardPaymentInfo.setPaymentAuthText(paymentResponse.getResponseSummary());
            creditCardPaymentInfo.setPaymentAuthTime(LocalDateTime.now());
            creditCardPaymentInfo.setUpdateTime(LocalDateTime.now());
            creditCardPaymentInfoService.updateById(creditCardPaymentInfo);
        } catch (Exception e) {
            FeiShuMessageUtil.storeGeneralMessage("信用卡信息（卡bin国 & AuthInfo）记录失败。原因：" + e.getMessage(), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        saveAuthResultPaymentInfo(paymentResponse);
    }

    /**
     * 保存auth payment info
     *
     * @param paymentResponse
     */
    protected void saveAuthResultPaymentInfo(CreateCkoCreatePaymentResponse paymentResponse) {
        // 单独保存订单的多条auth信息
        CreditCardPaymentAuthResult creditCardPaymentAuthResult = new CreditCardPaymentAuthResult();
        creditCardPaymentAuthResult.setOrderNumber(paymentResponse.getReference());
        creditCardPaymentAuthResult.setPayMethod(StorePaymentMethodEnum.CKO_PAYMENT.getName());
        creditCardPaymentAuthResult.setActionType(CreditCardPaymentActionEnum.PAYED_RESULT.getActionName());
        creditCardPaymentAuthResult.setAuthType(paymentResponse.getAuthType() == null ? paymentResponse.getStatus() : paymentResponse.getAuthType());
        creditCardPaymentAuthResult.setAuthCode(paymentResponse.getResponseCode());
        creditCardPaymentAuthResult.setAuthText(paymentResponse.getResponseSummary());
        String recommendationCode = Objects.nonNull(paymentResponse.getProcessing()) && Objects.nonNull(paymentResponse.getProcessing().getRecommendationCode())
                ? paymentResponse.getProcessing().getRecommendationCode() : StringUtils.EMPTY;
        creditCardPaymentAuthResult.setRecommendationCode(recommendationCode);
        creditCardPaymentAuthResult.setCreateTime(LocalDateTime.now());
        creditCardPaymentAuthResult.setUpdateTime(LocalDateTime.now());
        creditCardPaymentAuthResultService.save(creditCardPaymentAuthResult);
    }

    /**
     * 保存payment info
     *
     * @param order
     * @param paymentChannelId
     * @param paymentResponse
     */
    protected void saveOrUpdatePaymentInfo(Order order, Integer paymentChannelId, CreateCkoCreatePaymentResponse paymentResponse) {
        CreditCardPaymentInfo creditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(order.getOrderNumber());
        // 可能存在其他支付方式记录
        if (Objects.isNull(creditCardPaymentInfo)) {
            creditCardPaymentInfo = new CreditCardPaymentInfo();
            creditCardPaymentInfo.setCreateTime(LocalDateTime.now());
        }

        String creditCardType = null;
        if (Objects.nonNull(paymentResponse)) {
            // 支付结果
            boolean success = CkoPaymentStatusEnum.isAuthSuccess(paymentResponse.getStatus()) && CkoPaymentConstant.SUCCESS_CODE.equals(paymentResponse.getResponseCode());
            if (success) {
                creditCardPaymentInfo.setPaymentResult(true);
                creditCardPaymentInfo.setOrderPayTime(LocalDateTime.now());
            }

            if (Objects.nonNull(paymentResponse.getSource())) {
                Source source = paymentResponse.getSource();
                creditCardType = source.getScheme();
            }
        }
        creditCardPaymentInfo.setOrderNumber(order.getOrderNumber());
        creditCardPaymentInfo.setPayMethod(StorePaymentMethodEnum.CKO_PAYMENT.getName());
        creditCardPaymentInfo.setPayChannelId(paymentChannelId);
        creditCardPaymentInfo.setCreditCardType(creditCardType);
        creditCardPaymentInfo.setOrderCountry(order.getArea());
        creditCardPaymentInfo.setOrderCreateTime(order.getCreateTime());
        creditCardPaymentInfoService.saveOrUpdate(creditCardPaymentInfo);
    }
}
