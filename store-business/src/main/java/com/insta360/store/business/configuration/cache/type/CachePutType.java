package com.insta360.store.business.configuration.cache.type;

/**
 * @Author: wbt
 * @Date: 2023/11/09
 * @Description:
 */
public interface CachePutType {

    /**
     * 主页套餐列表缓存更新目录
     */
    String HOME_ITEM_KEY = "HomeItem";

    /**
     * 首页缓存更新目录
     */
    String HOME_ITEM_INFO = "HomeItemInfo";

    /**
     * 首页缓存目录
     */
    String HOME_PAGE_KEY = "HomePage";

    /**
     * 主页导航栏缓存更新目录
     */
    String NAVIGATION_BAR_CATEGORY_KEY = "NavigationBarCategory";

    /**
     * top bar缓存更新目录
     */
    String TOP_BAR = "TopBar";

    /**
     * 交易点缓存更新目录
     */
    String TRADE_POINT = "TradePoint";

    /**
     * 产品信息缓存更新目录
     */
    String PRODUCT_INFO = "ProductInfo";

    /**
     * 产品视频信息缓存更新目录
     */
    String PRODUCT_VIDEO = "ProductVideo";

    /**
     * 产品faq缓存更新目录
     */
    String PRODUCT_FAQ = "ProductFaq";

    /**
     * 套餐信息缓存更新目录
     */
    String COMMODITY_INFO = "CommodityInfo";

    /**
     * 套餐价格缓存更新目录
     */
    String COMMODITY_PRICE = "CommodityPrice";

    /**
     * 套餐价格批量操作
     */
    String COMMODITY_PRICE_BATCH = "CommodityPriceBatch";

    /**
     * 套餐销售状态缓存更新目录
     */
    String COMMODITY_SALE_STATE = "CommoditySaleState";

    /**
     * 套餐信息缓存更新目录
     */
    String COMMODITY_DELIVERY_TIME = "CommodityDeliveryTime";

    /**
     * 套餐tag缓存更新目录
     */
    String COMMODITY_TAG = "CommodityTag";

    /**
     * 套餐主图缓存目录
     */
    String COMMODITY_DISPLAY = "CommodityDisplay";

    /**
     * 套餐功能描述缓存目录
     */
    String COMMODITY_FUNCTION_DESCRIPTION = "CommodityFunctionDescription";

    /**
     * 套餐推荐缓存更新目录
     */
    String COMMODITY_RECOMMENDATION = "CommodityRecommendation";

    /**
     * 购物车套餐推荐缓存更新目录
     */
    String COMMODITY_RECOMMENDATION_CART = "CommodityRecommendationCart";

    /**
     * faq页面缓存更新目录（类目结构）
     */
    String FAQ_QUESTION = "FaqQuestion";

    /**
     * faq页面缓存更新目录（非类目结构）
     */
    String FAQ_OTHER_QUESTION = "FaqOtherQuestion";

    /**
     * 配件产品overview缓存更新目录
     */
    String OVERVIEW_INFO = "OverviewInfo";

    /**
     * seo info缓存更新目录
     */
    String SEO_INFO = "SeoInfo";

    /**
     * 直播页面缓存更新目录
     */
    String LIVE_BROADCAST_PAGE = "LiveBroadcastPage";

    /**
     * 评论信息缓存更新目录
     */
    String REVIEW_INFO = "ReviewInfo";

    /**
     * 图文导航缓存更新目录
     */
    String GRAPHIC_NAVIGATION = "GraphicNavigation";

    /**
     * avalara税州缓存更新目录
     */
    String AVALARA_TAX_PROVINCE = "AvalaraTaxProvince";

    /**
     * 产品配件兼容性缓存更新目录
     */
    String PRODUCT_ACCESSORY_COMPATIBILITY = "ProductAccessoryCompatibility";

    /**
     * 产品适配机型缓存更新目录
     */
    String PRODUCT_ADAPTER_TYPE = "ProductAdapterType";

    /**
     * 适配类型缓存更新目录
     */
    String ADAPTER_TYPE_MAIN = "AdapterTypeMain";

    /**
     * 适配类型多语言缓存更新目录
     */
    String ADAPTER_TYPE_INFO = "AdapterTypeInfo";

    /**
     * 产品包装清单缓存更新目录
     */
    String PRODUCT_PACK_LIST = "ProductPackList";

    /**
     * 类目筛选器
     */
    String CATEGORY_PAGE_FILTER = "CategoryPageFilter";

    /**
     * 类目页
     */
    String CATEGORY_PAGE = "CategoryPage";

    /**
     * banner缓存更新目录
     */
    String BANNER_INFO = "BannerInfo";

    /**
     * 元信息物流运费
     */
    String META_SHIPPING_COST = "MetaShippingCost";

    /**
     * 产品主视频
     */
    String PRODUCT_MAIN_VIDEO = "ProductMainVideo";

    /**
     * 场景标签
     */
    String SCENERY_TAG_MAIN = "SceneryTagMain";

    /**
     * 增值服务
     */
    String CLIMB_SERVICE_COMMODITY = "ClimbServiceCommodity";

    /**
     * 场景专区
     */
    String SCENERY_SECTION = "ScenerySection";

    /**
     * 活动配置页面数据
     */
    String META_ACTIVITY_PAGE = "MetaActivityPage";
}
