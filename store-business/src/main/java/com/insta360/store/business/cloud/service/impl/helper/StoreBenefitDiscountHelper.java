package com.insta360.store.business.cloud.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.cloud.bo.BenefitDiscountCalculateBO;
import com.insta360.store.business.cloud.bo.BenefitDiscountItemBO;
import com.insta360.store.business.cloud.bo.BenefitDiscountResultBO;
import com.insta360.store.business.cloud.bo.QuotaAdjustmentBO;
import com.insta360.store.business.cloud.config.CloudStorageConfig;
import com.insta360.store.business.cloud.constant.StoreBenefitConstant;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.enums.QuotaAdjustmentType;
import com.insta360.store.business.cloud.enums.SkuSubscribeType;
import com.insta360.store.business.cloud.model.CloudStorageCompensateDetail;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitDetail;
import com.insta360.store.business.cloud.service.CloudStorageCompensateDetailService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitDetailService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitService;
import com.insta360.store.business.discount.dto.bo.DiscountCheckResult;
import com.insta360.store.business.discount.dto.bo.DiscountDetailBO;
import com.insta360.store.business.order.constants.OrderCommonConstant;
import com.insta360.store.business.order.dto.OrderItemBO;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.StoreAccountService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/18
 */
@Component
public class StoreBenefitDiscountHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreBenefitDiscountHelper.class);

    @Autowired
    OrderService orderService;

    @Autowired
    CloudStorageConfig cloudStorageConfig;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    StoreAccountService storeAccountService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Autowired
    CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    CloudStorageCompensateDetailService cloudStorageCompensateDetailService;

    @Autowired
    CloudStorageStoreBenefitDetailService cloudStorageStoreBenefitDetailService;

    /**
     * 异步绑定设备序列号和代金券代码。
     * 此方法用于补偿老用户在使用care权益时相机已激活超过时间限制的场景，将代金券与设备序列号绑定。
     *
     * @param userId       用户ID，表示代金券的拥有者。
     * @param serialNumber 设备序列号，用于唯一标识设备。
     * @param giftCardCode 代金券code
     * @Async 注解表明此方法为异步执行，即不会等待方法执行完毕。
     */
    @Async
    public void bindDeviceSerialGiftCard(Integer userId, String serialNumber, String giftCardCode) {
        // 检查传入参数的有效性，如果任一参数为null或空字符串，则直接返回，不执行绑定操作。
        if (Objects.isNull(userId) || StringUtils.isBlank(serialNumber) || StringUtils.isBlank(giftCardCode)) {
            return;
        }

        CloudStorageStoreBenefit storeBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(userId);
        if (Objects.isNull(storeBenefit) || storeBenefit.isExpired()) {
            return;
        }

        CloudStorageStoreBenefitDetail storeBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetailUsable(storeBenefit.getId(), BenefitType.CARE.getType());
        if (Objects.isNull(storeBenefitDetail)) {
            return;
        }

        // 保存绑定信息到云存储补偿详情表中。
        CloudStorageCompensateDetail compensateDetail = new CloudStorageCompensateDetail(userId, serialNumber, giftCardCode, BenefitType.CARE);
        // 设置care权益已被使用
        storeBenefitDetail.setUsed(true);
        storeBenefitDetail.setUsedQuota(1);
        storeBenefitDetail.setRemainderQuota(storeBenefitDetail.getFixedQuota() - 1);
        storeBenefitDetail.setModifyTime(LocalDateTime.now());

        cloudStorageCompensateDetailService.bind(compensateDetail, storeBenefitDetail);
    }

    /**
     * 配件折扣权益-优惠金额计算
     *
     * @param discountCalculateBo
     * @return
     */
    public BenefitDiscountResultBO discountHandle(BenefitDiscountCalculateBO discountCalculateBo) {
        // 当前登陆用户
        StoreAccount storeAccount = discountCalculateBo.getStoreAccount();
        if (Objects.isNull(storeAccount)) {
            return new BenefitDiscountResultBO();
        }
        // 地区限制
        InstaCountry country = discountCalculateBo.getCountry();
        if (Objects.isNull(country) || Lists.newArrayList(InstaCountry.US, InstaCountry.CN).contains(country)) {
            return new BenefitDiscountResultBO();
        }

        CloudStorageStoreBenefit storeBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(storeAccount.getInstaAccount());
        if (Objects.isNull(storeBenefit)
                || storeBenefit.isExpired()
                || !SkuSubscribeType.YEARLY.equals(storeBenefit.parseSkuSubscribeType())) {
            return new BenefitDiscountResultBO();
        }

        CloudStorageStoreBenefitDetail storeBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.ACCESSORIES_DISCOUNT.getType());
        if (Objects.isNull(storeBenefitDetail) || storeBenefitDetail.getExpired() || storeBenefitDetail.getRemainderQuota() <= 0) {
            return new BenefitDiscountResultBO();
        }

        // 当前加购的商品列表
        List<OrderItem> orderItemList = discountCalculateBo.getOrderItemList();
        // 参与coupon优惠的商品折扣明细列表
        DiscountCheckResult couponDiscountResult = discountCalculateBo.getCouponDiscountResult();

        // 已参与优惠券、代金券折扣的套餐ID集合
        List<Integer> couponCommodityIds = new ArrayList<>(orderItemList.size());
        if (Objects.nonNull(couponDiscountResult) && CollectionUtils.isNotEmpty(couponDiscountResult.getDiscountDetails())) {
            couponCommodityIds = couponDiscountResult.getDiscountDetails()
                    .stream()
                    .filter(DiscountDetailBO::isEfficientDiscount)
                    .map(DiscountDetailBO::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
        }

        List<Integer> finalCouponCommodityIds = couponCommodityIds;
        orderItemList = orderItemList.stream()
                .filter(orderItem -> !orderItem.getIsGift())
                .filter(orderItem -> !finalCouponCommodityIds.contains(orderItem.getCommodity()))
                .collect(Collectors.toList());

        // 剩下未参与优惠券优惠的的所有套餐对应的产品ID集合
        List<Integer> productIds = orderItemList.stream()
                .map(OrderItem::getProduct)
                .distinct()
                .collect(Collectors.toList());
        // 套餐对应的产品集合
        Map<Integer, Product> productMap = productBatchHelper.productMapProductIds(productIds);

        orderItemList = orderItemList.stream()
                .filter(orderItem -> {
                    Product product = productMap.get(orderItem.getProduct());
                    ProductCategoryMainType categoryMainByKey = productCategoryHelper.getCategoryMainByKey(product.getCategoryKey());
                    return ProductCategoryMainType.CM_ACCESSORY.equals(categoryMainByKey) && !cloudStorageConfig.getDisableCommodityIds().contains(orderItem.getCommodity());
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderItemList)) {
            return new BenefitDiscountResultBO();
        }

        List<OrderItemBO> orderItemBoList = orderItemList.stream()
                .map(orderItem -> OrderItemBO.convert(orderItem, ProductCategoryMainType.CM_ACCESSORY))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 获取配件商品折扣金额优惠明细列表
        List<BenefitDiscountItemBO> benefitDiscountItemList = this.getBenefitDiscountResult(storeBenefitDetail.getRemainderQuota(), orderItemBoList);
        BigDecimal totalDiscountAmount = benefitDiscountItemList.stream().map(BenefitDiscountItemBO::getBenefitDiscountTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        return new BenefitDiscountResultBO(benefitDiscountItemList, totalDiscountAmount);
    }

    /**
     * 配件折扣权益-获取各类配件套餐优惠金额明细结果
     *
     * @param remainderQuota
     * @param accessoriesItemList
     * @return
     */
    public List<BenefitDiscountItemBO> getBenefitDiscountResult(Integer remainderQuota, List<OrderItemBO> accessoriesItemList) {
        if (CollectionUtils.isEmpty(accessoriesItemList) || Objects.isNull(remainderQuota) || remainderQuota <= 0) {
            return new ArrayList<>();
        }
        List<BenefitDiscountItemBO> benefitDiscountItems = this.benefitDiscountHandle(accessoriesItemList);
        if (CollectionUtils.isEmpty(benefitDiscountItems)) {
            return new ArrayList<>();
        }

        // 参与订阅折扣优惠的商品数量
        int participateItemNumber = benefitDiscountItems.stream().mapToInt(BenefitDiscountItemBO::getNumber).sum();
        if (participateItemNumber <= remainderQuota) {
            return benefitDiscountItems;
        }

        // 重排序
        benefitDiscountItems.sort(Comparator.comparing(BenefitDiscountItemBO::getSingleBenefitDiscountAmount, Comparator.reverseOrder()));

        List<BenefitDiscountItemBO> benefitDiscountItemList = Lists.newArrayList();
        for (BenefitDiscountItemBO benefitDiscountItem : benefitDiscountItems) {
            if (benefitDiscountItem.getNumber() <= remainderQuota) {
                benefitDiscountItemList.add(benefitDiscountItem);
                remainderQuota = remainderQuota - benefitDiscountItem.getNumber();
            } else {
                if (remainderQuota > 0) {
                    // 商品总订阅折扣金额
                    BigDecimal benefitDiscountTotalAmount = benefitDiscountItem.getSingleBenefitDiscountAmount().multiply(new BigDecimal(String.valueOf(remainderQuota)));
                    benefitDiscountItem.setBenefitDiscountTotalAmount(benefitDiscountTotalAmount);
                    benefitDiscountItem.setUsedQuota(remainderQuota);
                    benefitDiscountItemList.add(benefitDiscountItem);
                    break;
                }
            }
        }

        return benefitDiscountItemList;
    }

    /**
     * 处理额度调整的业务逻辑。
     * 根据传入的配额调整操作对象，判断并执行相应的配额调整操作。
     * 此函数主要涉及检查订单有效性、用户存在性以及权益的有效性，然后根据调整类型执行相应的配额扣除操作。
     *
     * @param quotaAdjustmentBo 配额调整操作对象，包含订单ID、调整类型和调整量等信息。
     */
    public void handleQuotaAdjustment(QuotaAdjustmentBO quotaAdjustmentBo) {
        LOGGER.info("[商城云服务]处理商城特殊权益-调整配件折扣权益配额开始. quotaAdjustmentBo: {}", JSON.toJSONString(quotaAdjustmentBo));
        if (Objects.isNull(quotaAdjustmentBo)) {
            return;
        }
        // 订单
        Order order = orderService.getById(quotaAdjustmentBo.getOrderId());
        if (Objects.isNull(order) || order.isGuestOrder()) {
            return;
        }
        // 用户
        Integer userId = order.getUserId();
        if (Objects.isNull(userId)) {
            return;
        }
        // 是否存在权益记录
        CloudStorageStoreBenefit storeBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(userId);
        if (Objects.isNull(storeBenefit) || storeBenefit.isExpired()) {
            return;
        }

        // 额度调整处理
        QuotaAdjustmentType quotaAdjustmentType = quotaAdjustmentBo.getQuotaAdjustmentType();
        switch (quotaAdjustmentType) {
            case ADD:
                cloudStorageStoreBenefitDetailService.quotaGoBack(quotaAdjustmentBo.getQuota(), storeBenefit.getId());
                break;
            case DEDUCT:
                cloudStorageStoreBenefitDetailService.quotaDeduct(quotaAdjustmentBo.getQuota(), storeBenefit.getId());
                break;
            default:
                break;
        }
        LOGGER.info("[商城云服务]处理商城特殊权益-调整配件折扣权益配额结束. quotaAdjustmentBo: {}", JSON.toJSONString(quotaAdjustmentBo));
    }

    private List<BenefitDiscountItemBO> benefitDiscountHandle(List<OrderItemBO> orderItemBoList) {
        return orderItemBoList.stream()
                .map(orderItemBo -> {
                    // 原价
                    BigDecimal originAmount = orderItemBo.getOriginAmount();
                    // 现价
                    BigDecimal currentAmount = orderItemBo.getPrice();
                    // 原价折扣后金额
                    BigDecimal afterDiscountAmount = originAmount.subtract((originAmount.multiply(StoreBenefitConstant.fIXED_DISCOUNT_RATE)));
                    // 如果原价折扣后金额 >= 现价，则说明现价更便宜
                    if (afterDiscountAmount.compareTo(currentAmount) >= 0) {
                        return null;
                    }
                    // 单个商品订阅折扣金额
                    BigDecimal singleBenefitDiscountAmount = currentAmount.subtract(afterDiscountAmount);

                    // 日韩台小数取整
                    if (OrderCommonConstant.JKT_Store_Currency.contains(orderItemBo.getCurrency()) && singleBenefitDiscountAmount.stripTrailingZeros().scale() > 0) {
                        singleBenefitDiscountAmount = singleBenefitDiscountAmount.setScale(0, RoundingMode.CEILING);
                    }

                    // 商品订阅折扣总金额
                    BigDecimal benefitDiscountTotalAmount = singleBenefitDiscountAmount.multiply(new BigDecimal(String.valueOf(orderItemBo.getNumber()))).setScale(2, RoundingMode.DOWN);
                    // 活动价差额
                    BigDecimal activityPriceDifference = orderItemBo.getOriginAmount().subtract(orderItemBo.getPrice()).setScale(2, RoundingMode.DOWN);
                    // 订阅折扣商品
                    BenefitDiscountItemBO benefitDiscountItemBo = new BenefitDiscountItemBO();
                    benefitDiscountItemBo.setCommodityId(orderItemBo.getCommodityId());
                    benefitDiscountItemBo.setNumber(orderItemBo.getNumber());
                    benefitDiscountItemBo.setCurrentAmount(orderItemBo.getPrice());
                    benefitDiscountItemBo.setOriginAmount(orderItemBo.getOriginAmount());
                    benefitDiscountItemBo.setBenefitDiscountTotalAmount(benefitDiscountTotalAmount);
                    benefitDiscountItemBo.setSingleBenefitDiscountAmount(singleBenefitDiscountAmount);
                    benefitDiscountItemBo.setActivityPriceDifference(activityPriceDifference);
                    benefitDiscountItemBo.setCurrency(orderItemBo.getCurrency());
                    benefitDiscountItemBo.setUsedQuota(orderItemBo.getNumber());
                    return benefitDiscountItemBo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
