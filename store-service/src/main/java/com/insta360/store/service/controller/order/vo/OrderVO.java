package com.insta360.store.service.controller.order.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.store.business.admin.order.print.bo.PrintRuleCheckResultBO;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderInvoice;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.controller.tradeup.vo.TradeupOrderVO;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderVO {

    private Integer id;

    @JSONField(name = "is_guest_order")
    private Boolean isGuestOrder;

    private List<OrderItemVO> items;

    private OrderInvoice invoice;

    private OrderPaymentVO payment;

    @JSONField(name = "allow_refund")
    private Boolean allowRefund;

    @JSONField(name = "trade_up_order")
    private TradeupOrderVO tradeupOrder;

    @JSONField(name = "order_delivery_part")
    private List<OrderDeliveryPartlyVO> orderDeliveryPartlies;

    @JSONField(name = "order_state_records")
    private List<OrderStateRecordVO> orderStateRecordVO;

    /**
     * 是否允许售后申请 默认：true
     */
    private boolean refundApply = true;

    /**
     * 海外订单发票按钮
     */
    private PrintRuleCheckResultBO printRuleCheckResult;

    /**
     * user id
     */
    private Integer userId;

    @JSONField(name = "order_number")
    private String orderNumber;

    @JSONField(name = "pay_type")
    private String payType;

    @JSONField(name = "contact_email")
    private String contactEmail;

    private Integer state;

    @JSONField(name = "create_time")
    private LocalDateTime createTime;

    @JSONField(name = "promo_code")
    private String promoCode;

    @JSONField(name = "coupon_code")
    private String couponCode;

    @JSONField(name = "gift_card_code")
    private String giftCardCode;

    private String area;

    @JSONField(name = "backup_email")
    private String backupEmail;

    @JSONField(name = "backup_phone")
    private String backupPhone;

    @JSONField(name = "individual_tax_number")
    private String individualTaxNumber;

    @JSONField(name = "tax_title")
    private String taxTitle;

    @JSONField(name = "invoice_send")
    private Boolean invoiceSend;

    private String remark;

    private Boolean reserved;

    private String inscp;

    @JSONField(name = "stock_handled")
    private Boolean stockHandled;

    @JSONField(name = "sync_op_tracking")
    private Boolean syncOpTracking;

    @JSONField(name = "force_allow_withdraw")
    private Boolean forceAllowWithdraw;

    @JSONField(name = "no_auto_success")
    private Boolean noAutoSuccess;

    private Integer endpoint;

    private String ip;

    /**
     * 是否一次性全部发货
     */
    private Boolean allAtOnceShip;

    /**
     * 计税类型
     */
    private Integer taxType;

    private OrderDeliveryVO delivery;

    /**
     * 是否优先发货
     */
    private Boolean shipPriority;

    public OrderVO() {
    }

    public OrderVO(Order order) {
        if (order != null) {
            BeanUtils.copyProperties(order, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public OrderDeliveryVO getDelivery() {
        return delivery;
    }

    public void setDelivery(OrderDeliveryVO delivery) {
        this.delivery = delivery;
    }

    public Boolean getGuestOrder() {
        return isGuestOrder;
    }

    public void setGuestOrder(Boolean guestOrder) {
        isGuestOrder = guestOrder;
    }

    public Boolean getAllAtOnceShip() {
        return allAtOnceShip;
    }

    public void setAllAtOnceShip(Boolean allAtOnceShip) {
        this.allAtOnceShip = allAtOnceShip;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getPromoCode() {
        return promoCode;
    }

    public void setPromoCode(String promoCode) {
        this.promoCode = promoCode;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getGiftCardCode() {
        return giftCardCode;
    }

    public void setGiftCardCode(String giftCardCode) {
        this.giftCardCode = giftCardCode;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getBackupEmail() {
        return backupEmail;
    }

    public void setBackupEmail(String backupEmail) {
        this.backupEmail = backupEmail;
    }

    public String getBackupPhone() {
        return backupPhone;
    }

    public void setBackupPhone(String backupPhone) {
        this.backupPhone = backupPhone;
    }

    public String getIndividualTaxNumber() {
        return individualTaxNumber;
    }

    public void setIndividualTaxNumber(String individualTaxNumber) {
        this.individualTaxNumber = individualTaxNumber;
    }

    public String getTaxTitle() {
        return taxTitle;
    }

    public void setTaxTitle(String taxTitle) {
        this.taxTitle = taxTitle;
    }

    public Boolean getInvoiceSend() {
        return invoiceSend;
    }

    public void setInvoiceSend(Boolean invoiceSend) {
        this.invoiceSend = invoiceSend;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getReserved() {
        return reserved;
    }

    public void setReserved(Boolean reserved) {
        this.reserved = reserved;
    }

    public String getInscp() {
        return inscp;
    }

    public void setInscp(String inscp) {
        this.inscp = inscp;
    }

    public Boolean getStockHandled() {
        return stockHandled;
    }

    public void setStockHandled(Boolean stockHandled) {
        this.stockHandled = stockHandled;
    }

    public Boolean getSyncOpTracking() {
        return syncOpTracking;
    }

    public void setSyncOpTracking(Boolean syncOpTracking) {
        this.syncOpTracking = syncOpTracking;
    }

    public Boolean getForceAllowWithdraw() {
        return forceAllowWithdraw;
    }

    public void setForceAllowWithdraw(Boolean forceAllowWithdraw) {
        this.forceAllowWithdraw = forceAllowWithdraw;
    }

    public Boolean getNoAutoSuccess() {
        return noAutoSuccess;
    }

    public void setNoAutoSuccess(Boolean noAutoSuccess) {
        this.noAutoSuccess = noAutoSuccess;
    }

    public Integer getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Integer endpoint) {
        this.endpoint = endpoint;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getTaxType() {
        return taxType;
    }

    public void setTaxType(Integer taxType) {
        this.taxType = taxType;
    }

    public boolean isRefundApply() {
        return refundApply;
    }

    public void setRefundApply(boolean refundApply) {
        this.refundApply = refundApply;
    }

    public Boolean getIsGuestOrder() {
        return isGuestOrder;
    }

    public void setIsGuestOrder(Boolean guestOrder) {
        isGuestOrder = guestOrder;
    }

    public List<OrderItemVO> getItems() {
        return items;
    }

    public void setItems(List<OrderItemVO> items) {
        this.items = items;
    }

    public OrderInvoice getInvoice() {
        return invoice;
    }

    public void setInvoice(OrderInvoice invoice) {
        this.invoice = invoice;
    }

    public OrderPaymentVO getPayment() {
        return payment;
    }

    public void setPayment(OrderPaymentVO payment) {
        this.payment = payment;
    }

    public Boolean getAllowRefund() {
        return allowRefund;
    }

    public void setAllowRefund(Boolean allowRefund) {
        this.allowRefund = allowRefund;
    }

    public TradeupOrderVO getTradeupOrder() {
        return tradeupOrder;
    }

    public void setTradeupOrder(TradeupOrderVO tradeupOrder) {
        this.tradeupOrder = tradeupOrder;
    }

    public List<OrderDeliveryPartlyVO> getOrderDeliveryPartlies() {
        return orderDeliveryPartlies;
    }

    public void setOrderDeliveryPartlies(List<OrderDeliveryPartlyVO> orderDeliveryPartlies) {
        this.orderDeliveryPartlies = orderDeliveryPartlies;
    }

    public List<OrderStateRecordVO> getOrderStateRecordVO() {
        return orderStateRecordVO;
    }

    public void setOrderStateRecordVO(List<OrderStateRecordVO> orderStateRecordVO) {
        this.orderStateRecordVO = orderStateRecordVO;
    }

    public PrintRuleCheckResultBO getPrintRuleCheckResult() {
        return printRuleCheckResult;
    }

    public void setPrintRuleCheckResult(PrintRuleCheckResultBO printRuleCheckResult) {
        this.printRuleCheckResult = printRuleCheckResult;
    }

    public Boolean getShipPriority() {
        return shipPriority;
    }

    public void setShipPriority(Boolean shipPriority) {
        this.shipPriority = shipPriority;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "OrderVO{" +
                "id=" + id +
                ", isGuestOrder=" + isGuestOrder +
                ", items=" + items +
                ", invoice=" + invoice +
                ", payment=" + payment +
                ", allowRefund=" + allowRefund +
                ", tradeupOrder=" + tradeupOrder +
                ", orderDeliveryPartlies=" + orderDeliveryPartlies +
                ", orderStateRecordVO=" + orderStateRecordVO +
                ", refundApply=" + refundApply +
                ", printRuleCheckResult=" + printRuleCheckResult +
                ", userId=" + userId +
                ", orderNumber='" + orderNumber + '\'' +
                ", payType='" + payType + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", state=" + state +
                ", createTime=" + createTime +
                ", promoCode='" + promoCode + '\'' +
                ", couponCode='" + couponCode + '\'' +
                ", giftCardCode='" + giftCardCode + '\'' +
                ", area='" + area + '\'' +
                ", backupEmail='" + backupEmail + '\'' +
                ", backupPhone='" + backupPhone + '\'' +
                ", individualTaxNumber='" + individualTaxNumber + '\'' +
                ", taxTitle='" + taxTitle + '\'' +
                ", invoiceSend=" + invoiceSend +
                ", remark='" + remark + '\'' +
                ", reserved=" + reserved +
                ", inscp='" + inscp + '\'' +
                ", stockHandled=" + stockHandled +
                ", syncOpTracking=" + syncOpTracking +
                ", forceAllowWithdraw=" + forceAllowWithdraw +
                ", noAutoSuccess=" + noAutoSuccess +
                ", endpoint=" + endpoint +
                ", ip='" + ip + '\'' +
                ", allAtOnceShip=" + allAtOnceShip +
                ", taxType=" + taxType +
                ", delivery=" + delivery +
                ", shipPriority=" + shipPriority +
                '}';
    }

    /**
     * 是否游客订单
     *
     * @return
     */
    public boolean isGuestOrder() {
        return StoreAccount.GUEST_USER_ID.equals(getUserId());
    }
}
