package com.insta360.store.business.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.common.constants.CommonConstant;
import com.insta360.store.business.trade.dao.UserCartEmailSendRecordDao;
import com.insta360.store.business.trade.enums.UserCartEmailSendStatus;
import com.insta360.store.business.trade.model.UserCartEmailSendRecord;
import com.insta360.store.business.trade.service.UserCartEmailSendRecordService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-12-18
 * @Description:
 */
@Service
public class UserCartEmailSendRecordServiceImpl extends BaseServiceImpl<UserCartEmailSendRecordDao, UserCartEmailSendRecord> implements UserCartEmailSendRecordService {

    @Override
    public UserCartEmailSendRecord getByEmailAndAfter(String email, LocalDateTime dateTime, Integer code) {
        QueryWrapper<UserCartEmailSendRecord> qw = new QueryWrapper<>();
        qw.eq("email", email);
        qw.eq("status", UserCartEmailSendStatus.Send);
        qw.ge("send_time", dateTime);
        qw.eq("email_type", code);
        qw.last(CommonConstant.LAST_LIMIT_ONE);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<UserCartEmailSendRecord> listByExclusiveCodes(String email, LocalDateTime dateTime, List<Integer> codes) {
        QueryWrapper<UserCartEmailSendRecord> qw = new QueryWrapper<>();
        qw.eq("email", email);
        qw.eq("status", UserCartEmailSendStatus.Send);
        qw.ge("send_time", dateTime);
        qw.in("email_type", codes);
        qw.last(CommonConstant.LAST_LIMIT_ONE);
        return baseMapper.selectList(qw);
    }
}
