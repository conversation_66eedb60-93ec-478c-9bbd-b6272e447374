package com.insta360.store.business.payment.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.compass.core.util.JsonUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.store.business.cloud.dto.CloudSubscribeDTO;
import com.insta360.store.business.cloud.dto.SubscribeBillingAddressDTO;
import com.insta360.store.business.cloud.enums.CloudSubscribeActionEnum;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.model.SubscribeBillingAddress;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.SubscribeBillingAddressService;
import com.insta360.store.business.cloud.service.impl.helper.StoreSubscribeHelper;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.common.constants.RedisKeyConstant;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.configuration.utils.EncodingUtil;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.exception.RetryHandlerException;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderBillingAddress;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderBillingAddressService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.payment.bo.OceanPaymentProductInfoBO;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.constants.OceanConstant;
import com.insta360.store.business.payment.constants.PaymentConstant;
import com.insta360.store.business.payment.enums.*;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.ocean.config.OceanPaymentConfig;
import com.insta360.store.business.payment.lib.ocean.config.normal.OceanCNCreditCardConfig;
import com.insta360.store.business.payment.lib.ocean.config.normal.OceanCreditCardConfig;
import com.insta360.store.business.payment.lib.ocean.config.normal.OceanOtherCreditCardConfig;
import com.insta360.store.business.payment.lib.ocean.config.normal.OceanUSCreditCardConfig;
import com.insta360.store.business.payment.lib.ocean.config.subscribe.*;
import com.insta360.store.business.payment.lib.ocean.request.store.CreateIDEncryptionPaymentRequest;
import com.insta360.store.business.payment.lib.ocean.request.store.OceanDirectPaymentRequest;
import com.insta360.store.business.payment.lib.ocean.request.store.OceanPreAuthRequest;
import com.insta360.store.business.payment.lib.ocean.response.CreateIdEncryptionPaymentResponse;
import com.insta360.store.business.payment.lib.ocean.response.OceanDirectPaymentResponse;
import com.insta360.store.business.payment.lib.ocean.response.OceanPreAuthResponse;
import com.insta360.store.business.payment.model.OceanFreeCheckRecord;
import com.insta360.store.business.payment.service.OceanFreeCheckRecordService;
import com.insta360.store.business.payment.service.impl.handler.PaymentHandlerFactory;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wkx
 * @Date: 2024/05/23
 * @Description:
 */
@Component
public class OceanPaymentHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OceanPaymentHelper.class);

    @Autowired
    OceanCreditCardConfig oceanCreditCardConfig;

    @Autowired
    OceanOtherCreditCardConfig oceanOtherCreditCardConfig;

    @Autowired
    OceanCNCreditCardConfig oceanCNCreditCardConfig;

    @Autowired
    OceanUSCreditCardConfig oceanUSCreditCardConfig;

    @Autowired
    OceanCreditCardFirstSubscribeConfig oceanCreditCardFirstSubscribeConfig;

    @Autowired
    OceanOtherCreditCardFirstSubscribeConfig otherCreditCardFirstSubscribeConfig;

    @Autowired
    OceanCNFirstSubscribeConfig oceanCNFirstSubscribeConfig;

    @Autowired
    OceanUSCreditCardFirstSubscribeConfig oceanUSCreditCardFirstSubscribeConfig;

    // 免费试用配置 开始————————————
    @Autowired
    OceanCNFreeSubscribeConfig oceanCNFreeSubscribeConfig;

    @Autowired
    OceanCreditCardFreeSubscribeConfig oceanCreditCardFreeSubscribeConfig;

    @Autowired
    OceanOtherCreditCardFreeSubscribeConfig otherCreditCardFreeSubscribeConfig;

    @Autowired
    OceanUSCreditCardFreeSubscribeConfig oceanUSCreditCardFreeSubscribeConfig;

    @Autowired
    PaymentHandlerFactory paymentHandlerFactory;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    // 免费试用配置 结束————————————

    @Autowired
    OrderBillingAddressService orderBillingAddressService;

    @Autowired
    StoreSdkCallRecordService storeSdkCallRecordService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    OceanFreeCheckRecordService oceanFreeCheckRecordService;

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    SubscribeBillingAddressService subscribeBillingAddressService;

    @Value("${payment.ocean_payment.subscribe.back_url}")
    private String subscribeBackUrl;

    @Value("${payment.ocean_payment.subscribe.notice_url}")
    private String subscribeNoticeUrl;

    /**
     * 获取ocean 配置PK
     *
     * @param creditCardName
     * @param country
     * @param paymentBusinessType
     * @return
     */
    public OceanPaymentConfig getOceanPaymentConfig(String creditCardName, InstaCountry country, PaymentBusinessType paymentBusinessType) {
        switch (paymentBusinessType) {
            case NORMAL_PAY:
                return getOceanNormalPublicKey(creditCardName, country);
            case SUBSCRIBE_PAY:
                return getOceanSubscribePublicKey(creditCardName, country);
            case FREE_SUBSCRIBE_PAY:
                return getOceanFreeSubscribePublicKey(creditCardName, country);
            default:
                throw new InstaException(PaymentErrorCode.InvalidPaymentMethodException);
        }
    }

    /**
     * 获取钱海卡信息更新
     *
     * @param storeAccount
     * @param orderNumber
     * @param userPayInfo
     * @param cloudSubscribeParam
     * @param paymentExtra        支付信息额外
     * @return {@link String }
     */
    public String getOceanSubscribeInfo(StoreAccount storeAccount,
                                        String orderNumber,
                                        UserPayInfo userPayInfo,
                                        CloudSubscribeDTO cloudSubscribeParam,
                                        PaymentExtra paymentExtra) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        Integer payMode = cloudSubscribeParam.getPayMode();
        PaymentBusinessType paymentBusinessType = Optional.ofNullable(PaymentBusinessType.parse(payMode)).orElse(PaymentBusinessType.SUBSCRIBE_PAY);

        String cardData = cloudSubscribeParam.getCardData();
        String creditCardName = cloudSubscribeParam.getCreditCardName();
        // 必要参数校验
        if (StringUtil.isBlank(cardData) || StringUtil.isBlank(creditCardName)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 保存订阅auth result
        StoreSubscribeHelper subscribeHelper = ApplicationContextHolder.getApplicationContext().getBean(StoreSubscribeHelper.class);
        StorePaymentMethodEnum paymentMethod = userPayInfo.parsePaymentMethod();
        subscribeHelper.saveSubscribeAuthResult(storeAccount, paymentMethod, CloudSubscribeActionEnum.OCEAN_CREATE_PAYMENT_ID, paymentMethod.name(), StringUtils.EMPTY, StringUtils.EMPTY);
        String serialNumber = UUIDUtils.generateUuid().toUpperCase();
        LOGGER.info(String.format("[订阅支付0元验卡]-主流程 serialNumber:%s orderNumber:%s 流程开始 .....", serialNumber, orderNumber));

        // 获取终端等配置
        OceanPaymentConfig oceanPaymentConfig = getOceanPaymentConfig(creditCardName, order.country(), paymentBusinessType);
        LOGGER.info("[订阅支付0元验卡]-主流程 获取终端配置 serialNumber:{} orderNumber:{} 配置:{} paymentBusinessType:{}", serialNumber, orderNumber, oceanPaymentConfig, paymentBusinessType);

        // 请求加密创建钱海quick pay id Create ID Encryption
        CreateIdEncryptionPaymentResponse createIdEncryptionPaymentResponse = doCreateIDEncryptionPaymentRequest(cardData, serialNumber, orderNumber, oceanPaymentConfig, order, cloudSubscribeParam.getSubscribeBillingAddress());
        String quickPayId = createIdEncryptionPaymentResponse.getQuickPayId();
        OceanFreeCheckRecord oceanFreeCheckRecord = new OceanFreeCheckRecord();
        if (PaymentBusinessType.FREE_SUBSCRIBE_PAY.equals(paymentBusinessType)) {
            oceanFreeCheckRecord.setUserId(userPayInfo.getInstaAccount());
            oceanFreeCheckRecord.setEmail(userPayInfo.getContactEmail());
            oceanFreeCheckRecord.setOrderNumber(orderNumber);
            oceanFreeCheckRecord.setSerialNumber(serialNumber);
            oceanFreeCheckRecord.setCreditCardName(creditCardName);
            oceanFreeCheckRecord.setPaymentId("");
            oceanFreeCheckRecord.setCounty(order.country().name());
            oceanFreeCheckRecord.setMethod("");
            oceanFreeCheckRecord.setDeductCategory(createIdEncryptionPaymentResponse.getCardNumber());
            oceanFreeCheckRecord.setCardType(createIdEncryptionPaymentResponse.getCardType());
            oceanFreeCheckRecord.setCardCountry(createIdEncryptionPaymentResponse.getCardCountry());
            oceanFreeCheckRecord.setBillingAddress(JSON.toJSONString(cloudSubscribeParam.getSubscribeBillingAddress()));
            LOGGER.info("[订阅支付0元验卡]-主流程 保存免费使用支付临时信息 serialNumber:{} orderNumber:{} oceanFreeCheckRecord:{}", serialNumber, orderNumber, oceanFreeCheckRecord);
            oceanFreeCheckRecordService.save(oceanFreeCheckRecord);

            LOGGER.info("[订阅支付0元验卡]-主流程 预授权流程开始 serialNumber:{} orderNumber:{}", serialNumber, orderNumber);
            // Quick-pay id 创建成功后调用direct pay接口发起预授权支付请求  Direct pay
            OceanDirectPaymentResponse oceanDirectPaymentResponse = doOceanDirectPayment(
                    cloudSubscribeParam,
                    paymentExtra,
                    oceanPaymentConfig,
                    order,
                    quickPayId,
                    oceanFreeCheckRecord,
                    cloudSubscribeParam.getSubscribeBillingAddress());

            // 判断3DS校验
            String payUrl = oceanDirectPaymentResponse.getPayUrl();
            if (StringUtil.isNotBlank(payUrl)) {
                LOGGER.info(String.format("[订阅支付0元验卡]-主流程 需要3DS serialNumber:{%s} orderNumber:{%s} pay_url:%s ", serialNumber, orderNumber, payUrl));
                return payUrl;
            }

            // 验证判断
            if (!oceanDirectPaymentResponse.isPending()) {
                throw new InstaException(CloudSubscribeErrorCode.OceanUpdateCardInfoException);
            }

            // 保存订阅auth result
            subscribeHelper.saveSubscribeAuthResult(storeAccount, paymentMethod, CloudSubscribeActionEnum.OCEAN_AUTH_NOTIFY, paymentMethod.name(), oceanDirectPaymentResponse.getPaymentStatus(), oceanDirectPaymentResponse.getPaymentDetails());
        }

        boolean signatureValid = createIdEncryptionPaymentResponse.isSignatureValid(oceanPaymentConfig.getSecureCode());
        if (!signatureValid) {
            String message = String.format("[订阅支付0元验卡]-主流程 签名校验失败, serialNumber:{%s} orderNumber:{%s}", serialNumber, orderNumber);
            LOGGER.error(message);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
            throw new InstaException(CloudSubscribeErrorCode.OceanUpdateCardInfoException);
        }

        if (StringUtil.isBlank(quickPayId)) {
            String message = String.format("[订阅支付0元验卡]-主流程 获取QuickPayId失败, serialNumber:{%s} orderNumber:{%s}", serialNumber, orderNumber);
            LOGGER.error(message);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
            throw new InstaException(CloudSubscribeErrorCode.OceanUpdateCardInfoException);
        }

        // 更新卡信息
        userPayInfo.setPayId(quickPayId);
        userPayInfo.setCardType(createIdEncryptionPaymentResponse.getCardType());
        userPayInfo.setDeductCategory(createIdEncryptionPaymentResponse.getCardNumber());
        userPayInfo.setCardCountry(createIdEncryptionPaymentResponse.getCardCountry());

        if (PaymentBusinessType.FREE_SUBSCRIBE_PAY.equals(paymentBusinessType)) {
            String message = String.format("[订阅支付0元验卡]-主流程 无需3DS更新卡信息 serialNumber:%s orderNumber:%s", serialNumber, orderNumber);
            LOGGER.info(message);
            updateSubscribeBillingAddress(oceanFreeCheckRecord);

            LOGGER.info("[订阅支付0元验卡]-主流程 开始撤销预授权 serialNumber:{} orderNumber:{}", serialNumber, orderNumber);
            // 预授权成功，则代表客户支付方式验证成功，接下来需要取消预授权释放资金
            doPreAuthRequest(oceanFreeCheckRecord, oceanPaymentConfig);
        }
        return "";
    }

    /**
     * 执行预授权
     *
     * @param oceanFreeCheckRecord
     * @param oceanPaymentConfig
     */
    @Retryable(value = RetryHandlerException.class, include = RetryHandlerException.class, maxAttempts = 1, backoff = @Backoff(value = 100L, multiplier = 1.1))
    public void doPreAuthRequest(OceanFreeCheckRecord oceanFreeCheckRecord, OceanPaymentConfig oceanPaymentConfig) {
        String preAuthResponse = null;
        OceanPreAuthRequest oceanPreAuthRequest = null;
        String serialNumber = oceanFreeCheckRecord.getSerialNumber();
        String orderNumber = oceanFreeCheckRecord.getOrderNumber();
        String startMessage = String.format("[订阅支付0元验卡]-PreAuth 撤销预授权流程开始 serialNumber:{%s} orderNumber:{%s}", serialNumber, orderNumber);
        LOGGER.info(startMessage);
        try {
            oceanPreAuthRequest = new OceanPreAuthRequest(oceanPaymentConfig);
            oceanPreAuthRequest.setPayment_authType(PaymentConstant.OCEAN_PRE_AUTH);
            oceanPreAuthRequest.setPayment_id(oceanFreeCheckRecord.getPaymentId());
            oceanPreAuthRequest.setSecureCode(oceanPaymentConfig.getSecureCode());
            oceanPreAuthRequest.sign();
            preAuthResponse = oceanPreAuthRequest.executePost();
            LOGGER.info("[订阅支付0元验卡]-PreAuth 撤销预授权请求完毕 serialNumber:{} orderNumber:{} request:{} response:{}", serialNumber, orderNumber, JSON.toJSONString(oceanPreAuthRequest), preAuthResponse);
        } catch (Exception e) {
            String message = String.format("[订阅支付0元验卡]-PreAuth 撤销预授权接口请求失败-准备重试 serialNumber:{%s} orderNumber:{%s} message:{%s} request:%s response:%s", serialNumber, orderNumber, e.getMessage(), JSON.toJSONString(oceanPreAuthRequest), preAuthResponse);
            LOGGER.error(message, e);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
            throw new RetryHandlerException("[订阅支付0元验卡]-PreAuth 撤销预授权接口请求失败-准备重试 error:" + e.getMessage());
        }
        if (StringUtil.isBlank(preAuthResponse)) {
            LOGGER.info("[订阅支付0元验卡]-PreAuth serialNumber:{} orderNumber:{} 撤销预授权响应为空-准备重试 data:{}", serialNumber, orderNumber, preAuthResponse);
            throw new RetryHandlerException("重试一次");
        }
        JSONObject preAuthJson = JsonUtil.xmlStrToJson(preAuthResponse);
        OceanPreAuthResponse oceanPreAuthResponse = JSONObject.parseObject(preAuthJson.getString("respon"), OceanPreAuthResponse.class);
        if (oceanPreAuthResponse == null) {
            String message = String.format("[订阅支付0元验卡]-PreAuth 撤销预授权时 json解析响应为空-准备重试 serialNumber:{%s} orderNumber:{%s}", serialNumber, orderNumber);
            LOGGER.info(message);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.DevNotice, FeiShuAtUser.WXQ);
            return;
        }
        LOGGER.info("[订阅支付0元验卡]-PreAuth 撤销预授权数据解析 serialNumber:{} orderNumber:{} status:{} data:{}", serialNumber, orderNumber, oceanPreAuthResponse.getPaymentStatus(), oceanPreAuthResponse);
        if (!oceanPreAuthResponse.isSuccess()) {
            LOGGER.info("[订阅支付0元验卡]-PreAuth 撤销预授权失败-再次重试 serialNumber:{} orderNumber:{} status:{} data:{}", serialNumber, orderNumber, oceanPreAuthResponse.getPaymentStatus(), oceanPreAuthResponse);
            throw new RetryHandlerException("重试一次");
        }
        LOGGER.info("[订阅支付0元验卡]-PreAuth 撤销预授权成功 serialNumber:{} orderNumber:{} status:{}", serialNumber, orderNumber, oceanPreAuthResponse.getPaymentStatus());
        LOGGER.info("[订阅支付0元验卡]-主流程 撤销预流程结束 serialNumber:{} orderNumber:{}", serialNumber, orderNumber);

    }

    /**
     * 预授权失败重试兜底
     *
     * @param retryHandlerException
     * @param oceanFreeCheckRecord
     * @param oceanPaymentConfig
     */
    @Recover
    public void recover(RetryHandlerException retryHandlerException, OceanFreeCheckRecord oceanFreeCheckRecord, OceanPaymentConfig oceanPaymentConfig) {
        LOGGER.error("Fallback after all retries failed: " + retryHandlerException.getMessage());
        String serialNumber = oceanFreeCheckRecord.getSerialNumber();
        String recoverMessage = String.format("钱海预授权交易撤销失败，请于钱海后台手动撤销\n钱海帐号:%s 预授权交易号%s", oceanPaymentConfig.getAccount(), oceanFreeCheckRecord.getPaymentId());
        LOGGER.error(recoverMessage);
        FeiShuMessageUtil.storeGeneralMessage(recoverMessage, FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.ZM, FeiShuAtUser.LCX);
        FeiShuMessageUtil.storeGeneralMessage(String.format("[订阅支付0元验卡]-PreAuth 错误信息：%s orderNumber:%s\n%s", recoverMessage, serialNumber, retryHandlerException.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
    }

    /**
     * 记录ocean payment调用记录
     *
     * @param orderNumber
     * @param requestBody
     * @param responseBody
     */
    @Async
    public void saveOceanPaymentCallRecord(Object orderNumber, Map<String, Object> requestBody, String responseBody) {
        storeSdkCallRecordService.saveSdkCallRecord((String) orderNumber, (String) orderNumber, JSONObject.toJSONString(requestBody), JsonUtil.xmlStrToJson(responseBody).toJSONString(),
                StoreSdkCallBusinessType.OCEAN_PAYMENT, StoreSdkCallApiType.OCEAN_PAYMENT_API);
    }

    /**
     * 更新钱海支付信息
     *
     * @param orderNumber
     * @param quickPayId
     * @param cardType
     * @param cardCountry
     * @param cardNumber
     */
    @Async
    public void updateOceanPaymentPayIdAndCardInfo(String orderNumber, String quickPayId, String cardType,
                                                   String cardCountry, String cardNumber) {
        UserPayInfo userPayInfo = paymentHelper.getUserPayInfo(orderNumber);
        if (StringUtil.isBlank(quickPayId)) {
            LOGGER.info(String.format("ocean获取pay id失败！订单号【%s】", orderNumber));
            return;
        }

        String encryptPayId = AESUtil.encode(AESUtil.PAY_TOKEN_KEY, quickPayId);
        userPayInfo.setPayId(encryptPayId);
        userPayInfo.setCardType(cardType);
        userPayInfo.setCardCountry(cardCountry);
        userPayInfo.setDeductCategory(cardNumber);
        userPayInfo.setPaymentMethod(StorePaymentMethodEnum.OCEAN_PAYMENT.getName());
        userPayInfo.setPaymentSubMethod(StringUtils.EMPTY);
        userPayInfo.setPaymentTradeId(StringUtils.EMPTY);
        userPayInfo.setUpdateTime(LocalDateTime.now());
        paymentHelper.saveOrUpdatePayInfo(userPayInfo);
    }

    /**
     * ocean 支付ModelAndView
     *
     * @param result
     * @return
     */
    public ModelAndView getModelAndView(Object result) {
        JSONObject resultJson = (JSONObject) JSONObject.toJSON(result);
        String requestUrl = resultJson.getString("requestUrl");
        String method = resultJson.getString("method");
        resultJson.remove("requestUrl");
        resultJson.remove("method");

        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/payment/ocean_payment.btl");
        modelAndView.addObject("requestUrl", requestUrl + method);
        modelAndView.addObject("params", resultJson);
        return modelAndView;
    }

    /**
     * 判断是否需重试-12h后
     *
     * @param order
     * @param paymentDetails
     */
    public void handleRetryLogic(Order order, String paymentDetails) {
        // 对首次扣款失败发送邮件
        paymentHelper.processSubscribeRenewOrderFailed(order);
        if (StringUtils.isBlank(paymentDetails)) {
            return;
        }
        // 交易支付失败处理,对首次扣款失败进行重试
        LOGGER.info(String.format("扣款失败，尝试重新扣款。。。。 订单号{%s}", order.getOrderNumber()));
        String[] detailsArrays = paymentDetails.split(":");
        if (detailsArrays.length == OceanConstant.REASON_NUMBER) {
            String responseCode = detailsArrays[0];
            // 发送延迟扣款消息
            if (NumberUtils.isNumber(responseCode) && !OceanConstant.RETRY_CODES.contains(Integer.parseInt(responseCode))) {
                paymentHelper.doRenewOrderPayRetryLogic(order);
            }
        }
    }

    /**
     * 更新订阅地址信息
     *
     * @param oceanFreeCheckRecord
     */
    public void updateSubscribeBillingAddress(OceanFreeCheckRecord oceanFreeCheckRecord) {
        String billingAddressStr = oceanFreeCheckRecord.getBillingAddress();
        SubscribeBillingAddressDTO billingAddressParam = JSON.parseObject(billingAddressStr, SubscribeBillingAddressDTO.class);
        Integer userId = oceanFreeCheckRecord.getUserId();
        String serialNumber = oceanFreeCheckRecord.getSerialNumber();
        // 不存在有效订阅记录，不允许更新账单信息
        CloudStorageSubscribe storageSubscribe = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(userId);
        if (Objects.isNull(storageSubscribe)) {
            LOGGER.error(String.format("[订阅支付0元验卡]-更新账单地址 不存在有效订阅记录，不允许更新账单信息【%s】", userId));
            throw new InstaException(com.insta360.store.business.exception.CommonErrorCode.InvalidParameterException);
        }

        // 转换账单信息
        SubscribeBillingAddress billingAddressDb = subscribeBillingAddressService.getByInstaAccount(userId);
        SubscribeBillingAddress subscribeBillingAddress = billingAddressParam.buildPojoObject();
        subscribeBillingAddress.setInstaAccount(userId);
        if (Objects.nonNull(billingAddressDb)) {
            subscribeBillingAddress.setId(billingAddressDb.getId());
            subscribeBillingAddress.setInstaAccount(billingAddressDb.getInstaAccount());
            subscribeBillingAddress.setCountry(billingAddressDb.getCountry());
        }
        Order order = orderService.getByOrderNumber(storageSubscribe.getOrderNumber());
        OrderBillingAddress orderBillingAddress = orderBillingAddressService.getOrderBillingAddressByOrderId(order.getId());
        if (orderBillingAddress != null) {
            subscribeBillingAddress.setCountry(orderBillingAddress.getCountry());
            subscribeBillingAddress.setCountryCode(orderBillingAddress.getCountryCode());
        }

        String message = String.format("[订阅支付0元验卡]-更新账单地址 账单数据封装完成serialNumber:%s orderNumber:%s billingAddressParam:%s billingAddress:%s", serialNumber, storageSubscribe.getOrderNumber(), JSON.toJSONString(billingAddressParam), JSON.toJSONString(billingAddressDb));
        LOGGER.info(message);

        subscribeBillingAddressService.saveOrUpdate(subscribeBillingAddress);
        LOGGER.info("[订阅支付0元验卡]-更新账单地址 账单地址更新完毕 billingAddress:{}", subscribeBillingAddress);
    }

    /**
     * 初始化部分支付信息
     *
     * @param order
     * @return
     */
    protected OceanPaymentProductInfoBO initPaymentInfo(Order order) {
        OceanPaymentProductInfoBO paymentInfo = new OceanPaymentProductInfoBO();

        StringBuilder productSku = new StringBuilder();
        StringBuilder productName = new StringBuilder();
        StringBuilder productNum = new StringBuilder();
        StringBuilder productPrice = new StringBuilder();

        // 产品信息
        List<OrderItem> items = orderItemService.getByOrder(order.getId());
        CountryConfig countryConfig = countryConfigService.getByCountry(order.country());

        for (int i = 0, j = items.size() - 1; i <= j; i++) {
            OrderItem orderItem = items.get(i);

            ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(orderItem.getProduct(), countryConfig.language());
            CommodityPrice commodityPrice = commodityPriceService.getPrice(orderItem.getCommodity(), order.country());
            CommodityCode commodityCode = commodityCodeService.getCommodityCode(orderItem.getCommodity(), order.getArea());
            if (i != j) {
                productSku.append(commodityCode == null ? " " : commodityCode.getCode()).append(",");
                productName.append(productInfo == null ? "" : productInfo.getName()).append(",");
                productNum.append(orderItem.getNumber()).append(",");
                productPrice.append(commodityPrice == null ? "" : commodityPrice.getAmount()).append(",");
            } else {
                productSku.append(commodityCode == null ? " " : commodityCode.getCode());
                productName.append(productInfo == null ? "" : productInfo.getName());
                productNum.append(orderItem.getNumber());
                productNum.append(commodityPrice.getAmount());
            }
        }

        // 产品名称字段不能超出255个字符，达到阈值后最后三位补上"..."
        int maxProductNameLength = 255;
        if (productName.length() >= maxProductNameLength) {
            productName = new StringBuilder(productName.substring(0, 252) + "...");
        }

        paymentInfo.setProductName(productName.toString());
        paymentInfo.setProductSku(StringUtils.isBlank(productSku.toString()) ? "N/A" : productSku.toString());
        paymentInfo.setProductNum(productNum.toString());
        paymentInfo.setProductPrice(productPrice.toString());

        OrderPayment orderPayment = orderPaymentService.getByOrderNumber(order.getOrderNumber());
        paymentInfo.setProductPrice(orderPayment.getTotalPayPrice().getAmount() + "");
        LOGGER.info("ocean payment DirectPayment productPack orderNumber:{},productInfo:{}", order.getOrderNumber(), JSONObject.toJSONString(paymentInfo));

        return paymentInfo;
    }

    /**
     * 执行 OceanDirectPaymentRequest 接口
     *
     * @param cloudSubscribeParam
     * @param paymentExtra
     * @param oceanPaymentConfig
     * @param order
     * @param payId
     * @param oceanFreeCheckRecord
     * @param subscribeBillingAddress
     * @return
     */
    private OceanDirectPaymentResponse doOceanDirectPayment(CloudSubscribeDTO cloudSubscribeParam,
                                                            PaymentExtra paymentExtra,
                                                            OceanPaymentConfig oceanPaymentConfig,
                                                            Order order,
                                                            String payId,
                                                            OceanFreeCheckRecord oceanFreeCheckRecord,
                                                            SubscribeBillingAddressDTO subscribeBillingAddress) {
        String orderNumber = oceanFreeCheckRecord.getOrderNumber();
        String serialNumber = oceanFreeCheckRecord.getSerialNumber();
        OceanDirectPaymentRequest oceanDirectPaymentRequest = new OceanDirectPaymentRequest(oceanPaymentConfig);
        buildDirectPaymentParam(oceanDirectPaymentRequest, order, payId, cloudSubscribeParam, paymentExtra, oceanFreeCheckRecord, subscribeBillingAddress);
        oceanDirectPaymentRequest.sign();
        LOGGER.info("[订阅支付0元验卡]-预授权 开始请求 serialNumber:{} orderNumber:{} request:{}", serialNumber, orderNumber, JSONObject.toJSONString(oceanDirectPaymentRequest));

        String executePost = oceanDirectPaymentRequest.executePost();
        LOGGER.info("[订阅支付0元验卡]-预授权 预授权请求完毕 serialNumber:{} orderNumber:{} request:{} 原始报文response:{}", serialNumber, orderNumber, JSONObject.toJSONString(oceanDirectPaymentRequest), executePost);
        JSONObject directPaymentResultJson = JsonUtil.xmlStrToJson(executePost);
        OceanDirectPaymentResponse oceanDirectPaymentResponse = JSONObject.parseObject(directPaymentResultJson.getString("response"), OceanDirectPaymentResponse.class);
        LOGGER.info("[订阅支付0元验卡]-预授权 解析json serialNumber:{} orderNumber:{} request:{} response:{}", serialNumber, orderNumber, JSONObject.toJSONString(oceanDirectPaymentRequest), JSON.toJSONString(oceanDirectPaymentResponse));

        oceanFreeCheckRecord.setPaymentId(oceanDirectPaymentResponse.getPaymentId());
        oceanFreeCheckRecordService.updateById(oceanFreeCheckRecord);

        return oceanDirectPaymentResponse;
    }

    /**
     * 执行 CreateIdEncryptionPayment接口
     *
     * @param cardData
     * @param orderNumber
     * @param serialNumber
     * @param oceanPaymentConfig
     * @param order
     * @param subscribeBillingAddress
     * @return
     */
    private CreateIdEncryptionPaymentResponse doCreateIDEncryptionPaymentRequest(String cardData, String orderNumber, String serialNumber, OceanPaymentConfig oceanPaymentConfig, Order order, SubscribeBillingAddressDTO subscribeBillingAddress) {
        LOGGER.info("[订阅支付0元验卡]-主流程 获取payId开始 serialNumber:{} orderNumber:{}", serialNumber, orderNumber);
        CreateIDEncryptionPaymentRequest encryptionPaymentRequest = new CreateIDEncryptionPaymentRequest(oceanPaymentConfig);
        buildCreateIDEncryptionParam(encryptionPaymentRequest, cardData, order, subscribeBillingAddress);
        encryptionPaymentRequest.sign();
        LOGGER.info("[订阅支付0元验卡]-获取payId 开始请求 serialNumber:{} orderNumber:{} request:{}", serialNumber, orderNumber, JSONObject.toJSONString(encryptionPaymentRequest));
        String executePost = encryptionPaymentRequest.executePost();
        LOGGER.info("[订阅支付0元验卡]-获取payId 获取payIdq请求结束 serialNumber:{} orderNumber:{} request:{} 原始报文response:{}", serialNumber, orderNumber, JSON.toJSONString(encryptionPaymentRequest), executePost);
        JSONObject resultJson = JsonUtil.xmlStrToJson(executePost);
        CreateIdEncryptionPaymentResponse response = new CreateIdEncryptionPaymentResponse(resultJson.getJSONObject("response"));
        LOGGER.info("[订阅支付0元验卡]-获取payId 报文解析json serialNumber:{} orderNumber:{} request:{} response:{}", serialNumber, orderNumber, JSON.toJSONString(encryptionPaymentRequest), resultJson.toJSONString());
        return response;
    }

    /**
     * 获取redis中的ID
     *
     * @param orderNumberTemp
     * @return
     */
    private String getPaymentId(String orderNumberTemp) {
        return Optional.ofNullable(RedisTemplateUtil.getValue(RedisKeyConstant.OCEAN_ORDER_PAYMENT_ID + orderNumberTemp)).map(Object::toString).orElse(StringUtils.EMPTY);
    }

    /**
     * 获取免费订阅支付 free
     *
     * @param creditCardName
     * @param country
     * @return
     */
    private OceanPaymentConfig getOceanFreeSubscribePublicKey(String creditCardName, InstaCountry country) {
        return getFreeSubscribeNewPk(country, creditCardName);
    }

    /**
     * 获取订阅支付 PK
     *
     * @param creditCardName
     * @param country
     * @return
     */
    private OceanPaymentConfig getOceanSubscribePublicKey(String creditCardName, InstaCountry country) {
        return getSubscribeNewPk(country, creditCardName);
    }

    /**
     * 获取普通支付 PK
     *
     * @param creditCardName
     * @param country
     * @return
     */
    private OceanPaymentConfig getOceanNormalPublicKey(String creditCardName, InstaCountry country) {
        return getNewPk(country, creditCardName);
    }

    /**
     * 获取钱海新公钥（美国主体切换）
     *
     * @param country
     * @param creditCardName
     * @return
     */
    private OceanPaymentConfig getNewPk(InstaCountry country, String creditCardName) {
        switch (country) {
            case CN:
                return OceanCreditCardType.isVMM(creditCardName) ? oceanCNCreditCardConfig : null;
            case US:
                return OceanCreditCardType.isVMAJDD(creditCardName) ? oceanUSCreditCardConfig : null;
            default:
                return OceanCreditCardType.isVMM(creditCardName) ? oceanCreditCardConfig : OceanCreditCardType.isADJDC(creditCardName) ? oceanOtherCreditCardConfig : null;
        }
    }

    /**
     * 获取钱海新公钥（免费试用订阅）
     *
     * @param country
     * @param creditCardName
     * @return
     */
    private OceanPaymentConfig getFreeSubscribeNewPk(InstaCountry country, String creditCardName) {
        switch (country) {
            case CN:
                return OceanCreditCardType.isVMM(creditCardName) ? oceanCNFreeSubscribeConfig : null;
            case US:
                return OceanCreditCardType.isVMAJDD(creditCardName) ? oceanUSCreditCardFreeSubscribeConfig : null;
            default:
                return OceanCreditCardType.isVMM(creditCardName) ? oceanCreditCardFreeSubscribeConfig : OceanCreditCardType.isADJDC(creditCardName) ? otherCreditCardFreeSubscribeConfig : null;
        }
    }

    /**
     * 获取钱海新公钥（订阅）
     *
     * @param country
     * @param creditCardName
     * @return
     */
    private OceanPaymentConfig getSubscribeNewPk(InstaCountry country, String creditCardName) {
        switch (country) {
            case CN:
                return OceanCreditCardType.isVMM(creditCardName) ? oceanCNFirstSubscribeConfig : null;
            case US:
                return OceanCreditCardType.isVMAJDD(creditCardName) ? oceanUSCreditCardFirstSubscribeConfig : null;
            default:
                return OceanCreditCardType.isVMM(creditCardName) ? oceanCreditCardFirstSubscribeConfig : OceanCreditCardType.isADJDC(creditCardName) ? otherCreditCardFirstSubscribeConfig : null;
        }
    }

    /**
     * 构建创建快捷支付id参数
     *
     * @param encryptionPaymentRequest
     * @param order                    订单
     * @param payId
     * @param cloudSubscribeParam
     * @param paymentExtra             支付信息额外
     * @param oceanFreeCheckRecord     海洋自由检查记录
     * @param subscribeBillingAddress
     */
    private void buildDirectPaymentParam(OceanDirectPaymentRequest encryptionPaymentRequest,
                                         Order order,
                                         String payId,
                                         CloudSubscribeDTO cloudSubscribeParam,
                                         PaymentExtra paymentExtra,
                                         OceanFreeCheckRecord oceanFreeCheckRecord,
                                         SubscribeBillingAddressDTO subscribeBillingAddress) {
        String serialNumber = oceanFreeCheckRecord.getSerialNumber();
        String orderNumber = order.getOrderNumber();
        OrderPayment orderPayment = orderPaymentService.getByOrderNumber(orderNumber);
        encryptionPaymentRequest.setCard_data(cloudSubscribeParam.getCardData());
        Integer userId = order.getUserId();
        oceanFreeCheckRecord.setUserId(userId);
        encryptionPaymentRequest.setCustomer_id(String.valueOf(userId));
        encryptionPaymentRequest.setOrder_number(serialNumber);

        encryptionPaymentRequest.setBackUrl(subscribeBackUrl + String.format("?insta_callback=%s", EncodingUtil.encode(RSAUtil.encryptByMyPub(orderNumber))));
        encryptionPaymentRequest.setNoticeUrl(subscribeNoticeUrl + String.format("?insta_callback=%s", EncodingUtil.encode(RSAUtil.encryptByMyPub(serialNumber))));
        encryptionPaymentRequest.setMethods(OceanPaymentMethod.credit_card.getMethodParaName());
        encryptionPaymentRequest.setCard_secureCode(cloudSubscribeParam.getCardCvv());
        encryptionPaymentRequest.setQuickpay_id(payId);

        String currency = orderPayment.currency().name();
        encryptionPaymentRequest.setOrder_currency(currency);

        OceanDirectPaymentAmountEnum oceanDirectPaymentAmountEnum = OceanDirectPaymentAmountEnum.matchCode(currency);
        if (Objects.isNull(oceanDirectPaymentAmountEnum)) {
            String message = String.format("[订阅支付0元验卡]-预授权 支付金额转换失败 serialNumber:{%s} orderNumber:{%s} currency:{%s}", serialNumber, orderNumber, currency);
            LOGGER.info(message);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW, FeiShuAtUser.WXQ);
        } else {
            encryptionPaymentRequest.setOrder_amount(oceanDirectPaymentAmountEnum.getAmount().toString());
        }
        encryptionPaymentRequest.setOrder_notes(OceanConstant.FIRST_ORDER_NOTES);

        if (Objects.isNull(subscribeBillingAddress)) {
            OrderBillingAddress orderBillingAddress = orderBillingAddressService.getOrderBillingAddressByOrderId(order.getId());
            subscribeBillingAddress = new SubscribeBillingAddressDTO();
            BeanUtil.copyProperties(orderBillingAddress, subscribeBillingAddress);
            throw new InstaException(OrderErrorCode.OrderInfoMissingException);
        }

        // 账单地址
        encryptionPaymentRequest.setBilling_firstName(subscribeBillingAddress.getFirstName());
        encryptionPaymentRequest.setBilling_lastName(subscribeBillingAddress.getLastName());
        encryptionPaymentRequest.setBilling_email(order.getContactEmail());
        encryptionPaymentRequest.setBilling_phone(subscribeBillingAddress.getPhone());
        encryptionPaymentRequest.setBilling_country(order.getArea());
        encryptionPaymentRequest.setBilling_state(subscribeBillingAddress.getOceanCode());
        encryptionPaymentRequest.setBilling_city(subscribeBillingAddress.getCity());
        encryptionPaymentRequest.setBilling_address(subscribeBillingAddress.getAddress());
        encryptionPaymentRequest.setBilling_ip(paymentExtra.getClientIP());
        encryptionPaymentRequest.setBilling_zip(subscribeBillingAddress.getZipCode());

        OceanPaymentProductInfoBO oceanPaymentProductInfoBO = initPaymentInfo(order);
        // 商品信息
        encryptionPaymentRequest.setProductSku(oceanPaymentProductInfoBO.getProductSku());
        encryptionPaymentRequest.setProductName(oceanPaymentProductInfoBO.getProductName());
        encryptionPaymentRequest.setProductNum(oceanPaymentProductInfoBO.getProductNum());
        encryptionPaymentRequest.setProductPrice(oceanPaymentProductInfoBO.getProductPrice());
        Boolean threeDomainTrade = cloudSubscribeParam.getThreeDomainTrade();
        // 是否走3D验证（强制--本土支付无需该参数）
        if (threeDomainTrade != null) {
            LOGGER.info("[订阅支付0元验卡]-预授权 支付金额转换失败 serialNumber:{} orderNumber:{} three_domain_trade:{}", serialNumber, orderNumber, threeDomainTrade);
            encryptionPaymentRequest.setET_NOTES(threeDomainTrade ? OceanConstant.THREE_DOMAIN_TRADE : OceanConstant.NOT_THREE_DOMAIN_TRADE);
        }
        encryptionPaymentRequest.setService_mode(OceanConstant.SERVICE_MODE_E1);
    }

    /**
     * 构建创建快捷支付id参数
     *
     * @param encryptionPaymentRequest
     * @param cardData                 卡数据
     * @param order                    订单
     * @param billingAddressDTO        订单计费地址
     */
    private void buildCreateIDEncryptionParam(CreateIDEncryptionPaymentRequest encryptionPaymentRequest, String cardData, Order order, SubscribeBillingAddressDTO billingAddressDTO) {
        encryptionPaymentRequest.setCard_data(cardData);
        Integer userId = order.getUserId();
        encryptionPaymentRequest.setCustomer_id(String.valueOf(userId));
        encryptionPaymentRequest.setMethods(OceanPaymentMethod.credit_card.getMethodParaName());
        encryptionPaymentRequest.setOrder_number(order.getOrderNumber());

        // 发版过渡兼容
        if (Objects.isNull(billingAddressDTO)) {
            billingAddressDTO = new SubscribeBillingAddressDTO();
            OrderBillingAddress orderBillingAddress = orderBillingAddressService.getOrderBillingAddressByOrderId(order.getId());
            if (orderBillingAddress == null) {
                throw new InstaException(OrderErrorCode.OrderInfoMissingException);
            }
            BeanUtil.copyProperties(orderBillingAddress, billingAddressDTO);
        }

        // 账单地址
        encryptionPaymentRequest.setBilling_firstName(billingAddressDTO.getFirstName());
        encryptionPaymentRequest.setBilling_lastName(billingAddressDTO.getLastName());
        encryptionPaymentRequest.setBilling_email(order.getContactEmail());
        encryptionPaymentRequest.setBilling_phone(billingAddressDTO.getPhone());
        encryptionPaymentRequest.setBilling_city(billingAddressDTO.getCity());
        encryptionPaymentRequest.setBilling_zip(billingAddressDTO.getZipCode());
        encryptionPaymentRequest.setBilling_address(billingAddressDTO.getAddress());
        encryptionPaymentRequest.setBilling_state(billingAddressDTO.getOceanCode());
        encryptionPaymentRequest.setBilling_country(order.getArea());
    }
}

