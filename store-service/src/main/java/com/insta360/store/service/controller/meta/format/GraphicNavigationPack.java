package com.insta360.store.service.controller.meta.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommoditySaleState;
import com.insta360.store.business.commodity.service.CommoditySaleStateService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.enums.GraphicNavigationType;
import com.insta360.store.business.meta.model.GraphicNavigation;
import com.insta360.store.business.meta.model.GraphicNavigationInfo;
import com.insta360.store.business.meta.model.GraphicNavigationMain;
import com.insta360.store.business.meta.service.GraphicNavigationInfoService;
import com.insta360.store.business.meta.service.GraphicNavigationMainService;
import com.insta360.store.business.meta.service.GraphicNavigationService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.service.controller.meta.vo.GraphicNavigationInfoVO;
import com.insta360.store.service.controller.meta.vo.GraphicNavigationMainVO;
import com.insta360.store.service.controller.meta.vo.GraphicNavigationVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/3/14
 * @Description:
 */
@Component
public class GraphicNavigationPack {

    @Autowired
    GraphicNavigationService graphicNavigationService;

    @Autowired
    GraphicNavigationMainService graphicNavigationMainService;

    @Autowired
    GraphicNavigationInfoService graphicNavigationInfoService;

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    /**
     * 图文导航封装
     *
     * @param country
     * @param language
     * @param endpoint
     */
    public List<GraphicNavigationVO> packGraphicNavigation(InstaCountry country, InstaLanguage language, Integer endpoint) {
        List<GraphicNavigation> graphicNavigations = graphicNavigationService.listByEndpoint(endpoint);
        if (CollectionUtils.isEmpty(graphicNavigations)) {
            return new ArrayList<>(0);
        }

        List<Integer> gnIds = graphicNavigations.stream().map(GraphicNavigation::getId).collect(Collectors.toList());
        List<GraphicNavigationMain> graphicNavigationMains = graphicNavigationMainService.listByGraphicNavigationIdsEnabled(gnIds);

        // 过滤禁售的产品套餐
        List<Integer> commodityIds = graphicNavigationMains.stream().map(GraphicNavigationMain::getCommodityId)
                .filter(commodityId -> !commodityId.equals(-1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(commodityIds)) {
            // 规则过滤
            Set<Integer> filterCommodityIds = filterCommodityIds(commodityIds, country);
            if (CollectionUtils.isNotEmpty(filterCommodityIds)) {
                graphicNavigationMains = graphicNavigationMains
                        .stream().filter(graphicNavigationMain -> {
                            // 只需过滤产品类型
                            if (graphicNavigationMain.getCommodityId() != -1) {
                                return !filterCommodityIds.contains(graphicNavigationMain.getCommodityId());
                            }
                            return true;
                        }).collect(Collectors.toList());
            }
        }
        // 无生效配置
        if (CollectionUtils.isEmpty(graphicNavigationMains)) {
            return new ArrayList<>(0);
        }

        List<Integer> mainIds = graphicNavigationMains.stream().map(GraphicNavigationMain::getId).collect(Collectors.toList());
        List<GraphicNavigationInfo> graphicNavigationInfoList = graphicNavigationInfoService.listGraphicNavigationInfo(country, language, mainIds);
        if (CollectionUtils.isEmpty(graphicNavigationInfoList)) {
            return new ArrayList<>(0);
        }

        // 用户封装url key
        List<Integer> needCommodityIds = graphicNavigationMains.stream().map(GraphicNavigationMain::getCommodityId)
                .filter(commodityId -> !commodityId.equals(-1)).collect(Collectors.toList());

        // 产品套餐映射
        Map<Integer, Commodity> commodityMap = null;
        Map<Integer, Product> productMap = null;
        if (CollectionUtils.isNotEmpty(needCommodityIds)) {
            List<Commodity> commodities = commodityService.listByCommodities(needCommodityIds);
            commodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, c -> c));
            productMap = productService.listEnableByProductIds(commodityMap.values()
                    .stream().map(Commodity::getProduct).collect(Collectors.toList())).stream()
                    .collect(Collectors.toMap(Product::getId, p -> p));
        }
        Map<Integer, GraphicNavigationInfo> ngInfoMap = graphicNavigationInfoList.stream().collect(Collectors.toMap(GraphicNavigationInfo::getMainId, i -> i));
        Map<Integer, GraphicNavigationMain> ngMainMap = graphicNavigationMains.stream().collect(Collectors.toMap(GraphicNavigationMain::getId, m -> m));
        Map<Integer, GraphicNavigation> ngMap = graphicNavigations.stream().collect(Collectors.toMap(GraphicNavigation::getId, n -> n));

        // 数据封装
        return doPackGraphicNavigation(ngInfoMap, ngMainMap, ngMap, commodityMap, productMap);
    }

    /**
     * 图文导航数据封装
     *
     * @param ngInfoMap
     * @param ngMainMap
     * @param ngMap
     * @param commodityMap
     * @param productMap
     * @return
     */
    private List<GraphicNavigationVO> doPackGraphicNavigation(Map<Integer, GraphicNavigationInfo> ngInfoMap, Map<Integer,
            GraphicNavigationMain> ngMainMap, Map<Integer, GraphicNavigation> ngMap, Map<Integer, Commodity> commodityMap,
                                                              Map<Integer, Product> productMap) {
        List<GraphicNavigationVO> graphicNavigationList = new ArrayList<>(ngInfoMap.size());
        ngInfoMap.forEach((key, value) -> {
            GraphicNavigationMainVO navigationMainVo = new GraphicNavigationMainVO(ngMainMap.get(key));
            navigationMainVo.setGraphicNavigationInfo(new GraphicNavigationInfoVO(value));
            GraphicNavigationVO graphicNavigationVo = new GraphicNavigationVO(ngMap.get(navigationMainVo.getGraphicNavigationId()));
            // 产品类型 返回url key
            if (GraphicNavigationType.PRODUCT.getType().equals(graphicNavigationVo.getNavigationType())) {
                Commodity commodity = commodityMap.get(navigationMainVo.getCommodityId());
                navigationMainVo.setUrlKey(productMap.get(commodity.getProduct()).getUrlKey());
                navigationMainVo.setWebLink(null);
            }
            // web类型
            if (GraphicNavigationType.WEB.getType().equals(graphicNavigationVo.getNavigationType())) {
                navigationMainVo.setCommodityId(null);
            }
            graphicNavigationVo.setGraphicNavigationMain(navigationMainVo);
            graphicNavigationList.add(graphicNavigationVo);
        });

        // 排序
        graphicNavigationList.sort((g1, g2) -> g2.getGraphicNavigationMain().getOrderIndex() - g1.getGraphicNavigationMain().getOrderIndex());

        return graphicNavigationList;
    }

    /**
     * 过滤禁售商品
     *
     * @param commodityIds
     * @param country
     * @return
     */
    private Set<Integer> filterCommodityIds(List<Integer> commodityIds, InstaCountry country) {
        List<Commodity> commodities = commodityService.listByCommodities(commodityIds);
        Set<Integer> filterCommodityIds = new HashSet<>(commodityIds.size());

        // 套餐全禁用 提前return
        if (CollectionUtils.isEmpty(commodities)) {
            filterCommodityIds.addAll(commodityIds);
            return filterCommodityIds;
        }

        List<Integer> enableCommodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        // 去重
        if (commodityIds.size() != enableCommodityIds.size()) {
            commodityIds.removeAll(enableCommodityIds);
            filterCommodityIds.addAll(commodityIds);
        }
        List<Integer> productIds = commodities.stream().map(Commodity::getProduct).collect(Collectors.toList());
        Map<Integer, CommoditySaleState> saleStateMap = commoditySaleStateService.listSaleStateByCommodityIds(enableCommodityIds, country).stream()
                .collect(Collectors.toMap(CommoditySaleState::getCommodityId, s -> s));
        Map<Integer, Product> productMap = productService.listEnableByProductIds(productIds).stream().collect(Collectors.toMap(Product::getId, p -> p));

        filterCommodityIds.addAll(commodities.stream().filter(commodity -> {
            CommoditySaleState commoditySaleState = saleStateMap.get(commodity.getId());
            // 禁售套餐状态
            if (Objects.isNull(commoditySaleState) || SaleState.remove.getCode() == commoditySaleState.getSaleState()) {
                return true;
            }
            // 禁用产品
            return Objects.isNull(productMap.get(commodity.getProduct()));
        }).map(Commodity::getId).collect(Collectors.toList()));

        return filterCommodityIds;
    }
}
