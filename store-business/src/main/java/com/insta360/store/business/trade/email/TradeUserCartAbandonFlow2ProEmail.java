package com.insta360.store.business.trade.email;

import com.insta360.store.business.meta.bo.EmailTemplateParams;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/5/30 下午2:12
 */
@Scope("prototype")
@Component
public class TradeUserCartAbandonFlow2ProEmail extends BaseTradeEmail {

    /**
     * 邮件模板key
     */
    protected String templateName;

    @Override
    public String getTemplateName() {
        return getUserCartEmailEnum().getTemplateKey();
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        // 产品套餐信息
        templateParams.addBodyParam("cartList", packCartCommodityInfo());

        // 购物车信息币种
        templateParams.addBodyParam("signal", getCartSignal());

        // 是否标志前缀
        templateParams.addBodyParam("isSuffix", getSuffix());

        // 购物车链接
        templateParams.addBodyParam("cartLink", getCartLink(UserCartEmailEnum.FLOW2PRO_FIRST_EMAIL.getLinkName()));
    }
}
