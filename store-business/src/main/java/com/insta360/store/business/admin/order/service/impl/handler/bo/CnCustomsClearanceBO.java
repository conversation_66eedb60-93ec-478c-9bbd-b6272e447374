package com.insta360.store.business.admin.order.service.impl.handler.bo;

import com.insta360.store.business.admin.order.enums.ShipToCountry;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.order.model.OrderItem;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/3/14
 */
public class CnCustomsClearanceBO implements Serializable {

    /**
     * 套餐报关配置
     */
    private CommodityMeta commodityMeta;

    /**
     * 订单商品
     */
    private OrderItem orderItem;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 运单号
     */
    private String waybillNumber;

    /**
     * 运抵国
     */
    private ShipToCountry shipToCountry;

    /**
     * 排序
     */
    private Integer sortId;


    public CommodityMeta getCommodityMeta() {
        return commodityMeta;
    }

    public void setCommodityMeta(CommodityMeta commodityMeta) {
        this.commodityMeta = commodityMeta;
    }

    public OrderItem getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(OrderItem orderItem) {
        this.orderItem = orderItem;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getWaybillNumber() {
        return waybillNumber;
    }

    public void setWaybillNumber(String waybillNumber) {
        this.waybillNumber = waybillNumber;
    }

    public ShipToCountry getShipToCountry() {
        return shipToCountry;
    }

    public void setShipToCountry(ShipToCountry shipToCountry) {
        this.shipToCountry = shipToCountry;
    }

    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

    @Override
    public String toString() {
        return "CnCustomsClearanceBO{" +
                "commodityMeta=" + commodityMeta +
                ", orderItem=" + orderItem +
                ", orderNumber='" + orderNumber + '\'' +
                ", waybillNumber='" + waybillNumber + '\'' +
                ", shipToCountry=" + shipToCountry +
                ", sortId=" + sortId +
                '}';
    }
}
