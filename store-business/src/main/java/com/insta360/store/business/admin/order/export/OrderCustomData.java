package com.insta360.store.business.admin.order.export;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 订单定制数据
 *
 * @Author: hyc
 * @Date: 2019-08-27
 * @Description:
 */
public class OrderCustomData {

    @ExcelProperty("生产包装袋序号")
    private String id;

    @ExcelProperty("订单号")
    private String orderNumber;

    @ExcelProperty("订单状态")
    private String orderState;

    @ExcelProperty("RMA类型")
    private String rmaType;

    @ExcelProperty("RMA状态")
    private String rmaState;

    @ExcelProperty("订单项")
    private String orderItemName;

    @ExcelProperty("数量")
    private String orderItemNumber;

    @ExcelProperty("图纸")
    private String produceImageUrl;

    @ExcelProperty("效果图")
    private String customImageUrl;

    @ExcelProperty("背景图类型")
    private String imageOrigin;

    @ExcelProperty("ICON类型")
    private String iconType;

    public OrderCustomData() {
    }

    public OrderCustomData(String id, String orderNumber, String orderState, String rmaType, String rmaState, String orderItemName, String orderItemNumber, String produceImageUrl, String customImageUrl, String imageOrigin, String iconType) {
        this.id = id;
        this.orderNumber = orderNumber;
        this.orderState = orderState;
        this.rmaType = rmaType;
        this.rmaState = rmaState;
        this.orderItemName = orderItemName;
        this.orderItemNumber = orderItemNumber;
        this.produceImageUrl = produceImageUrl;
        this.customImageUrl = customImageUrl;
        this.imageOrigin = imageOrigin;
        this.iconType = iconType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public String getRmaState() {
        return rmaState;
    }

    public void setRmaState(String rmaState) {
        this.rmaState = rmaState;
    }

    public String getOrderItemName() {
        return orderItemName;
    }

    public void setOrderItemName(String orderItemName) {
        this.orderItemName = orderItemName;
    }

    public String getOrderItemNumber() {
        return orderItemNumber;
    }

    public void setOrderItemNumber(String orderItemNumber) {
        this.orderItemNumber = orderItemNumber;
    }

    public String getProduceImageUrl() {
        return produceImageUrl;
    }

    public void setProduceImageUrl(String produceImageUrl) {
        this.produceImageUrl = produceImageUrl;
    }

    public String getCustomImageUrl() {
        return customImageUrl;
    }

    public void setCustomImageUrl(String customImageUrl) {
        this.customImageUrl = customImageUrl;
    }

    public String getImageOrigin() {
        return imageOrigin;
    }

    public void setImageOrigin(String imageOrigin) {
        this.imageOrigin = imageOrigin;
    }

    public String getIconType() {
        return iconType;
    }

    public void setIconType(String iconType) {
        this.iconType = iconType;
    }

    @Override
    public String toString() {
        return "OrderCustomData{" +
                "id='" + id + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", orderState='" + orderState + '\'' +
                ", rmaType='" + rmaType + '\'' +
                ", rmaState='" + rmaState + '\'' +
                ", orderItemName='" + orderItemName + '\'' +
                ", orderItemNumber='" + orderItemNumber + '\'' +
                ", produceImageUrl='" + produceImageUrl + '\'' +
                ", customImageUrl='" + customImageUrl + '\'' +
                ", imageOrigin='" + imageOrigin + '\'' +
                ", iconType='" + iconType + '\'' +
                '}';
    }
}
