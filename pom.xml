<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.insta360.compass</groupId>
        <artifactId>compass-parent</artifactId>
        <version>1.11.2-SNAPSHOT</version>
    </parent>

    <!--parent模块信息-->
    <groupId>com.insta360.store</groupId>
    <artifactId>parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <!--定义子模块-->
    <modules>
        <module>store-business</module>
        <module>store-admin</module>
        <module>store-service</module>
        <module>store-job</module>
    </modules>

    <!--仓库定义-->
    <repositories>
        <repository>
            <id>insta-repository-public</id>
            <url>http://nexus.arashivision.com:9999/repository/maven-backend-public/</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
    </dependencies>

    <!-- 参与打包的资源 -->
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.btl</include>
                    <include>**/*.html</include>
                    <include>**/*.png</include>
                    <include>**/*.jpg</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.btl</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
</project>
