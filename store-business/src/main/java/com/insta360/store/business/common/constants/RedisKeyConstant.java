package com.insta360.store.business.common.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/15
 */
public interface RedisKeyConstant {

    /**
     * 定制图像
     */
    String CUSTOM_IMAGE_KEY = "business:custom:";

    /**
     * PayPal缓存目录
     */
    String PAYPAL_REDIS_KEY = "business:payment:paypal:";

    /**
     * 商城云服务订阅用户缓存key
     */
    String STORE_CLOUD_SUBSCRIBE_USER_KEY = "store:cloud:subscribe:user:";

    /**
     * 商城美国关税sku缓存key
     */
    String STORE_US_CUSTOMS_TAX_SKU_KEY = "store:customs:tax:sku";

    /**
     * 用户教育课程邮件限制数量
     */
    String EMAIL_EDUCATION_COURSE_LIMIT_KEY = "business:email:course:";

    /**
     * redisson锁
     */
    String EMAIL_LOCK = "email:course:lock";

    String OCEAN_CREATE_ID_ENCRYPTION_PAYMENT_RESPONSE = "ocean:create_id_encryption_payment_response:";

    String OCEAN_ORDER_CACHE = "ocean:order_number:";

    String OCEAN_ORDER_PAYMENT_ID = "ocean:payment_id:";

    /**
     * 游客下单邮件风控
     */
    String GUEST_ORDER_EMAIL_CONTROL = "guest:order:email:control:";

    /**
     * 获取prime bwp token
     */
    String PRIME_BWP_TOKEN = "prime:bwp:token";

    /**
     * prime lwa token
     */
    String PRIME_LWA_ACCESS_TOKEN = "prime:lwa:access_token:";

    String PRIME_LWA_REFRESH_TOKEN = "prime:lwa:refresh_token:";

    String PRIME_OFFERS_PROVIDER = "prime:offers:provider:";
}
