package com.insta360.store.business.order.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/11/19
 * @Description:
 */
public class OrderQueryDTO implements Serializable {

    @JSONField(name = "page_number")
    private Integer pageNumber;

    @JSONField(name = "page_size")
    private Integer pageSize;

    private String type;

    @JSONField(name = "order_number")
    private String orderNumber;

    @JSONField(name = "user_email")
    private String userEmail;

    private String inscp;

    @JSONField(name = "promo_code")
    private String promoCode;

    @JSONField(name = "coupon_code")
    private String couponCode;

    @JSONField(name = "pay_channel")
    private String payChannel;

    private JSONArray paymentChannelList;

    private JSONArray noPaymentChannelList;

    @JSONField(name = "country_code")
    private String countryCode;

    private JSONArray countryCodeList;

    private JSONArray noCountryCodeList;

    @JSONField(name = "order_endpoint")
    private Integer orderEndpoint;

    /**
     * 开始时间
     */
    private LocalDateTime from;

    /**
     * 结束时间
     */
    private LocalDateTime end;

    private JSONArray states;

    private JSONArray products;

    @JSONField(name = "no_products")
    private JSONArray noProducts;

    private JSONArray commodities;

    @JSONField(name = "rma_state")
    private Integer rmaState;

    @JSONField(name = "no_rma_state")
    private Integer noRmaState;

    /**
     * 以旧换新订单过滤标记
     */
    private Boolean tradeupOrderFilterMark;

    /**
     * 订单是否包含卖家备注
     */
    private Boolean orderSellerRemarkMark;

    /**
     * 售后状态
     */
    private List<Integer> rmaStateList;

    /**
     * 序列号
     */
    private String deviceSerial;

    /**
     * 物流号
     */
    private String logisticsNumber;

    /**
     * utm来源
     */
    private List<String> utmSourceList;

    public List<Integer> getRmaStateList() {
        return rmaStateList;
    }

    public void setRmaStateList(List<Integer> rmaStateList) {
        this.rmaStateList = rmaStateList;
    }

    public Boolean getTradeupOrderFilterMark() {
        return tradeupOrderFilterMark;
    }

    public void setTradeupOrderFilterMark(Boolean tradeupOrderFilterMark) {
        this.tradeupOrderFilterMark = tradeupOrderFilterMark;
    }

    public Boolean getOrderSellerRemarkMark() {
        return orderSellerRemarkMark;
    }

    public void setOrderSellerRemarkMark(Boolean orderSellerRemarkMark) {
        this.orderSellerRemarkMark = orderSellerRemarkMark;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getInscp() {
        return inscp;
    }

    public void setInscp(String inscp) {
        this.inscp = inscp;
    }

    public String getPromoCode() {
        return promoCode;
    }

    public void setPromoCode(String promoCode) {
        this.promoCode = promoCode;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Integer getOrderEndpoint() {
        return orderEndpoint;
    }

    public void setOrderEndpoint(Integer orderEndpoint) {
        this.orderEndpoint = orderEndpoint;
    }

    public LocalDateTime getFrom() {
        return from;
    }

    public void setFrom(LocalDateTime from) {
        this.from = from;
    }

    public LocalDateTime getEnd() {
        return end;
    }

    public void setEnd(LocalDateTime end) {
        this.end = end;
    }

    public JSONArray getStates() {
        return states;
    }

    public void setStates(JSONArray states) {
        this.states = states;
    }

    public JSONArray getProducts() {
        return products;
    }

    public void setProducts(JSONArray products) {
        this.products = products;
    }

    public JSONArray getCommodities() {
        return commodities;
    }

    public void setCommodities(JSONArray commodities) {
        this.commodities = commodities;
    }

    public Integer getRmaState() {
        return rmaState;
    }

    public void setRmaState(Integer rmaState) {
        this.rmaState = rmaState;
    }

    public Integer getNoRmaState() {
        return noRmaState;
    }

    public void setNoRmaState(Integer noRmaState) {
        this.noRmaState = noRmaState;
    }

    public JSONArray getNoProducts() {
        return noProducts;
    }

    public void setNoProducts(JSONArray noProducts) {
        this.noProducts = noProducts;
    }

    public JSONArray getNoCountryCodeList() {
        return noCountryCodeList;
    }

    public void setNoCountryCodeList(JSONArray noCountryCodeList) {
        this.noCountryCodeList = noCountryCodeList;
    }

    public JSONArray getCountryCodeList() {
        return countryCodeList;
    }

    public void setCountryCodeList(JSONArray countryCodeList) {
        this.countryCodeList = countryCodeList;
    }

    public JSONArray getPaymentChannelList() {
        return paymentChannelList;
    }

    public void setPaymentChannelList(JSONArray paymentChannelList) {
        this.paymentChannelList = paymentChannelList;
    }

    public JSONArray getNoPaymentChannelList() {
        return noPaymentChannelList;
    }

    public void setNoPaymentChannelList(JSONArray noPaymentChannelList) {
        this.noPaymentChannelList = noPaymentChannelList;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getLogisticsNumber() {
        return logisticsNumber;
    }

    public void setLogisticsNumber(String logisticsNumber) {
        this.logisticsNumber = logisticsNumber;
    }

    public List<String> getUtmSourceList() {
        return utmSourceList;
    }

    public void setUtmSourceList(List<String> utmSourceList) {
        this.utmSourceList = utmSourceList;
    }
}
