package com.insta360.store.admin.controller.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.controller.test.dto.ResellerCommissionUpdateDTO;
import com.insta360.store.admin.controller.test.handler.CommodityMetaExcelDTO;
import com.insta360.store.admin.controller.test.handler.CommodityMetaListener;
import com.insta360.store.business.commodity.bo.CommodityDeliveryTimeConfigBO;
import com.insta360.store.business.commodity.enums.CommodityTypeEnum;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.commodity.service.impl.helper.CommodityHelper;
import com.insta360.store.business.discount.constant.DiscountCommonConstant;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.model.GiftCardThreshold;
import com.insta360.store.business.discount.model.GiftCardThresholdItem;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.discount.service.GiftCardThresholdItemService;
import com.insta360.store.business.discount.service.GiftCardThresholdService;
import com.insta360.store.business.discount.utils.DiscountCodeGenerator;
import com.insta360.store.business.insurance.service.ClimbServiceCommodityService;
import com.insta360.store.business.insurance.service.ProductBindClimbServiceService;
import com.insta360.store.business.integration.wto.enums.OmsIntegrationBusinessType;
import com.insta360.store.business.integration.wto.oms.service.handler.OmsService;
import com.insta360.store.business.integration.wto.oms.service.helper.OmsBundleCommodityCodeSyncHelper;
import com.insta360.store.business.integration.wto.oms.service.helper.OmsPushPlatformRouterHelper;
import com.insta360.store.business.integration.wto.service.factory.IntegrationSimpleFactory;
import com.insta360.store.business.integration.yipiaoyun.service.YiPiaoYunAutoInvoiceService;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.model.StoreConfig;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.reseller.helper.ResellerSendMessageHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.reseller.bo.CommissionDetailBO;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.model.ResellerOrderItem;
import com.insta360.store.business.reseller.service.ResellerOrderItemService;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.reseller.service.impl.helper.ResellerOrderHelper;
import com.insta360.store.business.reseller.service.impl.helper.commission.ResellerCommissionCalculateContext;
import com.insta360.store.business.reseller.service.impl.helper.commission.ResellerOrderCommissionCounterHelper;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/16
 */
@RestController
public class TestController {

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    OrderService orderService;

    @Autowired
    ResellerOrderCommissionCounterHelper resellerOrderCommissionCounterHelper;

    @Autowired
    ResellerOrderHelper resellerOrderHelper;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    ResellerOrderItemService resellerOrderItemService;

    @Autowired
    YiPiaoYunAutoInvoiceService yiPiaoYunAutoInvoiceService;

    @Autowired
    ResellerSendMessageHelper resellerSendMessageHelper;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    GiftCardThresholdService giftCardThresholdService;

    @Autowired
    GiftCardThresholdItemService giftCardThresholdItemService;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    CommodityMetaService commodityMetaService;

    @Autowired
    OmsPushPlatformRouterHelper omsPushPlatformRouterHelper;

    @Autowired
    ClimbServiceCommodityService climbServiceCommodityService;

    @Autowired
    ProductBindClimbServiceService productBindClimbServiceService;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductCommodityTypeService productCommodityTypeService;

    @Autowired
    BundleCommodityDetailService bundleCommodityDetailService;

    @Autowired
    IntegrationSimpleFactory integrationSimpleFactory;

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    OmsBundleCommodityCodeSyncHelper omsBundleCommodityCodeSyncHelper;

    @Autowired
    CommodityDeliveryTimeConfigService commodityDeliveryTimeConfigService;

    @Autowired
    CommodityHelper commodityHelper;

    /**
     * 分销订单佣金重计算
     *
     * @param resellerCommissionUpdateParam
     * @return
     */
    @PostMapping("/admin/test/commissionRecalculation")
    public Response<Object> resellerOrderCommissionRecalculation(@Validated @RequestBody ResellerCommissionUpdateDTO resellerCommissionUpdateParam) {
        if (Objects.isNull(resellerCommissionUpdateParam) || CollectionUtils.isEmpty(resellerCommissionUpdateParam.getOrderNumbers()) || StringUtils.isBlank(resellerCommissionUpdateParam.getPromoCode())) {
            return Response.failed("参数为空");
        }

        Reseller reseller = resellerService.getByPromoCode(resellerCommissionUpdateParam.getPromoCode());
        if (Objects.isNull(reseller)) {
            return Response.failed("分销商不存在");
        }

        List<ResellerOrder> resellerOrderList = resellerOrderService.listByOrderNumbers(resellerCommissionUpdateParam.getOrderNumbers());
        if (CollectionUtils.isEmpty(resellerOrderList)) {
            return Response.failed("订单不存在");
        }

        List<ResellerOrder> updateResellerOrderList = Lists.newArrayListWithCapacity(resellerOrderList.size());
        List<ResellerOrderItem> updateResellerOrderItemList = Lists.newArrayList();

        for (ResellerOrder resellerOrder : resellerOrderList) {
            // 获取订单商品列表
            List<ResellerOrderItem> resellerOrderItemList = resellerOrderItemService.getByOrder(resellerOrder.getId());
            if (CollectionUtils.isEmpty(resellerOrderItemList)) {
                continue;
            }

            // 订单提现币种
            Currency withdrawCurrency = resellerOrder.useCurrency();

            resellerOrderItemList.stream()
                    .forEach(resellerOrderItem -> {
                        // 售后单
                        RmaOrder rmaOrder = rmaOrderService.getByOrderItem(resellerOrderItem.getOrderItemId());
                        // 订单商品
                        OrderItem orderItem = orderItemService.getById(resellerOrderItem.getOrderItemId());

                        // 订单商品佣金重计算
                        ResellerCommissionCalculateContext context = ResellerCommissionCalculateContext.buildContext(reseller, orderItem, rmaOrder, withdrawCurrency, resellerOrderItem.getInitCommissionRate(), resellerOrder.getPayCurrencyUsdRate(), resellerOrder.getWithdrawCurrencyUsdRate(), false);
                        CommissionDetailBO commissionDetail = resellerOrderCommissionCounterHelper.orderItemCommissionInitCalculate(context);
                        resellerOrderItem.setEstimatedIncome(commissionDetail.getEstimatedIncome());
                    });

            // 订单佣金总收益
            BigDecimal totalEstimatedIncome = resellerOrderItemList.stream()
                    .map(resellerOrderItem -> new BigDecimal(String.valueOf(resellerOrderItem.getEstimatedIncome())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            resellerOrder.setCommission(totalEstimatedIncome.doubleValue());

            updateResellerOrderList.add(resellerOrder);
            updateResellerOrderItemList.addAll(resellerOrderItemList);
        }

        if (CollectionUtils.isNotEmpty(updateResellerOrderList) && CollectionUtils.isNotEmpty(updateResellerOrderItemList)) {
            // 更新DB
            resellerOrderHelper.batchUpdateResellerOrder(updateResellerOrderList, updateResellerOrderItemList);
        }

        return Response.ok();
    }

    /**
     * 创建分销订单
     *
     * @param orderNumber
     * @return
     */
    @GetMapping("/admin/test/createResellerOrder")
    public Response<Object> createResellerOrder(@RequestParam("orderNumber") String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            return Response.failed();
        }

        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (!OrderPaymentState.PAYED.equals(orderPayment.paymentState())) {
            return Response.failed();
        }
        resellerSendMessageHelper.sendCreateResellerOrderMsg(order, orderPayment.getPayTime());

        return Response.ok();
    }


    /**
     * 国内订单开票
     *
     * @param orderNumber
     * @return
     */
    @GetMapping("/admin/test/billing")
    public Response<Object> billing(@RequestParam("orderNumber") String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            return Response.failed();
        }
        yiPiaoYunAutoInvoiceService.autoInvoiceHandle(order);
        return Response.ok();
    }

    /**
     * 免单券复制
     *
     * @param giftCardCode
     * @return
     */
    @GetMapping("/admin/test/freeCouponCopyTest")
    public Response<Object> freeCouponCopyTest(@RequestParam("giftCardCode") String giftCardCode, @RequestParam("number") Integer number) {
        GiftCard giftCard = giftCardService.getByCode(giftCardCode);
        if (Objects.isNull(giftCard)) {
            return Response.failed("giftCard不存在");
        }

        GiftCardThreshold giftCardThreshold = giftCardThresholdService.getThresholdByGiftCardCode(giftCardCode);
        if (Objects.isNull(giftCardThreshold)) {
            return Response.failed("giftCardThreshold不存在");
        }

        List<GiftCardThresholdItem> giftCardThresholdItemList = giftCardThresholdItemService.listByThresholdId(giftCardThreshold.getId());
        if (CollectionUtils.isEmpty(giftCardThresholdItemList)) {
            return Response.failed("giftCardThresholdItem不存在");
        }

        Set<String> codes = this.getCodes(number);

        List<GiftCard> targetCodeList = codes.stream()
                .map(code -> {
                    GiftCard targetCode = new GiftCard();
                    BeanUtils.copyProperties(giftCard, targetCode);
                    targetCode.setGiftCardCode(code);
                    targetCode.setId(null);
                    return targetCode;
                })
                .collect(Collectors.toList());

        List<GiftCardThreshold> targetGiftCardThresholdList = codes.stream()
                .map(code -> {
                    GiftCardThreshold targetGiftCardThreshold = new GiftCardThreshold();
                    targetGiftCardThreshold.setGiftCardCode(code);
                    targetGiftCardThreshold.setThresholdTypeMark(giftCardThreshold.getThresholdTypeMark());
                    return targetGiftCardThreshold;
                })
                .collect(Collectors.toList());


        giftCardService.saveBatch(targetCodeList);
        giftCardThresholdService.saveBatch(targetGiftCardThresholdList);


        List<GiftCardThresholdItem> targetGiftCardThresholdItemList = targetGiftCardThresholdList.stream()
                .map(threshold -> giftCardThresholdItemList.stream()
                        .map(thresholdItem -> {
                            GiftCardThresholdItem targetGiftCardThresholdItem = new GiftCardThresholdItem();
                            targetGiftCardThresholdItem.setNum(thresholdItem.getNum());
                            targetGiftCardThresholdItem.setThresholdId(threshold.getId());
                            targetGiftCardThresholdItem.setProductCommodityId(thresholdItem.getProductCommodityId());
                            return targetGiftCardThresholdItem;
                        })
                        .collect(Collectors.toList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());

        giftCardThresholdItemService.batchCreateThresholdItem(targetGiftCardThresholdItemList);

        return Response.ok(codes);
    }

    /**
     * 生成100个code
     *
     * @return
     */
    private Set<String> getCodes(Integer number) {
        String code;
        HashSet<String> codeSet = new HashSet<>(number);
        do {
            do {
                code = new DiscountCodeGenerator().generate(DiscountCommonConstant.GIFT_CARD_PREFIX);
            } while (StringUtils.isBlank(code) || Objects.nonNull(giftCardService.getByCode(code)));
            // 添加code
            codeSet.add(code);
        } while (codeSet.size() < number);

        return codeSet;
    }

    /**
     * 批量导入商品元数据
     *
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/admin/test/commodityMetaExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response<Object> commodityMetaExcel(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();

        CommodityMetaListener listener = new CommodityMetaListener();
        EasyExcel.read(inputStream, CommodityMetaExcelDTO.class, listener)
                .sheet()
                .doRead();

        List<CommodityMetaExcelDTO> dataList = listener.getExcelCacheDataList();

        if (CollectionUtils.isEmpty(dataList)) {
            return Response.failed("Excel数据读取失败.");
        }

        List<Integer> commodityIdList = dataList.stream()
                .map(CommodityMetaExcelDTO::getCommodityId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, CommodityMeta> commodityMetaMap = commodityBatchHelper.commodityMetaMapByCommodityIds(commodityIdList);

        List<CommodityMeta> commodityMetaList = dataList.stream()
                .map(commodityMetaExcel -> {
                    CommodityMeta commodityMeta = commodityMetaMap.get(commodityMetaExcel.getCommodityId());
                    if (Objects.isNull(commodityMeta)) {
                        return null;
                    }
                    if (Objects.nonNull(commodityMetaExcel.getWeight())) {
                        commodityMeta.setWeight(commodityMetaExcel.getWeight());
                    }
                    if (Objects.nonNull(commodityMetaExcel.getGw())) {
                        commodityMeta.setGw(commodityMetaExcel.getGw());
                    }
                    if (Objects.nonNull(commodityMetaExcel.getNw())) {
                        commodityMeta.setNw(commodityMetaExcel.getNw());
                    }

                    return commodityMeta;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        boolean result = commodityMetaService.updateBatchById(commodityMetaList);

        return Response.ok(result);
    }

    /**
     * 同步订单到oms
     *
     * @param orderNumberList
     * @return
     */
    @PostMapping(value = "/admin/test/syncToOmsOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> syncToOmsOrder(@RequestBody List<String> orderNumberList) {
        if (CollectionUtils.isNotEmpty(orderNumberList)) {
            return Response.failed();
        }
        List<Order> orders = orderService.listByOrderNumber(orderNumberList);
        if (CollectionUtils.isEmpty(orders)) {
            return Response.failed();
        }
        omsPushPlatformRouterHelper.determinePlatformForOrder(orders);
        return Response.ok();
    }

    @PostMapping(value = "/admin/test/bundleCommodityInit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> addBundleCommodities() {
        // 只配置非工单套餐
        List<Integer> productIds = productService.getProducts(false)
                .stream()
                .map(Product::getId)
                .collect(Collectors.toList());
        List<Commodity> commodities = commodityService.listByProductIds(productIds);
        // 待更新套餐类型与组合套餐明细
        List<ProductCommodityType> productCommodityTypeList = new ArrayList<>();
        List<BundleCommodityDetail> bundleCommodityDetailList = new ArrayList<>();
        for (Commodity commodity : commodities) {
            String includeCommodities = commodity.getIncludeCommodities();
            // 判断是否为组合套餐
            if (StringUtils.isNotBlank(includeCommodities) && !"[]".equals(includeCommodities)) {
                productCommodityTypeList.add(new ProductCommodityType(commodity.getId(), commodity.getProduct(), CommodityTypeEnum.BUNDLE.getType()));
                List<JSONObject> jsonObjects = JSON.parseArray(includeCommodities, JSONObject.class);
                for (JSONObject jsonObject : jsonObjects) {
                    Integer id = jsonObject.getInteger("id");
                    Integer number = jsonObject.getInteger("number");
                    // 添加子商品明细
                    bundleCommodityDetailList.add(new BundleCommodityDetail(commodity.getId(), commodity.getProduct(), id, number));
                }
            } else {
                productCommodityTypeList.add(new ProductCommodityType(commodity.getId(), commodity.getProduct(), CommodityTypeEnum.SINGLE.getType()));
            }
        }
        productCommodityTypeService.addCommodityTypeList(productCommodityTypeList);
        bundleCommodityDetailService.addBundleCommodityDetailList(bundleCommodityDetailList);
        return Response.ok();
    }

    /**
     * 组合商品OMS同步
     *
     * @return
     */
    @PostMapping(value = "/admin/test/bundleCommodityCodeOmsSync", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> bundleCommodityCodeOmsSync() {
        // 只配置非工单套餐
        List<Product> products = productService.getProducts(false);
        List<Integer> productIds = products
                .stream()
                .map(Product::getId)
                .collect(Collectors.toList());
        List<Commodity> commodities = commodityService.listByProductIds(productIds);
        // 套餐与对应的启用状态
        Map<Integer, Boolean> commodityEnableMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getEnabled));
        // 商品与对应的启用状态
        Map<Integer, Boolean> productEnableMap = products.stream().collect(Collectors.toMap(Product::getId, Product::getEnabled));

        // 待更新套餐类型
        List<ProductCommodityType> productCommodityTypeList = new ArrayList<>();

        // 组合套餐集合
        List<Commodity> bundleCommodityList = new ArrayList<>();
        // 遍历套餐集合，分类型添加
        for (Commodity commodity : commodities) {
            String includeCommodities = commodity.getIncludeCommodities();
            // 判断是否为组合套餐
            if (StringUtils.isNotBlank(includeCommodities) && !"[]".equals(includeCommodities)) {
                productCommodityTypeList.add(new ProductCommodityType(commodity.getId(), commodity.getProduct(), CommodityTypeEnum.BUNDLE.getType()));
                bundleCommodityList.add(commodity);
            } else {
                productCommodityTypeList.add(new ProductCommodityType(commodity.getId(), commodity.getProduct(), CommodityTypeEnum.SINGLE.getType()));
            }
        }

        List<Integer> bundleCommodityIds = bundleCommodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        // 批量查询CN地区组合商品料号，并分组
        Map<Integer, CommodityCode> commodityCodeMap = commodityCodeService.listCommodityCode(bundleCommodityIds, InstaCountry.CN.name())
                .stream()
                .collect(Collectors.toMap(CommodityCode::getCommodity, Function.identity()));
        // 获取oms服务
        OmsService omsService = integrationSimpleFactory.getOmsService(OmsIntegrationBusinessType.STORE_BUNDLE_COMMODITY_CODE_QUERY);
        // 异步处理
        omsBundleCommodityCodeSyncHelper.bundleCommodityBatchSync(bundleCommodityList, omsService, commodityCodeMap, commodityEnableMap, productEnableMap);
        productCommodityTypeService.addCommodityTypeList(productCommodityTypeList);
        return Response.ok();
    }

    /**
     * 获取美国商品发货时间配置缓存
     *
     * @return
     */
    @GetMapping("/admin/test/getUsDeliveryTimeConfig")
    public Response<Object> getUsDeliveryTimeConfigCache(@RequestParam String key) {
        String configValue = storeConfigService.getConfigValue(StoreConfigKey.commodity_delivery_time_config_key);
        List<CommodityDeliveryTimeConfigBO> commodityDeliveryTimeConfigBOList = JSON.parseArray(configValue, CommodityDeliveryTimeConfigBO.class);
        return Response.ok(JSON.toJSONString(commodityDeliveryTimeConfigBOList));
    }

    /**
     * 更新美国商品发货时间配置缓存
     *
     * @return
     */
    @PostMapping("/admin/test/updateUsDeliveryTimeConfig")
    public Response<Object> updateUsDeliveryTimeConfigCache(@RequestBody List<CommodityDeliveryTimeConfigBO> deliveryTimeConfigs) {
        if (CollectionUtils.isEmpty(deliveryTimeConfigs)) {
            return Response.failed("参数不能为空");
        }

        String json = JSON.toJSONString(deliveryTimeConfigs);
        StoreConfig storeConfig = new StoreConfig();
        storeConfig.setValue(json);
        storeConfig.setKey(StoreConfigKey.commodity_delivery_time_config_key.name());
        int i = storeConfigService.updateByKey(storeConfig);

        return Response.ok(i);
    }
}
