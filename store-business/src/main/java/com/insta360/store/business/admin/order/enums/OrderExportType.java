package com.insta360.store.business.admin.order.enums;

import com.insta360.store.business.admin.order.export.*;
import com.insta360.store.business.admin.reseller.export.ResellerOrderData;
import com.insta360.store.business.integration.google.model.GoogleCommodity;
import com.insta360.store.business.integration.google.model.GoogleCommodityInsertData;
import com.insta360.store.business.integration.google.model.GoogleUpShelfCommodityData;

import java.util.Arrays;

/**
 * @Author: wkx
 * @Date: 2020/12/2
 * @Description:
 */
public enum OrderExportType {

    /**
     * FedEx导出
     */
    FEDEX_EXPORT(FedexOrderExportData.class, "fedex_export"),

    /**
     * 订单导出
     */
    ORDER_EXPORT(OrderExportData.class, "order_export"),

    /**
     * 订单导出
     */
    FEDEX_VAT_EXPORT(VatOrderExportData.class, "fedex_vat_export"),

    /**
     * DHL导出
     */
    DHL_EXPORT(DhlOrderExportData.class, "dhl_export"),

    /**
     * 分销订单导出
     */
    RESELLER_EXPORT(ResellerOrderData.class, "reseller_export"),

    /**
     * GO2定制订单导出
     */
    CUSTOM_EXPORT(OrderCustomData.class, "custom_export"),

    /**
     * FMG定制订单导出
     */
    FMG_CUSTOM_EXPORT(OrderCustomData.class, "fmg_custom_export"),

    /**
     * GO3定制订单导出
     */
    GO3_CUSTOM_EXPORT(OrderCustomData.class, "go3_custom_export"),

    /**
     * GO3S定制订单导出
     */
    GO3S_CUSTOM_EXPORT(OrderCustomData.class, "go3s_custom_export"),

    /**
     * 商城订单批量导出
     */
    STORE_NUMBER_EXPORT(OrderExportData.class, "store_number_export"),

    /**
     * 马帮订单导出
     */
    MB_EXPORT(DhlOrderExportData.class, "mb_export"),

    /**
     * Google商品套餐导出
     */
    GOOGLE_COMMODITY_EXPORT(GoogleCommodity.class, "google_commodity_export"),

    /**
     * Google商品套餐-新增
     */
    GOOGLE_COMMODITY_INSERT_EXPORT(GoogleCommodityInsertData.class, "google_commodity_insert_export"),

    /**
     * Google套餐上下架
     */
    GOOGLE_COMMODITY_UP_SHELF_EXPORT(GoogleUpShelfCommodityData.class, "google_commodity_up_shelf_export"),

    /**
     * DHL VAT海关报税导出
     */
    DHL_VAT_EXPORT(DhlVatOrderExportData.class, "dhl_vat_export"),

    /**
     * DHL VAT海关报关'发票'、'转运联'、'运底联'导出
     */
    DHL_VAT_WAYBILL(null, "dhl_vat_wayBill"),

    /**
     * 物流商报关导出
     */
    LOGISTICS_QUOTATION(LogisticsExportData.class, "LOGISTICS_QUOTATION"),

    /**
     * XML报关
     */
    SIX_IN_ONE_XML(null, "six_in_one_xml"),

    /**
     * Fedex API（发票、运单、底联）
     */
    FEDEX_API(null,"fedex_api"),

    /**
     * 9610报关
     */
    CN_CUSTOMS_CLEARANCE(OrderCnCustomsClearanceExcelData.class,"cn_customs_clearance")

    ;

    /**
     * 对应的数据实体类
     */
    private final Class<?> dataModuleClass;

    /**
     * 导出类型名称
     */
    private final String exportType;

    OrderExportType(Class<?> dataModuleClass, String exportType) {
        this.dataModuleClass = dataModuleClass;
        this.exportType = exportType;
    }

    public Class<?> getDataModuleClass() {
        return dataModuleClass;
    }

    public String getExportType() {
        return exportType;
    }

    public static OrderExportType param(String param) {
        for (OrderExportType exportType : values()) {
            if (exportType.getExportType().equals(param)) {
                return exportType;
            }
        }
        return null;
    }

    /**
     * 是定制导出类型
     *
     * @param orderExportType 订单导出类型
     * @return {@link Boolean }
     */
    public static Boolean isCustomExportType(OrderExportType orderExportType) {
        return Arrays.asList(CUSTOM_EXPORT, GO3_CUSTOM_EXPORT, GO3S_CUSTOM_EXPORT, FMG_CUSTOM_EXPORT)
                     .contains(orderExportType);
    }
}
