package com.insta360.store.business.order.service.constant;

import com.insta360.store.business.meta.enums.Currency;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 用于自动推单过滤的常量池
 * @Date 2024/7/23 15:18
 */
public class OrderAutoPushConstantPool {

    /**
     * 补差价套餐，在判断相机数量时排除
     */
    public static final List<Integer> DIFFERENCE_PACKAGE = Arrays.asList(991, 3660);

    /**
     * 低价套餐id集合，仅含此类套餐时，不需要遵循低价拦截规则
     */
    public static final List<Integer> LOW_PRICE_COMMODITY_IDS = Arrays.asList(3351, 3352, 3353, 991, 3660);

    /**
     * 内部邮箱后缀
     */
    public static final String INTERNAL_EMAIL_SUFFIX = "@insta360.com";

    /**
     * 德国的一些公司信息
     */
    public static final List<String> GERMANY_COMPANY_MSG = Arrays.asList("GESELLSCHAFT MIT BESCHRÄNKTER HAFTUNG", "UNTERNEHMERGESELLSCHAFT UG, HAFTUNGSBESCHRÄNKT", "AKTIENGESELLSCHAFT",
            "KOMMANDITGESELLSCHAFT AUF AKTIEN", "GMBH", "MINI-GMBH", "KGAA", "GMBH & CO.KG", "HAFTUNGSBESCHRÄNKT");

    /**
     * 德国的一些公司信息（短字符）
     */
    public static final List<String> GERMANY_COMPANY_MSG_SHORT = Arrays.asList("MBH", "AG", "GBR", "OHG", "KG", "UG");

    /**
     * 一些敏感词
     */
    public static final List<String> ROC_ARRAY = Arrays.asList("ROC", "R.O.C", "REPUBLIC OF CHINA", "中华民国", "中華民國");

    /**
     * 德国公司的最小支付金额
     */
    public static final float GERMANY_COMPANY_MIN_PRICE = 150.0f;

    /**
     * 以往订单最小单量，超过即可能重复订单
     */
    public static final int MIN_QUANTITY_IN_PAST = 3;

    /**
     * 同类型机器数最小值
     */
    public static final int MIN_QUANTITY_THE_SAME_PRODUCT = 4;

    /**
     * 订单商品数量限制-相机限制数量
     */
    public static final int AUTO_PUSH_CAMERA_COUNT_LIMIT = 2;

    /**
     * 订单商品数量限制-配件限制数量
     */
    public static final int AUTO_PUSH_ACCESSORY_COUNT_LIMIT = 3;

    /**
     * NCC指定配件
     */
    public static final List<Integer> NCC_COMMODITY_ID = Arrays.asList(2156, 2661);

    /**
     * NCC指定二级类目
     */
    public static final List<String> NCC_CATEGORY = Arrays.asList("CF_PROFESSION_CAMERA", "CF_360_3D_CAMERA", "CF_ACTION_CAMERA");

    /**
     * NCC 商品购买数量限制
     */
    public static final int NCC_NUMBER_LIMIT = 2;

    /**
     * 美属维京群岛邮编
     */
    public static final List<String> VIRGIN_ISLAND = Arrays.asList("00801", "00802", "00803", "00804", "00805");

    /**
     * 州到邮政编码范围的映射
     */
    public static Map<String, List<ZipRange>> US_STATE_ZIP_RANGES = new HashMap<>();

    /**
     * 先声明
     */
    private static final Map<Currency, Float> MIN_PRICE_MAP;

    static {
        MIN_PRICE_MAP = new HashMap<>();
        MIN_PRICE_MAP.put(Currency.CNY, 18f);
        MIN_PRICE_MAP.put(Currency.USD, 9f);
        MIN_PRICE_MAP.put(Currency.AUD, 14f);
        MIN_PRICE_MAP.put(Currency.CAD, 13f);
        MIN_PRICE_MAP.put(Currency.EUR, 10f);
        MIN_PRICE_MAP.put(Currency.GBP, 8f);
        MIN_PRICE_MAP.put(Currency.HKD, 72f);
        MIN_PRICE_MAP.put(Currency.JPY, 1299f);
        MIN_PRICE_MAP.put(Currency.KRW, 10999f);
        MIN_PRICE_MAP.put(Currency.SGD, 14f);
        MIN_PRICE_MAP.put(Currency.TWD, 298f);
    }

    // 防止实例化
    private OrderAutoPushConstantPool() {
    }

    /**
     * 返回国家对最小金额的map
     *
     * @return
     */
    public static Map<Currency, Float> getMinPriceMap() {
        return MIN_PRICE_MAP;
    }

    static {
        // 添加关岛邮编范围
        US_STATE_ZIP_RANGES.put("GU", Arrays.asList(new ZipRange(96910, 96932)));
        // 北马里亚纳群岛
        US_STATE_ZIP_RANGES.put("MA", Arrays.asList(new ZipRange(96950, 96952)));
        // 美属萨摩亚
        US_STATE_ZIP_RANGES.put("SA", Arrays.asList(new ZipRange(96799, 96799)));
    }

    // 邮政编码范围类
    public static class ZipRange {

        /**
         * 邮政编码最小值（包含）
         */
        final int min;

        /**
         * 邮政编码最大值（包含）
         */
        final int max;

        public ZipRange(int min, int max) {
            this.min = min;
            this.max = max;
        }

        /**
         * 判断邮编是否在区间中
         *
         * @param zipCode
         * @return
         */
        public boolean contains(int zipCode) {
            return zipCode >= min && zipCode <= max;
        }
    }
}
