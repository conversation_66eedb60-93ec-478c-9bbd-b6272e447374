package com.insta360.store.business.order.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaCountry;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Author: hyc
 * @Date: 2019/2/11
 * @Description:
 */
@TableName("order_delivery")
public class OrderDelivery extends BaseModel<OrderDelivery> {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @TableId(value = "`order`")
    private Integer order;

    /**
     * last name
     */
    @JSONField(name = "last_name")
    private String lastName;

    /**
     * first name
     */
    @JSONField(name = "first_name")
    private String firstName;

    /**
     * 邮编
     */
    @JSONField(name = "zip_code")
    private String zipCode;

    /**
     * 国家编码
     */
    @JSONField(name = "country_code")
    private String countryCode;

    /**
     * 国家地区
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份/洲 对应代码
     */
    private String provinceCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 附加地址
     */
    @JSONField(name = "sub_address")
    private String subAddress;

    /**
     * 手机区号
     */
    @JSONField(name = "phone_code")
    private String phoneCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 快递公司
     */
    @JSONField(name = "express_company")
    private String expressCompany;

    /**
     * 快递编号
     */
    @JSONField(name = "express_code")
    private String expressCode;

    /**
     * 发货时间
     */
    @JSONField(name = "express_time")
    private LocalDateTime expressTime;

    /**
     * 发货通知时间（已废弃）
     */
    @JSONField(name = "express_notify_time")
    private LocalDateTime expressNotifyTime;

    /**
     * 收货时间
     */
    @JSONField(name = "receive_time")
    private LocalDateTime receiveTime;

    /**
     * 预计发货日期
     */
    @JSONField(name = "estimated_delivery_date")
    private LocalDate estimatedDeliveryDate;

    /**
     * 预估发货的天数
     */
    private Integer estimateDays;

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getExpressCompany() {
        return expressCompany;
    }

    public void setExpressCompany(String expressCompany) {
        this.expressCompany = expressCompany;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public LocalDateTime getExpressTime() {
        return expressTime;
    }

    public void setExpressTime(LocalDateTime expressTime) {
        this.expressTime = expressTime;
    }

    public LocalDateTime getExpressNotifyTime() {
        return expressNotifyTime;
    }

    public void setExpressNotifyTime(LocalDateTime expressNotifyTime) {
        this.expressNotifyTime = expressNotifyTime;
    }

    public LocalDateTime getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(LocalDateTime receiveTime) {
        this.receiveTime = receiveTime;
    }

    public LocalDate getEstimatedDeliveryDate() {
        return estimatedDeliveryDate;
    }

    public void setEstimatedDeliveryDate(LocalDate estimatedDeliveryDate) {
        this.estimatedDeliveryDate = estimatedDeliveryDate;
    }

    public Integer getEstimateDays() {
        return estimateDays;
    }

    public void setEstimateDays(Integer estimateDays) {
        this.estimateDays = estimateDays;
    }

    @Override
    public String toString() {
        return "OrderDelivery{" +
                "order=" + order +
                ", lastName='" + lastName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", phone='" + phone + '\'' +
                ", expressCompany='" + expressCompany + '\'' +
                ", expressCode='" + expressCode + '\'' +
                ", expressTime=" + expressTime +
                ", expressNotifyTime=" + expressNotifyTime +
                ", receiveTime=" + receiveTime +
                ", estimatedDeliveryDate=" + estimatedDeliveryDate +
                ", estimateDays=" + estimateDays +
                '}';
    }

    @JSONField(serialize = false)
    public InstaCountry country() {
        return InstaCountry.parse(countryCode);
    }

    public String getCombinedAdress() {
        String address = this.getAddress();
        String subAddress = this.getSubAddress();
        if (subAddress != null) {
            address += " " + subAddress;
        }
        return address;
    }

}
