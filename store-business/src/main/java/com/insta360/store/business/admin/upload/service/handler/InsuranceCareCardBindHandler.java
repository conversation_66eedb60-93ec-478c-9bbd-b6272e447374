package com.insta360.store.business.admin.upload.service.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.admin.insurance.export.InsuranceCareCardExcelData;
import com.insta360.store.business.admin.upload.bo.UploadExcelDataBO;
import com.insta360.store.business.admin.upload.enums.UploadBusinessType;
import com.insta360.store.business.admin.upload.enums.UploadModuleType;
import com.insta360.store.business.admin.upload.enums.UploadStatus;
import com.insta360.store.business.admin.upload.exception.AdminUploadErrorCode;
import com.insta360.store.business.admin.upload.service.data.BaseErrorCommonExcelData;
import com.insta360.store.business.admin.upload.service.listener.BaseListener;
import com.insta360.store.business.admin.upload.service.listener.StoreAdminExcelListener;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.model.*;
import com.insta360.store.business.insurance.service.*;
import com.insta360.store.business.insurance.service.impl.helper.generator.ActivationCardCodeGenerator;
import com.insta360.store.business.insurance.service.impl.helper.http.DeviceInfoHelper;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceActivationInfo;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 保险批量绑定
 * @Date 2023/10/18
 */
@Scope("prototype")
@Component
public class InsuranceCareCardBindHandler extends BaseUploadExcelHandler<InsuranceCareCardExcelData, StoreAdminExcelListener> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InsuranceCareCardBindHandler.class);

    @Autowired
    CareActivationCardService careActivationCardService;

    @Autowired
    DeviceInfoHelper deviceInfoHelper;

    @Autowired
    CareInsuranceActivationCardService careInsuranceActivationCardService;

    @Autowired
    ActivationCardCodeGenerator activationCardCodeGenerator;

    @Autowired
    InsuranceServiceTypeService insuranceServiceTypeService;

    @Autowired
    CareCardDeviceTypeService careCardDeviceTypeService;

    @Autowired
    CareInsuranceService careInsuranceService;

    @Autowired
    CarePlusInsuranceService carePlusInsuranceService;

    @Override
    protected List<? extends BaseErrorCommonExcelData> mainContentCheck(List<UploadExcelDataBO<InsuranceCareCardExcelData>> excelRowDataList) {
        return null;
    }

    @Override
    protected List<? extends BaseErrorCommonExcelData> overwriteDataCheck(List<UploadExcelDataBO<InsuranceCareCardExcelData>> excelRowDataList) {
        return null;
    }

    @Override
    protected StoreAdminExcelListener excelParse(String ossUrl, String taskId) {
        // 获取文件输入流
        InputStream in = super.getFileInputStream(ossUrl);
        // 读取Excel
        StoreAdminExcelListener listener = new StoreAdminExcelListener(taskId);
        EasyExcel.read(in, InsuranceCareCardExcelData.class, listener)
                .autoTrim(true)
                .ignoreEmptyRow(true)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet()
                .doRead();

        return listener;
    }

    @Override
    protected void updateData(List<UploadExcelDataBO<InsuranceCareCardExcelData>> excelRowDataList) {
        excelRowDataList.forEach(insuranceCareCardUploadExcelDataBO -> {
            InsuranceCareCardExcelData insuranceCareCardExcelData = insuranceCareCardUploadExcelDataBO.getData();

            // 绑定时间
            LocalDateTime bindTime = LocalDateTime.now();
            String deviceSerial = insuranceCareCardExcelData.getDeviceSerial();
            DeviceActivationInfo deviceActivationInfo = deviceInfoHelper.getDeviceActivationInfo(deviceSerial);
            if (Objects.nonNull(deviceActivationInfo)) {
                bindTime = deviceActivationInfo.getCreateTime();
            }

            String activationCode = insuranceCareCardExcelData.getActivationCode();

            // 设备类型
            DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(insuranceCareCardExcelData.getDeviceSerial());
            if (Objects.isNull(deviceInfo)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[care绑定批量导入]序列号:%s不存在,激活码:%s", deviceSerial, activationCode), FeiShuGroupRobot.CareCardBindNotify);
                return;
            }

            // 激活码真实性校验
            CareActivationCard careActivationCard = careActivationCardService.getByActivationCode(activationCode);
            if (Objects.isNull(careActivationCard) || careActivationCard.getIsActivation()) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[care绑定批量导入]激活码不存在或已被使用:%s已经使用!序列号:%s", activationCode, deviceSerial), FeiShuGroupRobot.CareCardBindNotify);
                return;
            }

            // check 激活码序列号是否已绑定
            CareInsuranceActivationCard serialCard = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
            CareInsurance careInsurance = careInsuranceService.getCareInsurance(deviceSerial);
            CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(deviceSerial);
            if (Objects.nonNull(serialCard) || Objects.nonNull(careInsurance) || Objects.nonNull(carePlusInsurance)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[care绑定批量导入]序列号:%s已经绑定实体卡/care/flexiCare!激活码:%s", deviceSerial, activationCode), FeiShuGroupRobot.CareCardBindNotify);
                return;
            }

            // 激活码是否已经使用
            List<CareInsuranceActivationCard> activationCodeCheckCard = careInsuranceActivationCardService.listByActivation(activationCode);
            if (CollectionUtils.isNotEmpty(activationCodeCheckCard)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[care绑定批量导入]激活码:%s已经使用!序列号:%s", activationCode, deviceSerial), FeiShuGroupRobot.CareCardBindNotify);
                return;
            }

            // 获取保险类型
            Integer deviceTypeId = careActivationCard.getDeviceTypeId();
            CareCardDeviceType careCardDeviceType = careCardDeviceTypeService.getById(deviceTypeId);
            String insuranceType = Objects.isNull(careCardDeviceType) ? ServiceType.care.name() : careCardDeviceType.getInsuranceType();

            // build
            CareInsuranceActivationCard careInsuranceActivationCard = new CareInsuranceActivationCard();
            careInsuranceActivationCard.setInsuranceType(insuranceCareCardExcelData.getDeviceSerial());
            careInsuranceActivationCard.setDeviceSerial(insuranceCareCardExcelData.getDeviceSerial());
            careInsuranceActivationCard.setInsuranceNumber(activationCardCodeGenerator.generateInsuranceNumber());
            careInsuranceActivationCard.setInsuranceType(insuranceType);
            careInsuranceActivationCard.setActivationCode(activationCode);
            careInsuranceActivationCard.setDeviceSerial(deviceSerial);
            careInsuranceActivationCard.setDeviceType(deviceInfo.getDeviceType());
            careInsuranceActivationCard.setCountry(insuranceCareCardExcelData.getArea());
            careInsuranceActivationCard.setName(insuranceCareCardExcelData.getUserName());
            careInsuranceActivationCard.setPhone(insuranceCareCardExcelData.getPhone());
            careInsuranceActivationCard.setEmail(insuranceCareCardExcelData.getEmail());
            careInsuranceActivationCard.setBindTime(bindTime);
            careInsuranceActivationCard.setExpireTime(insuranceServiceTypeService.getExpireDay(insuranceType, bindTime));
            careInsuranceActivationCard.setCreateTime(LocalDateTime.now());
            // save
            careInsuranceActivationCardService.saveCard(careInsuranceActivationCard, careActivationCard);
        });
    }

    @Override
    protected void execute(BaseListener listener) {
        if (Objects.isNull(listener)) {
            LOGGER.info("[care绑定批量导入]listener不存在... taskId:{}", uploadTaskRecord.getTaskId());
            throw new InstaException(AdminUploadErrorCode.file_not_parse_data);
        }

        try {
            // 直接导入
            long startTime = System.currentTimeMillis();
            LOGGER.info("[care绑定批量导入]导入数据落库开始... taskId:{}", uploadTaskRecord.getTaskId());

            // Excel数据落库
            this.updateData(listener.getExcelRowDataList());

            long endTime = System.currentTimeMillis();
            LOGGER.info("[care绑定批量导入]导入数据落库结束... taskId:{}，耗时:{}ms", uploadTaskRecord.getTaskId(), endTime - startTime);

            // 修改状态
            uploadTaskRecord.setState(UploadStatus.SUCCESS.getCode());
            this.updateUploadRecord(uploadTaskRecord);
        } catch (Exception e) {
            uploadTaskRecord.setState(UploadStatus.FAIL.getCode());
            this.updateUploadRecord(uploadTaskRecord);
            LOGGER.error(String.format("[care绑定批量导入]导入数据失败... taskId:%s,异常信息:%s", uploadTaskRecord.getTaskId(), e.getMessage()), e);
        }
    }

    @Override
    protected UploadModuleType getModuleType() {
        return UploadModuleType.INSURANCE_MODULE;
    }

    @Override
    protected UploadBusinessType getBusinessType() {
        return UploadBusinessType.care_card_bind;
    }
}
