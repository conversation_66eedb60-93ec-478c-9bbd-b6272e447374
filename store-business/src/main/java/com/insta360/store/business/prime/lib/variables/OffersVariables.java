package com.insta360.store.business.prime.lib.variables;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.prime.bo.PrimeShippingAddressBO;
import com.insta360.store.business.prime.constants.PrimeConstants;
import com.insta360.store.business.prime.lib.variables.OffersVariables.InputDTO.LineItemsDTO.ProductDTO.IdentifierDTO;
import com.insta360.store.business.prime.lib.variables.OffersVariables.InputDTO.LineItemsDTO.ProductDTO.PriceDTO;
import com.insta360.store.business.prime.lib.variables.OffersVariables.InputDTO.LineItemsDTO.ProductDTO.PurchaseGroupMembershipDTO;
import com.insta360.store.business.prime.lib.variables.order.ShopperIdentityDTO;
import com.insta360.store.business.utils.MathUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

public class OffersVariables implements PrimeVariables {

    private InputDTO input;

    public InputDTO getInput() {
        return input;
    }

    public void setInput(InputDTO input) {
        this.input = input;
    }

    public static class InputDTO {

        private ShopperIdentityDTO shopperIdentity;

        private List<LineItemsDTO> lineItems;

        private LocationDTO location;

        public ShopperIdentityDTO getShopperIdentity() {
            return shopperIdentity;
        }

        public void setShopperIdentity(ShopperIdentityDTO shopperIdentity) {
            this.shopperIdentity = shopperIdentity;
        }

        public List<LineItemsDTO> getLineItems() {
            return lineItems;
        }

        public void setLineItems(List<LineItemsDTO> lineItems) {
            this.lineItems = lineItems;
        }

        public LocationDTO getLocation() {
            return location;
        }

        public void setLocation(LocationDTO location) {
            this.location = location;
        }

        public static class DeliveryTermsDTO {

            private Boolean isPrimeEligible;

            public DeliveryTermsDTO() {
            }

            public DeliveryTermsDTO(Boolean isPrimeEligible) {
                this.isPrimeEligible = isPrimeEligible;
            }

            public Boolean getIsPrimeEligible() {
                return isPrimeEligible;
            }

            public void setIsPrimeEligible(Boolean isPrimeEligible) {
                this.isPrimeEligible = isPrimeEligible;
            }
        }

        public static class LocationDTO {

            private ShippingAddressDTO shippingAddress;

            public LocationDTO() {
            }

            public LocationDTO(PrimeShippingAddressBO shippingAddress) {
                ShippingAddressDTO shippingAddressDTO = new ShippingAddressDTO();
                BeanUtil.copyProperties(shippingAddress, shippingAddressDTO);
                this.shippingAddress = shippingAddressDTO;
            }

            public ShippingAddressDTO getShippingAddress() {
                return shippingAddress;
            }

            public void setShippingAddress(ShippingAddressDTO shippingAddress) {
                this.shippingAddress = shippingAddress;
            }

            public static class ShippingAddressDTO {

                private String name;

                private String streetAddress;

                private String locality;

                private String region;

                private String countryCode;

                private String postalCode;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public String getStreetAddress() {
                    return streetAddress;
                }

                public void setStreetAddress(String streetAddress) {
                    this.streetAddress = streetAddress;
                }

                public String getLocality() {
                    return locality;
                }

                public void setLocality(String locality) {
                    this.locality = locality;
                }

                public String getRegion() {
                    return region;
                }

                public void setRegion(String region) {
                    this.region = region;
                }

                public String getCountryCode() {
                    return countryCode;
                }

                public void setCountryCode(String countryCode) {
                    this.countryCode = countryCode;
                }

                public String getPostalCode() {
                    return postalCode;
                }

                public void setPostalCode(String postalCode) {
                    this.postalCode = postalCode;
                }
            }
        }

        public static class LineItemsDTO {

            private AmountDTO amount;

            private ProductDTO product;

            private DeliveryTermsDTO deliveryTerms;

            public LineItemsDTO() {
            }

            public static LineItemsDTO init(Integer quantity, BigDecimal priceAmount, Currency currency, String externalId, String bundleQuantity, String bundleExternalId, String title) {
                LineItemsDTO lineItemsDTO = new LineItemsDTO();
                String defaultUnit = PrimeConstants.Commodity.DEFAULT_UNIT;
                lineItemsDTO.setAmount(new AmountDTO(MathUtil.getBigDecimal(quantity), defaultUnit));
                ProductDTO productDTO = new ProductDTO();
                productDTO.setIdentifier(new IdentifierDTO(externalId));
                productDTO.setPrice(new PriceDTO(priceAmount, currency.name()));
                productDTO.setTitle(title);
                if (StringUtils.isNotBlank(bundleQuantity) && StringUtils.isNotBlank(bundleExternalId)) {
                    productDTO.setPurchaseGroupMembership(new PurchaseGroupMembershipDTO(bundleQuantity, bundleExternalId));
                }
                lineItemsDTO.setProduct(productDTO);
                lineItemsDTO.setDeliveryTerms(new DeliveryTermsDTO(true));
                return lineItemsDTO;
            }

            public AmountDTO getAmount() {
                return amount;
            }

            public void setAmount(AmountDTO amount) {
                this.amount = amount;
            }

            public ProductDTO getProduct() {
                return product;
            }

            public void setProduct(ProductDTO product) {
                this.product = product;
            }

            public DeliveryTermsDTO getDeliveryTerms() {
                return deliveryTerms;
            }

            public void setDeliveryTerms(DeliveryTermsDTO deliveryTerms) {
                this.deliveryTerms = deliveryTerms;
            }

            public static class AmountDTO {

                private BigDecimal value;

                private String unit;

                public AmountDTO() {
                }

                public AmountDTO(BigDecimal value, String unit) {
                    this.value = value;
                    this.unit = unit;
                }

                public BigDecimal getValue() {
                    return value;
                }

                public void setValue(BigDecimal value) {
                    this.value = value;
                }

                public String getUnit() {
                    return unit;
                }

                public void setUnit(String unit) {
                    this.unit = unit;
                }
            }

            public static class ProductDTO {

                private IdentifierDTO identifier;

                private PriceDTO price;

                private String title;

                private PurchaseGroupMembershipDTO purchaseGroupMembership;

                public IdentifierDTO getIdentifier() {
                    return identifier;
                }

                public void setIdentifier(IdentifierDTO identifier) {
                    this.identifier = identifier;
                }

                public PriceDTO getPrice() {
                    return price;
                }

                public void setPrice(PriceDTO price) {
                    this.price = price;
                }

                public String getTitle() {
                    return title;
                }

                public void setTitle(String title) {
                    this.title = title;
                }

                public PurchaseGroupMembershipDTO getPurchaseGroupMembership() {
                    return purchaseGroupMembership;
                }

                public void setPurchaseGroupMembership(PurchaseGroupMembershipDTO purchaseGroupMembership) {
                    this.purchaseGroupMembership = purchaseGroupMembership;
                }

                public static class IdentifierDTO {

                    private String externalId;

                    public IdentifierDTO(String externalId) {
                        this.externalId = externalId;
                    }

                    public IdentifierDTO() {
                    }

                    public String getExternalId() {
                        return externalId;
                    }

                    public void setExternalId(String externalId) {
                        this.externalId = externalId;
                    }
                }

                public static class PriceDTO {

                    private BigDecimal amount;

                    private String currencyCode;

                    public PriceDTO(BigDecimal amount, String currencyCode) {
                        this.amount = amount;
                        this.currencyCode = currencyCode;
                    }

                    public PriceDTO() {
                    }

                    public BigDecimal getAmount() {
                        return amount;
                    }

                    public void setAmount(BigDecimal amount) {
                        this.amount = amount;
                    }

                    public String getCurrencyCode() {
                        return currencyCode;
                    }

                    public void setCurrencyCode(String currencyCode) {
                        this.currencyCode = currencyCode;
                    }
                }

                public static class PurchaseGroupMembershipDTO {

                    private MemberAmountDTO memberAmount;

                    private ProductGroupDTO product;

                    public PurchaseGroupMembershipDTO() {
                    }

                    public PurchaseGroupMembershipDTO(MemberAmountDTO memberAmount, ProductGroupDTO product) {
                        this.memberAmount = memberAmount;
                        this.product = product;
                    }

                    public PurchaseGroupMembershipDTO(String bundleQuantity, String bundleExternalId) {
                        this.memberAmount = new MemberAmountDTO(bundleQuantity, PrimeConstants.Commodity.DEFAULT_UNIT);
                        this.product = new ProductGroupDTO(bundleExternalId);
                    }

                    public MemberAmountDTO getMemberAmount() {
                        return memberAmount;
                    }

                    public void setMemberAmount(MemberAmountDTO memberAmount) {
                        this.memberAmount = memberAmount;
                    }

                    public ProductGroupDTO getProduct() {
                        return product;
                    }

                    public void setProduct(ProductGroupDTO product) {
                        this.product = product;
                    }

                    public static class MemberAmountDTO {

                        private String value;

                        private String unit;

                        public MemberAmountDTO() {
                        }

                        public MemberAmountDTO(String value, String unit) {
                            this.value = value;
                            this.unit = unit;
                        }

                        public String getValue() {
                            return value;
                        }

                        public void setValue(String value) {
                            this.value = value;
                        }

                        public String getUnit() {
                            return unit;
                        }

                        public void setUnit(String unit) {
                            this.unit = unit;
                        }
                    }

                    public static class ProductGroupDTO {

                        private IdentifierDTO identifier;

                        public ProductGroupDTO() {
                        }

                        public ProductGroupDTO(IdentifierDTO identifier) {
                            this.identifier = identifier;
                        }

                        public ProductGroupDTO(String bundleExternalId) {
                            this.identifier = new IdentifierDTO(bundleExternalId);
                        }

                        public IdentifierDTO getIdentifier() {
                            return identifier;
                        }

                        public void setIdentifier(IdentifierDTO identifier) {
                            this.identifier = identifier;
                        }
                    }
                }
            }
        }
    }
}
