package com.insta360.store.business.order.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.store.business.order.bo.OrderPromotionBO;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/11/20
 * @Description:
 */
public class OrderTradeDTO implements Serializable {

    /**
     * 付费物料
     */
    private JSONArray items;

    /**
     * 赠品子项列表
     */
    private JSONArray itemInteriors;

    /**
     * 美国消费税赠送care
     */
    private JSONArray otherItems;

    /**
     * 折扣券
     */
    @JSONField(name = "coupon_code")
    private String couponCode;

    @JSONField(name = "gift_card_code")
    private String giftCardCode;

    private String inscp;

    /**
     * 是否订阅
     */
    private Boolean subscribe;

    /**
     * 物流id
     */
    @JSONField(name = "delivery_id")
    private Integer deliveryId;

    @JSONField(name = "ocean_id")
    private Integer oceanId;

    private JSONObject delivery;

    private JSONObject billing;

    /**
     * 用户发票信息
     */
    private JSONObject invoice;

    private String remark;

    @JSONField(name = "contact_email")
    private String contactEmail;

    /**
     * 分组ID
     */
    private String offerId;

    /**
     *  亚马逊用户id
     */
    private String amazonUserId;

    /**
     * 是否选择amazon履约（prime订单）如选择amazon履约则为true
     */
    private Boolean prime ;

    /**
     * 订单来源终端
     */
    @JSONField(name = "order_end_point")
    private String orderEndPoint;

    @JSONField(name = "pro_sn")
    private String proSn;

    @JSONField(name = "commodity_ids")
    private List<Integer> commodityIds;

    private String code;

    /**
     * 无门槛包邮ABT参数
     */
    private String apt;

    @JSONField(name = "device_serial")
    private String deviceSerial;

    @JSONField(name = "discount_page")
    private String discountPage;

    @JSONField(name = "commodity_id")
    private Integer commodityId;

    /**
     * 定制图片信息集合
     */
    private List<CustomItemImageInfoDTO> customItemImageInfos;

    /**
     * 达人推广分销记录
     */
    private OrderPromotionBO orderPromotion;

    /**
     * 订单额外信息
     */
    private OrderExtraDTO orderExtra;


    /**
     * 是否赠送云服务
     */
    private Boolean cloud;

    /**
     * 云服务订阅类型
     */
    private Integer cloudSubscribeType;

    /**
     * 预计最早送达时间
     */
    private Long earliestEstimatedDelivery;

    /**
     * 预计最晚送达时间
     */
    private Long latestEstimatedDelivery;

    public OrderPromotionBO getOrderPromotion() {
        return orderPromotion;
    }

    public void setOrderPromotion(OrderPromotionBO orderPromotionDto) {
        this.orderPromotion = orderPromotionDto;
    }

    public JSONArray getItems() {
        return items;
    }

    public void setItems(JSONArray items) {
        this.items = items;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getGiftCardCode() {
        return giftCardCode;
    }

    public void setGiftCardCode(String giftCardCode) {
        this.giftCardCode = giftCardCode;
    }

    public String getInscp() {
        return inscp;
    }

    public void setInscp(String inscp) {
        this.inscp = inscp;
    }

    public Boolean getSubscribe() {
        return subscribe;
    }

    public void setSubscribe(Boolean subscribe) {
        this.subscribe = subscribe;
    }

    public Integer getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(Integer deliveryId) {
        this.deliveryId = deliveryId;
    }

    public Integer getOceanId() {
        return oceanId;
    }

    public void setOceanId(Integer oceanId) {
        this.oceanId = oceanId;
    }

    public JSONObject getDelivery() {
        return delivery;
    }

    public void setDelivery(JSONObject delivery) {
        this.delivery = delivery;
    }

    public JSONObject getBilling() {
        return billing;
    }

    public void setBilling(JSONObject billing) {
        this.billing = billing;
    }

    public JSONObject getInvoice() {
        return invoice;
    }

    public void setInvoice(JSONObject invoice) {
        this.invoice = invoice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getAmazonUserId() {
        return amazonUserId;
    }

    public void setAmazonUserId(String amazonUserId) {
        this.amazonUserId = amazonUserId;
    }

    public String getOfferId() {
        return offerId;
    }

    public void setOfferId(String offerId) {
        this.offerId = offerId;
    }

    public Boolean getPrime() {
        return prime;
    }

    public void setPrime(Boolean prime) {
        this.prime = prime;
    }

    public String getOrderEndPoint() {
        return orderEndPoint;
    }

    public void setOrderEndPoint(String orderEndPoint) {
        this.orderEndPoint = orderEndPoint;
    }

    public String getProSn() {
        return proSn;
    }

    public void setProSn(String proSn) {
        this.proSn = proSn;
    }

    public List<Integer> getCommodityIds() {
        return commodityIds;
    }

    public void setCommodityIds(List<Integer> commodityIds) {
        this.commodityIds = commodityIds;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getDiscountPage() {
        return discountPage;
    }

    public void setDiscountPage(String discountPage) {
        this.discountPage = discountPage;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public List<CustomItemImageInfoDTO> getCustomItemImageInfos() {
        return customItemImageInfos;
    }

    public void setCustomItemImageInfos(List<CustomItemImageInfoDTO> customItemImageInfos) {
        this.customItemImageInfos = customItemImageInfos;
    }

    public JSONArray getItemInteriors() {
        return itemInteriors;
    }

    public void setItemInteriors(JSONArray itemInteriors) {
        this.itemInteriors = itemInteriors;
    }

    public OrderExtraDTO getOrderExtra() {
        return orderExtra;
    }

    public void setOrderExtra(OrderExtraDTO orderExtra) {
        this.orderExtra = orderExtra;
    }

    public JSONArray getOtherItems() {
        return otherItems;
    }

    public void setOtherItems(JSONArray otherItems) {
        this.otherItems = otherItems;
    }

    public Boolean getCloud() {
        return cloud;
    }

    public void setCloud(Boolean cloud) {
        this.cloud = cloud;
    }

    public Integer getCloudSubscribeType() {
        return cloudSubscribeType;
    }

    public void setCloudSubscribeType(Integer cloudSubscribeType) {
        this.cloudSubscribeType = cloudSubscribeType;
    }

    public String getApt() {
        return apt;
    }

    public void setApt(String apt) {
        this.apt = apt;
    }

    public Long getEarliestEstimatedDelivery() {
        return earliestEstimatedDelivery;
    }

    public void setEarliestEstimatedDelivery(Long earliestEstimatedDelivery) {
        this.earliestEstimatedDelivery = earliestEstimatedDelivery;
    }

    public Long getLatestEstimatedDelivery() {
        return latestEstimatedDelivery;
    }

    public void setLatestEstimatedDelivery(Long latestEstimatedDelivery) {
        this.latestEstimatedDelivery = latestEstimatedDelivery;
    }

    @Override
    public String toString() {
        return "OrderTradeDTO{" +
                "items=" + items +
                ", itemInteriors=" + itemInteriors +
                ", otherItems=" + otherItems +
                ", couponCode='" + couponCode + '\'' +
                ", giftCardCode='" + giftCardCode + '\'' +
                ", inscp='" + inscp + '\'' +
                ", subscribe=" + subscribe +
                ", deliveryId=" + deliveryId +
                ", oceanId=" + oceanId +
                ", delivery=" + delivery +
                ", billing=" + billing +
                ", invoice=" + invoice +
                ", remark='" + remark + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", offerId='" + offerId + '\'' +
                ", amazonUserId='" + amazonUserId + '\'' +
                ", prime=" + prime +
                ", orderEndPoint='" + orderEndPoint + '\'' +
                ", proSn='" + proSn + '\'' +
                ", commodityIds=" + commodityIds +
                ", code='" + code + '\'' +
                ", apt='" + apt + '\'' +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", discountPage='" + discountPage + '\'' +
                ", commodityId=" + commodityId +
                ", customItemImageInfos=" + customItemImageInfos +
                ", orderPromotion=" + orderPromotion +
                ", orderExtra=" + orderExtra +
                ", cloud=" + cloud +
                ", cloudSubscribeType=" + cloudSubscribeType +
                ", earliestEstimatedDelivery=" + earliestEstimatedDelivery +
                ", latestEstimatedDelivery=" + latestEstimatedDelivery +
                '}';
    }
}
