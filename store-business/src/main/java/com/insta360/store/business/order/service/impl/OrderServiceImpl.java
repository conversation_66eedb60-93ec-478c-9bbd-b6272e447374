package com.insta360.store.business.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.store.business.admin.order.service.impl.creation.OrderCustomExportCreation;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.commodity.bo.CommodityStockNoticeBO;
import com.insta360.store.business.common.constants.CommonConstant;
import com.insta360.store.business.discount.enums.CouponUsageType;
import com.insta360.store.business.discount.service.CouponService;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.order.bo.RepeatOrderQueryBO;
import com.insta360.store.business.order.dao.OrderDao;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.*;
import com.insta360.store.business.order.service.impl.helper.OrderCreator;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import com.insta360.store.business.user.model.StoreAccount;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/24
 * @Description:
 */
@Service
public class OrderServiceImpl extends BaseServiceImpl<OrderDao, Order> implements OrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    OrderCreator orderCreator;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    CouponService couponService;

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    OrderCancelRecordService orderCancelRecordService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Override
    public Order getByBetweenTime(String email, LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("contact_email", email);
        qw.between("create_time", startTime, endTime);
        qw.last(CommonConstant.LAST_LIMIT_ONE);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<Order> getByContactEmail(String contactEmail) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("contact_email", contactEmail);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Order> listGuestOrderByContactEmail(String contactEmail) {
        if (StringUtils.isBlank(contactEmail)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("contact_email", contactEmail);
        qw.eq("user_id", StoreAccount.GUEST_USER_ID);
        return baseMapper.selectList(qw);
    }

    @Override
    public Order createOrder(OrderCreation orderCreation) {
        return orderCreator.create(orderCreation);
    }

    @Override
    public Order getByOrderNumber(String orderNumber) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("order_number", orderNumber);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<Order> listByOrderNumber(List<String> orderNumbers) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.in("order_number", orderNumbers);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Order> getByPromoCode(String promoCode) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("promo_code", promoCode);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Order> getByOrderState(List<OrderState> orderStates) {
        List<Integer> orderStateCodes = orderStates.stream().map(OrderState::getCode).collect(Collectors.toList());

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.in("state", orderStateCodes);
        // 排除工单系统订单
        qw.eq("is_repair", false);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Order> getByOrderState(String email, List<OrderState> orderStates) {
        List<Integer> orderStateCodes = orderStates.stream().map(OrderState::getCode).collect(Collectors.toList());

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.in("state", orderStateCodes);
        qw.eq("contact_email", email);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Order> listByEmail(String email, List<OrderState> orderStates, LocalDateTime now) {
        List<Integer> orderStateCodes = orderStates.stream().map(OrderState::getCode).collect(Collectors.toList());
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("contact_email", email);
        qw.between("create_time", now.minusDays(7), now);
        qw.in("state", orderStateCodes);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Order> getByOrderState(List<OrderState> orderStates, LocalDateTime now) {
        List<Integer> orderStateCodes = orderStates.stream().map(OrderState::getCode).collect(Collectors.toList());

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.between("create_time", now.minusDays(7), now);
        qw.in("state", orderStateCodes);
        // 排除工单系统订单
        qw.eq("is_repair", false);
        return baseMapper.selectList(qw);
    }

    @Override
    public PageResult<Order> getUserOrders(StoreAccount storeAccount, PageQuery pageQuery) {
        if (storeAccount == null) {
            return null;
        }

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("user_id", storeAccount.getInstaAccount());
        qw.eq("is_repair", false);
        qw.orderByDesc("create_time");

        // 查询该用户是否存在‘未支付’的云服务续费订单
        List<Order> renewalOrder = this.listCloudSubscribeOrder(storeAccount.getInstaAccount(), Lists.newArrayList(OrderState.init.getCode(), OrderState.payment_pending.getCode(), OrderState.canceled.getCode()), ServiceScenesType.RENEW);
        if (CollectionUtils.isNotEmpty(renewalOrder)) {
            List<Integer> orderIds = renewalOrder.stream().map(Order::getId).collect(Collectors.toList());
            qw.notIn("id", orderIds);
        }

        IPage<Order> iPage = baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw);
        return PageUtil.toPageResult(iPage);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void cancelOrder(Integer orderId, StoreAccount account, String reason) {
        Order order = checkPermission(orderId, account);

        // 常规订单只考虑‘未支付’状态，云服务订阅升级订单还需考虑’支付处理中‘状态
        List<OrderState> orderStates = Lists.newArrayList(OrderState.init);
        if (order.isSubscribeUpgradeOrder()) {
            orderStates.add(OrderState.payment_pending);
        }
        if (!orderStates.contains(order.orderState())) {
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        // 修改状态
        order.logAndSetState(OrderState.canceled.getCode());
        baseMapper.updateById(order);

        // 使用了代金券则去除代金券使用标记
        if (StringUtil.isNotBlank(order.getGiftCardCode())) {
            giftCardService.removeOrderUseMark(order.getGiftCardCode());
        }

        // 使用了优惠券则回退使用次数
        if (StringUtil.isNotBlank(order.getCouponCode())) {
            couponService.updateCouponUsageCount(order.getCouponCode(), CouponUsageType.add);
        }

        // 记录取消原因
        orderCancelRecordService.recordReason(order, reason);

        // 订单取消事件通知
        orderMessageSendHelper.sendOrderCancelMessage(order, reason);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void confirmDelivery(Integer orderId, StoreAccount account) {
        Order order = checkPermission(orderId, account);

        // 检查订单状态，只有"已发货"状态的订单可以确认收货
        if (!OrderState.on_delivery.equals(order.orderState())) {
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        // 修改状态
        order.logAndSetState(OrderState.success.getCode());
        baseMapper.updateById(order);

        // 记录收货时间
        orderDeliveryService.recordReceiveTime(order, LocalDateTime.now());

        // 订单已完成事件通知
        orderMessageSendHelper.sendOrderSuccessMessage(order);
    }

    @Override
    public boolean containProduct(Integer orderId, Integer productId) {
        List<OrderItem> productItems = orderItemService.getByProduct(orderId, productId);
        return productItems != null && !productItems.isEmpty();
    }

    @Override
    public boolean containsCommodity(Integer orderId, Integer commodityId) {
        List<OrderItem> commodityItems = orderItemService.getByCommodity(orderId, commodityId);
        return commodityItems != null && !commodityItems.isEmpty();
    }

    @Override
    public List<CommodityStockNoticeBO> listByCommodityIds(List<Integer> commodityIds, List<OrderState> orderStates, LocalDateTime now) {
        List<Integer> orderStateCodes = orderStates.stream().map(OrderState::getCode).collect(Collectors.toList());
        return baseMapper.listByCommodityIds(commodityIds, orderStateCodes, now.minusDays(7), now);
    }

    @Override
    public List<Order> listByPhoneOrAddress(RepeatOrderQueryBO repeatOrderQueryParams) {
        return baseMapper.listByPhoneOrAddress(repeatOrderQueryParams);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void shipOrder(Order order) {
        LOGGER.info("ship order begin info... " + order);

        // 修改发货时间
        OrderDelivery delivery = orderDeliveryService.getById(order.getId());
        if (delivery != null) {
            delivery.setExpressTime(LocalDateTime.now());
            orderDeliveryService.updateById(delivery);
        }

        // 修改状态
        order.logAndSetState(OrderState.on_delivery.getCode());
        boolean statusBoolean = this.updateById(order);
        LOGGER.info("ship order update status... " + statusBoolean);

        // 订单发货事件通知
        orderMessageSendHelper.sendOrderOnDeliveryMessage(order);

        LOGGER.info("ship order end info... " + order);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void partlyOrder(Order order) {
        // 修改发货时间
        OrderDelivery delivery = orderDeliveryService.getById(order.getId());
        if (delivery != null) {
            delivery.setExpressTime(LocalDateTime.now());
            orderDeliveryService.updateById(delivery);
        }

        // 修改状态
        order.logAndSetState(OrderState.part_delivery.getCode());
        baseMapper.updateById(order);
    }

    @Override
    public List<Order> listCustomOrders(OrderCustomExportCreation customExportCreation) {
        return baseMapper.listCustomOrders(customExportCreation.getOrderNumbers(),
                customExportCreation.getStates(),
                customExportCreation.getCustomType(),
                customExportCreation.getFromTime(),
                customExportCreation.getEndTime());
    }

    @Override
    public List<Order> listExportOrders(Long fromTimeLong, Long endTimeLong, List<Integer> orderStates, String tradeCodeType, List<String> tradeCodes, String orderNumber) {
        LocalDateTime fromTime = fromTimeLong != null ? TimeUtil.parseLocalDateTime(fromTimeLong) : null;
        LocalDateTime endTime = endTimeLong != null ? TimeUtil.parseLocalDateTime(endTimeLong) : null;
        List<String> orderNumbers = orderNumber != null ? Arrays.asList(orderNumber.split(",")) : null;

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.in(CollectionUtils.isNotEmpty(orderNumbers), "order_number", orderNumbers);
        qw.in(CollectionUtils.isNotEmpty(orderStates), "state", orderStates);
        qw.gt(fromTime != null, "create_time", fromTime);
        qw.lt(endTime != null, "create_time", endTime);
        qw.in(StringUtil.isNotBlank(tradeCodeType), tradeCodeType, tradeCodes);
        qw.eq("is_repair", false);
        qw.orderByDesc("id");
        return baseMapper.selectList(qw);
    }

    @Override
    public PageResult<Order> getOrderPage(Long fromTimeLong, Long endTimeLong, Integer id, List<Integer> orderStates, PageQuery pageQuery) {
        LocalDateTime fromTime = fromTimeLong != null ? TimeUtil.parseLocalDateTime(fromTimeLong) : null;
        LocalDateTime endTime = endTimeLong != null ? TimeUtil.parseLocalDateTime(endTimeLong) : null;

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.gt(fromTime != null, "create_time", fromTime);
        qw.lt(endTime != null, "create_time", endTime);
        qw.in("state", orderStates);
        qw.eq("is_repair", false);
        qw.lt(id != null && id > 0, "id", id);
        qw.orderByDesc("create_time");

        IPage<Order> iPage = baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw);
        return PageUtil.toPageResult(iPage);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateBatchOrders(List<Order> orders, String adminJobNumber) {
        orders.forEach(order -> {
            orderHelper.setState(order, adminJobNumber);
        });
        this.updateBatchById(orders);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateOrder(Order order, String adminJobNumber) {
        orderHelper.setState(order, adminJobNumber);
        this.updateById(order);
    }

    @Override
    public List<Order> listByTimeRangeAndArea(LocalDateTime from, LocalDateTime end, String country) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.gt("create_time", from);
        qw.lt("create_time", end);
        qw.eq(StringUtil.isNotBlank(country), "area", country);
        return baseMapper.selectList(qw);
    }

    @Override
    public Integer countExportOrders(QueryWrapper<Order> orderQueryWrapper) {
        return baseMapper.selectCount(orderQueryWrapper);
    }

    @Override
    public IPage<Order> pageExportOrders(Page<Order> page, QueryWrapper<Order> orderQueryWrapper) {
        return baseMapper.selectPage(page, orderQueryWrapper);
    }

    @Override
    public List<Order> pageCustomOrders(Page<Order> page, OrderCustomExportCreation customExportCreation) {
        return baseMapper.pageCustomOrders(page, customExportCreation.getOrderNumbers(),
                customExportCreation.getStates(),
                customExportCreation.getCustomType(),
                customExportCreation.getFromTime(),
                customExportCreation.getEndTime());
    }

    @Override
    public Integer countCustomExportOrders(OrderCustomExportCreation customExportCreation) {
        return baseMapper.countCustomExportOrders(customExportCreation.getOrderNumbers(),
                customExportCreation.getStates(),
                customExportCreation.getCustomType(),
                customExportCreation.getFromTime(),
                customExportCreation.getEndTime());
    }

    @Override
    public List<Order> listCloudSubscribeOrderByEmail(String email, Boolean isFixedId, Boolean isContainProcessing) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        QueryWrapper<Order> qw = new QueryWrapper<>();
        if (Objects.nonNull(isFixedId) && isFixedId) {
            // todo 待确认
            qw.eq("user_id", StoreAccount.GUEST_USER_ID);
        }
        // 订单状态列表
        ArrayList<Integer> orderStates = Lists.newArrayList(OrderState.payed.getCode(), OrderState.prepared.getCode(), OrderState.on_delivery.getCode(), OrderState.part_delivery.getCode(), OrderState.success.getCode());
        if (Objects.nonNull(isContainProcessing) && isContainProcessing) {
            orderStates.add(OrderState.payment_pending.getCode());
        }
        qw.eq("contact_email", email);
        qw.in("state", orderStates);
        qw.eq("cloud_subscribe_mark", Boolean.TRUE);
        qw.orderByDesc("create_time");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<Order> listUnpaidCloudSubscribeOrder(String email) {
        if (StringUtils.isBlank(email)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("contact_email", email);
        qw.eq("cloud_subscribe_mark", Boolean.TRUE);
        qw.eq("state", OrderState.init.getCode());

        return baseMapper.selectList(qw);
    }

    @Override
    public Integer batchUpdateOrderState(List<Integer> orderIdList, Integer orderState) {
        if (CollectionUtils.isEmpty(orderIdList) || Objects.isNull(orderState)) {
            return 0;
        }
        return baseMapper.batchUpdateOrderState(orderIdList, orderState);
    }

    @Override
    public Order checkPermission(Integer orderId, StoreAccount account) {
        Order order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 兼容forter风控可以不用登录
        if (account != null && !order.isBelongTo(account)) {
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        return order;
    }

    @Override
    public List<Order> listCloudSubscribeOrderByState(Integer userId, List<Integer> orderStateList) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(orderStateList)) {
            return null;
        }
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("user_id", userId);
        qw.eq("cloud_subscribe_mark", Boolean.TRUE);
        qw.in("state", orderStateList);
        return baseMapper.selectList(qw);
    }

    @Override
    public Order getCloudSubscribeRenewOrder(Integer userId, Integer orderState) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("user_id", userId);
        qw.eq("cloud_subscribe_mark", Boolean.TRUE);
        qw.eq("subscribe_scenes_type", ServiceScenesType.RENEW.name());
        qw.eq("state", orderState);
        qw.orderByDesc("id");
        qw.last(CommonConstant.LAST_LIMIT_ONE);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<Order> listCloudSubscribeOrder(Integer userId, List<Integer> orderStateList, ServiceScenesType serviceScenesType) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(orderStateList) || Objects.isNull(serviceScenesType)) {
            return null;
        }
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("user_id", userId);
        qw.eq("cloud_subscribe_mark", Boolean.TRUE);
        qw.eq("subscribe_scenes_type", serviceScenesType.name());
        qw.in("state", orderStateList);
        return baseMapper.selectList(qw);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = RuntimeException.class)
    @Override
    public int batchSyncUserId(Integer limit) {
        if (Objects.isNull(limit)) {
            return 0;
        }

        return baseMapper.batchSyncUserId(limit);
    }

    @Override
    public List<Order> listByPspOrderNumber(List<String> orderNumbers) {
        if (CollectionUtils.isEmpty(orderNumbers)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<Order> qw = new QueryWrapper<>();

        // 订单状态列表
        ArrayList<Integer> orderStates = Lists.newArrayList(OrderState.payed.getCode(), OrderState.prepared.getCode(), OrderState.on_delivery.getCode(), OrderState.part_delivery.getCode(), OrderState.success.getCode());
        qw.in("order_number", orderNumbers);
        qw.in("state", orderStates);
        qw.orderByDesc("create_time");

        return baseMapper.selectList(qw);
    }

    /**
     * 获取小程序订单创建时间
     *
     * @return
     */
    private LocalDateTime getCreateTime(Integer days) {
        // 获取昨天的时间
        LocalDate localDate = LocalDate.now().minusDays(days);

        return LocalDateTime.of(localDate.getYear(), localDate.getMonth(), localDate.getDayOfMonth(), 16, 0, 0);
    }
}
