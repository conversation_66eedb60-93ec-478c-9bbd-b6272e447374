package com.insta360.store.business.cloud.utils;

import org.apache.commons.lang.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 时间转换工具包
 * @Date 2024/5/16
 */
public class TimeConvertUtil {

    private static final SimpleDateFormat INT_DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat STR_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat BASE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    public static long daysBetween(long startTime, long endTime) {
        Instant start = Instant.ofEpochMilli(startTime);
        Instant end = Instant.ofEpochMilli(endTime);
        return ChronoUnit.DAYS.between(start, end);
    }



    public static long utcTime2Long(String utcTime) {
        // 解析字符串到 Instant 对象
        Instant instant = Instant.parse(utcTime);
        // 获取时间戳
        return instant.toEpochMilli();
    }


    public static String longTime2Str(Long timestamp) {

        // 创建一个新的Date对象
        Date date = new Date(timestamp);

        // 创建一个新的SimpleDateFormat对象
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 使用SimpleDateFormat对象将Date转换为期望的字符串格式
        return STR_DATE_FORMAT.format(date);
    }

    public static Integer calculateDays(long lastTime) {
        // 将给定的时间戳转换为Instant对象
        Instant givenInstant = Instant.ofEpochMilli(lastTime);

        // 获取当前时间的Instant对象
        Instant currentInstant = Instant.now();

        // 计算两个instant对象之间的duration
        Duration duration = Duration.between(givenInstant, currentInstant);

        // 将duration转换为天数
        long days = duration.toDays();
        return Integer.parseInt(String.valueOf(days));
    }

    public static Integer calculateDays(long lastTime,long recentlyTime) {
        // 将给定的时间戳转换为Instant对象
        Instant givenInstant = Instant.ofEpochMilli(lastTime);

        // 获取当前时间的Instant对象
        Instant currentInstant = Instant.ofEpochMilli(recentlyTime);

        // 计算两个instant对象之间的duration
        Duration duration = Duration.between(givenInstant, currentInstant);

        // 将duration转换为天数
        long days = duration.toDays();
        return Integer.parseInt(String.valueOf(days));
    }

    /**
     * 计算两个时间戳之间的天数差
     *
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @return 两个时间戳之间的天数差
     */
    public static int calculateDaysBetween(long startTime, long endTime) {
        // 将时间戳转换为 LocalDate，忽略时分秒
        LocalDate startDate = Instant.ofEpochMilli(startTime)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        LocalDate endDate = Instant.ofEpochMilli(endTime)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 计算两个日期之间的天数差
        long days = ChronoUnit.DAYS.between(startDate, endDate);

        return Integer.parseInt(String.valueOf(days));
    }

    public static Long getCurrentDayStartTime() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        ZonedDateTime zdt = startOfDay.atZone(ZoneId.systemDefault());
        return zdt.toInstant().toEpochMilli();
    }

    /**
     * 返回日期的int数字
     *
     * @param date 日期
     * @return eg: 20230815
     */
    public static Integer getDateIntValue(Date date) {
        String format = INT_DATE_FORMAT.format(date);
        return Integer.parseInt(format);
    }


    /**
     * 获取未来日期的int数字
     *
     * @param days 未来日期比现在晚的天数
     * @return int
     */
    public static Integer getFutureDateIntValueByBaseTime(long baseTime, int days) {
        Date date = new Date(getFutureTime(baseTime, days));
        return getDateIntValue(date);
    }

    public static Integer getFutureDateIntValueByBaseDate(Integer baseDate, int days) {
        try {
            Date date = INT_DATE_FORMAT.parse(baseDate.toString());
            Date newDate = DateUtils.addDays(date, days);
            return getDateIntValue(newDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Integer getFutureDateIntValue(long time) {
        Date date = new Date(time);
        return getDateIntValue(date);
    }

    public static Long getFutureTime(long baseTime, Integer days) {
        return baseTime + days * 24 * 3600 * 1000L;
    }

    public static Date getFutureDate(Integer days) {
        return new Date(getFutureTime(getFirstMillCurrentDay(), days));
    }

    public static synchronized String dateInt2Str(Integer dateIntValue) {
        try {
            Date date = INT_DATE_FORMAT.parse(String.valueOf(dateIntValue));
            return STR_DATE_FORMAT.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获取当前天的最后一个毫秒值
     *
     * @return
     */
    public static long getLastMillCurrentDay() {
        Calendar calendar = Calendar.getInstance();
        //将小时至0
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至0
        calendar.set(Calendar.MINUTE, 59);
        //将秒至0
        calendar.set(Calendar.SECOND, 59);
        //将毫秒至0
        calendar.set(Calendar.MILLISECOND, 999);
        // 获取当前这一天的最后一个毫秒值
        return calendar.getTimeInMillis();
    }

    public static long getFirstMillCurrentDay() {
        Calendar calendar = Calendar.getInstance();
        //将小时至0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        calendar.set(Calendar.MINUTE, 0);
        //将秒至0
        calendar.set(Calendar.SECOND, 0);
        //将毫秒至0
        calendar.set(Calendar.MILLISECOND, 0);
        // 获取当前这一天的最后一个毫秒值
        return calendar.getTimeInMillis();
    }

    public static String date2Str(Date date) {
        return BASE_DATE_FORMAT.format(date);
    }
}
