package com.insta360.store.service.controller.product.format;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.enums.CommodityTagType;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.model.AdapterTypeInfo;
import com.insta360.store.business.meta.service.AdapterTypeInfoService;
import com.insta360.store.business.product.model.*;
import com.insta360.store.business.product.service.*;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.product.vo.CommodityTagInfoVO;
import com.insta360.store.service.controller.product.vo.CommodityVO;
import com.insta360.store.service.controller.product.vo.ProductMainVideoVO;
import com.insta360.store.service.controller.product.vo.ProductVO;
import com.insta360.store.service.controller.reseller.vo.ProductCategoryMainVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/1/25
 * @Description:
 */
@Component
public class ProductPack {

    @Autowired
    CommodityPack commodityPack;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductFaqService productFaqService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    ProductVideoService productVideoService;

    @Autowired
    ProductFeatureService productFeatureService;

    @Autowired
    ProductFaqMoreService productFaqMoreService;

    @Autowired
    AdapterTypeInfoService adapterTypeInfoService;

    @Autowired
    ProductAdapterTypeService productAdapterTypeService;

    @Autowired
    ProductDeliveryTimeTextService productDeliveryTimeTextService;

    @Autowired
    ProductService productService;

    @Autowired
    ProductCategorySubsetService productCategorySubsetService;

    @Autowired
    FaqQuestionService faqQuestionService;

    @Autowired
    ProductMainVideoPack productMainVideoPack;

    /**
     * 产品套餐信息封装
     *
     * @param productId
     * @param country
     * @param language
     * @return
     */
    public ProductVO doPackProductInfo(Integer productId, InstaCountry country, InstaLanguage language) {
        return this.doPack(productService.getById(productId), getProductSetting(country, language, null));
    }

    /**
     * 产品各项数据封装
     *
     * @param product
     * @param setting
     * @return
     */
    public ProductVO doPack(Product product, PackSetting setting) {
        // 产品禁用提前返回
        if (!product.getEnabled()) {
            return new ProductVO();
        }

        ProductVO productVO = new ProductVO(product);

        // 产品套餐信息
        packCommodities(productVO, setting);

        // 产品发货信息文案（发货文案要在套餐之后pack）
        packDeliveryTimeText(productVO, setting);

        // 产品faq
        packFaq(productVO, setting);

        // 产品注意事项
        packFaqMore(productVO, setting);

        // 产品特性
        packFeature(productVO, setting);

        // 产品详细信息
        packInfo(productVO, setting);

        // 产品视频
        packVideos(productVO, setting);

        // 产品适配机型
        packProductAdapterType(productVO, setting);

        // 产品类目
        packProductCategory(productVO);

        // 产品主视频
        packProductMainVideo(productVO, setting);

        return productVO;
    }

    /**
     * 产品主视频封装
     *
     * @param productVO
     * @param setting
     */
    private void packProductMainVideo(ProductVO productVO, PackSetting setting) {
        if (setting.withMainVideo) {
            List<ProductMainVideoVO> mainVideos = productMainVideoPack.listMainVideos(productVO.getId(), setting.country, setting.language);
            productVO.setProductMainVideoVoList(mainVideos);
        }
    }

    /**
     * 单产品类目封装
     *
     * @param productVo
     * @return
     */
    private void packProductCategory(ProductVO productVo) {
        ProductCategoryMainVO categoryMainVo = new ProductCategoryMainVO();
        String categoryKey = productVo.getCategoryKey();
        ProductCategorySubset categorySubset = productCategorySubsetService.getByCategorySubsetKey(categoryKey);
        if (Objects.nonNull(categorySubset)) {
            categoryMainVo.setCategoryKey(categorySubset.getCategoryMainKey());
            categoryMainVo.setCategorySubsetKey(categorySubset.getCategorySubsetKey());
            productVo.setProductCategory(categoryMainVo);
            return;
        }
        categoryMainVo.setCategoryKey(categoryKey);
        productVo.setProductCategory(categoryMainVo);
    }

    /**
     * 产品发货信息文案
     *
     * @param productVO
     * @param setting
     */
    protected void packDeliveryTimeText(ProductVO productVO, PackSetting setting) {
        if (setting.withDeliveryTimeText) {
            ProductDeliveryTimeText deliveryTimeText = productDeliveryTimeTextService.getDeliveryTimeText(productVO.getId(), setting.language);
            if (deliveryTimeText == null) {
                return;
            }

            productVO.setEstimateDeliveryTime(deliveryTimeText.getContent());
            if (setting.withCommodities) {
                String commoditySpecificContent = deliveryTimeText.getCommoditySpecificContent();
                if (StringUtil.isNotBlank(commoditySpecificContent)) {
                    JSONArray array = JSONArray.parseArray(commoditySpecificContent);

                    Map<String, String> map = new HashMap<>();
                    for (Object o : array) {
                        JSONObject json = (JSONObject) o;
                        String id = json.getString("id");
                        String content = json.getString("content");
                        map.put(id, content);
                    }

                    if (!map.isEmpty()) {
                        List<CommodityVO> commodities = productVO.getCommodities();
                        commodities.forEach(c -> c.setDeliveryTimeText(map.get(c.getId().toString())));
                    }
                }
            }
        }
    }

    /**
     * 产品faq
     *
     * @param productVO
     * @param setting
     */
    protected void packFaq(ProductVO productVO, PackSetting setting) {
        if (setting.withFaq) {
            List<ProductFaq> faqs;
            // todo:上线后再使用
            List<FaqQuestion> faqQuestions = faqQuestionService.listByProductId(productVO.getId());
            if (CollectionUtils.isNotEmpty(faqQuestions)) {
                List<Integer> questionIds = faqQuestions.stream().map(FaqQuestion::getId).collect(Collectors.toList());
                List<ProductFaq> faqInfos = productFaqService.listByQuestionIdAndLanguage(questionIds, setting.language);
                Map<Integer, ProductFaq> faqMap = faqInfos.stream().collect(Collectors.toMap(ProductFaq::getQuestionId, o -> o));
                faqs = questionIds.stream().map(faqMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            } else {
                faqs = productFaqService.getFaqs(productVO.getId(), setting.language);
            }
            productVO.setFaqs(faqs);
        }
    }

    /**
     * 产品注意事项
     *
     * @param productVO
     * @param setting
     */
    protected void packFaqMore(ProductVO productVO, PackSetting setting) {
        if (setting.withFaqMore) {
            List<ProductFaqMore> faqMores = productFaqMoreService.listProductFaqMore(productVO.getId(), setting.language);
            productVO.setFaqMores(faqMores);
        }
    }

    /**
     * 产品特性
     *
     * @param productVO
     * @param setting
     */
    protected void packFeature(ProductVO productVO, PackSetting setting) {
        if (setting.withFeature) {
            List<ProductFeature> features = productFeatureService.getFeatures(productVO.getId(), setting.language);
            productVO.setFeatures(features);
        }
    }

    /**
     * 产品详细信息
     *
     * @param productVO
     * @param setting
     */
    protected void packInfo(ProductVO productVO, PackSetting setting) {
        if (setting.withInfo) {
            ProductInfo info = productInfoService.getInfoDefaultEnglish(productVO.getId(), setting.language);
            productVO.setInfo(info);
        }
    }

    /**
     * 产品视频
     *
     * @param productVO
     * @param setting
     */
    protected void packVideos(ProductVO productVO, PackSetting setting) {
        if (setting.withVideos) {
            List<ProductVideo> videos = productVideoService.getVideos(productVO.getId(), setting.language);
            productVO.setVideos(videos);
        }
    }

    /**
     * 产品套餐信息
     *
     * @param productVO
     * @param setting
     */
    protected void packCommodities(ProductVO productVO, PackSetting setting) {
        if (setting.withCommodities) {
            List<Commodity> commodities = commodityService.getCommodities(productVO.getId(), true);

            /**
             * 产品第一个套餐信息
             * @param productVO
             * @param setting
             */
            if (setting.withFirstCommodity) {
                commodities = commodities.subList(0, 1);
            }

            List<CommodityVO> commodityVOList = commodityPack.batchPack(commodities, setting.commodityPackSetting);
            // 产品页只封装最新套餐推荐tag
            commodityVOList.forEach(commodity -> {
                List<CommodityTagInfoVO> commodityTagInfoList = commodity.getCommodityTagInfos().stream()
                        .filter(commodityTagInfo -> CommodityTagType.RECOMMEND_COMMODITY.getType().equals(commodityTagInfo.getTagGroupType()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isEmpty(commodityTagInfoList)) {
                    commodity.setCommodityTagInfos(commodityTagInfoList);
                }

                if (CollectionUtils.isNotEmpty(commodityTagInfoList)) {
                    commodity.setCommodityTagInfos(Lists.newArrayList(commodityTagInfoList.get(commodityTagInfoList.size() - 1)));
                }
            });

            productVO.setCommodities(commodityVOList);
        }
    }

    /**
     * 产品适配机型
     *
     * @param productVO
     * @param setting
     */
    private void packProductAdapterType(ProductVO productVO, PackSetting setting) {
        if (setting.withAdapterType) {
            // 产品支持的适配类型
            List<ProductAdapterType> productAdapterTypes = productAdapterTypeService.listEnabledAdapterType(productVO.getId());
            if (CollectionUtils.isEmpty(productAdapterTypes)) {
                return;
            }

            // 适配类型列表名称
            List<String> adapterTypeNames = productAdapterTypes.stream().map(productAdapterType -> {
                // 适配类型文案集合
                AdapterTypeInfo adapterTypeInfo = adapterTypeInfoService.getInfos(productAdapterType.getAdapterTypeId(), setting.language);
                if (adapterTypeInfo == null) {
                    return null;
                }
                return adapterTypeInfo.getInfoName();
            }).filter(Objects::nonNull).collect(Collectors.toList());

            productVO.setAdapterTypeNames(adapterTypeNames);
        }
    }

    /**
     * 产品套餐配置
     *
     * @return
     */
    private PackSetting getProductSetting(InstaCountry country, InstaLanguage language, String resellerCode) {
        PackSetting setting = new PackSetting(country, language);
        setting.setWithInfo(true);
        setting.setWithFeature(true);
        setting.setWithFaq(true);
        setting.setWithDeliveryTimeText(true);
        setting.setWithVideos(true);
        setting.setWithMainVideo(true);
        setting.setWithCommodities(true);
        setting.setWithFaqMore(true);
        setting.setWithAdapterType(true);

        CommodityPack.PackSetting commodityPackSetting = new CommodityPack.PackSetting(country, language, resellerCode);
        commodityPackSetting.setWithCommodityInfo(true);
        commodityPackSetting.setWithPrice(true);
        commodityPackSetting.setWithDisplay(true);
        commodityPackSetting.setWithTradeRule(true);
        commodityPackSetting.setWithSaleState(true);
        commodityPackSetting.setWithStock(true);
        commodityPackSetting.setWithClimbService(true);
        commodityPackSetting.setWithServiceType(true);
        commodityPackSetting.setWithDeliveryTimeConfig(true);
        commodityPackSetting.setWithCommodityTag(true);
        commodityPackSetting.setWithSavePrice(true);

        setting.setCommodityPackSetting(commodityPackSetting);

        return setting;
    }

    public static class PackSetting {

        BaseApi api;

        InstaLanguage language;
        InstaCountry country;

        // 对应所有产品的数据表
        boolean withCategory;
        boolean withCommodities;
        boolean withDeliveryTimeConfig;
        boolean withDeliveryTimeText;
        boolean withFaq;
        boolean withFeature;
        boolean withInfo;
        boolean withMeta;
        boolean withVideos;
        boolean withFaqMore;
        boolean withAdapterType;
        boolean withMainVideo;

        // 额外信息
        protected boolean withFirstCommodity;
        boolean withCommodityQuantity;

        // PC or MOB
        boolean isMobile;

        CommodityPack.PackSetting commodityPackSetting;

        public PackSetting(BaseApi api) {
            this.api = api;
            this.language = api.getApiLanguage();
            this.country = api.getApiCountry();
        }

        public PackSetting() {

        }

        public PackSetting(InstaCountry country, InstaLanguage language) {
            this.country = country;
            this.language = language;
        }

        public void setWithDeliveryTimeText(boolean withDeliveryTimeText) {
            this.withDeliveryTimeText = withDeliveryTimeText;
        }

        public void setWithCategory(boolean withCategory) {
            this.withCategory = withCategory;
        }

        public void setWithInfo(boolean withInfo) {
            this.withInfo = withInfo;
        }

        public void setWithDeliveryTimeConfig(boolean withDeliveryTimeConfig) {
            this.withDeliveryTimeConfig = withDeliveryTimeConfig;
        }

        public void setWithVideos(boolean withVideos) {
            this.withVideos = withVideos;
        }

        public void setWithFirstCommodity(boolean withFirstCommodity) {
            this.withFirstCommodity = withFirstCommodity;
        }

        public void setWithFaq(boolean withFaq) {
            this.withFaq = withFaq;
        }

        public void setWithFaqMore(boolean withFaqMore) {
            this.withFaqMore = withFaqMore;
        }

        public void setWithMeta(boolean withMeta) {
            this.withMeta = withMeta;
        }

        public void setWithCommodities(boolean withCommodities) {
            this.withCommodities = withCommodities;
        }

        public void setWithCommodityQuantity(boolean withCommodityQuantity) {
            this.withCommodityQuantity = withCommodityQuantity;
        }

        public void setWithFeature(boolean withFeature) {
            this.withFeature = withFeature;
        }

        public void setCommodityPackSetting(CommodityPack.PackSetting commodityPackSetting) {
            this.commodityPackSetting = commodityPackSetting;
        }

        public void setWithMainVideo(boolean withMainVideo) {
            this.withMainVideo = withMainVideo;
        }

        public boolean isWithCategory() {
            return withCategory;
        }

        public boolean isWithCommodities() {
            return withCommodities;
        }

        public boolean isWithDeliveryTimeConfig() {
            return withDeliveryTimeConfig;
        }

        public boolean isWithDeliveryTimeText() {
            return withDeliveryTimeText;
        }

        public boolean isWithFaq() {
            return withFaq;
        }

        public boolean isWithFaqMore() {
            return withFaqMore;
        }

        public boolean isWithFeature() {
            return withFeature;
        }

        public boolean isWithInfo() {
            return withInfo;
        }

        public boolean isWithMeta() {
            return withMeta;
        }

        public boolean isWithVideos() {
            return withVideos;
        }

        public boolean isWithFirstCommodity() {
            return withFirstCommodity;
        }

        public boolean isWithCommodityQuantity() {
            return withCommodityQuantity;
        }

        public boolean isWithAdapterType() {
            return withAdapterType;
        }

        public boolean isWithMainVideo() {
            return withMainVideo;
        }

        public void setWithAdapterType(boolean withAdapterType) {
            this.withAdapterType = withAdapterType;
        }

        public CommodityPack.PackSetting getCommodityPackSetting() {
            return commodityPackSetting;
        }

        public boolean isMobile() {
            return isMobile;
        }

        public void setMobile(boolean mobile) {
            isMobile = mobile;
        }

        @Override
        public String toString() {
            return "PackSetting{" +
                    "api=" + api +
                    ", language=" + language +
                    ", country=" + country +
                    ", withCategory=" + withCategory +
                    ", withCommodities=" + withCommodities +
                    ", withDeliveryTimeConfig=" + withDeliveryTimeConfig +
                    ", withDeliveryTimeText=" + withDeliveryTimeText +
                    ", withFaq=" + withFaq +
                    ", withFeature=" + withFeature +
                    ", withInfo=" + withInfo +
                    ", withMeta=" + withMeta +
                    ", withVideos=" + withVideos +
                    ", withFaqMore=" + withFaqMore +
                    ", withAdapterType=" + withAdapterType +
                    ", withMainVideo=" + withMainVideo +
                    ", withFirstCommodity=" + withFirstCommodity +
                    ", withCommodityQuantity=" + withCommodityQuantity +
                    ", isMobile=" + isMobile +
                    ", commodityPackSetting=" + commodityPackSetting +
                    '}';
        }
    }
}
