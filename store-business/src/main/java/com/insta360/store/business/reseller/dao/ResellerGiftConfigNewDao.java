package com.insta360.store.business.reseller.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.reseller.model.ResellerGiftConfigNew;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-25
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface ResellerGiftConfigNewDao extends BaseDao<ResellerGiftConfigNew> {

    /**
     * 特殊分销商赠品批量保存
     *
     * @param resellerGiftConfigList
     * @return
     */
    int batchSaveSpecial(List<ResellerGiftConfigNew> resellerGiftConfigList);

    /**
     * 默认分销商赠品批量保存
     * @param resellerGiftConfigList
     * @return
     */
    int batchSaveDefault(List<ResellerGiftConfigNew> resellerGiftConfigList);
}
