package com.insta360.store.service.controller.trade.controller;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.security.annotation.Authorization;
import com.insta360.store.business.admin.order.enums.OrderSceneType;
import com.insta360.store.business.admin.order.service.impl.handler.LogisticsRestrictionCheckHandler;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.configuration.prerelease.annotation.ProductDataPreRelease;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.order.dto.OrderTradeDTO;
import com.insta360.store.business.trade.model.UserCart;
import com.insta360.store.business.trade.service.UserCartService;
import com.insta360.store.business.trade.service.impl.helper.cart.UserCartHelper;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.product.format.CommodityPack;
import com.insta360.store.service.controller.product.format.GiftPack;
import com.insta360.store.service.controller.product.vo.CommodityVO;
import com.insta360.store.service.controller.product.vo.GiftVO;
import com.insta360.store.service.controller.trade.cache.CartCachePack;
import com.insta360.store.service.controller.trade.filter.CartDataFilter;
import com.insta360.store.service.controller.trade.format.CartPack;
import com.insta360.store.service.controller.trade.format.OrderSheetParaHelper;
import com.insta360.store.service.controller.trade.vo.UserCartResponseVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/11
 * @Description:
 */
@RestController
public class CartApi extends BaseApi {

    @Autowired
    GiftPack giftPack;

    @Autowired
    CartPack cartPack;

    @Autowired
    CartCachePack cartCachePack;

    @Autowired
    UserCartHelper userCartHelper;

    @Autowired
    CartDataFilter cartDataFilter;

    @Autowired
    UserCartService userCartService;

    @Autowired
    OrderSheetParaHelper orderSheetParaHelper;

    @Autowired
    LogisticsRestrictionCheckHandler logisticsRestrictionCheckHandler;

    /**
     * 获取用户的购物车内容
     *
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization
    @GetMapping("/store/trade/cart/getUserCartDetail")
    public Response<? extends Map> getUserCartDetail() {
        StoreAccount storeAccount = getAccessUser();
        UserCart userCart = userCartService.getByUserId(storeAccount.getInstaAccount());

        if (userCart == null) {
            return Response.ok();
        }

        // 解析用户购物车数据
        List<OrderSheet.SheetItem> cartItems = userCartService.parseCartData(userCart.getItems());
        if (cartItems.isEmpty()) {
            return Response.ok();
        }

        // 购物车数据校验过滤
        return Response.ok("cartItems", cartPack.cartDataFilter(cartItems, getApiCountry()));
    }

    /**
     * 获取购物车详细信息
     *
     * @param orderTradeParam
     * @return
     */
//    @AvoidRepeatableCommit(timeOut = 200)
    @PostMapping(path = "/store/trade/cart/loadCartDetail", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<UserCartResponseVO> loadCartDetail(@RequestBody OrderTradeDTO orderTradeParam) {
        // 购物车数据校验过滤
        OrderSheet orderSheet = orderSheetParaHelper.getBasicOrderSheet(this, orderTradeParam);
        List<OrderSheet.SheetItem> cartItems = cartPack.cartDataFilter(orderSheet.getSheetItems(), getApiCountry());

        // 用于计算套餐分销折扣
        List<Commodity> commodities = new ArrayList<>();

        // 套餐源数据
        CommodityPack.PackSetting setting = super.getCommoditySetting();
        setting.setWithCommodityAttribute(true);
        List<CommodityVO> commodityVos = cartPack.doPackCommodityInfo(cartItems, setting, commodities);

        // 赠品信息
        List<GiftVO> gifts = giftPack.listGiftItemPack(orderSheet, setting);

        // 获取赠品及选购商品的报关信息
        List<Integer> giftsCommodityIds = gifts.stream().map(GiftVO::getCommodity).map(Commodity::getId).collect(Collectors.toList());
        List<Integer> commodityIds = commodityVos.stream().map(Commodity::getId).collect(Collectors.toList());
        commodityIds.addAll(giftsCommodityIds);

        // 对物流限制进行校验
        OrderSceneType orderSceneType = logisticsRestrictionCheckHandler.check(commodityIds, getApiCountry());

        // 分销减价
        Map<String, Price> discounts = userCartHelper.resellerDiscount(commodities, getResellerCode(), getApiCountry());

        // 返回值
        UserCartResponseVO userCartResponse = new UserCartResponseVO();
        userCartResponse.setCommodityInfos(commodityVos);
        userCartResponse.setGifts(gifts);
        userCartResponse.setDiscounts(discounts);
        userCartResponse.setOrderSceneType(orderSceneType.getCode());
        return Response.ok(userCartResponse);
    }

    /**
     * 更新购物车信息
     *
     * @param orderTradeParam
     * @return
     */
//    @AvoidRepeatableCommit(timeOut = 200)
    @Authorization
    @PostMapping(path = "/store/trade/cart/updateCart", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateCart(@RequestBody OrderTradeDTO orderTradeParam) {
        // 删除全部数据
        if (CollectionUtils.isEmpty(orderTradeParam.getItems())) {
            userCartService.update(getAccessUser(), null);
            return Response.ok();
        }

        OrderSheet orderSheet = orderSheetParaHelper.getBasicOrderSheet(this, orderTradeParam);
        userCartService.update(getAccessUser(), orderSheet.getSheetItems());
        return Response.ok();
    }

    /**
     * 获取加购商品的套餐推荐配置
     *
     * @param orderTradeParam
     */
    @ProductDataPreRelease
    @PostMapping(path = "/store/trade/cart/getCartCommodityRecommendationInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> getCartCommodityRecommendationInfo(@RequestBody OrderTradeDTO orderTradeParam) {
        List<Integer> commodityIds = orderTradeParam.getCommodityIds();
        if (CollectionUtils.isEmpty(commodityIds)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 大于50则处理50，防止页面抛异常
        if (commodityIds.size() > 50) {
            commodityIds = commodityIds.stream().limit(50).collect(Collectors.toList());
        }

        List<CommodityVO> commodityVos = cartCachePack.doPackCartCommodityRecommendation(commodityIds, getApiCountry(), getApiLanguage());
        return Response.ok("recommend_accessories_info", getProductNewDataDisplay() ? commodityVos : cartDataFilter.getCartCommodityRecommendationInfoDataFilter(commodityVos));
    }
}