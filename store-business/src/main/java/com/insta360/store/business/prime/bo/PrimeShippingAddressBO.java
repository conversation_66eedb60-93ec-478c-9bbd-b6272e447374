package com.insta360.store.business.prime.bo;

import com.insta360.store.business.configuration.utils.ProfileUtil;
import com.insta360.store.business.order.model.OrderDelivery;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/22
 */
public class PrimeShippingAddressBO {

    private String name;

    private String streetAddress;

    private String extendedAddress;

    private String locality;

    private String region;

    private String countryCode;

    private String postalCode;

    public PrimeShippingAddressBO() {
    }

    // todo prime 确认订单地址
    public PrimeShippingAddressBO(OrderDelivery orderDelivery) {
        this.name = ProfileUtil.getFullName(orderDelivery.getFirstName(), orderDelivery.getLastName());
        this.streetAddress = orderDelivery.getAddress();
        this.extendedAddress = orderDelivery.getSubAddress();
        this.locality = orderDelivery.getCity();
        this.region = orderDelivery.getProvinceCode();
        this.countryCode = orderDelivery.getCountryCode();
        this.postalCode = orderDelivery.getZipCode();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStreetAddress() {
        return streetAddress;
    }

    public void setStreetAddress(String streetAddress) {
        this.streetAddress = streetAddress;
    }

    public String getExtendedAddress() {
        return extendedAddress;
    }

    public void setExtendedAddress(String extendedAddress) {
        this.extendedAddress = extendedAddress;
    }

    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
}
