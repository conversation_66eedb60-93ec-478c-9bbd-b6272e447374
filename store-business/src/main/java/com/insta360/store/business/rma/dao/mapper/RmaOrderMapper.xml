<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.rma.dao.RmaOrderDao">
    <!--  批量更新数据  -->
    <update id="batchUpdateRmaOrderById">
        <foreach collection="rmaOrderList" item="item" index="index" open="" close="" separator=";">
            UPDATE rma_order
            <set>
                <if test="item.id != null"> id = #{item.id}, </if>
                <if test="item.rmaNumber != null"> rma_number = #{item.rmaNumber}, </if>
                <if test="item.rmaType != null"> rma_type = #{item.rmaType}, </if>
                <if test="item.contactEmail != null"> contact_email = #{item.contactEmail}, </if>
                <if test="item.orderId != null"> order_id = #{item.orderId}, </if>
                <if test="item.orderItemId != null"> order_item_id = #{item.orderItemId}, </if>
                <if test="item.quantity != null"> quantity = #{item.quantity}, </if>
                <if test="item.createTime != null"> create_time = #{item.createTime}, </if>
                <if test="item.state != null"> state = #{item.state}, </if>
                <if test="item.closeState != null"> close_state = #{item.closeState}, </if>
                <if test="item.needReturn != null"> need_return = #{item.needReturn}, </if>
                <if test="item.refundAmount != null"> refund_amount = #{item.refundAmount}, </if>
                <if test="item.refundCurrency != null"> refund_currency = #{item.refundCurrency}, </if>
                <if test="item.reason != null"> reason = #{item.reason}, </if>
                <if test="item.extraReason != null"> extra_reason = #{item.extraReason}, </if>
                <if test="item.adminRemark != null"> admin_remark = #{item.adminRemark}, </if>
                <if test="item.rmaMainDuty != null"> rma_main_duty = #{item.rmaMainDuty}, </if>
                <if test="item.adminReason != null"> admin_reason = #{item.adminReason}, </if>
                <if test="item.finishTime != null"> finish_time = #{item.finishTime}, </if>
                <if test="item.negotiationOptionsType != null"> negotiation_options_type = #{item.negotiationOptionsType}, </if>
                <if test="item.platformSource != null"> platform_source = #{item.platformSource}, </if>
                <if test="item.recoverUser != null"> recover_user = #{item.recoverUser}, </if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <!-- 根据多条件查询订单ID集合 -->
    <select id="getRmaOrderByOrder" resultType="com.insta360.store.business.rma.model.RmaOrder" >
        select * from rma_order
        <where>
            <if test="rmaType != null" >
                and rma_type = #{rmaType}
            </if>
            <if test="orderIdList != null">
                and order_id in
                <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 分页查询售后单 -->
    <select id="selectRmaOrderByPage" resultType="com.insta360.store.business.rma.model.RmaOrder">
        select r1.* from rma_order r1 join `order` o1 on r1.order_id = o1.id
        <where>
            <choose>
                <when test="condition.rmaNumber != null and condition.rmaNumber != ''">
                    and r1.rma_number = #{condition.rmaNumber}
                </when>
                <when test="condition.orderNumber != null and condition.orderNumber != ''">
                    and o1.order_number = #{condition.orderNumber}
                </when>
                <otherwise>
                    <if test="condition.rmaType != null">
                        and r1.rma_type = #{condition.rmaType}
                    </if>
                    <if test="condition.rmaStates != null">
                        and r1.state in
                        <foreach collection="condition.rmaStates" item="state" open="(" close=")" separator=",">
                            #{state}
                        </foreach>
                    </if>
                    <if test="condition.fromTime != null and condition.endTime != null">
                        and r1.create_time between #{condition.fromTime} and #{condition.endTime}
                    </if>
                    <if test="condition.finishFromTime != null and condition.finishEndTime != null">
                        and r1.finish_time between #{condition.finishFromTime} and #{condition.finishEndTime}
                    </if>
                    <if test="condition.customerRefundReason != null and condition.customerRefundReason != ''">
                        and r1.reason = #{condition.customerRefundReason}
                    </if>
                    <if test="condition.negotiationOptionsKey != null and condition.negotiationOptionsKey != ''">
                        and r1.negotiation_options_type = #{condition.negotiationOptionsKey}
                    </if>
                    <if test="condition.needReturn != null">
                        and r1.need_return = #{condition.needReturn}
                    </if>
                    <if test="condition.userEmail != null and condition.userEmail != ''">
                        and r1.contact_email like CONCAT(#{condition.userEmail}, '%')
                    </if>
                    <if test="condition.countryCode != null and condition.countryCode != ''">
                        and o1.area = #{condition.countryCode}
                    </if>
                </otherwise>
            </choose>
        </where>
        order by r1.create_time desc
    </select>

    <select id="selectRmaOrderHistory" resultType="com.insta360.store.business.rma.model.RmaOrder">
        select
        ro.*
        from
        rma_order ro
        join `order` o on ro.order_id = o.id
        <where>
            <if test="rmaTypeList != null and rmaTypeList.size() > 0">
                and ro.rma_type in
                <foreach collection="rmaTypeList" item="rmaType" open="(" close=")" separator=",">
                    #{rmaType}
                </foreach>
            </if>
            <if test="orderStateList != null and orderStateList.size() > 0">
                and o.state in
                <foreach collection="orderStateList" item="orderState" open="(" close=")" separator=",">
                    #{orderState}
                </foreach>
            </if>
            <if test="isRepair != null">
                and o.is_repair = #{isRepair}
            </if>
            <if test="startTime != null and endTime != null">
                and o.create_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>
</mapper>
