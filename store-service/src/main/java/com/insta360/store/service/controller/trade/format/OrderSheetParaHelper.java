package com.insta360.store.service.controller.trade.format;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.ServletUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.cloud.enums.SubscribeType;
import com.insta360.store.business.configuration.utils.ServletUtils;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.enums.OrderEndpoint;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.bo.CustomItemImageInfoBO;
import com.insta360.store.business.order.bo.OrderExtraBO;
import com.insta360.store.business.order.bo.OrderPromotionBO;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.order.dto.CustomItemImageInfoDTO;
import com.insta360.store.business.order.dto.OrderExtraDTO;
import com.insta360.store.business.order.dto.OrderTradeDTO;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.OrderBillingAddress;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderInvoice;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.impl.helper.OrderNumberGenerator;
import com.insta360.store.business.trade.exception.TradeErrorCode;
import com.insta360.store.business.trade.service.impl.helper.CartItemsParser;
import com.insta360.store.business.user.exception.UserErrorCode;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserDelivery;
import com.insta360.store.business.user.service.UserDeliveryService;
import com.insta360.store.service.common.BaseApi;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/15
 * @Description:
 */
@Component
public class OrderSheetParaHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderSheetParaHelper.class);

    @Autowired
    HttpServletRequest request;

    @Autowired
    CartPack cartPack;

    @Autowired
    CartItemsParser cartItemsParser;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    UserDeliveryService userDeliveryService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderNumberGenerator orderNumberGenerator;

    public OrderSheet getBasicOrderSheet(BaseApi api, OrderTradeDTO orderTradeParam) {
        OrderSheet orderSheet = new OrderSheet();

        // 是否是工单订单
        orderSheet.setRepairOrder(false);

        // 账户信息
        orderSheet.setStoreAccount(api.getAccessUser());

        // 购买地区
        orderSheet.setCountry(api.getApiCountry());

        // 购物车内容
        JSONArray commodityArray = orderTradeParam == null ? null : orderTradeParam.getItems();
        if (commodityArray == null || commodityArray.isEmpty()) {
            throw new InstaException(OrderErrorCode.OrderItemNotFoundException);
        }

        // 需转赠品列表
        JSONArray giftItemArray = orderTradeParam.getItemInteriors();
        if (CollectionUtils.isNotEmpty(giftItemArray)) {
            List<OrderSheet.SheetItem> sheetItems = cartItemsParser.parse(giftItemArray);
            sheetItems = cartPack.cartDataFilter(sheetItems, api.getApiCountry());
            orderSheet.setSheetGiftItems(sheetItems);
        }

        // 美国送增care
        JSONArray insuranceItemArray = orderTradeParam.getOtherItems();
        if (CollectionUtils.isNotEmpty(insuranceItemArray)) {
            List<OrderSheet.SheetItem> sheetItems = cartItemsParser.parse(insuranceItemArray);
            orderSheet.setInsuranceGiftItems(sheetItems);
        }

        // 数据校验与过滤
        List<OrderSheet.SheetItem> sheetItems = cartItemsParser.parse(commodityArray);
        sheetItems = cartPack.cartDataFilter(sheetItems, api.getApiCountry());

        // sku合并和数量校验
        doMergeAndCheck(sheetItems, orderSheet);

        // 优惠码
        String couponCode = orderTradeParam == null ? null : orderTradeParam.getCouponCode();
        orderSheet.setCouponCode(couponCode);

        // 代金券
        String giftCardCode = orderTradeParam == null ? null : orderTradeParam.getGiftCardCode();
        orderSheet.setGiftCardCode(giftCardCode);

        // 相机序列号
        String deviceSerial = orderTradeParam == null ? null : orderTradeParam.getDeviceSerial();
        orderSheet.setDeviceSerial(deviceSerial);

        // 分销码
        String resellerCode = api.getResellerCode();
        if (StringUtil.isNotBlank(resellerCode)) {
            orderSheet.setResellerCode(resellerCode);
        }

        // 分销限时推广码
        String resellerFlashPromoCode = api.getFlashPromoCode();
        if (StringUtil.isNotBlank(resellerFlashPromoCode)) {
            orderSheet.setResellerFlashPromoCode(resellerFlashPromoCode);
        }

        // 流量来源
        String inscp = orderTradeParam == null ? null : orderTradeParam.getInscp();
        orderSheet.setInscp(inscp);

        // 邮件订阅
        Boolean subscribe = orderTradeParam == null && orderTradeParam.getSubscribe();
        orderSheet.setSubscribe(subscribe);

        // 下单ip地址
        orderSheet.setIp(ServletUtil.getClientIP(request));

        // 是否送增云服务
        Boolean cloud = orderTradeParam.getCloud() != null && orderTradeParam.getCloud();
        orderSheet.setCloud(cloud);

        return orderSheet;
    }

    public OrderSheet getOrderSheet(BaseApi api, OrderTradeDTO orderTradeParam) {
        StoreAccount storeAccount = api.getAccessUser();

        // 禁止某些地区下单
        if (!checkOrderCountry(api.getApiCountry())) {
            return null;
        }

        OrderSheet orderSheet = getBasicOrderSheet(api, orderTradeParam);

        // 收货信息
        if (orderSheet.getStoreAccount() == null) {
            // 游客订单
            JSONObject deliveryJson = orderTradeParam.getDelivery();
            if (deliveryJson != null) {
                orderSheet.setOrderDelivery(this.parseOrderDelivery(deliveryJson));
            }
        } else {
            // 用户地址信息
            Integer deliveryId = orderTradeParam.getDeliveryId();
            if (deliveryId != null) {
                UserDelivery userDelivery = userDeliveryService.getById(deliveryId);
                if (userDelivery == null) {
                    throw new InstaException(UserErrorCode.UserDeliveryNotFoundException);
                }
                orderSheet.setOrderDelivery(orderDeliveryService.fromUserDelivery(userDelivery));
            } else {
                JSONObject deliveryJson = orderTradeParam.getDelivery();
                if (deliveryJson != null) {
                    orderSheet.setOrderDelivery(this.parseOrderDelivery(deliveryJson));
                }
            }
        }

        // 账单地址
        JSONObject billingJson = orderTradeParam.getBilling();
        if (billingJson != null) {
            OrderBillingAddress billingAddress = new OrderBillingAddress();
            billingAddress.setCountry(billingJson.getString("country"));
            billingAddress.setOceanCode(billingJson.getString("ocean_code"));
            billingAddress.setProvince(billingJson.getString("province"));
            billingAddress.setCity(billingJson.getString("city"));
            billingAddress.setAddress(billingJson.getString("address"));
            billingAddress.setSubAddress(billingJson.getString("sub_address"));
            billingAddress.setDistrict(billingJson.getString("district"));

            String countryCode = billingJson.getString("country_code");
            String zipCode = billingJson.getString("zip_code");
            // 邮编规则校验
            this.checkZipCode(zipCode, countryCode);
            billingAddress.setCountryCode(countryCode);
            billingAddress.setZipCode(zipCode);
            billingAddress.setFirstName(billingJson.getString("first_name"));
            billingAddress.setLastName(billingJson.getString("last_name"));
            billingAddress.setPhone(billingJson.getString("phone"));
            billingAddress.setPhoneCode(billingJson.getString("phone_code"));
            billingAddress.setTaxNumber(billingJson.getString("tax_number"));
            billingAddress.setTaxTitle(billingJson.getString("tax_title"));

            orderSheet.setBillingAddress(billingAddress);
        }

        // 发票信息
        JSONObject invoiceJson = orderTradeParam.getInvoice();
        if (invoiceJson != null) {
            OrderInvoice orderInvoice = new OrderInvoice();
            orderInvoice.setType(invoiceJson.getString("type"));
            orderInvoice.setTitle(invoiceJson.getString("title"));
            orderInvoice.setTaxNumber(invoiceJson.getString("tax_number"));
            orderInvoice.setCompanyAddress(invoiceJson.getString("company_address"));
            orderInvoice.setCompanyPhone(invoiceJson.getString("company_phone"));
            orderInvoice.setCompanyBank(invoiceJson.getString("company_bank"));
            orderInvoice.setCompanyBankAccount(invoiceJson.getString("company_bank_account"));
            orderInvoice.setCompanyFile(invoiceJson.getString("company_file"));
            orderInvoice.setSendAddress(invoiceJson.getString("send_address"));
            orderInvoice.setSendEmail(invoiceJson.getString("send_email"));
            orderSheet.setInvoice(orderInvoice);
        }

        // 订单备注
        orderSheet.setRemark(orderTradeParam.getRemark());

        // 联系邮箱
        String contactEmail = orderTradeParam.getContactEmail();
        if (StringUtil.isBlank(contactEmail)) {
            contactEmail = storeAccount.getUsername();
        }
        orderSheet.setContactEmail(contactEmail);

        // 定制贴信息
        List<CustomItemImageInfoDTO> customItemImageInfos = orderTradeParam.getCustomItemImageInfos();
        if (CollectionUtils.isNotEmpty(customItemImageInfos)) {
            List<CustomItemImageInfoBO> customItemImageInfoBos = customItemImageInfos.stream().map(CustomItemImageInfoBO::new).collect(Collectors.toList());
            orderSheet.setCustomItemImageInfos(customItemImageInfoBos);
        }

        // 订单来源终端
        OrderEndpoint orderEndpoint = null;
        String endpointStr = orderTradeParam.getOrderEndPoint();
        if (StringUtil.isNotBlank(endpointStr)) {
            orderEndpoint = OrderEndpoint.parse(Integer.valueOf(endpointStr));
        }

        if (orderEndpoint == null) {
            orderEndpoint = ServletUtils.isMobile(request) ? OrderEndpoint.mobile : OrderEndpoint.pc;
        }
        orderSheet.setOrderEndpoint(orderEndpoint);

        // 海外达人推广链接记录
        String resellerCode = api.getResellerCode();
        OrderPromotionBO orderPromotion = orderTradeParam.getOrderPromotion();
        if (orderPromotion != null && StringUtil.isNotBlank(resellerCode)) {
            orderPromotion.setPromoCode(resellerCode);
            orderSheet.setOrderPromotion(orderPromotion);
        }

        // 云存储用户数据存储所属地相关信息
        OrderExtraDTO orderExtra = orderTradeParam.getOrderExtra();
        if(Objects.nonNull(orderExtra)) {
            OrderExtraBO orderExtraBo = new OrderExtraBO();
            BeanUtils.copyProperties(orderExtra, orderExtraBo);
            orderSheet.setOrderExtra(orderExtraBo);
        }

        // 云服务订阅类型
        if (Objects.nonNull(orderTradeParam.getCloudSubscribeType())) {
            orderSheet.setCloudSubscribeType(SubscribeType.parse(orderTradeParam.getCloudSubscribeType()));
        }

        // prime 相关字段
        orderSheet.setPrime(orderTradeParam.getPrime());
        orderSheet.setAmazonUserId(orderTradeParam.getAmazonUserId());
        orderSheet.setOfferId(orderTradeParam.getOfferId());

        return orderSheet;
    }

    /**
     * sku合并和数量处理
     *
     * @param sheetItems
     * @param orderSheet
     * @see OrderSheet.SheetItem#equals(Object)
     * @see OrderSheet.SheetItem#hashCode()
     */
    private void doMergeAndCheck(List<OrderSheet.SheetItem> sheetItems, OrderSheet orderSheet) {
        sheetItems.forEach(si -> {
            // 商品数量需要是正数，如果携带小数的话，利用int的机制保留整数即可。
            Integer number = si.getNumber();
            if (number <= 0) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("商品数量小于等于0{%s}", orderSheet), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }
        });

        // 合并相同sku
        // todo 相同sku绑定不同bindServices
        orderSheet.setSheetItems(new ArrayList<>(sheetItems.stream().collect(Collectors.toMap(OrderSheet.SheetItem::getCommodityId, item -> item, (i1, i2) -> {
            i1.setNumber(i1.getNumber() + i2.getNumber());
            return i1;
        })).values()));

        // 数量格式校验
        List<OrderSheet.SheetItem> sheetGiftItems = orderSheet.getSheetGiftItems();
        sheetGiftItems.forEach(si -> {
            // 商品数量需要是正数，如果携带小数的话，利用int的机制保留整数即可。
            Integer number = si.getNumber();
            if (number <= 0) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("商品数量小于等于0{%s}", orderSheet), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }
        });

        // todo 相同sku绑定不同bindServices
        orderSheet.setSheetGiftItems(new ArrayList<>(sheetGiftItems.stream()
                .collect(Collectors.toMap(OrderSheet.SheetItem::getCommodityId, item -> item, (i1, i2) -> {
                    i1.setNumber(i1.getNumber() + i2.getNumber());
                    return i1;
                })).values()));
    }

    /**
     * 校验下单地区
     *
     * @param country
     * @return
     */
    private boolean checkOrderCountry(InstaCountry country) {
        String disabledCountry = storeConfigService.getConfigValue(StoreConfigKey.disabled_create_order_country);
        String[] countries = disabledCountry.split(",");
        for (String str : countries) {
            if (str.equals(country.name())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 邮编校验
     *
     * @param zipCode
     * @param countryCode
     */
    private void checkZipCode(String zipCode, String countryCode) {
        if (zipCode == null) {
            // 中国、香港、澳门为非必填
            if (InstaCountry.CN.name().equals(countryCode) || InstaCountry.HK.name().equals(countryCode) || InstaCountry.MO.name().equals(countryCode)) {
                return;
            } else {
                throw new InstaException(TradeErrorCode.InvalidZipCodeException);
            }
        }

        // 所有地区的邮编都不可超过50个字符
        if (zipCode.length() > 50) {
            throw new InstaException(TradeErrorCode.InvalidZipCodeException);
        }
    }

    /**
     * 解析 OrderDelivery
     *
     * @param deliveryJson
     * @return
     */
    private OrderDelivery parseOrderDelivery(JSONObject deliveryJson) {
        OrderDelivery orderDelivery = new OrderDelivery();
        orderDelivery.setCountry(deliveryJson.getString("country"));
        orderDelivery.setProvince(deliveryJson.getString("province"));
        orderDelivery.setProvinceCode(deliveryJson.getString("provinceCode"));

        orderDelivery.setCity(deliveryJson.getString("city"));
        orderDelivery.setAddress(deliveryJson.getString("address"));
        orderDelivery.setDistrict(deliveryJson.getString("district"));
        orderDelivery.setSubAddress(deliveryJson.getString("sub_address"));

        String zipCode = deliveryJson.getString("zip_code");
        String countryCode = deliveryJson.getString("country_code");
        // 邮编校验
        this.checkZipCode(zipCode, countryCode);
        orderDelivery.setZipCode(zipCode);
        orderDelivery.setCountryCode(countryCode);
        orderDelivery.setLastName(deliveryJson.getString("last_name"));
        orderDelivery.setFirstName(deliveryJson.getString("first_name"));
        orderDelivery.setPhone(deliveryJson.getString("phone"));
        orderDelivery.setPhoneCode(deliveryJson.getString("phone_code"));
        return orderDelivery;
    }
}
