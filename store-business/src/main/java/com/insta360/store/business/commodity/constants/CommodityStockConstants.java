package com.insta360.store.business.commodity.constants;

import com.insta360.compass.core.enums.InstaCountry;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/11 上午11:32
 */
public class CommodityStockConstants {

    /**
     * 欧盟地区
     */
    public static final List<InstaCountry> EU_REGION = Arrays.asList(InstaCountry.AT, InstaCountry.BE, InstaCountry.BG, InstaCountry.CZ,
            InstaCountry.FR, InstaCountry.DE, InstaCountry.HU, InstaCountry.LV, InstaCountry.LT, InstaCountry.LU, InstaCountry.NL,
            InstaCountry.PL, InstaCountry.PT, InstaCountry.RO, InstaCountry.SK, InstaCountry.ES);

    /**
     * 德国，法国，西班牙
     */
    public static final List<String> DE_FR_ES_REGION = Arrays.asList(InstaCountry.DE.name(), InstaCountry.FR.name(), InstaCountry.ES.name());

    /**
     * 库存告警阈值
     */
    public static final Integer STOCK_ALARM_VALUE = 2;

    /**
     * 库存缓存刷新阈值
     */
    public static final Integer STOCK_CACHE_REFRESH_THRESHOLD = 10;

    /**
     * 告警文案的地区顺序
     */
    public static final List<InstaCountry> NOTICE_AREA_ORDER = Arrays.asList(InstaCountry.GB, InstaCountry.JP, InstaCountry.AU, InstaCountry.US,
            InstaCountry.IT, InstaCountry.AT, InstaCountry.BE, InstaCountry.BG, InstaCountry.CZ, InstaCountry.FR, InstaCountry.DE,
            InstaCountry.HU, InstaCountry.LV, InstaCountry.LT, InstaCountry.LU, InstaCountry.NL, InstaCountry.PL, InstaCountry.PT,
            InstaCountry.RO, InstaCountry.SK, InstaCountry.ES);

    /**
     * 库存告警模板
     */
    public static final String STOCK_NOTICE_TEMPLATE = "【库存告警】 \n " +
            "产品名称：%s \n" +
            "套餐名称：%s \n" +
            "套餐ID：%d \n" +
            "该套餐的总仓库存数量已小于等于2，请及时补货或注意跟进套餐销售状态  \n" +
            "\n" +
            "%s \n" +
            "\n" +
            "%s";

    /**
     * 库存告警地区套餐信息
     */
    public static final String STOCK_NOTICE_AREA_COMMODITY = "7天内未⽀付&已⽀付&部分发货&⽀付处理中，订单内套餐总数=%d：\n %s";

    /**
     * 库存告警地区套餐信息为空时提示
     */
    public static final String STOCK_NOTICE_EMPTY_AREA_COMMODITY = "7天内未⽀付&已⽀付&部分发货&⽀付处理中，订单内套餐总数=0";

    /**
     * 库存告警已配货订单信息
     */
    public static final String STOCK_NOTICE_PREPARED_ORDER = "已配货：订单内套餐数量为%d，订单号分别为\n %s";

    /**
     * 库存告警已配货订单信息为空时提示
     */
    public static final String STOCK_NOTICE_EMPTY_PREPARED_ORDER = "已配货：订单内套餐数量为0";

    /**
     * 美东虚拟仓编码
     */
    public static final String US_EAST_WAREHOUSE_CODE = "80015096-SHARE";

    /**
     * 仓库默认的可发货地区
     * key -> 仓库id  value -> 发货地区集合
     */
    public static final Map<Integer, List<InstaCountry>> STORAGE_DEFAULT_DELIVERY_AREA = new HashMap<>();

    static {
        STORAGE_DEFAULT_DELIVERY_AREA.put(2, Arrays.asList(InstaCountry.GB));
        STORAGE_DEFAULT_DELIVERY_AREA.put(3, Arrays.asList(InstaCountry.JP));
        STORAGE_DEFAULT_DELIVERY_AREA.put(4, EU_REGION);
        STORAGE_DEFAULT_DELIVERY_AREA.put(5, EU_REGION);
        STORAGE_DEFAULT_DELIVERY_AREA.put(6, EU_REGION);
        STORAGE_DEFAULT_DELIVERY_AREA.put(7, Arrays.asList(InstaCountry.AU));
        STORAGE_DEFAULT_DELIVERY_AREA.put(8, Arrays.asList(InstaCountry.US));
        STORAGE_DEFAULT_DELIVERY_AREA.put(9, Arrays.asList(InstaCountry.US));
        STORAGE_DEFAULT_DELIVERY_AREA.put(10, Arrays.asList(InstaCountry.US));
        STORAGE_DEFAULT_DELIVERY_AREA.put(11, Arrays.asList(InstaCountry.IT));
    }
}
