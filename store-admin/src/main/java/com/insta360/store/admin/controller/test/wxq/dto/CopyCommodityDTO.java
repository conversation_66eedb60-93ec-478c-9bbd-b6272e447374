package com.insta360.store.admin.controller.test.wxq.dto;

import com.insta360.store.business.configuration.validation.annotation.MinIntegerNumber;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/17
 */
public class CopyCommodityDTO implements Serializable {

    @NotNull
    @MinIntegerNumber
    private Integer productId;

    private List<Integer> commodityIds;

    private Boolean allCommodity;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<Integer> getCommodityIds() {
        return commodityIds;
    }

    public void setCommodityIds(List<Integer> commodityIds) {
        this.commodityIds = commodityIds;
    }

    public Boolean getAllCommodity() {
        return allCommodity;
    }

    public void setAllCommodity(Boolean allCommodity) {
        this.allCommodity = allCommodity;
    }
}
