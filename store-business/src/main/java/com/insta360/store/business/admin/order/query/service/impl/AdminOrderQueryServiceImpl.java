package com.insta360.store.business.admin.order.query.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.AssertUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.admin.order.query.bo.AdminOrderQueryCondition;
import com.insta360.store.business.admin.order.query.service.AdminOrderQueryService;
import com.insta360.store.business.configuration.utils.PageResultUtil;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.dao.OrderDao;
import com.insta360.store.business.order.dto.AdminOrderQueryBO;
import com.insta360.store.business.order.dto.PayedOrderQueryDTO;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.order.service.*;
import com.insta360.store.business.reseller.service.ResellerUtmSourceService;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import com.insta360.store.business.tradeup.service.TradeupOrderService;
import com.insta360.store.business.user.model.StoreAccount;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019-05-15
 * @Description:
 */
@Service
public class AdminOrderQueryServiceImpl extends BaseServiceImpl<OrderDao, Order> implements AdminOrderQueryService {

    @Autowired
    OrderPaymentService paymentService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderAdminRemarkService orderAdminRemarkService;

    @Autowired
    TradeupOrderService tradeupOrderService;

    @Autowired
    OrderDeliveryUniqueCodeService orderDeliveryUniqueCodeService;

    @Autowired
    OrderDeliveryPartlyService orderDeliveryPartlyService;

    @Autowired
    ResellerUtmSourceService resellerUtmSourceService;

    @Override
    public Page<Order> queryByComplexCondition(IPage<Order> iPage, AdminOrderQueryCondition condition) {
        return (Page<Order>) iPage.setRecords(baseMapper.queryByComplexCondition(iPage, condition));
    }

    @Override
    public PageResult<Order> queryByCountry(InstaCountry country, PageQuery pageQuery) {
        AssertUtil.notNull(country);
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("area", country.name());

        // 排除工单系统订单
        qw.eq("is_repair", false);
        qw.orderByDesc("create_time");
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw));
    }

    @Override
    public PageResult<Order> queryByInscp(String inscp, PageQuery pageQuery) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("inscp", inscp);
        qw.orderByDesc("create_time");
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw));
    }

    @Override
    public PageResult<Order> queryByOrderNumber(String orderNumber, PageQuery pageQuery) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.likeRight(StringUtil.isNotBlank(orderNumber), "order_number", orderNumber.trim());
        qw.orderByDesc("create_time");
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw));
    }

    @Override
    public PageResult<Order> queryByPayChannel(PaymentChannel channel, PageQuery pageQuery) {
        QueryWrapper<OrderPayment> qwPayment = new QueryWrapper<>();
        qwPayment.eq("channel", channel.name());
        qwPayment.orderByDesc("`order`");
        List<OrderPayment> orderPayments = paymentService.list(qwPayment);

        // empty return
        if (CollectionUtils.isEmpty(orderPayments)) {
            return PageResultUtil.getEmptyResult();
        }

        List<Integer> orderIds = orderPayments.stream().map(OrderPayment::getOrder).collect(Collectors.toList());
        QueryWrapper<Order> qwOrder = new QueryWrapper<>();
        qwOrder.in("id", orderIds);
        qwOrder.eq("is_repair", false);
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qwOrder));
    }

    @Override
    public Page<Order> queryByTradeCodeAndUtmSource(String tradeCode, AdminOrderQueryCondition condition, PageQuery pageQuery) {
        IPage<Order> iPage = PageUtil.toIPage(pageQuery);
        List<Order> list = baseMapper.queryByTradeCodeAndUtmSource(iPage, tradeCode, condition.getQueryUtmSourceList(), condition);
        return (Page<Order>) iPage.setRecords(list);

    }

    @Override
    public PageResult<Order> queryByUserEmail(String email, PageQuery pageQuery) {
        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.likeRight("contact_email", email);
        qw.orderByDesc("create_time");
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw));
    }

    @Override
    public PageResult<Order> queryPaidOrderByComplexCondition(PayedOrderQueryDTO condition, PageQuery pageQuery) {
        Boolean tradeupOrderFilterMark = condition.getTradeupOrderFilterMark();
        Boolean orderSellerRemarkMark = condition.getOrderSellerRemarkMark();

        //查询条件参数校验及构建
        AdminOrderQueryBO query = queryConditionCheck(condition);

        List<Integer> orderIdList = baseMapper.listOrderIdByComplexConditions(query);

        //1、是否有指定'不包含'产品？
        List<Integer> noProductIds = condition.getNoProducts();
        List<Integer> productOrderIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(noProductIds)) {
            productOrderIdList = orderItemService.getOrderIdByProductAndOrder(noProductIds, orderIdList);
        }

        //2、是否需过滤'以旧换新'订单？
        List<Integer> tradeupOrderIdList = Lists.newArrayList();
        if (Objects.nonNull(tradeupOrderFilterMark) && tradeupOrderFilterMark) {
            tradeupOrderIdList = tradeupOrderService.getByExistOrderIds(orderIdList);
        }

        //3、在原有'已支付'订单池中去除指定'产品'订单及以旧换新订单
        List<Integer> finalProductOrderIdList = productOrderIdList;
        List<Integer> finalTradeupOrderIdList = tradeupOrderIdList;
        orderIdList = orderIdList.stream()
                .filter(orderId -> !(finalProductOrderIdList.contains(orderId) || finalTradeupOrderIdList.contains(orderId)))
                .collect(Collectors.toList());

        //4、查询是否指定'备注'订单
        if (Objects.nonNull(orderSellerRemarkMark)) {
            List<Integer> remarkOrderIdList = orderAdminRemarkService.getByOrderIdList(orderIdList);
            //4.1 orderSellerRemarkMark = true 则代表查询包含'备注'的订单，反之则查询不包含'备注'的订单
            orderIdList = orderSellerRemarkMark ? remarkOrderIdList : Optional.of(orderIdList)
                    .orElse(Lists.newArrayList())
                    .stream().filter(orderId -> !remarkOrderIdList.contains(orderId))
                    .collect(Collectors.toList());
        }

        //5、去除存在售后中 or 订单商品已全部售后的订单
        List<RmaOrder> rmaOrderList = rmaOrderService.getRmaOrderByOrder(RmaType.rma_refund.name(), orderIdList);
        if (CollectionUtils.isNotEmpty(rmaOrderList)) {
            Set<Integer> rmaOrderIdSet = rmaOrderList.stream().map(RmaOrder::getOrderId).collect(Collectors.toSet());
            //临时剔除掉存在售后单的订单
            orderIdList = orderIdList.stream().filter(orderId -> !rmaOrderIdSet.contains(orderId)).collect(Collectors.toList());
            //将所有售后单过滤一遍，筛选出还未退完且未存在售后中的订单
            Set<Integer> qualifiedOrders = refundOrderFilterHandler(rmaOrderList);
            //合并所有符合条件需展示的订单
            orderIdList.addAll(qualifiedOrders);
        }

        //6、使用最终符合条件的订单ID集合进行数据分页再查询
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new PageResult();
        }

        IPage iPage = PageUtil.toIPage(pageQuery);
        iPage.setRecords(baseMapper.listOrderByPage(iPage, orderIdList,condition.getShipPriority()));
        return PageUtil.toPageResult(iPage);
    }

    /**
     * 过滤存在售后中及商品已全部售后的订单
     *
     * @param rmaOrders
     * @return
     */
    private Set<Integer> refundOrderFilterHandler(List<RmaOrder> rmaOrders) {
        // 筛选出'待审核'的售后订单
        Set<Integer> auditSet = Optional.ofNullable(rmaOrders)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(
                        rmaOrder -> RmaState.init.equals(RmaState.parse(rmaOrder.getState())))
                .map(RmaOrder::getOrderId)
                .collect(Collectors.toSet());
        //1、从售后单池子中剔除包含'待审核'售后单的商城订单
        Set<Integer> orderIdSet = Optional.ofNullable(rmaOrders)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(
                        rmaOrder -> !auditSet.contains(rmaOrder.getOrderId()))
                .map(RmaOrder::getOrderId)
                .collect(Collectors.toSet());
        List<OrderItem> orderItemList = orderItemService.getOrderItemByOrderIds(Lists.newArrayList(orderIdSet));
        // key: orderId  value: orderItemState
        Map<Integer, List<Integer>> orderItemStateMap = orderItemList.stream()
                .collect(
                        Collectors.groupingBy(OrderItem::getOrder, Collectors.mapping(OrderItem::getState, Collectors.toList()))
                );
        //2、过滤订单商品已全部售后成功的订单
        if (!orderItemStateMap.isEmpty()) {
            orderIdSet = orderItemStateMap.entrySet().stream()
                    .filter(
                            entry -> entry.getValue()
                                    .stream()
                                    .filter(
                                            orderItemState -> OrderItemState.refunded.getCode() != orderItemState)
                                    .findFirst()
                                    .isPresent()
                    ).map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
        }

        return orderIdSet;
    }

    /**
     * 参数检查
     *
     * @param condition
     * @return
     */
    private AdminOrderQueryBO queryConditionCheck(PayedOrderQueryDTO condition) {
        AdminOrderQueryBO query = new AdminOrderQueryBO();

        Boolean shipPriority = condition.getShipPriority();
        if (Objects.nonNull(shipPriority)){
            query.setShipPriority(shipPriority);
        }

        List<String> countryList = condition.getCountryCodeList();
        if (CollectionUtils.isNotEmpty(countryList)) {
            query.setCountryList(countryList);
        }

        List<String> noCountryList = condition.getNoCountryCodeList();
        if (CollectionUtils.isNotEmpty(noCountryList)) {
            query.setNoCountryList(noCountryList);
        }

        List<Integer> productIdList = condition.getProducts();
        if (CollectionUtils.isNotEmpty(productIdList)) {
            query.setProductIdList(productIdList);
        }

        List<Integer> rmaStateList = condition.getRmaStateList();
        if (CollectionUtils.isNotEmpty(rmaStateList)) {
            query.setRmaStateList(rmaStateList);
            query.setRmaType(RmaType.rma_refund.name());
        }

        List<String> paymentChannelList = condition.getPaymentChannelList();
        if (CollectionUtils.isNotEmpty(paymentChannelList)) {
            query.setPaymentChannelList(paymentChannelList);
        }

        List<String> noPaymentChannelList = condition.getNoPaymentChannelList();
        if (CollectionUtils.isNotEmpty(noPaymentChannelList)) {
            query.setNoPaymentChannelList(noPaymentChannelList);
        }

        LocalDateTime fromTime = condition.getFrom();
        LocalDateTime endTime = condition.getEnd();
        if (Objects.nonNull(fromTime) && Objects.nonNull(endTime)) {
            query.setFromTime(fromTime);
            query.setEndTime(endTime);
        }

        query.setPaymentState(OrderPaymentState.PAYED.getCode());
        query.setOrderState(OrderState.payed.getCode());
        return query;
    }

    @Override
    public PageResult<Order> queryByDeviceSerial(String deviceSerial, PageQuery pageQuery) {
        List<OrderDeliveryUniqueCode> orderDeliveryUniqueCodes = orderDeliveryUniqueCodeService.listByUniqueCode(deviceSerial);
        List<Integer> orderIds = orderDeliveryUniqueCodes.stream().map(OrderDeliveryUniqueCode::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return PageResultUtil.getEmptyResult();
        }

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.in("id", orderIds);
        qw.orderByDesc("create_time");
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw));
    }

    @Override
    public PageResult<Order> queryByLogisticsNo(String logisticsNumber, PageQuery pageQuery) {
        List<OrderDeliveryPartly> deliveryPartly = orderDeliveryPartlyService.getDeliveryPartly(logisticsNumber);
        List<Integer> orderIds = deliveryPartly.stream().map(OrderDeliveryPartly::getOrderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return PageResultUtil.getEmptyResult();
        }

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.in("id", orderIds);
        qw.orderByDesc("create_time");
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw));
    }
}
