package com.insta360.store.business.order.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/11/19
 * @Description:
 */
public class PayedOrderQueryDTO implements Serializable {

    @JSONField(name = "page_number")
    private Integer pageNumber;

    @JSONField(name = "page_size")
    private Integer pageSize;

    /**
     * 支付渠道包含
     */
    private List<String> paymentChannelList;

    /**
     * 支付渠道不包含
     */
    private List<String> noPaymentChannelList;

    /**
     * 国家地区包含
     */
    private List<String> countryCodeList;


    /**
     * 国家地区不包含
     */
    private List<String> noCountryCodeList;

    /**
     * 支付开始时间
     */
    private LocalDateTime from;

    /**
     * 支付结束时间
     */
    private LocalDateTime end;

    /**
     * 产品包含
     */
    private List<Integer> products;

    /**
     * 产品不包含
     */
    private List<Integer> noProducts;

    /**
     * 售后状态
     */
    private List<Integer> rmaStateList;

    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 以旧换新订单过滤标记
     */
    private Boolean tradeupOrderFilterMark;

    /**
     * 订单是否包含卖家备注
     */
    private Boolean orderSellerRemarkMark;

    /**
     * 是否优先发货
     */
    private Boolean shipPriority;

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getPaymentChannelList() {
        return paymentChannelList;
    }

    public void setPaymentChannelList(List<String> paymentChannelList) {
        this.paymentChannelList = paymentChannelList;
    }

    public List<String> getNoPaymentChannelList() {
        return noPaymentChannelList;
    }

    public void setNoPaymentChannelList(List<String> noPaymentChannelList) {
        this.noPaymentChannelList = noPaymentChannelList;
    }

    public List<String> getCountryCodeList() {
        return countryCodeList;
    }

    public void setCountryCodeList(List<String> countryCodeList) {
        this.countryCodeList = countryCodeList;
    }

    public List<String> getNoCountryCodeList() {
        return noCountryCodeList;
    }

    public void setNoCountryCodeList(List<String> noCountryCodeList) {
        this.noCountryCodeList = noCountryCodeList;
    }

    public LocalDateTime getFrom() {
        return from;
    }

    public void setFrom(LocalDateTime from) {
        this.from = from;
    }

    public LocalDateTime getEnd() {
        return end;
    }

    public void setEnd(LocalDateTime end) {
        this.end = end;
    }

    public List<Integer> getProducts() {
        return products;
    }

    public void setProducts(List<Integer> products) {
        this.products = products;
    }

    public List<Integer> getNoProducts() {
        return noProducts;
    }

    public void setNoProducts(List<Integer> noProducts) {
        this.noProducts = noProducts;
    }

    public List<Integer> getRmaStateList() {
        return rmaStateList;
    }

    public void setRmaStateList(List<Integer> rmaStateList) {
        this.rmaStateList = rmaStateList;
    }

    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public Boolean getTradeupOrderFilterMark() {
        return tradeupOrderFilterMark;
    }

    public void setTradeupOrderFilterMark(Boolean tradeupOrderFilterMark) {
        this.tradeupOrderFilterMark = tradeupOrderFilterMark;
    }

    public Boolean getOrderSellerRemarkMark() {
        return orderSellerRemarkMark;
    }

    public void setOrderSellerRemarkMark(Boolean orderSellerRemarkMark) {
        this.orderSellerRemarkMark = orderSellerRemarkMark;
    }

    public Boolean getShipPriority() {
        return shipPriority;
    }

    public void setShipPriority(Boolean shipPriority) {
        this.shipPriority = shipPriority;
    }

    @Override
    public String toString() {
        return "PayedOrderQueryDTO{" +
                "pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", paymentChannelList=" + paymentChannelList +
                ", noPaymentChannelList=" + noPaymentChannelList +
                ", countryCodeList=" + countryCodeList +
                ", noCountryCodeList=" + noCountryCodeList +
                ", from=" + from +
                ", end=" + end +
                ", products=" + products +
                ", noProducts=" + noProducts +
                ", rmaStateList=" + rmaStateList +
                ", orderState=" + orderState +
                ", tradeupOrderFilterMark=" + tradeupOrderFilterMark +
                ", orderSellerRemarkMark=" + orderSellerRemarkMark +
                ", shipPriority=" + shipPriority +
                '}';
    }
}
