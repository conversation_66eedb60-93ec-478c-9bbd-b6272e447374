package com.insta360.store.business.integration.wto.oms.service.handler;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteResultBO;
import com.insta360.store.business.integration.wto.oms.configuration.OmsConfiguration;
import com.insta360.store.business.integration.wto.oms.lib.requrest.BaseOmsRequest;
import com.insta360.store.business.integration.wto.oms.utils.OmsCommonUtil;
import com.insta360.store.business.integration.wto.service.BaseStoreService;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.AvalaraTaxInfoService;
import com.insta360.store.business.meta.service.AvalaraTaxRateSummaryService;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/2
 */
public abstract class BaseOmsService extends BaseStoreService implements OmsService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BaseOmsService.class);

    @Autowired
    protected OrderService orderService;

    @Autowired
    protected OmsConfiguration omsConfiguration;

    @Autowired
    protected OrderItemService orderItemService;

    @Autowired
    protected OrderPaymentService orderPaymentService;

    @Autowired
    protected CommodityCodeService commodityCodeService;

    @Autowired
    protected AvalaraTaxInfoService avalaraTaxInfoService;

    @Autowired
    protected AvalaraTaxRateSummaryService avalaraTaxRateSummaryService;

    @Autowired
    protected StoreSdkCallRecordService storeSdkCallRecordService;

    /**
     * 商城订单
     */
    protected Order order;

    /**
     * 订单支付信息
     */
    protected OrderPayment orderPayment;

    /**
     * 执行oms平台操作
     *
     * @param omsExecuteBo
     * @return
     */
    @Override
    public OmsExecuteResultBO executeOmsTransaction(OmsExecuteBO omsExecuteBo) {
        if (this.initializable()) {
            this.init(omsExecuteBo);
        }
        return this.doExecuteOmsTransaction(omsExecuteBo);
    }

    /**
     * 订单信息初始化，具体子类可重写
     *
     * @param omsExecuteBo
     */
    protected void init(OmsExecuteBO omsExecuteBo) {
        Integer orderId = omsExecuteBo.getOrderId();
        if (orderId == null) {
            LOGGER.error("[oms初始化]订单id为空");
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        Order order = orderService.getById(orderId);
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        this.order = order;

        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (Objects.isNull(orderPayment)) {
            throw new InstaException(OrderErrorCode.OrderPaymentNotFoundException);
        }
        this.orderPayment = orderPayment;
    }

    /**
     * 执行oms平台操作，由具体子类实现
     *
     * @param omsExecuteBo
     * @return
     */
    protected abstract OmsExecuteResultBO doExecuteOmsTransaction(OmsExecuteBO omsExecuteBo);

    /**
     * 获取请求参数
     *
     * @param omsExecuteBo
     * @return
     */
    protected abstract BaseOmsRequest getOmsRequest(OmsExecuteBO omsExecuteBo);

    /**
     * 时间戳转换
     *
     * @param time
     * @return
     */
    protected String timestampConversion(LocalDateTime time) {
        // 转换为东八区时间
        ZonedDateTime zonedDateTime = time.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneId.of("Asia/Shanghai"));

        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return zonedDateTime.format(formatter);
    }

    /**
     * 获取oms店铺代码
     *
     * @return
     */
    protected String getShopCode() {
        // 获取支付渠道
        PaymentChannel channel = PaymentChannel.parse(orderPayment.getChannel());
        return OmsCommonUtil.shopCodeMapping(channel);
    }

    /**
     * 保存调用记录
     *
     * @param orderNumber
     * @param subCode
     * @param requestJsonInfo
     * @param responseJsonInfo
     * @param callBusinessType
     * @param callApiType
     */
    protected void saveCallRecord(String orderNumber, String subCode, String requestJsonInfo, String responseJsonInfo,
                                  StoreSdkCallBusinessType callBusinessType, StoreSdkCallApiType callApiType) {
        storeSdkCallRecordService.saveSdkCallRecord(orderNumber, subCode, requestJsonInfo, responseJsonInfo, callBusinessType, callApiType);
    }

    /**
     * 执行POST请求并处理结果
     * <p>
     * 该方法负责调用BaseOmsRequest对象的executePost方法发起POST请求，并处理请求结果或异常
     * 在finally块中，无论请求是否成功，都会调用saveCallRecord方法保存调用记录
     *
     * @param omsRequest 基础OMS请求对象，用于执行POST请求
     * @return 请求的结果字符串，如果发生异常则返回null
     */
    protected String doExecutePost(BaseOmsRequest omsRequest) {
        String result = null;
        try {
            // 执行POST请求并获取结果
            result = omsRequest.executePost();
        } catch (Exception e) {
            // 捕获并记录异常，确保官方商城售后单同步的异常信息被记录
            String message = String.format("[OMS系统]官方商城 请求执行失败,存在异常.method:%s omsRequest: %s", omsRequest.getMethod(), JSON.toJSONString(omsRequest));
            LOGGER.error(message, e);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning);
        } finally {
            // 保存调用记录，确保每次请求的信息都被记录下来
            this.saveCallRecord(order.getOrderNumber(), getSubCode(), JSON.toJSONString(omsRequest), result, StoreSdkCallBusinessType.JY_OMS, getApiType());
        }
        return result;
    }

    /**
     * 获取子代码
     * <p>
     * 此方法用于获取当前对象的子代码子代码可以是例如产品类别代码，部门代码等
     * 在实际应用中，此方法需要根据具体业务逻辑进行实现并返回相应的子代码
     *
     * @return String 返回子代码如果当前对象没有子代码，则返回空字符串
     */
    protected String getSubCode() {
        return StringUtils.EMPTY;
    }

    /**
     * 获取api类型
     *
     * @return
     */
    protected StoreSdkCallApiType getApiType() {
        return StoreSdkCallApiType.UNKNOWN;
    }

}
