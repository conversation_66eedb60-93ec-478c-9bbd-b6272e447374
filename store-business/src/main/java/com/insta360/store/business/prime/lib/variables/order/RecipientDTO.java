package com.insta360.store.business.prime.lib.variables.order;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.prime.bo.PrimeShippingAddressBO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/1
 */
public class RecipientDTO {

    private String id;

    private DeliveryAddressDTO deliveryAddress;

    public RecipientDTO(PrimeShippingAddressBO shippingAddress) {
        DeliveryAddressDTO deliveryAddressDTO = new DeliveryAddressDTO();
        BeanUtil.copyProperties(shippingAddress, deliveryAddressDTO);
        this.deliveryAddress = deliveryAddressDTO;
    }

    public RecipientDTO() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public DeliveryAddressDTO getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(DeliveryAddressDTO deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }
}
