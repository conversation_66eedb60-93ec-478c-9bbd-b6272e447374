package com.insta360.store.business.commodity.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.commodity.bo.CommodityStockBO;
import com.insta360.store.business.commodity.config.CommodityCommonConfig;
import com.insta360.store.business.commodity.constants.CommodityStockConstants;
import com.insta360.store.business.commodity.dto.CommodityStockInfoDTO;
import com.insta360.store.business.commodity.dto.CommodityStorageStockDTO;
import com.insta360.store.business.commodity.enums.CommodityStockLimitType;
import com.insta360.store.business.commodity.enums.CommodityTypeEnum;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.common.constants.RedisKeyConstant;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.outgoing.mq.check.helper.DoubleCheckSendHelper;
import com.insta360.store.business.utils.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 套餐库存服务
 * @Date 2024/4/2
 */
@Component
public class CommodityStockHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommodityStockHelper.class);

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    StorageInfoService storageInfoService;

    @Autowired
    ProductCommodityTypeService productCommodityTypeService;

    @Autowired
    StorageDeliveryAreaService storageDeliveryAreaService;

    @Autowired
    ProductCommodityStockService productCommodityStockService;

    @Autowired
    BundleCommodityDetailService bundleCommodityDetailService;

    @Autowired
    DoubleCheckSendHelper doubleCheckSendHelper;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    CommodityCommonConfig commodityCommonConfig;

    /**
     * 库存扣减
     * 对于指定订单中的每个商品，根据商品的套餐库存情况进行库存扣减，
     * 若库存扣减后小于等于2，则发送相应通知。
     *
     * @param order 订单，用于查询订单项以进行库存扣减。
     */
    public List<Integer> stockDeduction(Order order) {
        // 获取到下单地区
        String area = order.getArea();
        // 根据订单ID获取订单项列表
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        LOGGER.info("[库存扣减]开始处理订单，订单号:{} 订单子项:{}", order.getOrderNumber(), orderItemList.toString());
        // amazon履约商品不进行库存扣减
        orderItemList = orderItemList.stream().filter(item -> !item.deliveryProvider().isAmazon()).collect(Collectors.toList());
        LOGGER.info("[库存扣减]订单项去除amazon履约处理完成，订单号：{} 订单子项：{}", order.getOrderNumber(), orderItemList);
        // 如果订单项列表为空，则直接返回
        if (CollectionUtils.isEmpty(orderItemList)) {
            return new ArrayList<>(0);
        }

        // 套餐id集合
        List<Integer> commodityIdList = orderItemList.stream().map(OrderItem::getCommodity).collect(Collectors.toList());

        // 构建扣减顺序
        List<Integer> deductionOrderStorageIds = storageInfoService.buildDeductionSequence(area);
        LOGGER.info("[库存扣减]构建扣减顺序完成，订单号：{}，仓库id顺序：{}", order.getOrderNumber(), deductionOrderStorageIds.toString());

        // 把可发货仓按套餐分组
        List<ProductCommodityStock> commodityStocks = productCommodityStockService.listByCommodityIdsAndArea(commodityIdList, area);

        // key -> commodityId  value -> 可发货仓库集合
        Map<Integer, List<ProductCommodityStock>> commodityStockMap = commodityStocks.stream().collect(Collectors.groupingBy(ProductCommodityStock::getCommodityId, Collectors.toList()));

        // 判断是否开启美国关税
        String usCustomsTaxSwitch = storeConfigService.getConfigValue(StoreConfigKey.us_customs_tax_switch);

        // 待更新库存集合
        List<ProductCommodityStock> forUpdateStockList;
        if (InstaCountry.US.equals(order.country()) && StringUtils.isNotBlank(usCustomsTaxSwitch) && Boolean.parseBoolean(usCustomsTaxSwitch)) {
            forUpdateStockList = this.usWarehouseStockDeduction(orderItemList, commodityStockMap, deductionOrderStorageIds, order.country());
        } else {
            forUpdateStockList = this.otherWarehouseStockDeduction(orderItemList, commodityStockMap, deductionOrderStorageIds);
        }

        // 批量更新库存
        productCommodityStockService.updateCommodityStocks(forUpdateStockList);
        String forUpdateStockInfos = forUpdateStockList.stream().map(Object::toString).collect(Collectors.joining(","));
        LOGGER.info(String.format("[库存扣减]库存更新完毕，订单号：%s，库存更新信息：%s", order.getOrderNumber(), forUpdateStockInfos));

        // 总仓id
        Integer generalStorageId = storageInfoService.getGeneralStorageId();

        // 库存告警
        this.stockNotice(generalStorageId, forUpdateStockList);

        // 美国仓库存告警
        if (InstaCountry.US.equals(order.country())) {
            this.usStockNotice(commodityStockMap, orderItemList);
        }

        // 刷新美国关税SKU收税列表
        if (InstaCountry.US.equals(order.country()) && StringUtils.isNotBlank(usCustomsTaxSwitch) && Boolean.parseBoolean(usCustomsTaxSwitch)) {
            this.skuRedisRefresh(forUpdateStockList, order);
        }

        return forUpdateStockList.stream()
                .filter(stock -> generalStorageId.equals(stock.getStorageId()) && stock.getStockCount() <= CommodityStockConstants.STOCK_CACHE_REFRESH_THRESHOLD)
                .map(ProductCommodityStock::getCommodityId)
                .collect(Collectors.toList());
    }

    /**
     * 刷新关税sku列表缓存
     *
     * @param forUpdateStockList
     * @param order
     */
    private void skuRedisRefresh(List<ProductCommodityStock> forUpdateStockList, Order order) {
        if (!InstaCountry.US.equals(order.country())) {
            return;
        }
        LOGGER.info("[库存扣减]刷新美国关税SKU列表开始. 订单号:{}", order.getOrderNumber());
        // us本地仓
        List<Integer> localStorageIds = storageInfoService.listByArea(order.country().name()).stream().map(StorageInfo::getId).collect(Collectors.toList());
        Map<Integer, Integer> localStorageStockMap = forUpdateStockList.stream()
                .filter(stock -> localStorageIds.contains(stock.getStorageId()))
                .collect(Collectors.groupingBy(ProductCommodityStock::getCommodityId, Collectors.summingInt(ProductCommodityStock::getStockCount)));

        // 总库存数=0的sku列表
        List<Integer> skuList = localStorageStockMap.entrySet().stream().filter(entry -> entry.getValue() <= 0).map(Map.Entry::getKey).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuList)) {
            LOGGER.info("[库存扣减]本次订单商品各仓库存充足无需刷新关税SKU列表. 订单号:{}", order.getOrderNumber());
            return;
        }

        Long add = RedisTemplateUtil.getSetOperations().add(RedisKeyConstant.STORE_US_CUSTOMS_TAX_SKU_KEY, skuList.toArray());
        LOGGER.info("[库存扣减]刷新美国关税SKU列表结束. 订单号: {}, 共计刷新：{} 个元素, 本次sku列表 : {}", order.getOrderNumber(), add, JSON.toJSONString(skuList));
    }

    /**
     * 常规仓库库存扣减
     *
     * @param orderItemList            订单项列表
     * @param commodityStockMap        套餐id与可发货仓库集合的映射关系
     * @param deductionOrderStorageIds 扣减顺序
     */
    private List<ProductCommodityStock> otherWarehouseStockDeduction(List<OrderItem> orderItemList, Map<Integer, List<ProductCommodityStock>> commodityStockMap, List<Integer> deductionOrderStorageIds) {
        // 遍历订单项，对每个订单项进行库存扣减处理
        return orderItemList.stream()
                .map(orderItem -> {
                    // 套餐id
                    Integer commodityId = orderItem.getCommodity();

                    // 套餐的可发货仓库集合
                    List<ProductCommodityStock> commodityStockList = commodityStockMap.get(commodityId);

                    // key -> 仓库id  value -> 库存
                    Map<Integer, ProductCommodityStock> storageCommodityStockMap = commodityStockList.stream().collect(Collectors.toMap(ProductCommodityStock::getStorageId, p -> p));

                    // 按扣减顺序，将仓库排序
                    commodityStockList = this.processSortedStocks(deductionOrderStorageIds, storageCommodityStockMap);

                    // 获取订单项购买数量
                    Integer quantity = orderItem.getNumber();

                    // 执行库存扣减逻辑
                    return this.doStockDeductionRule(quantity, commodityStockList);
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * US本地仓库库存扣减
     *
     * @param orderItemList
     * @param commodityStockMap
     * @param deductionOrderStorageIds
     * @param country
     * @return
     */
    private List<ProductCommodityStock> usWarehouseStockDeduction(List<OrderItem> orderItemList, Map<Integer, List<ProductCommodityStock>> commodityStockMap, List<Integer> deductionOrderStorageIds, InstaCountry country) {
        // 订单商品对应套餐ID列表
        List<Integer> commodityIdList = orderItemList.stream().map(OrderItem::getCommodity).collect(Collectors.toList());

        // 获取组合商品列表
        List<ProductCommodityType> bundleCommodityList = productCommodityTypeService.listByCommodityIdsAndType(commodityIdList, CommodityTypeEnum.BUNDLE);

        if (CollectionUtils.isEmpty(bundleCommodityList)) {
            return this.otherWarehouseStockDeduction(orderItemList, commodityStockMap, deductionOrderStorageIds);
        }

        // 组合商品id列表
        List<Integer> bundleCommodityIds = bundleCommodityList.stream().map(ProductCommodityType::getCommodityId).collect(Collectors.toList());
        // 组合商品map
        Map<Integer, List<BundleCommodityDetail>> bundleCommodityMap = Optional.ofNullable(bundleCommodityDetailService.listByBundleCommodityId(bundleCommodityIds))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream().collect(Collectors.groupingBy(BundleCommodityDetail::getCommodityId)))
                .orElse(new HashMap<>(0));


        // 都扣减库存商品map(组合商品库存数还未二次更新)
        Map<Integer, OrderItem> forUpdateItemStockMap = new HashMap<>();
        // 第一次计算待扣减库存（不含组合商品自身），若是组合商品则平铺到最小粒度sku
        for (OrderItem orderItem : orderItemList) {
            // 当前商品购买数量
            Integer buyNum = orderItem.getNumber();
            // 获取组合商品子明细
            List<BundleCommodityDetail> bundleCommodityDetailList = bundleCommodityMap.get(orderItem.getCommodity());
            if (CollectionUtils.isEmpty(bundleCommodityDetailList)) {
                if (forUpdateItemStockMap.containsKey(orderItem.getCommodity())) {
                    OrderItem item = forUpdateItemStockMap.get(orderItem.getCommodity());
                    int totalNum = item.getNumber() + buyNum;
                    orderItem.setNumber(totalNum);
                    forUpdateItemStockMap.put(orderItem.getCommodity(), orderItem);
                } else {
                    forUpdateItemStockMap.put(orderItem.getCommodity(), orderItem);
                }
            } else {
                bundleCommodityDetailList
                        .stream()
                        .forEach(bundleCommodityDetail -> {
                            Integer subCommodityId = bundleCommodityDetail.getSubCommodityId();
                            // 子商品在当前一件标准组合商品中的最低构成数
                            Integer childCommodityNum = bundleCommodityDetail.getNumber();
                            // 子商品本次总扣减数 = tu购买数量 * 单个标准件数量
                            int childCommodityTotalNum = buyNum * childCommodityNum;
                            OrderItem singleItem;
                            if (forUpdateItemStockMap.containsKey(subCommodityId)) {
                                singleItem = forUpdateItemStockMap.get(subCommodityId);
                                childCommodityTotalNum += singleItem.getNumber();
                            } else {
                                singleItem = new OrderItem();
                                singleItem.setCommodity(subCommodityId);
                            }
                            singleItem.setNumber(childCommodityTotalNum);
                            forUpdateItemStockMap.put(subCommodityId, singleItem);
                        });
            }
        }

        // 开始组合商品的待扣库存数计算
        for (OrderItem orderItem : orderItemList) {
            // 获取组合商品子明细
            List<BundleCommodityDetail> bundleCommodityDetailList = bundleCommodityMap.get(orderItem.getCommodity());
            if (CollectionUtils.isEmpty(bundleCommodityDetailList)) {
                continue;
            }
            Integer bundleItemNum = bundleCommodityDetailList.stream()
                    .map(bundleCommodityDetail -> {
                        OrderItem childItem = forUpdateItemStockMap.get(bundleCommodityDetail.getSubCommodityId());
                        // 子商品总待扣减数
                        Integer totalNum = childItem.getNumber();
                        // 子商品在当前一件标准组合商品中的最低构成数
                        Integer childCommodityNum = bundleCommodityDetail.getNumber();
                        return new BigDecimal(String.valueOf(totalNum)).divide(new BigDecimal(String.valueOf(childCommodityNum)), 0, BigDecimal.ROUND_UP).intValue();
                    })
                    .max(Comparator.comparing(Integer::intValue))
                    .get();

            orderItem.setNumber(bundleItemNum);
            forUpdateItemStockMap.put(orderItem.getCommodity(), orderItem);
        }

        // 最终要扣库存的商品列表（含组合商品拆分出来的子项）
        List<OrderItem> orderItems = Lists.newArrayList(forUpdateItemStockMap.values());
        // 最终的套餐ID列表
        commodityIdList = orderItems.stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        // 把可发货仓按套餐分组
        List<ProductCommodityStock> commodityStocks = productCommodityStockService.listByCommodityIdsAndArea(commodityIdList, country.name());
        // key -> commodityId  value -> 可发货仓库集合
        commodityStockMap = commodityStocks.stream().collect(Collectors.groupingBy(ProductCommodityStock::getCommodityId, Collectors.toList()));
        // 库存扣减
        return this.otherWarehouseStockDeduction(orderItems, commodityStockMap, deductionOrderStorageIds);
    }

    /**
     * 满足条件进行库存告警
     *
     * @param generalStorageId
     * @param forUpdateStockList
     */
    private void stockNotice(Integer generalStorageId, List<ProductCommodityStock> forUpdateStockList) {
        // 告警时机为总仓库存变为小于等于2时
        List<Integer> needNoticeCommodityIds = forUpdateStockList.stream()
                .filter(stock -> generalStorageId.equals(stock.getStorageId()) && stock.getStockCount() <= CommodityStockConstants.STOCK_ALARM_VALUE)
                .map(ProductCommodityStock::getCommodityId)
                .collect(Collectors.toList());

        // 需要告警的套餐不为空，走MQ进行库存告警
        if (CollectionUtils.isNotEmpty(needNoticeCommodityIds)) {
            DoubleCheckBO doubleCheck = new DoubleCheckBO();
            doubleCheck.setCheckType(DoubleCheckEnum.CommodityStockNoticeCheck);
            doubleCheck.setCommodityIds(needNoticeCommodityIds);
            doubleCheckSendHelper.sendDoubleCheckMessage(doubleCheck);
        }
    }

    /**
     * 美国仓库存告警
     *
     * @param commodityStockMap
     * @param orderItemList
     */
    private void usStockNotice(Map<Integer, List<ProductCommodityStock>> commodityStockMap, List<OrderItem> orderItemList) {
        // key -> commodityId  value -> number
        Map<Integer, Integer> orderItemMap = orderItemList.stream()
                .collect(
                        Collectors.toMap(OrderItem::getCommodity, OrderItem::getNumber, Integer::sum)
                );
        // 检查是否符合告警时机
        List<Integer> needNoticeCommodityIds = commodityStockMap.entrySet()
                .stream()
                .filter(entry ->
                {
                    Integer commodityId = entry.getKey();
                    List<ProductCommodityStock> stocks = entry.getValue();
                    int remainingStock = stocks.stream().mapToInt(ProductCommodityStock::getStockCount).sum();
                    int originalStock = remainingStock + orderItemMap.get(commodityId);
                    // 触发条件：套餐在指定套餐集合中，初始库存高于安全库存，但扣除后低于等于安全库存
                    return commodityCommonConfig.getUsStockNoticeCommodityIds().contains(commodityId)
                            && originalStock > commodityCommonConfig.getUsSafeStock()
                            && remainingStock <= commodityCommonConfig.getUsSafeStock();
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        // 告警
        if (CollectionUtils.isNotEmpty(needNoticeCommodityIds)) {
            // 拼接告警文案
            String alertMessage = String.format("套餐ID{%s}在美国海外仓库存总和已低于安全库存{%s}，请及时监控库存和回货情况",
                    StringUtils.join(needNoticeCommodityIds, ","),
                    commodityCommonConfig.getUsSafeStock());
            // 发送飞书消息
            FeiShuMessageUtil.storeGeneralMessage(alertMessage, FeiShuGroupRobot.UsOutOfStockNotice, FeiShuAtUser.LCN, FeiShuAtUser.XK);
        }
    }

    /**
     * 库存更新
     *
     * @param commodityStockParams
     */
    public void doUpdateStocks(CommodityStockInfoDTO commodityStockParams) {
        // 套餐id
        Integer commodityId = commodityStockParams.getCommodityId();
        // 要更新的库存信息
        List<CommodityStorageStockDTO> stockInfos = commodityStockParams.getStockInfos();

        // 参数预处理
        this.preHandleStockInfos(stockInfos);

        List<Integer> storageIds = stockInfos.stream().map(CommodityStorageStockDTO::getStorageId).collect(Collectors.toList());
        // 查出套餐库存信息
        List<ProductCommodityStock> commodityStocks = productCommodityStockService.listByStorageIdsAndCommodity(commodityId, storageIds);
        List<Integer> commodityStockIds = commodityStocks.stream().map(ProductCommodityStock::getId).collect(Collectors.toList());
        // 参数构造
        List<ProductCommodityStock> commodityStockList = new ArrayList<>();
        List<List<StorageDeliveryArea>> storageDeliveryAreas = new ArrayList<>();
        stockInfos.forEach(stockInfo -> {
            commodityStockList.add(new ProductCommodityStock(stockInfo.getStorageId(), commodityId, stockInfo.getStockCount()));
            List<StorageDeliveryArea> deliveryAreas = new ArrayList<>();
            stockInfo.getDeliveryAreaList().forEach(area -> {
                deliveryAreas.add(new StorageDeliveryArea(area));
            });
            storageDeliveryAreas.add(deliveryAreas);
        });

        // 该套餐没有配置，走保存逻辑
        List<ProductCommodityStock> stocks = productCommodityStockService.listByCommodity(commodityId);
        if (CollectionUtils.isEmpty(stocks)) {
            productCommodityStockService.doSaveStockCount(commodityStockList, storageDeliveryAreas);
            return;
        }
        // 执行更新操作
        productCommodityStockService.doUpdateStockCount(commodityStockIds, commodityStockList, storageDeliveryAreas);
    }

    /**
     * 获取套餐全部库存数量
     * 总仓按负数计算总和
     *
     * @param commodityId
     * @return
     */
    public Integer getAllStockCount(Integer commodityId) {
        Integer generalStorageId = storageInfoService.getGeneralStorageId();
        List<ProductCommodityStock> commodityStocks = productCommodityStockService.listByCommodity(commodityId);
        return commodityStocks.stream()
                .map(commodityStock -> generalStorageId.equals(commodityStock.getStorageId()) ? commodityStock.getStockCount() : Math.max(0, commodityStock.getStockCount()))
                .reduce(0, (a, b) -> Math.min(a + b, CommodityStockLimitType.unlimited_stock.getQuantity()));
    }

    /**
     * 获取某套餐在某地的库存数量
     *
     * @param commodityId
     * @param instaCountry
     * @return
     */
    public Integer getByCommodityAndArea(Integer commodityId, InstaCountry instaCountry) {
        List<ProductCommodityStock> commodityStockList = productCommodityStockService.listByCommodityId(commodityId);
        if (CollectionUtils.isEmpty(commodityStockList)) {
            return 0;
        }
        Integer generalStorageId = storageInfoService.getGeneralStorageId();
        List<Integer> commodityStockIds = commodityStockList.stream().map(ProductCommodityStock::getId).collect(Collectors.toList());
        List<StorageDeliveryArea> deliveryAreas = storageDeliveryAreaService.listByAreaAndStockIds(instaCountry.name(), commodityStockIds);
        List<Integer> deliveryStockIds = deliveryAreas.stream().map(StorageDeliveryArea::getCommodityStockId).distinct().collect(Collectors.toList());
        // 可发货库存集合
        List<ProductCommodityStock> deliveryStocks = commodityStockList.stream().filter(commodityStock -> deliveryStockIds.contains(commodityStock.getId())
                || generalStorageId.equals(commodityStock.getStorageId())).collect(Collectors.toList());
        return deliveryStocks.stream()
                .map(commodityStock -> Math.max(0, commodityStock.getStockCount()))
                .reduce(0, (a, b) -> Math.min(a + b, CommodityStockLimitType.unlimited_stock.getQuantity()));
    }

    /**
     * 批量更新套餐库存
     *
     * @param commodityStockMap
     */
    public void batchUpdateCommodityStock(Map<Integer, List<CommodityStockBO>> commodityStockMap) {
        if (CommonUtil.isEmpty(commodityStockMap)) {
            return;
        }
        List<ProductCommodityStock> productCommodityStockList = commodityStockMap.entrySet().stream().map(entry -> entry.getValue()
                        .stream()
                        .map(commodityStock -> new ProductCommodityStock(entry.getKey(), commodityStock.getCommodityId(), commodityStock.getStoreSellableQuantity()))
                        .collect(Collectors.toList()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 更新库存
        productCommodityStockService.updateCommodityStocks(productCommodityStockList);
    }

    /**
     * 批量扣减，仅允许最后一个仓库库存为负数
     *
     * @param quantity           扣减数量
     * @param commodityStockList 库存列表（按扣减顺序排列）
     * @return 待更新库存
     */
    private List<ProductCommodityStock> doStockDeductionRule(Integer quantity, List<ProductCommodityStock> commodityStockList) {
        List<ProductCommodityStock> commodityStocks = new ArrayList<>();
        // 按照事先编排好的顺序进行扣减
        for (int i = 0; i < commodityStockList.size(); i++) {
            // 是否扣减到最后一个仓库
            boolean isLast = i == commodityStockList.size() - 1;

            // 待更新库存
            ProductCommodityStock commodityStock = commodityStockList.get(i);

            // 当前库存
            Integer stockCount = commodityStock.getStockCount();

            // 本轮扣减数量
            int deductStock = Math.min(quantity, stockCount);

            // 新的库存值
            int newStockCount;
            if (isLast) {
                // 这里把所有quantity减掉，允许出现负数
                newStockCount = stockCount - quantity;
            } else {
                newStockCount = stockCount - deductStock;
            }

            // 设置新的库存值
            commodityStock.setStockCount(newStockCount);

            // 待更新库存，收集起来，批量更新
            commodityStocks.add(commodityStock);

            // 更新剩余需要扣减的数量
            quantity -= deductStock;

            // 本轮已扣完所有
            if (quantity <= 0) {
                return commodityStocks;
            }
        }
        return commodityStocks;
    }

    /**
     * 将可发货仓库排序
     *
     * @param deductionOrderStorageIds
     * @param storageCommodityStockMap
     * @return
     */
    private List<ProductCommodityStock> processSortedStocks(List<Integer> deductionOrderStorageIds, Map<Integer, ProductCommodityStock> storageCommodityStockMap) {
        List<ProductCommodityStock> sortedStocks = new ArrayList<>(storageCommodityStockMap.size());
        deductionOrderStorageIds.forEach(storageId -> {
            ProductCommodityStock productCommodityStock = storageCommodityStockMap.get(storageId);
            if (productCommodityStock != null) {
                sortedStocks.add(productCommodityStock);
            }
        });
        return sortedStocks;
    }

    /**
     * 参数特殊处理
     *
     * @param stockInfos
     */
    private void preHandleStockInfos(List<CommodityStorageStockDTO> stockInfos) {
        // 总仓id
        Integer generalStorageId = storageInfoService.getGeneralStorageId();

        // 总仓可发货地区置空
        stockInfos.forEach(stockInfo -> {
            if (generalStorageId.equals(stockInfo.getStorageId())) {
                stockInfo.setDeliveryAreaList(new ArrayList<>());
            }
        });
    }

    /**
     * 工单套餐设置无限库存
     *
     * @param commodityId
     */
    @Async
    public void repairCommodityStockInit(Integer commodityId) {
        LOGGER.info(String.format("工单套餐库存初始化开始，套餐id：%d", commodityId));
        try {
            productCommodityStockService.addUnlimitedStock(commodityId);
        } catch (Exception e) {
            LOGGER.error(String.format("工单套餐库存初始化失败，套餐id：%d，异常原因：%s", commodityId, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("工单套餐库存初始化失败，套餐id：%d，异常原因：%s", commodityId, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        LOGGER.info(String.format("工单套餐库存初始化完成，套餐id：%d", commodityId));
    }

    /**
     * 填充默认的初始库存与可发货地区
     *
     * @param commodityId
     */
    @Async
    public void fillDeliveryArea(Integer commodityId) {
        LOGGER.info(String.format("仓库填充默认可发货地区开始，套餐id：%d", commodityId));
        try {
            // 先查询该套餐是否已配置库存，已配置，直接退出
            List<ProductCommodityStock> existCommodityStocks = productCommodityStockService.listByCommodityId(commodityId);
            if (CollectionUtils.isNotEmpty(existCommodityStocks)) {
                return;
            }
            // 未查询到仓库，提前退出
            List<StorageInfo> storageInfos = storageInfoService.listStorageInfos();
            if (CollectionUtils.isEmpty(storageInfos)) {
                return;
            }
            // 先初始化待新增的实体类
            List<ProductCommodityStock> commodityStockList = new ArrayList<>(storageInfos.size());
            List<List<StorageDeliveryArea>> deliveryAreaList = new ArrayList<>(storageInfos.size());
            // 为每个仓库创建默认库存和配送区域
            storageInfos.forEach(
                    storageInfo -> {
                        Integer storageId = storageInfo.getId();
                        // 得到每个仓库的默认配送区域
                        List<InstaCountry> defaultAreas = CommodityStockConstants.STORAGE_DEFAULT_DELIVERY_AREA.get(storageId);
                        List<StorageDeliveryArea> deliveryAreas = CollectionUtils.isEmpty(defaultAreas) ?
                                new ArrayList<>() :
                                defaultAreas.stream()
                                        .map(area -> new StorageDeliveryArea(area.name()))
                                        .collect(Collectors.toList());
                        // 每个仓库的默认库存数给到9个9
                        commodityStockList.add(new ProductCommodityStock(storageId, commodityId, CommodityStockLimitType.unlimited_stock.getQuantity()));
                        deliveryAreaList.add(deliveryAreas);
                    }
            );
            // 执行入库操作
            productCommodityStockService.doSaveStockCount(commodityStockList, deliveryAreaList);
        } catch (Exception e) {
            LOGGER.error(String.format("仓库填充默认可发货地区失败，套餐id：%d，异常原因：%s", commodityId, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(
                    String.format("仓库填充默认可发货地区失败，套餐id：%d，异常原因：%s", commodityId, e.getMessage()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        LOGGER.info(String.format("仓库填充默认可发货地区完成，套餐id：%d", commodityId));
    }
}
