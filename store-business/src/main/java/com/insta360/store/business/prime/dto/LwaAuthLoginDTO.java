package com.insta360.store.business.prime.dto;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/17
 */
public class LwaAuthLoginDTO implements Serializable {

    /**
     * 登录授权码
     */
    @NotBlank(message = "code不允许为空")
    private String code;

    /**
     * 密文 codeVerifier 加密
     */
    @NotBlank(message = "codeVerifier不允许为空")
    private String codeVerifier;

    /**
     * 跳转地址
     */
    @NotBlank(message = "redirectUri不允许为空")
    private String redirectUri;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCodeVerifier() {
        return codeVerifier;
    }

    public void setCodeVerifier(String codeVerifier) {
        this.codeVerifier = codeVerifier;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

}
