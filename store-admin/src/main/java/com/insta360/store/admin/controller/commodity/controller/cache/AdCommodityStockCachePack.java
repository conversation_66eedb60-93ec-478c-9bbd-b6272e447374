package com.insta360.store.admin.controller.commodity.controller.cache;

import com.insta360.store.business.commodity.dto.CommodityStockInfoDTO;
import com.insta360.store.business.commodity.service.impl.helper.CommodityStockHelper;
import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/18 上午10:24
 */
@Component
public class AdCommodityStockCachePack {

    @Autowired
    CommodityStockHelper commodityStockHelper;

    /**
     * 更新/新增库存，可发货地区
     *
     * @param cachePutKeyParameter 缓存更新参数
     * @param commodityStockParams 库存信息
     */
    @CacheEvict(value = {CacheableType.COMMODITY_RECOMMENDATION_CART, CacheableType.COMMODITY_LIST_INFO}, allEntries = true)
    @CachePutMonitor(cacheableType = CachePutType.COMMODITY_DISPLAY)
    public void doUpsertStockCount(CachePutKeyParameterBO cachePutKeyParameter, CommodityStockInfoDTO commodityStockParams) {
        commodityStockHelper.doUpdateStocks(commodityStockParams);
    }
}
