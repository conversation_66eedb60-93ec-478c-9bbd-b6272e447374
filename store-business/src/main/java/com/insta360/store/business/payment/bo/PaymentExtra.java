package com.insta360.store.business.payment.bo;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.user.model.StoreAccount;

/**
 * @Author: hyc
 * @Date: 2019/2/16
 * @Description:
 */
public class PaymentExtra {

    private InstaLanguage language;
    private String clientIP;
    private boolean isMobile;

    /**
     * 支付机构
     */
    private StorePaymentMethodEnum paymentMethod;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 商城账户
     */
    private StoreAccount storeAccount;

    /**
     * 微信支付参数
     */
    private String weixinCode;

    /**
     * 信用卡支付参数
     */
    private String cardNumber;
    private Integer cardYear;
    private Integer cardMonth;
    private String name;
    private String cvv;

    /**
     * 钱海参数
     */
    private String cardData;
    private String oceanPaymentMethods;
    private String lookupReferenceId;

    /**
     * 信用卡支付
     */
    private Boolean threeDomainTrade;
    private Boolean exemption;

    /**
     * cko参数
     */
    private String ckoPaymentMethod;
    private String ckoIdealBic;
    private Integer paymentChannelId;

    /**
     * 支付终端的类型，仅移动端有此参数
     */
    private String app;

    /**
     * WorldPay支付参数
     */
    private String merchantCode;
    private String installationId;
    private String xmlUsername;
    private String xmlPassword;
    private String checkCode;

    /**
     * Apple Pay参数
     */
    private ApplePayBO applePayBo;

    /**
     * Google Pay参数
     */
    private GooglePayBO googlePayBo;

    /**
     * token类型
     *
     * @see com.insta360.store.business.payment.enums.CkoTokenType
     */
    private String tokenType;

    /**
     * token格式
     */
    private String tokenFormat;

    /**
     * 透传参数
     */
    private Order order;

    /**
     * 是否扣款尝试中
     */
    private Boolean isRenewDeductionRetry;

    public InstaLanguage getLanguage() {
        return language;
    }

    public void setLanguage(InstaLanguage language) {
        this.language = language;
    }

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public String getWeixinCode() {
        return weixinCode;
    }

    public void setWeixinCode(String weixinCode) {
        this.weixinCode = weixinCode;
    }

    public String getCardData() {
        return cardData;
    }

    public void setCardData(String cardData) {
        this.cardData = cardData;
    }

    public boolean isMobile() {
        return isMobile;
    }

    public void setMobile(boolean mobile) {
        isMobile = mobile;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getOceanPaymentMethods() {
        return oceanPaymentMethods;
    }

    public void setOceanPaymentMethods(String oceanPaymentMethods) {
        this.oceanPaymentMethods = oceanPaymentMethods;
    }

    public String getCkoPaymentMethod() {
        return ckoPaymentMethod;
    }

    public void setCkoPaymentMethod(String ckoPaymentMethod) {
        this.ckoPaymentMethod = ckoPaymentMethod;
    }

    public String getCkoIdealBic() {
        return ckoIdealBic;
    }

    public void setCkoIdealBic(String ckoIdealBic) {
        this.ckoIdealBic = ckoIdealBic;
    }

    public Integer getPaymentChannelId() {
        return paymentChannelId;
    }

    public void setPaymentChannelId(Integer paymentChannelId) {
        this.paymentChannelId = paymentChannelId;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public Integer getCardYear() {
        return cardYear;
    }

    public void setCardYear(Integer cardYear) {
        this.cardYear = cardYear;
    }

    public Integer getCardMonth() {
        return cardMonth;
    }

    public void setCardMonth(Integer cardMonth) {
        this.cardMonth = cardMonth;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getLookupReferenceId() {
        return lookupReferenceId;
    }

    public void setLookupReferenceId(String lookupReferenceId) {
        this.lookupReferenceId = lookupReferenceId;
    }

    public Boolean getThreeDomainTrade() {
        return threeDomainTrade;
    }

    public void setThreeDomainTrade(Boolean threeDomainTrade) {
        this.threeDomainTrade = threeDomainTrade;
    }

    public Boolean getExemption() {
        return exemption;
    }

    public void setExemption(Boolean exemption) {
        this.exemption = exemption;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getInstallationId() {
        return installationId;
    }

    public void setInstallationId(String installationId) {
        this.installationId = installationId;
    }

    public String getXmlUsername() {
        return xmlUsername;
    }

    public void setXmlUsername(String xmlUsername) {
        this.xmlUsername = xmlUsername;
    }

    public String getXmlPassword() {
        return xmlPassword;
    }

    public void setXmlPassword(String xmlPassword) {
        this.xmlPassword = xmlPassword;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public ApplePayBO getApplePayBo() {
        return applePayBo;
    }

    public void setApplePayBo(ApplePayBO applePayBo) {
        this.applePayBo = applePayBo;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public GooglePayBO getGooglePayBo() {
        return googlePayBo;
    }

    public void setGooglePayBo(GooglePayBO googlePayBo) {
        this.googlePayBo = googlePayBo;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public StoreAccount getStoreAccount() {
        return storeAccount;
    }

    public void setStoreAccount(StoreAccount storeAccount) {
        this.storeAccount = storeAccount;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public String getTokenFormat() {
        return tokenFormat;
    }

    public void setTokenFormat(String tokenFormat) {
        this.tokenFormat = tokenFormat;
    }

    public Boolean getIsRenewDeductionRetry() {
        return isRenewDeductionRetry;
    }

    public void setIsRenewDeductionRetry(Boolean renewDeductionRetry) {
        isRenewDeductionRetry = renewDeductionRetry;
    }

    public StorePaymentMethodEnum getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(StorePaymentMethodEnum paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    @Override
    public String toString() {
        return "PaymentExtra{" +
                "language=" + language +
                ", clientIP='" + clientIP + '\'' +
                ", isMobile=" + isMobile +
                ", paymentMethod=" + paymentMethod +
                ", userAgent='" + userAgent + '\'' +
                ", storeAccount=" + storeAccount +
                ", weixinCode='" + weixinCode + '\'' +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardYear=" + cardYear +
                ", cardMonth=" + cardMonth +
                ", name='" + name + '\'' +
                ", cvv='" + cvv + '\'' +
                ", cardData='" + cardData + '\'' +
                ", oceanPaymentMethods='" + oceanPaymentMethods + '\'' +
                ", lookupReferenceId='" + lookupReferenceId + '\'' +
                ", threeDomainTrade=" + threeDomainTrade +
                ", exemption=" + exemption +
                ", ckoPaymentMethod='" + ckoPaymentMethod + '\'' +
                ", ckoIdealBic='" + ckoIdealBic + '\'' +
                ", paymentChannelId=" + paymentChannelId +
                ", app='" + app + '\'' +
                ", merchantCode='" + merchantCode + '\'' +
                ", installationId='" + installationId + '\'' +
                ", xmlUsername='" + xmlUsername + '\'' +
                ", xmlPassword='" + xmlPassword + '\'' +
                ", checkCode='" + checkCode + '\'' +
                ", applePayBo=" + applePayBo +
                ", googlePayBo=" + googlePayBo +
                ", tokenType='" + tokenType + '\'' +
                ", tokenFormat='" + tokenFormat + '\'' +
                ", order=" + order +
                ", isRenewDeductionRetry=" + isRenewDeductionRetry +
                '}';
    }
}
