package com.insta360.store.business.commodity;

import com.insta360.compass.devtool.DaoGenerator;
import com.insta360.store.business.common.constants.DataSourceConstant;

import java.util.Arrays;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
public class DevTool {

    public static void main(String[] args) {
        new DaoGenerator()
                .setDataSource(DataSourceConstant.STORE_DATASOURCE)
                .setReferenceClass(DevTool.class)
                .setModuleName("product")
                .setTables(Arrays.asList(
                        "product_commodity_customs_tax_rate"
                ))
                .setWithMapperXml(true)
                .setWithService(true)
                .run();
    }

}
