package com.insta360.store.admin.controller.insurance.controller;

import cn.hutool.core.lang.ObjectId;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.insurance.format.AdInsurancePack;
import com.insta360.store.admin.controller.insurance.helper.InsuranceOperationRecordHelper;
import com.insta360.store.admin.controller.insurance.vo.AdCareCardInsuranceVO;
import com.insta360.store.business.admin.upload.bo.UploadExcelBO;
import com.insta360.store.business.admin.upload.enums.UploadBusinessType;
import com.insta360.store.business.admin.upload.enums.UploadTemplateType;
import com.insta360.store.business.admin.upload.service.helper.AdminUploadHelper;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.insurance.dto.CareActivationCardDTO;
import com.insta360.store.business.insurance.dto.CareActivationCardQueryDTO;
import com.insta360.store.business.insurance.enums.CareOperationType;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.insurance.model.CareInsuranceActivationCard;
import com.insta360.store.business.insurance.model.InsuranceOperationRecord;
import com.insta360.store.business.insurance.service.CareActivationCardService;
import com.insta360.store.business.insurance.service.CareInsuranceActivationCardService;
import com.insta360.store.business.insurance.service.InsuranceOperationRecordService;
import com.insta360.store.business.insurance.service.impl.fatory.InsuranceFactory;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.insurance.service.impl.helper.card.ActivationCardCheckPassword;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/04/10
 * @Description:
 */
@RestController
@PermissionResource(code = "careActivationCard", desc = "保险-Care实体卡配置")
public class AdCareActivationCardApi extends BaseAdminApi {

    @Autowired
    CareInsuranceActivationCardService careInsuranceActivationCardService;

    @Autowired
    AdInsurancePack insurancePack;

    @Autowired
    InsuranceFactory insuranceFactory;

    @Autowired
    InsuranceOperationRecordService insuranceOperationRecordService;

    @Autowired
    InsuranceOperationRecordHelper insuranceOperationRecordHelper;

    @Autowired
    AdminUploadHelper adminUploadHelper;

    @Autowired
    CareActivationCardService careActivationCardService;

    /**
     * 实体卡care服务复杂查询
     *
     * @param careCardParam
     */
    @LogAttr(desc = "care实体卡复杂查询", logType = LogType.query)
    @Permission(code = "store.insurance.careActivationCard.query", desc = "实体卡care服务复杂查询")
    @PostMapping(path = "/admin/insurance/service/activationCard/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> query(@RequestBody CareActivationCardQueryDTO careCardParam) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(careCardParam.getPageNumber(), 1);
        pageQuery.setPageSize(careCardParam.getPageSize(), 10);

        PageResult<AdCareCardInsuranceVO> result = insurancePack.queryCareCard(careCardParam, pageQuery);
        return Response.ok(result);
    }

    /**
     * 密码校验
     *
     * @param careCardParam
     */
    @LogAttr(desc = "密码校验")
    @Permission(code = "store.insurance.careActivationCard.checkUser", desc = "密码校验")
    @PostMapping(path = "/admin/insurance/service/activationCard/checkUser", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> checkUser(@RequestBody CareActivationCardQueryDTO careCardParam) {
        if (Objects.isNull(careCardParam) || StringUtil.isBlank(careCardParam.getCheckCode())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        boolean result = ActivationCardCheckPassword.PASSWORD.equals(careCardParam.getCheckCode());
        return Response.ok("result", result);
    }

    /**
     * 禁用care实体卡
     *
     * @param careCardParam
     */
    @LogAttr(desc = "禁用care实体卡")
    @Permission(code = "store.insurance.careActivationCard.disable", desc = "禁用care实体卡")
    @PostMapping(path = "/admin/insurance/service/activationCard/disable", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> disable(@RequestBody @Validated CareActivationCardQueryDTO careCardParam) {
        if (Objects.nonNull(careCardParam) && !StringUtil.isBlank(careCardParam.getInsuranceNumber())) {
            careInsuranceActivationCardService.disable(careCardParam.getInsuranceNumber());

            // 操作日志记录
            InsuranceOperationRecord insuranceOperationRecord = new InsuranceOperationRecord();
            insuranceOperationRecord.setInsuranceNumber(careCardParam.getInsuranceNumber());
            insuranceOperationRecord.setOperationType(CareOperationType.CANCEL.getType());
            insuranceOperationRecord.setOperator(getOaUserInfo().getName());
            insuranceOperationRecord.setOperationReason(careCardParam.getOperationReason());
            insuranceOperationRecordService.save(insuranceOperationRecord);
        }
        return Response.ok();
    }

    /**
     * 修改care实体卡序列号
     *
     * @param careCardParam
     */
    @LogAttr(desc = "修改care实体卡序列号")
    @Permission(code = "store.insurance.careActivationCard.updateSerial", desc = "修改care实体卡序列号")
    @PostMapping(path = "/admin/insurance/service/activationCard/updateSerial", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateSerial(@RequestBody @Validated CareActivationCardQueryDTO careCardParam) {
        CareInsuranceActivationCard careInsuranceActivationCard = careInsuranceActivationCardService.getById(careCardParam.getId());
        if (careInsuranceActivationCard == null) {
            throw new InstaException(InsuranceErrorCode.InsuranceNotFoundException);
        }

        // 变更前序列号
        String serialFrom = careInsuranceActivationCard.getDeviceSerial();

        // 校验新序列号
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(ServiceType.care_card.name());
        insuranceHandler.adminCheckSerial(careCardParam.getDeviceSerial(), careInsuranceActivationCard.getDeviceType());

        // 更新
        careInsuranceActivationCard.setDeviceSerial(careCardParam.getDeviceSerial());
        careInsuranceActivationCardService.updateById(careInsuranceActivationCard);

        // 操作日志记录
        InsuranceOperationRecord insuranceOperationRecord = insuranceOperationRecordHelper.getInsuranceOperationRecord(
                careInsuranceActivationCard.getInsuranceNumber(),
                CareOperationType.UPDATE_SERIAL,
                getOaUserInfo().getName(),
                careCardParam.getOperationReason());
        insuranceOperationRecord.setSerialFrom(serialFrom);
        insuranceOperationRecord.setSerialTo(careCardParam.getDeviceSerial());
        insuranceOperationRecordService.save(insuranceOperationRecord);
        return Response.ok();
    }

    /**
     * 使用care实体卡
     *
     * @param careInsuranceActivationCardParam
     */
    @LogAttr(desc = "使用care实体卡")
    @Permission(code = "store.insurance.careActivationCard.useCard", desc = "使用care实体卡")
    @PostMapping(path = "/admin/insurance/service/activationCard/useCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> useCard(@RequestBody @Validated CareActivationCardQueryDTO careInsuranceActivationCardParam) {
        CareInsuranceActivationCard careInsuranceActivationCard = careInsuranceActivationCardService.getById(careInsuranceActivationCardParam.getId());
        if (careInsuranceActivationCard == null) {
            throw new InstaException(InsuranceErrorCode.InsuranceNotFoundException);
        }

        String insuranceType = careInsuranceActivationCardParam.getInsuranceType();
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(insuranceType);
        insuranceHandler.useCard(careInsuranceActivationCard, LocalDateTime.now());

        // 操作日志记录
        InsuranceOperationRecord insuranceOperationRecord = insuranceOperationRecordHelper.getInsuranceOperationRecord(
                careInsuranceActivationCard.getInsuranceNumber(),
                CareOperationType.USE,
                getOaUserInfo().getName(),
                careInsuranceActivationCardParam.getOperationReason());

        // 操作日志记录
        insuranceOperationRecordService.save(insuranceOperationRecord);
        return Response.ok();
    }

    /**
     * 延期care实体卡
     *
     * @param careCardParam
     */
    @LogAttr(desc = "延期care实体卡")
    @Permission(code = "store.insurance.careActivationCard.extendCard", desc = "延期care实体卡")
    @PostMapping(path = "/admin/insurance/service/activationCard/extendCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> extendCard(@RequestBody @Validated CareActivationCardQueryDTO careCardParam) {
        CareInsuranceActivationCard careInsuranceActivationCard = careInsuranceActivationCardService.getById(careCardParam.getId());
        if (careInsuranceActivationCard == null) {
            throw new InstaException(InsuranceErrorCode.InsuranceNotFoundException);
        }

        // 延期前过期时间
        LocalDateTime expireTimeFrom = careInsuranceActivationCard.getExpireTime();

        careInsuranceActivationCard.setExpireTime(careCardParam.getExpireTime());
        careInsuranceActivationCardService.updateById(careInsuranceActivationCard);

        // 操作日志记录
        InsuranceOperationRecord insuranceOperationRecord = insuranceOperationRecordHelper.getInsuranceOperationRecord(
                careInsuranceActivationCard.getInsuranceNumber(),
                CareOperationType.EXTEND,
                getOaUserInfo().getName(),
                careCardParam.getOperationReason());
        insuranceOperationRecord.setExpireTimeFrom(expireTimeFrom);
        insuranceOperationRecord.setExpireTimeTo(careCardParam.getExpireTime());
        insuranceOperationRecordService.save(insuranceOperationRecord);
        return Response.ok();
    }

    /**
     * 批量绑定实体卡
     *
     * @param careActivationCardParam
     */
    @LogAttr(desc = "批量绑定实体卡")
//    @Permission(code = "store.insurance.careActivationCard.batchBind", desc = "批量绑定实体卡")
    @PostMapping(path = "/admin/insurance/service/activationCard/batchBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> batchBind(@RequestBody CareActivationCardDTO careActivationCardParam) {
        String ossLink = careActivationCardParam.getOssLink();
        if (StringUtil.isBlank(ossLink)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "文件链接不存在");
        }

        String[] filenameArr = ossLink.split("\\.");
        String suffix = filenameArr[filenameArr.length - 1];
        if (!"xlsx".equals(suffix)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "不支持的文件格式");
        }

        // 发送上传通知
        UploadExcelBO uploadExcelBo = new UploadExcelBO();
        uploadExcelBo.setTaskId(ObjectId.next());
        uploadExcelBo.setExcelOssUrl(ossLink);
        uploadExcelBo.setUploadBusinessType(UploadBusinessType.care_card_bind.getType());
        uploadExcelBo.setJobNumber(getAdminJobNumber());
        uploadExcelBo.setUploadTemplateType(UploadTemplateType.SINGLE.getType());
        uploadExcelBo.setFileName(careActivationCardParam.getFileName());
        adminUploadHelper.uploadTransit(uploadExcelBo);
        return Response.ok();
    }

    /**
     * 批量绑定实体卡(新)
     *
     * @param ossLink
     * @param fileName
     * @return
     */
    @LogAttr(desc = "批量绑定实体卡(新)")
//    @Permission(code = "store.insurance.careActivationCard.batchBind", desc = "批量绑定实体卡")
    @GetMapping(path = "/admin/insurance/service/activationCard/batchBindCare")
    public Response<Object> batchBindCare(@RequestParam String ossLink, @RequestParam String fileName) {
        if (StringUtil.isBlank(ossLink)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "文件链接不存在");
        }

        String[] filenameArr = ossLink.split("\\.");
        String suffix = filenameArr[filenameArr.length - 1];
        if (!"xlsx".equals(suffix)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "不支持的文件格式");
        }

        // 发送上传通知
        UploadExcelBO uploadExcelBo = new UploadExcelBO();
        uploadExcelBo.setTaskId(ObjectId.next());
        uploadExcelBo.setExcelOssUrl(ossLink);
        uploadExcelBo.setUploadBusinessType(UploadBusinessType.care_card_bind.getType());
        uploadExcelBo.setJobNumber(getAdminJobNumber());
        uploadExcelBo.setUploadTemplateType(UploadTemplateType.SINGLE.getType());
        uploadExcelBo.setFileName(fileName);
        adminUploadHelper.uploadTransit(uploadExcelBo);
        return Response.ok();
    }

    /**
     * 批量修改code状态
     *
     * @param activationCodes
     * @return
     */
    @LogAttr(desc = "批量修改code状态")
    @PostMapping(path = "/admin/insurance/service/activationCard/batchBindCode", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> batchBindCode(@RequestBody List<String> activationCodes) {
        if (CollectionUtils.isEmpty(activationCodes)) {
            return Response.ok();
        }

        List<CareInsuranceActivationCard> careInsuranceActivationCards = careInsuranceActivationCardService.listByActivationCode(activationCodes);
        if (CollectionUtils.isEmpty(careInsuranceActivationCards)) {
            return Response.ok();
        }

        List<String> codeList = careInsuranceActivationCards.stream().map(CareInsuranceActivationCard::getActivationCode).distinct().collect(Collectors.toList());
        careActivationCardService.updateStateByCodes(codeList);
        return Response.ok();
    }
}
