package com.insta360.store.business.commodity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.insta360.compass.core.common.BaseModel;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@TableName("product_commodity_type")
public class ProductCommodityType extends BaseModel<ProductCommodityType> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品套餐id
     */
    private Integer commodityId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 0-独立商品，1-组合商品
     */
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public ProductCommodityType(Integer commodityId, Integer productId, Integer type) {
        this.commodityId = commodityId;
        this.productId = productId;
        this.type = type;
    }

    public ProductCommodityType() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ProductCommodityType{" +
                "id = " + id +
                ", commodityId = " + commodityId +
                ", productId = " + productId +
                ", type = " + type +
                ", createTime = " + createTime +
                ", updateTime = " + updateTime +
                "}";
    }
}
