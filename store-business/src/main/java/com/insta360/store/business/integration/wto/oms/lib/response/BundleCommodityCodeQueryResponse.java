package com.insta360.store.business.integration.wto.oms.lib.response;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/25 下午5:59
 */
public class BundleCommodityCodeQueryResponse extends BaseOmsResponse {

    /**
     * 响应数据
     */
    private List<String> data;

    /**
     * 参数解析
     *
     * @param resultJson
     * @return
     */
    public static BundleCommodityCodeQueryResponse parse(String resultJson) {
        return JSONObject.parseObject(resultJson, BundleCommodityCodeQueryResponse.class);
    }

    public List<String> getData() {
        return data;
    }

    public void setData(List<String> data) {
        this.data = data;
    }
}
