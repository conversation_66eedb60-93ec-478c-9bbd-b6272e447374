<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <springProperty scope="context" name="logging_path" source="logging.path"/>
    <springProperty scope="context" name="app_id" source="spring.application.name"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{EagleEye-TraceID:-}] %5p [%t] %-40.40logger{39} : traceId:[%X{mdc_trace_id}] %X{mdc_prefix} %X{mdc_prefix_sub} - %m%n</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="stdout"/>
    </root>

    <logger name="com.insta360.compass.core.web.log.RRLogFilter">
        <level value="DEBUG"/>
    </logger>

</configuration>